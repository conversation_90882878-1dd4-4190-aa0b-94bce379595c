<template>
	<div></div>
</template>

<script>
/* ===================================
 * vtu
 * Created by cjking on 2021/06/27.
 * Copyright 2021, Inc.
 * =================================== */
import 'vtk.js/Sources/favicon';

// Load the rendering pieces we want to use (for both WebGL and WebGPU)
import 'vtk.js/Sources/Rendering/Profiles/Geometry';

import vtkActor from 'vtk.js/Sources/Rendering/Core/Actor';
import vtkMapper from 'vtk.js/Sources/Rendering/Core/Mapper';
import vtkFullScreenRenderWindow from 'vtk.js/Sources/Rendering/Misc/FullScreenRenderWindow';
import vtkXMLPolyDataReader from 'vtk.js/Sources/IO/XML/XMLPolyDataReader';
import HttpDataAccessHelper from 'vtk.js/Sources/IO/Core/DataAccessHelper/HttpDataAccessHelper';
import { basename, noop } from '@/utils/utils';
import vtkDataArray from 'vtk.js/Sources/Common/Core/DataArray';
import { logger } from '@/utils/logger';
import vtkPointPicker from 'vtk.js/Sources/Rendering/Core/PointPicker';
import vtkSphereSource from 'vtk.js/Sources/Filters/Sources/SphereSource';
import vtkCoordinate from 'vtk.js/Sources/Rendering/Core/Coordinate';
import vtkScalarBarActor from 'vtk.js/Sources/Rendering/Core/ScalarBarActor';
import vtkColorMaps from 'vtk.js/Sources/Rendering/Core/ColorTransferFunction/ColorMaps';
import vtkColorTransferFunction from 'vtk.js/Sources/Rendering/Core/ColorTransferFunction';
import { ColorMode, ScalarMode } from 'vtk.js/Sources/Rendering/Core/Mapper/Constants';

const fileName = 'exm3_2_1.vtp';

const VColorMap = {
	WHITE: [1, 1, 1], // 白色
	BLACK: [0, 0, 0], // 黑色
	GREEN: [0.1, 0.8, 0.1],
	SELECTED: [52 / 255, 175 / 255, 245 / 255], // 选中色
};

function addFaceColor (arrayName, ds, values, r, g, b) {
	const size = ds.getNumberOfCells();
	const rgbArray = values || new Uint8Array(size * 3);
	if (!values) {
		let offset = 0;
		while (offset < size) {
			rgbArray[offset++] = r;
			rgbArray[offset++] = g;
			rgbArray[offset++] = b;
		}
	}

	ds.getCellData().setScalars(
		vtkDataArray.newInstance({
			name: arrayName || 'color',
			numberOfComponents: 3,
			values: rgbArray,
		}),
	);
}

export default {
	name: 'VtuView',
	data () {
		return {
			renderer: undefined,
			render: undefined,
			renderWindow: undefined,
		};
	},
	async mounted () {
		await this.init();
		this.initEvent();
	},
	methods: {
		async init () {
			// ----------------------------------------------------------------------------
			// Standard rendering code setup
			// ----------------------------------------------------------------------------
			const fullScreenRenderer = vtkFullScreenRenderWindow.newInstance();
			const renderer = fullScreenRenderer.getRenderer();
			this.renderer = renderer;
			const renderWindow = fullScreenRenderer.getRenderWindow();
			this.renderWindow = renderWindow;

			const resetCamera = renderer.resetCamera;
			const render = renderWindow.render;
			this.render = render;

			// ----------------------------------------------------------------------------
			// Example code
			// ----------------------------------------------------------------------------
			const url = `/models/vtp/${ fileName }`;
			const source = await this.downloadSingleData(url);
			if (source) {
				this.createRepresentation(source, { opacity: 0.2 });
				this.createRepresentation(source, { representation: 1 });

				// const values = [];
				// this.updateColor(source, values);
				// addFaceColor();

				const lookupTable = vtkColorTransferFunction.newInstance();
				const mapper = vtkMapper.newInstance({
					interpolateScalarsBeforeMapping: false,
					useLookupTableScalarRange: true,
					lookupTable,
					scalarVisibility: false,
				});
				const actor = vtkActor.newInstance();
				const scalars = source.getPointData().getScalars();
				const dataRange = [].concat(scalars ? scalars.getRange() : [0, 1]);
				// let activeArray = vtkDataArray;

				// --------------------------------------------------------------------
				// Color handling
				// --------------------------------------------------------------------
				this.applyPreset(lookupTable, dataRange);

				actor.setMapper(mapper);
				mapper.setInputData(source);
				renderer.addActor(actor);

				// 更新代理
				const RepresentationEnum = {
					// key: 'visibility:representation:edgeVisibility'
					'Hidden': '0:-1:0',
					'Points': '1:0:0',
					'Wireframe': '1:1:0',
					'Surface': '1:2:0',
					'Surface with Edge': '1:2:1',
				};
				this.updateRepresentation(RepresentationEnum['Surface with Edge'], actor);

				this.scalarBarActor = vtkScalarBarActor.newInstance();
				renderer.addActor(this.scalarBarActor);
				this.scalarBarActor.setScalarsToColors(mapper.getLookupTable());

				const options = source
					.getPointData()
					.getArrays()
					.map((a) => ({
						label: `(p) ${ a.getName() }`,
						value: `PointData:${ a.getName() }`,
					}));
				this.updateColorBy(options[0].value, dataRange, source, mapper, lookupTable);

				resetCamera();
				render();
			}
		},

		/**
		 * 更新代理
		 */
		updateRepresentation (value, actor) {
			const [visibility, representation, edgeVisibility] = value.split(':').map(Number);
			actor.getProperty().set({ representation, edgeVisibility });
			actor.setVisibility(!!visibility);
			this.render();
		},

		/**
		 * 颜色处理
		 */
		updateColorBy (colorByValue, dataRange, source, mapper, lookupTable) {
			const [location, colorByArrayName] = colorByValue.split(':');
			const interpolateScalarsBeforeMapping = location === 'PointData';
			let colorMode = ColorMode.DEFAULT;
			let scalarMode = ScalarMode.DEFAULT;
			const scalarVisibility = location.length > 0;
			if (scalarVisibility) {
				const newArray = source[`get${ location }`]().getArrayByName(colorByArrayName);
				const newDataRange = newArray.getRange();
				dataRange[0] = newDataRange[0];
				dataRange[1] = newDataRange[1];
				colorMode = ColorMode.MAP_SCALARS;
				scalarMode =
					location === 'PointData'
						? ScalarMode.USE_POINT_FIELD_DATA
						: ScalarMode.USE_CELL_FIELD_DATA;

				const numberOfComponents = newArray.getNumberOfComponents();
				if (numberOfComponents > 1) {
					// always start on magnitude setting
					if (mapper.getLookupTable()) {
						const lut = mapper.getLookupTable();
						lut.setVectorModeToMagnitude();
					}
				}
				this.scalarBarActor.setAxisLabel(colorByArrayName);
				this.scalarBarActor.setVisibility(true);
			} else {
				this.scalarBarActor.setVisibility(false);
			}
			mapper.set({
				colorByArrayName,
				colorMode,
				interpolateScalarsBeforeMapping,
				scalarMode,
				scalarVisibility,
			});
			this.applyPreset(lookupTable, dataRange);
		},

		applyPreset (lookupTable, dataRange) {
			const preset = vtkColorMaps.getPresetByName('rainbow');
			lookupTable.applyColorMap(preset);
			lookupTable.setMappingRange(dataRange[0], dataRange[1]);
			lookupTable.updateRange();
			this.renderWindow.render();
		},

		/**
		 * 初始化事件
		 */
		initEvent () {
			this.$nextTick(() => {
				// 覆盖默认事件
				this.renderWindow.getInteractor().onKeyPress((callData) => {
					const rwi = this.renderWindow.getInteractor();
					let actors = null;
					switch (callData.key) {
						case 'r':
						case 'R':
							callData.pokedRenderer.resetCamera();
							rwi.render();
							break;
						case 'w':
						case 'W':
						case 's':
						case 'S':
						case 'v':
						case 'V':
							actors = callData.pokedRenderer.getActors();
							this.setRepresentationToSurface(actors, rwi);
							break;
						default:
							break;
					}
				});
				this.renderWindow.getInteractor().onLeftButtonPress((callData) => this.onLeftButtonDown(callData));
			});
		},

		/**
		 * 左键按下
		 */
		async onLeftButtonDown (callData) {
			logger.time('左键按下');

			const renderer = this.renderer;
			if (renderer !== callData.pokedRenderer) {
				return;
			}

			// ----------------------------------------------------------------------------
			// Setup picking interaction
			// ----------------------------------------------------------------------------
			// Only try to pick cone points
			const picker = vtkPointPicker.newInstance();
			picker.setTolerance(0.0015); // 指定执行拾取操作的容差
			picker.setPickFromList(1);
			picker.initializePickList();
			picker.addPickList(renderer.getActors()[0]);
			const pos = callData.position;
			const point = [pos.x, pos.y, 0.0];
			picker.pick(point, renderer);

			if (picker.getActors().length === 0) {
				// const pickedPoint = picker.getPickPosition();
				// logger.log(`No cells picked, default: ${ pickedPoint }`);
			} else {
				const pickedPoint = picker.getPickPosition();
				logger.log(`获取世界坐标 pickedPoint: ${ pickedPoint }`);

				const pickedPointId = picker.getPointId();
				logger.log('Picked point: ', pickedPointId);

				const polyData = picker.getActors()[0]?.getMapper()?.getInputData();
				this.test(polyData, pickedPointId);

				const pickedPoints = picker.getPickedPositions();
				logger.log('pickedPoints: ', pickedPoints);
				for (let i = 0; i < pickedPoints.length; i++) {
					const pickedPoint = pickedPoints[i];
					logger.log(`Picked: ${ pickedPoint }`);
					const sphere = vtkSphereSource.newInstance();
					sphere.setCenter(pickedPoint);
					// sphere.setRadius(0.1);
					sphere.setRadius(0.01);
					const sphereMapper = vtkMapper.newInstance();
					sphereMapper.setInputData(sphere.getOutputData());
					const sphereActor = vtkActor.newInstance();
					sphereActor.setMapper(sphereMapper);
					sphereActor.getProperty().setColor(0.0, 1.0, 0.0);
					renderer.addActor(sphereActor);
				}
			}
			this.render();
			logger.timeEnd('左键按下');
		},

		test (polyData, pickedPointId) {
			logger.log('polyData: ', polyData);
			logger.log('pickedPointId: ', pickedPointId);
			const pointData = polyData?.getPointData();
			logger.log('pointData: ', pointData);
			logger.log('pointData.getArrays: ', pointData.getArrays());
			logger.log('获取点数据 pointData.getArrays.getData: ', pointData?.getArrays()[0]?.getData());
			// logger.log('获取点坐标 pointData.getPoint: ', pointData?.getPoints()[pickedPointId]);
			// logger.log('获取点坐标 pointData.getPoints: ', pointData?.getPoints());

			// const colorData = pointData.getArrayByName('Points');
			// if (colorData) {
			// 	const colorDataValues = [...colorData.getData()];
			// 	logger.log('colorDataValues: ', colorDataValues);
			// }

			const colorData = pointData.getArrayByName('Points');
			logger.log('colorData: ', colorData);
		},

		wTos (xMax, yMax) {
			// 获取右下角坐标(世界坐标系 > 屏幕坐标系)
			const coordMax = vtkCoordinate.newInstance();
			coordMax.setCoordinateSystemToWorld();
			coordMax.setValue([xMax, yMax]);
			const pDisVal = coordMax.getComputedDisplayValue(this.renderer);
			logger.log('pDisVal: ', pDisVal);
		},

		/**
		 * 创建代理对象
		 */
		createRepresentation (filter, actorProps = {}, mapperProps = {}) {
			const mapper = vtkMapper.newInstance();
			for (const methodName in mapperProps) {
				mapper[methodName](...mapperProps[methodName]);
			}
			if (filter.isA('vtkPolyData')) {
				mapper.setInputData(filter);
			} else {
				mapper.setInputConnection(filter.getOutputPort());
			}
			const actor = vtkActor.newInstance();
			actor.setMapper(mapper);
			actor.getProperty().set(actorProps);
			this.renderer.addActor(actor);
			return { actor, mapper };
		},

		/**
		 * 下载单个数据
		 */
		downloadSingleData (url, showLoadingProgress = false) {
			return new Promise(resolve => {
				HttpDataAccessHelper.fetchBinary(url + '?_=' + Date.now(), {
					progressCallback: showLoadingProgress ? this.progressCallback : noop,
				}).then((binary) => {
					const reader = vtkXMLPolyDataReader.newInstance();
					reader.parseAsArrayBuffer(binary);
					const source = reader.getOutputData(0);
					return resolve(source);
				});
			});
		},

		/**
		 * 更新颜色
		 */
		updateColor (polyData, values, color) {
			for (let i = 0; i < polyData.getNumberOfCells(); i++) {
				const pos = i * 3;
				values[pos] = color[0];
				values[pos + 1] = color[1];
				values[pos + 2] = color[2];
			}
			return values;
		},

		/**
		 * 将表示设置为线框
		 */
		setRepresentationToWireframe (actors, rwi) {
			actors = actors || this.renderer.getActors();
			actors.forEach((anActor) => {
				const prop = anActor.getProperty();
				if (prop.setRepresentationToWireframe) {
					prop.setRepresentationToWireframe();
				}
			});
			rwi && rwi.render();
			!rwi && this.render();
		},

		/**
		 * 将表示设置为面
		 */
		setRepresentationToSurface (actors, rwi) {
			actors = actors || this.renderer.getActors();
			actors.forEach((anActor) => {
				const prop = anActor.getProperty();
				if (prop.setRepresentationToSurface) {
					prop.setRepresentationToSurface();
				}
			});
			rwi && rwi.render();
			!rwi && this.render();
		},
	},
};
</script>
