<template>
	<div class="attr-item">
		<div class="attr-item-title" v-if="label">
			<div>
				<label style="margin-right: 6px;">
					<b><span
						v-if="required"
						style="margin-left: -6px;color:red"
					>*</span>{{ label }}{{ label ? ':' : '' }}</b>
				</label>
				<a-tooltip v-if="tip" placement="top">
					<template slot="title">
						{{ tip }}
					</template>
					<a-icon type="question-circle"></a-icon>
				</a-tooltip>
			</div>
			<div class="attr-item-content2">
				<a-switch
					defaultChecked
					v-model="fieldValue"
					checked-children="开启"
					un-checked-children="关闭"
					:style="{width: '60px'}"
					@change="handleValueChange"
					:disabled="disabled"
				/>
			</div>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/4/26.
 * Copyright 2022, Inc.
 * =================================== */
export default {
	components: {
	},
	props: {
		required: {
			type: Boolean,
			required: false,
			default: false,
		},
		title: {
			type: String,
			required: false,
			default: '',
		},
		label: {
			type: String,
			required: false,
			default: '',
		},
		name: {
			type: String,
			required: false,
			default: '',
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: [Number, Boolean],
			required: false,
			default: 0,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: false,
		}
	},
	data () {
		return {
			fieldValue: false,
		};
	},
	watch: {
		value: {
			deep: true,
			immediate: true,
			handler (newVal) {
				this.fieldValue = newVal === 1 || newVal === true;
			},
		},
	},
	methods: {
		handleValueChange (value) {
			this.$emit('valueChange', { value: value ? 1 : 0, key: this.name });
		}
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.attr-item {
	width: 100%;
	/deep/ .ant-slider-mark-text {
		top: -34px
	}
	&-title, &-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	&-title-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		> * {
			margin-left: 10px;
		}
	}
	&-content {
		background: #F8F8F8;
		padding: 10px;
		min-height: 40px;
	}
	&-content2 {
		padding: 10px;
		height: 40px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}
	&-icon {
		width: 20px;
		height: 20px;
	}
	&-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.svg-icon, input {
			margin-left: 10px;
			cursor: pointer;
		}
	}
	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}
	.svg-icon {
		color: @primary;
	}
}
</style>
