/* ===================================
 * 指令：点击元素外面才会触发的事件
 * Created by cjking on 2021/04/25.
 * Copyright 2021, Inc.
 * =================================== */
const CLICK = 'click';
const captureInstances = Object.create(null);           // 冒泡事件类型对象{}
const nonCaptureInstances = Object.create(null);        // 捕获事件类型对象{}
const captureEventHandlers = Object.create(null);       // 冒泡事件处理程序对象{}
const nonCaptureEventHandlers = Object.create(null);    // 捕获事件处理程序对象{}
const instancesList = [captureInstances, nonCaptureInstances];

const commonHandler = function onCommonEvent (context, instances, event, arg) {
	// 获取事件触发的目标元素
	const { target } = event;

	const itemIteratee = function itemIteratee (item) {
		// 获取指令绑定的元素，虚拟Dom，VNode
		const { el } = item;

		// 如果事件触发的目标元素，不是当前指令绑定的元素，或子元素
		// 那么就触发事件处理程序，并绑定上下文
		if (el !== target && !el.contains(target)) {
			const { binding } = item;
			if (binding.modifiers.stop) {
				event.stopPropagation();
			}
			if (binding.modifiers.prevent) {
				event.preventDefault();
			}

			// 触发事件处理程序，绑定上下文
			binding.value.call(context, event);
		}
	};

	instances[arg].forEach(itemIteratee);
};

/**
 * @param {boolean} useCapture，默认值是false表示事件冒泡，设置为true表示事件捕获
 * @param arg
 * 这个方法的返回值，是事件处理的核心程序
 * 每一种事件类型都对应一个方法，这也是事件触发时执行的方法，
 * 因此在这个方法中，可以拿到事件默认参数event，以及上下文this，事件触发的目标元素
 *
 * example:
 <div v-click-outside="onClickOutside"></div>

 <!-- 阻止冒泡 -->
 <div v-click-outside:mouseup="doThis"></div>
 <div v-click-outside:mouseup.stop="doThis"></div>

 <!-- 去除默认行为  -->
 <div v-click-outside.prevent="doThat"></div>

 <!-- 阻止冒泡与默认行为 -->
 <div v-click-outside.stop.prevent="doThat"></div>

 <!-- 事件捕获 -->
 <div v-click-outside.capture="doThis"></div>

 <!-- 绑定多个事件类型 -->
 <div
 v-click-outside.mouseup="onClickOutside1"
 v-click-outside:click="onClickOutside2"
 v-click-outside:dblclick.capture="onClickOutside3"
 ></div>
 */
const getEventHandler = function getEventHandler (useCapture, arg) {
	if (useCapture) {
		if (captureEventHandlers[arg]) {
			return captureEventHandlers[arg];
		}
		captureEventHandlers[arg] = function onCaptureEvent (event) {
			commonHandler(this, captureInstances, event, arg);
		};
		return captureEventHandlers[arg];
	}
	if (nonCaptureEventHandlers[arg]) {
		return nonCaptureEventHandlers[arg];
	}
	nonCaptureEventHandlers[arg] = function onNonCaptureEvent (event) {
		commonHandler(this, nonCaptureInstances, event, arg);
	};
	return nonCaptureEventHandlers[arg];
};

export const directive = Object.defineProperties(
	{},
	{
		$captureInstances: {
			value: captureInstances,
		},
		$nonCaptureInstances: {
			value: nonCaptureInstances,
		},
		$captureEventHandlers: {   // 捕获事件处理程序{}
			value: captureEventHandlers,
		},
		$nonCaptureEventHandlers: {  // 冒泡事件处理程序{}
			value: nonCaptureEventHandlers,
		},
		bind: {
			value: function bind (el, binding) {
				// 自定义指令绑定的值必须是一个函数
				if (typeof binding.value !== 'function') {
					throw new TypeError('Binding value must be a function.');
				}

				// 如果自定义指令没有传参默认是click，也就是事件处理程序触发的方式，默认是点击操作
				const arg = binding.arg || CLICK;
				const normalisedBinding = {  // 取到binding里面的值
					...binding,
					...{
						arg,

						// 修饰符对象，共有三个属性，默认冒泡、不阻止默认事件，不阻止冒泡，
						// 当然了指定定义了修饰符时，会覆盖默认值。
						modifiers: {
							...{
								capture: false,
								prevent: false,
								stop: false,
							},
							...binding.modifiers,
						},
					},
				};
				const useCapture = normalisedBinding.modifiers.capture;
				const instances = useCapture ? captureInstances : nonCaptureInstances;

				// 在冒泡或捕获对象中，如果arg这个属性值不是一个数组，那就变成数组。
				// 也就是说，每增加一种自定义指令触发事件处理程序的方式，
				// 都会多一对键值对，例如默认触发的方式是"click"，那么{ "click": []}，
				// 如果又增加一个通过双击触发的方式，就会变成{ "click": [], "dblclick": []}
				if (!Array.isArray(instances[arg])) {
					instances[arg] = [];
				}

				// 当每种事件类型第一次绑定时，我们会在document上监听相同的事件类型，
				// 并且，每种事件类型，document只会添加一个事件处理程序，也就是getEventHandler(useCapture, arg)
				// 这个方法的返回值就是事件处理的核心程序！
				if (instances[arg].push({ el, binding: normalisedBinding }) === 1) {
					if (typeof document === 'object' && document) {
						document.addEventListener(arg, getEventHandler(useCapture, arg), useCapture);
					}
				}
			},
		},

		unbind: {
			value: function unbind (el) {
				const compareElements = function compareElements (item) {
					return item.el !== el;
				};
				const instancesIteratee = function instancesIteratee (instances) {
					// 得到了事件类型的数组，eg: ["click", "dblclick"];
					const instanceKeys = Object.keys(instances);

					if (instanceKeys.length) {
						// 是否是捕获
						const useCapture = instances === captureInstances;

						const keysIteratee = function keysIteratee (eventName) {
							// 在当前解除的事件类型对应的事件处理数组中，找到那么些未解除绑定的事件处理程序
							const newInstance = instances[eventName].filter(compareElements);

							// 如果存在未解除绑定的事件处理程序，那么就将成为最新的事件处理数组，
							// 意思就是把解除绑定的事件从事件处理数组中删除掉！
							if (newInstance.length) {
								instances[eventName] = newInstance;
							} else {
								// 如果不存在未解除绑定的事件处理程序，那么就更简单了，直接让document取消对该事件类型的监听！
								// 并且在事件处理程序{}对象上，删除该属性
								if (typeof document === 'object' && document) {
									document.removeEventListener(eventName, getEventHandler(useCapture, eventName), useCapture);
								}
								delete instances[eventName];
							}
						};

						// 循环事件类型的数组
						instanceKeys.forEach(keysIteratee);
					}
				};

				// 指令与元素解除绑定时，循环冒泡对象与捕获对象，
				instancesList.forEach(instancesIteratee);
			},
		},
	},
);

//  export default ClickOutside;
export const ClickOutsideDirective = Vue => Vue.directive('click-outside', directive);
