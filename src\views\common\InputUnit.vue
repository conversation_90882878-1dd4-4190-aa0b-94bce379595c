<template>
	<a-row class="input-unit-wrapper">
		<a-col :span="colLeftSpan" :class="[showCheckbox ? 'col-left' : '']">
			<a-checkbox
				v-if="showCheckbox"
				style="margin-right: 5px;"
				:checked="innerChecked"
				:disabled="disabledCheckbox || showTable"
				@change="onCheckboxChange"
			/>
			<i-input
				v-if="!showTable"
				v-model="form[valueKey]"
				:min="min"
				:max="max"
				:disabled="disabled"
				:isNumber="isNumber"
				:maxLength="maxLength"
				:placeholder="placeholder"
				:isExponential="isExponential"
				:fractionDigits="fractionDigits"
				@blur="onBlur"
				@change="onChange"
			/>
			<a-input v-if="showTable" value="Table" disabled />
		</a-col>
		<a-col :span="colRightSpan" :offset="1" v-if="colRightSpan > 0">
			<a-select v-model="form[unitKey]" :disabled="disabled || showTable">
				<template v-for="unit in units">
					<a-select-option :key="unit" :value="unit">{{ unit }}</a-select-option>
				</template>
			</a-select>
		</a-col>
	</a-row>
</template>

<script>
/* ===================================
 * 含单位的input输入框
 * Created by cjking on 2021/10/20.
 * Copyright 2021, Inc.
 * =================================== */
import IInput from '@/components/common/IInput';

export default {
	name: 'InputUnit',
	components: {
		IInput,
	},
	props: {
		form: {
			type: Object,
			required: true,
		},
		units: {
			type: Array,
			required: true,
		},
		unitKey: {
			type: String,
			required: true,
		},
		valueKey: {
			type: String,
			required: true,
		},
		min: {
			type: Number,
			required: false,
			default: undefined,
		},
		max: {
			type: Number,
			required: false,
			default: undefined,
		},
		checked: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: false,
		},
		showCheckbox: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabledCheckbox: {
			type: Boolean,
			required: false,
			default: false,
		},
		showTable: {
			type: Boolean,
			required: false,
			default: false,
		},
		placeholder: {
			type: String,
			required: false,
			default: '',
		},
		maxLength: {
			type: Number,
			required: false,
			default: 50,
		},
		isNumber: { // 是否转换为Number数字
			type: Boolean,
			required: false,
			default: false,
		},
		isExponential: { // 是否转换为科学计数法
			type: Boolean,
			required: false,
			default: false,
		},
		fractionDigits: { // 小数位数
			type: Number,
			required: false,
			default: undefined,
		},
	},
	data () {
		return {
			innerChecked: false,
			colLeftSpan: 14,
		};
	},
	watch: {
		'checked': {
			immediate: true,
			handler () {
				this.innerChecked = this.checked;
			},
		},
		'showTable' () {
			this.colLeftSpan = this.showTable ? 24 : 14;
		},
		'units': {
			immediate: true,
			handler () {
				this.form[this.unitKey] = this.form[this.unitKey] || this.units[0];
			},
		},
	},
	computed: {
		colRightSpan () {
			return 24 - 1 - this.colLeftSpan;
		},
	},
	methods: {
		onBlur (event) {
			this.$emit('blur', event);
		},
		onChange (event) {
			this.$emit('change', event);
		},
		onCheckboxChange (event) {
			this.$emit('checkboxChange', event);
		},
	},
};
</script>

<style lang="less" scoped>
.input-unit-wrapper {
	.col-left {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}
</style>
