# 服务器 iptables 初始化及端口设置

> 在RHEL 7 / CentOS 7中，firewalld被引入来管理iptables。个人觉得, firewalld 更适合于工作站而不是服务器环境。
可以回到更经典的iptables设置管理。


注意：

iptables重启过后，所有docker容器网络会有异常，请注意做好服务备份后，再重启 iptables


(下面一是解决service iptables save出错please try to use systemctl.)

一、开启service服务

1、首先停止防火墙

```shell
systemctl stop firewalld
systemctl mask firewalld
```

2、然后安装iptables-services

```shell
yum install iptables-services
```

3、设置开机启动防火墙

```shell
systemctl enable iptables
```

其他管理命令iptables

```shell
systemctl [stop|start|restart] iptables
```

4、这时可以保存防火墙规则了

```shell
service iptables save
or
/usr/libexec/iptables/iptables.init save
```


二、创建 iptables 脚本

#### 新建文件

```shell
touch initIptables.sh
```

#### 添加运行权限

```shell
chmod +x initIptables.sh
```

#### 将附录内容复制到文件中，并启动

```shell
./initIptables
or
sh initIptables.sh
```

### 附录：

```shell
########################################################################

#!/bin/sh
yum install -y iptables
service iptables start

#首先在清除前要将policy INPUT改成ACCEPT,表示接受一切请求。
#这个一定要先做，不然清空后可能会悲剧
iptables -P INPUT ACCEPT
#清空默认所有规则
iptables -F
#清空自定义的所有规则
iptables -X
#计数器置0
iptables -Z

service iptables save

#允许来自于lo接口的数据包
#如果没有此规则，你将不能通过127.0.0.1访问本地服务，例如ping 127.0.0.1
iptables -A INPUT -i lo -j ACCEPT
#ssh端口22
iptables -A INPUT -p tcp --dport 22 -j ACCEPT
#FTP端口21
iptables -A INPUT -p tcp --dport 21 -j ACCEPT
#web服务端口80
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
#tomcat
iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
#mysql
iptables -A INPUT -p tcp --dport 3306 -j ACCEPT
#阿里云端口(自定义的一些端口，比如: 8081、8082、28981、28989)
iptables -A INPUT -p tcp --dport 8081 -j ACCEPT
iptables -A INPUT -p tcp --dport 8082 -j ACCEPT
iptables -A INPUT -p tcp --dport 28981 -j ACCEPT
iptables -A INPUT -p tcp --dport 28989 -j ACCEPT

#允许icmp包通过,也就是允许ping
iptables -A INPUT -p icmp -m icmp --icmp-type 8 -j ACCEPT
#允许所有对外请求的返回包
#本机对外请求相当于OUTPUT,对于返回数据包必须接收啊，这相当于INPUT了
iptables -A INPUT -m state --state ESTABLISHED -j ACCEPT
#如果要添加内网ip信任（接受其所有TCP请求），************是阿里云外网地址
iptables -A INPUT -p tcp -s ************ -j ACCEPT
#过滤所有非以上规则的请求
iptables -P INPUT DROP

service iptables save
service iptables restart
service iptables status

chkconfig iptables on

########################################################################
```

注意：

iptables 重启过后，所有docker容器会存在网络异常(比如数据库连不通了)，这时，需要重启所有 docker 容器

关闭所有docker容器

```shell
pkill docker
```

启动docker容器

```shell
docker start mongo
docker start redis
docker start oral-backend
docker start oral-frontend
```

查看 iptables 规则

```shell
iptables -L -n
```

![iptables规则](https://cjking-public.oss-cn-beijing.aliyuncs.com/static/markdown/20200821105549.png)

完！
