<template>
	<a-layout id="layout-basic">
		<GlobalHeader size="sm" :menu="'model'" justify-content="center">
			<template #leftAction>
				<div style="margin-left: 20px;margin-top: -2px;">
					<a v-href>
						<img :src="backHome" style="height: 30px; cursor: pointer;" alt="">
					</a>
				</div>
			</template>
			<template #logo>
				<div class="logo">
					<div>name</div>
				</div>
			</template>
			<template #title>
				<div>icons</div>
			</template>
		</GlobalHeader>

		<a-layout-sider
			ref="siderRef"
			class="project-sider"
			:width="siderWidth"
		>
			<!-- 模具 panel -->
			<CollapseTree
				showLine
				title="模具Part"
				panelKey="part"
				:treeData="[]"
				defaultExpandAll
				disabledRightMenu
			>
				<template #content>
					<ModelSettings
						:dataList="partList"
						@uploadCallback="uploadCallback"
					/>
				</template>
			</CollapseTree>

			<!-- 参数配置 panel -->
			<CollapseTree
				showLine
				title="参数配置"
				panelKey="model"
				defaultExpandAll
				:treeData="[]"
			>
				<template #content>

				</template>
			</CollapseTree>
		</a-layout-sider>

		<a-layout-content :style="{ background: DEFAULT_VIEW_BACKGROUND, position: 'relative' }">
			<!-- 视图渲染区 -->
			<ModelView
				ref="modelView"
			/>
		</a-layout-content>

		<div class="project-right" ref="rightRef">
			<!-- 结果 -->
		</div>

		<div class="tip-name">{{ tipName }}</div>
		<div class="network-status">网络状态：{{ online }}</div>

	</a-layout>
</template>

<script>
/* ===================================
 * 任务详情
 * Created by cjking on 2022/07/20.
 * Copyright 2022, Inc.
 * =================================== */
import * as THREE from 'three';
import * as Comlink from 'comlink';
import { mapGetters } from 'vuex';
import { DEFAULT_VIEW_BACKGROUND, SelectModeEnum } from '@/constants';

import JEllipsis from '@/components/common/JEllipsis';
import Navigation from '@/components/core/Navigation';
import FileLoader from '@/components/core/FileLoader';
import GlobalHeader from '@/components/page/GlobalHeader';
import CollapseTree from '@/components/common/CollapseTree';
import ModelView from '@/components/core/ModelView';
import ModelSettings from '@/views/module/model/ModelSettings';

import backHome from '@/assets/img/project/navigation/backHome.png';

import { logger } from '@/utils/logger';
import { deepCopy, getText } from '@/utils/utils';
import { defaultPartData } from '@/views/module/model/defaultJsonData';

const components = {
	Navigation,
	GlobalHeader,
	CollapseTree,
	FileLoader,
	JEllipsis,
	ModelView,
	ModelSettings,
};

const icons = {
	backHome,
};

export default {
	name: 'ModelDetail',
	mixins: [],
	components: {
		...components,
	},
	data () {
		return {
			...icons,
			DEFAULT_VIEW_BACKGROUND,
			siderWidth: 320,
			partList: [deepCopy(defaultPartData)],
			settings: [],
			modelList: [],
			online: '正常',
			downLink: '0 M/s',
			tipName: '',
		};
	},
	computed: {
		...mapGetters([
			'isLogin', 'userInfo',
		]),
	},
	mounted () {

	},
	methods: {
		/**
		 * 上传离散文件
		 */
		async uploadCallback ({ file }) {
			this.$refs.modelView.loadDataV3(file);
		},

	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/grid.less";
@import "~@/assets/styles/mixins/variables.less";

#layout-basic {
	position: relative;
	background: @primary-background;
	height: 100vh;
	overflow: hidden;

	.logo {
		font-size: 16px;
		font-weight: 500;
		color: #111111;
		margin-left: 20px;
	}

	.header-title {
		display: flex;
		width: 100%;

		span {
			font-size: 16px;
			font-weight: bold;
			text-align: center;
			width: 260px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			margin: 0 auto;
		}
	}

	.ant-layout-sider {
		margin-top: 52px;
	}

	.ant-layout-content {
		margin-top: 50px;
		height: 100%;
		overflow: hidden;
	}

	.project-sider {
		overflow: auto;
		height: 100%;
		overflow-x: hidden;
		background: transparent;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			left: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.project-right {
		overflow: auto;
		height: auto;
		overflow-x: hidden;
		background: linear-gradient(#C7D8E1, #E3EAEE);
		position: absolute;
		right: 0;
		top: 52px;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			right: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.ant-empty-normal {
		margin: 0 0;
	}

	.list {
		img {
			width: 20px;

			&.w18 {
				width: 18px;
				height: 18px;
			}
		}

		.item-meta {
			display: flex;
			align-items: center;
			cursor: pointer;

			&:hover {
				color: @primary;
			}

			.active {
				color: @primary;
			}
		}
	}

	.progress-item {
		.progress-bar {
			max-width: 160px;
			margin-left: 10px;
			min-width: 160px;
		}

		.ant-list-item-action {
			margin-left: 10px;
		}

		.ant-list-item-meta-title {
			display: flex;
			align-items: center;
		}

		.progress-title {
			display: flex;
			align-items: center;
			height: 22px;

			img {
				height: inherit;
				margin-right: 5px;
			}
		}

		.ant-progress {
			line-height: 0;
		}
	}

	.masking {
		position: fixed;
		top: 52px;
		left: 0;
		width: 315px;
		height: calc(100vh - 50px - 2px);
		background: rgba(0, 0, 0, 0.05);
		z-index: 999;
		cursor: not-allowed;
	}

	.tip-name {
		position: fixed;
		right: 200px;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.network-status {
		position: fixed;
		right: 0;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.upload-btn {
		width: 280px;

		/deep/ .ant-btn {
			border-radius: 3px;
			background-color: #6362FF;
		}
	}

	.submit {
		height: 52px;
		width: 296px;
		padding: 10px 4px;
		background-color: white;

		.ant-btn {
			width: 280px;
			height: 32px;
		}
	}
}
</style>
