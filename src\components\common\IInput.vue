<template>
	<a-input
		:type="type"
		:size="size"
		v-model="stateValue"
		:minLength="minLength"
		:maxLength="maxLength"
		:loading="loading"
		:placeholder="placeholder"
		:disabled="disabled"
		:readOnly="readOnly"
		@click="onClick"
		@input="onInput"
		@blur="onBlur"
		@keydown="onKeydown"
	>
		<slot name="extra"></slot>
	</a-input>
</template>

<script>
/* ===================================
 * input封装
 * Created by cjking on 2021/12/08.
 * Copyright 2021, Inc.
 * =================================== */
import { isEmpty, isNoEmpty, isString, toExponential, toNonExponential, trim } from '@/utils/utils';

const separator = '.';
const addSeparator = '+';
const minusSeparator = '-';

function commonCondition (value) {
	value = String(value);
	return !value.endsWith(separator) && !value.endsWith(addSeparator) && !value.endsWith(minusSeparator);
}

export default {
	name: 'IInput',
	props: {
		value: {
			type: [String, Number, null, undefined],
			required: false,
			default: undefined,
		},
		type: {
			type: String,
			required: false,
			default: 'text',
		},
		size: {
			type: String,
			required: false,
			default: 'default', // 'small', 'large', 'default'
		},
		placeholder: {
			type: String,
			required: false,
			default: '',
		},
		min: {
			type: Number,
			required: false,
			default: undefined,
		},
		max: {
			type: Number,
			required: false,
			default: undefined,
		},
		limitMinMax: { // 限制最小最大值
			type: Boolean,
			required: false,
			default: false,
		},
		minLength: {
			type: Number,
			required: false,
			default: undefined,
		},
		maxLength: {
			type: Number,
			required: false,
			default: undefined,
		},
		loading: {
			type: Boolean,
			required: false,
			default: undefined,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: undefined,
		},
		readOnly: {
			type: Boolean,
			required: false,
			default: undefined,
		},
		isNumber: { // 是否转换为Number数字
			type: Boolean,
			required: false,
			default: false,
		},
		isExponential: { // 是否转换为科学计数法
			type: Boolean,
			required: false,
			default: false,
		},
		fractionDigits: { // 小数位数
			type: Number,
			required: false,
			default: undefined,
		},
		scientificReservedDigits: { // 科学计数法保留位数
			type: Number,
			required: false,
			default: undefined,
		},
		limitLength: { // 限制长度大于等于6时，才开始转换为科学计数法
			type: Number,
			required: false,
			default: 6,
		},
	},
	model: {
		prop: 'value',
		event: 'change.value',
	},
	data () {
		return {
			stateValue: undefined,
			isNumberInitialized: false,
			isExponentialInitialized: false,
		};
	},
	watch: {
		value: {
			immediate: true,
			handler (value) {
				this.stateValue = value;
			},
		},
		isNumber: {
			immediate: true,
			handler (bool) {
				this.$nextTick(() => {
					if (bool && isNoEmpty(this.value) && isString(this.value) && this.isNumber && commonCondition(this.value) && !this.isNumberInitialized) {
						this.isNumberInitialized = true;
						this.stateValue = isNaN(Number(this.value)) ? null : Number(this.value);
						this.$emit('change.value', this.stateValue);
					}
				});
			},
		},
		isExponential: {
			immediate: true,
			handler (bool) {
				this.$nextTick(() => {
					if (bool && this.commonExponentialCondition(this.value) && !this.isExponential) {
						this.isExponential = true;
						this.stateValue = toExponential(this.value, this.scientificReservedDigits);
					}
				});
			},
		},
	},
	methods: {
		/**
		 * 点击事件
		 */
		onClick (event) {
			let value = event.target.value;
			if (this.isNumber && isEmpty(value)) {
				value = null;
			}
			// 科学计数法转数字字符串
			if (isNoEmpty(value) && String(value).includes('e')) {
				value = toNonExponential(value);
				value = this.isNumber ? Number(value) : value;
			}
			this.stateValue = value;
			this.$emit('change.value', value);
			this.$emit('click', event);
		},

		/**
		 * 输入事件
		 */
		onInput (event) {
			let value = event.target.value;
			value = trim(value);
			if (this.isNumber) {
				if (value.startsWith(separator)) {
					value = value.replace(separator, '');
				}
				if (commonCondition(value)) {
					value = value.replace(/[^\d\\.?\-+]/g, '');
					value = this.commonHandler(value);
				}
			}
			this.stateValue = value;
			this.$emit('change.value', value);
			this.$emit('change', event);
		},

		/**
		 * 光标离开事件
		 */
		onBlur (event) {
			let value = event.target.value;
			if (value.endsWith(separator) || value.endsWith(addSeparator) || value.endsWith(minusSeparator)) {
				value = value.slice(0, value.length - 1);
			}
			if (this.isNumber && isNoEmpty(value)) {
				value = this.commonHandler(value);
			}
			// 数字转科学计数法
			if (this.commonExponentialCondition(value)) {
				value = toExponential(value, this.scientificReservedDigits);
			}
			this.stateValue = value;
			this.$emit('change.value', value);
			this.$emit('change', event);
			this.$emit('blur', event);
		},

		/**
		 * 按键按下事件
		 */
		onKeydown (event) {
			if (event.keyCode === 13) {
				this.$emit('pressEnter', event);
			}
			this.$emit('keydown', event);
		},

		/**
		 * 公共处理部分
		 * @param value
		 * @return {number}
		 */
		commonHandler (value) {
			value = value ? Number(value) : null;
			if (this.limitMinMax) {
				if (isNoEmpty(this.min) && isNoEmpty(value) && value < this.min) {
					value = this.min;
				}
				if (isNoEmpty(this.max) && isNoEmpty(value) && value > this.max) {
					value = this.max;
				}
			}
			if (value && this.fractionDigits) {
				value = Number(value.toFixed(this.fractionDigits));
			}
			value = isEmpty(value) || isNaN(value) ? null : value;
			return value;
		},

		/**
		 * 共同指数条件
		 */
		commonExponentialCondition (value) {
			return isNoEmpty(value) && this.isExponential && String(value).length >= this.limitLength && !String(value).includes('e');
		},
	},
};
</script>
