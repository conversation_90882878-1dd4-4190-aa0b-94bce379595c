<template>
	<div class="collapse-result" v-if="dataList.length">
		<a-collapse expandIconPosition="right">
			<a-collapse-panel class="collapse-panel" key="1">

				<template slot="header">
					<div class="collapse-panel-header">
						<div class="title">结果</div>
						<div class="extra" @click="e => preventDefaults(e)">
							<slot name="extra"></slot>
						</div>
					</div>
				</template>

				<!-- 内容区域 -->
				<slot name="content">
					<div class="box">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">2D纹理线条模型</span>
								</div>
								<div class="item right">
									<div class="mr-10" @click="visibleHandler(TextureResultTypeEnum.visible2dWlLine)">
										<svg-icon :type="inner2DLineVisible ? 'param-visible' : 'param-hidden'" />
									</div>

									<div class="mr-10">
										<input type="color" v-model="inner2DLineColor" @input="colorHandler(TextureResultTypeEnum.visible2dWlLine)">
									</div>

									<a-popover :title="null" placement="topRight">
										<template #content>
											<div class="display-flex-center">
												<a-slider
													:min="0"
													:max="100"
													:step="1"
													v-model="inner2DLineOpacity"
													:style="{ width: '100px', margin: '10px 0 0 10px' }"
													@change="opacityHandler(TextureResultTypeEnum.visible2dWlLine)"
												/>
												<div class="opacity">{{ inner2DLineOpacity }}%</div>
											</div>
										</template>
										<svg-icon type="param-opacity" />
									</a-popover>
								</div>
							</div>
						</div>
						<div class="column">
							<div class="action grid">
								<div class="grid-cell u-1of3 text-left">
									<span class="text">.3dm</span>
									<span class="icon">
										<img v-if="innerData['result_wl_line_3dm']" class="down" :src="downIcon" @click="onDownloadFile(innerData['result_wl_line_3dm'])">
										<!--<a-icon v-else type="sync" spin />-->
										<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
									</span>
								</div>
								<div class="grid-cell u-1of3 text-center">
									<span class="text">.iges</span>
									<span class="icon">
										<img v-if="innerData['result_wl_line_iges']" class="down" :src="downIcon" @click="onDownloadFile(innerData['result_wl_line_iges'])">
										<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
									</span>
								</div>
								<div class="grid-cell u-1of3 text-right">
									<span class="text">.step</span>
									<span class="icon">
										<img v-if="innerData['result_wl_line_step']" class="down" :src="downIcon" @click="onDownloadFile(innerData['result_wl_line_step'])">
										<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
									</span>
								</div>
							</div>
						</div>
					</div>

					<div class="box mtb-16">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">2D纹理线条矢量</span>
								</div>
								<div class="item right">

									<div class="mr-10" @click="visibleHandler(TextureResultTypeEnum.visible2dWlVector)">
										<svg-icon :type="inner2DVectorVisible ? 'param-visible' : 'param-hidden'" />
									</div>

									<div class="mr-10">
										<input type="color" v-model="inner2DVectorColor" @input="colorHandler(TextureResultTypeEnum.visible2dWlVector)">
									</div>

									<a-popover :title="null" placement="topRight">
										<template #content>
											<div class="display-flex-center">
												<a-slider
													:min="0"
													:max="100"
													:step="1"
													v-model="inner2DVectorOpacity"
													:style="{ width: '100px', margin: '10px 0 0 10px' }"
													@change="opacityHandler(TextureResultTypeEnum.visible2dWlVector)"
												/>
												<div class="opacity">{{ inner2DVectorOpacity }}%</div>
											</div>
										</template>
										<svg-icon type="param-opacity" />
									</a-popover>
								</div>
							</div>
						</div>

						<div class="column">
							<div class="action grid jc-sb">
								<span class="title">.ai</span>
								<span class="icon">
									<img v-if="innerData['result_wl_line_ai']" class="down" :src="downIcon" @click="onDownloadFile(innerData['result_wl_line_ai'])">
									<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
								</span>
							</div>
						</div>
					</div>

					<div class="box mtb-16">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">2D纹理线条参数文档</span>
								</div>
							</div>
						</div>

						<div class="column">
							<div class="action grid jc-sb">
								<span class="title">.txt</span>
								<span class="icon">
									<img v-if="innerData['result_wl_line_params']" class="down" :src="downIcon" @click="onDownloadFile(innerData['result_wl_line_params'])">
									<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
								</span>
							</div>
						</div>
					</div>

					<div class="box mtb-16">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">PNG图片格式</span>
								</div>
							</div>
						</div>

						<div class="column">
							<div class="action grid jc-sb">
								<span class="title">.png</span>
								<span class="icon">
									<img v-if="innerData['result_wl_line_png']" class="down" :src="downIcon" @click="onDownloadFile(innerData['result_wl_line_png'])">
									<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
								</span>
							</div>
						</div>
					</div>
				</slot>

			</a-collapse-panel>
		</a-collapse>
	</div>
</template>

<script>
/* ===================================
 * 纹理-求解结果页
 * Created by cjking on 2022/04/26.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { preventDefaults } from '@/components/core/ViewUtils';
import { basename, downloadFileByNewWindow } from '@/utils/utils';
import { TextureFileTypeEnum, TextureResultTypeEnum } from '@/constants';

import downIcon from '@/assets/img/texture/down.png';

const icons = { downIcon };

export default {
	name: 'TextureResult',
	props: {
		activeKey: {
			type: Array,
			required: false,
			default: () => ['1'],
		},
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	data () {
		return {
			...icons,
			TextureFileTypeEnum,
			TextureResultTypeEnum,

			// 显示
			inner2DLineVisible: true,
			inner2DVectorVisible: true,

			// 颜色
			inner2DLineColor: '#6362FF',
			inner2DVectorColor: '#6362FF',

			// 透明度
			inner2DLineOpacity: 100,
			inner2DVectorOpacity: 100,

			innerData: {},
		};
	},
	watch: {
		dataList: {
			deep: true,
			immediate: true,
			handler () {
				this.innerData = {};
				this.dataList.forEach(data => {
					if (data.name === 'result_wl_line_3dm' && data.url) {
						this.innerData['result_wl_line_3dm'] = data.url;
					}
					if (data.name === 'result_wl_line_iges' && data.url) {
						this.innerData['result_wl_line_iges'] = data.url;
					}
					if (data.name === 'result_wl_line_step' && data.url) {
						this.innerData['result_wl_line_step'] = data.url;
					}
					if (data.name === 'result_wl_line_ai' && data.url) {
						this.innerData['result_wl_line_ai'] = data.url;
					}
					if (data.name === 'result_wl_line_params' && data.url) {
						this.innerData['result_wl_line_params'] = data.url;
					}
					if (data.name === 'result_wl_line_png' && data.url) {
						this.innerData['result_wl_line_png'] = data.url;
					}
				});
				logger.log('纹理结果数据 this.innerData: ', this.innerData);
			},
		},
	},
	methods: {
		preventDefaults,

		/**
		 * 显示/隐藏处理
		 */
		visibleHandler (type) {
			let visible;
			if (type === TextureResultTypeEnum.visible2dWlLine) {
				this.inner2DLineVisible = !this.inner2DLineVisible;
				visible = this.inner2DLineVisible;
			}
			if (type === TextureResultTypeEnum.visible2dWlVector) {
				this.inner2DVectorVisible = !this.inner2DVectorVisible;
				visible = this.inner2DVectorVisible;
			}
			this.$emit('onVisible', { type, visible });
		},

		/**
		 * 颜色处理
		 */
		colorHandler (type) {
			let color;
			if (type === TextureResultTypeEnum.visible2dWlLine) {
				color = this.inner2DLineColor;
			}
			if (type === TextureResultTypeEnum.visible2dWlVector) {
				color = this.inner2DVectorColor;
			}
			this.$emit('onColor', { type, color });
		},

		/**
		 * 透明度处理
		 */
		opacityHandler (type) {
			let opacity;
			if (type === TextureResultTypeEnum.visible2dWlLine) {
				opacity = this.inner2DLineOpacity;
			}
			if (type === TextureResultTypeEnum.visible2dWlVector) {
				opacity = this.inner2DVectorOpacity;
			}
			this.$emit('onOpacity', { type, opacity });
		},

		/**
		 * 文件下载
		 */
		onDownloadFile (filePath) {
			if (!filePath) {
				this.$message.error('未找到下载所需要的文件');
				return;
			}
			downloadFileByNewWindow(filePath, basename(filePath));
		},

		/**
		 * 文件加载中
		 */
		fileLoading () {
			this.$message.error('文件正在生成中，请稍后再试');
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.collapse-result {
	width: 320px;

	/deep/ .ant-collapse > .ant-collapse-item {
		border-bottom: 3px solid #E5E5E5;

		.ant-collapse-header {
			border-left: 2px solid @primary;
			border-radius: 0;
			background: #F9F9FF;
			padding: 9px 16px;
			padding-right: 40px;
		}
	}

	.collapse-panel {

		.disable-selection();

		.collapse-panel-header {
			display: flex;

			.icon {
				flex: 0 0 20px;
			}

			.title {
				flex: 1;
				margin-left: 8px;
				font-size: 16px;
				font-weight: bold;
				.disable-selection();
			}

			.extra {
				flex: 0 0 70px;
				text-align: end;
				.disable-selection();
			}
		}

		.label {
			height: 20px;
			line-height: 20px;
			font-size: 14px;
			font-weight: bold;
			color: #111111;
		}

		input[type="color"] {
			width: 20px;
			height: 22px;
			border: 0;
			background-color: transparent;
		}

		.box {
			&.mtb-16 {
				margin: 16px 0;
			}

			.content {
				display: flex;
				width: 100%;

				.item {
					flex: 0 0 50%;

					svg {
						width: 20px;
						height: 20px;
						color: @primary;
					}

					&.right {
						display: flex;
						justify-content: flex-end;
						align-items: center;

						svg, input[type="color"] {
							cursor: pointer;
						}

						.mr-10 {
							margin-right: 10px;
						}
					}
				}
			}

			.action {
				background: #F9F9F9;
				width: 100%;
				text-align: center;
				margin-top: 6px;

				.text-right {
					text-align: right;
					padding-right: 8px;
				}

				.text {
					font-size: 14px;
					font-weight: 600;
					color: #111111;
					margin-left: 10px;
					display: inline-block;
					padding: 6px 0;

					&.left {
						text-align: left;
					}
				}

				.icon {
					display: inline-block;
					margin-left: 8px;
					color: @primary;
					cursor: pointer;

					&.right {
						text-align: right;
					}
				}

				&.jc-sb {
					justify-content: space-between;
					padding: 6px 12px;

					.title {
						font-size: 14px;
						font-weight: 600;
						color: #111111;
					}
				}
			}
		}
	}

	.down {
		width: 20px;
		margin-top: -3px;
	}

	.sync {
		width: 20px;
		height: 20px;
		vertical-align: -5px;
	}
}
</style>
