# 端口及防火墙设置

> 某些情况下，docker端口未开放，需要手动开放后，api接口才能访问 比如：开放 8002 9901 端口:

```shell script
#添加防火墙端口
firewall-cmd --zone=public --permanent --add-port=80/tcp
firewall-cmd --zone=public --permanent --add-port=443/tcp
firewall-cmd --zone=public --permanent --add-port=8002/tcp
firewall-cmd --zone=public --permanent --add-port=9901/tcp

# 关闭80端口
firewall-cmd --remove-port=80/tcp --permanent

#重启防火墙
systemctl reload firewalld
#查看防火墙端口
firewall-cmd --list-ports
#查看所有已开启端口
netstat -nltp
iptables -L -n

#如果是aliyun的服务器，记得还要添加 `安全组规则`
#添加好后，不要急着访问页面，有可能还是访问不了，aliyun`安全组规则`解析大概要1~3分钟。
```

iptables方式

[传送门](./服务器iptables初始化及端口设置.md)
