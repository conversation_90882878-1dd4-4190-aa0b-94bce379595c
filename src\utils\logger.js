/* ===================================
 * 日志管理
 * 整个文件范围内禁止规则出现警告，将 eslint-disable 块注释放在文件顶部
 * Created by cjking on 2020/05/21.
 * Copyright 2020, Inc.
 * =================================== */
/* eslint-disable */
import Config from '@/config/Config';
import { formatDate } from '@/utils/utils';

function getTime () {
	return formatDate(new Date(), 'yyyy-MM-dd EEE HH:mm:ss.S');
}

function formatMsg (message) {
	return `[${ getTime() }] ` + message;
}

function getStackTrace () {
	const obj = {};
	Error.captureStackTrace(obj, getStackTrace);
	return obj.stack;
}

export const logger = {
	log (message, ...rest) {
		if (Config.log) {
			if (Config.logLine) {
				const stack = getStackTrace() || '';
				const matchResult = stack.match(/\(.*?\)/g) || [];
				const line = matchResult[1] || '';
				console.log(` [${ line }]`, formatMsg(message), ...rest);
				return;
			}
			console.log(formatMsg(message), ...rest);
		}
	},

	error (...args) {
		let message,
			error;
		if (args.length === 1) {
			message = '';
			error = args[0];
		} else if (args.length > 1) {
			message = args[0];
			error = args[1];
		}
		console.error(formatMsg(message), error);
	},

	warn (message, ...rest) {
		if (Config.log) {
			console.warn(formatMsg(message), ...rest);
		}
	},

	dom (ele) {
		if (Config.log) {
			console.log('%O', ele);
		}
	},

	time (timerName) {
		if (Config.log) {
			console.time(timerName);
		}
	},

	timeEnd (timerName) {
		if (Config.log) {
			console.timeEnd(timerName);
		}
	},

	group (...rest) {
		if (Config.log) {
			console.group(rest);
		}
	},

	groupEnd () {
		if (Config.log) {
			console.groupEnd();
		}
	},
};
