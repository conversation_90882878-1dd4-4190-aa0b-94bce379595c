<template>
	<a-layout id="layout-basic">

		<GlobalHeader size="sm" :menu="ModuleEnum.mold" justify-content="center">
			<template #leftAction>
				<div style="margin-left: 20px;margin-top: -2px;">
					<a v-href @click="goToView('/mold/project/detail/' + projectId)">
						<img :src="backHomeImg" style="height: 30px; cursor: pointer;" alt="">
					</a>
				</div>
			</template>
			<template #logo>
				<div class="logo">
					<JEllipsis :value="taskName" :length="14" />
				</div>
			</template>
			<template #title>
				<!-- 导航 -->
				<Navigation :action="curAction" :selectDraw="curDraw" :selectMode="curSelectMode"
					:renderMode="curRenderMode" :planeMode="curPlaneMode" :viewMode="curViewMode"
					:disabledSelectFace="disabledSelectFace"
					:disabledSelectSolid="disabledSelectSolid"
					:disabled="solveLoading || submitLoading"
					config="画点 绘制|控制|框选|重置视图|视图模式|渲染模式|选择模式|平面裁切|参考平面|其他操作" @onDraw="onDraw"
					@onAction="onAction" @resetView="resetView" @resetCamera="resetCamera"
					@updateView="updateView" @clearDrawn="clearAllDrawn"
					@changeCameraMode="changeCameraMode" @changeRenderMode="changeRenderMode"
					@selectModeHandle="selectModeHandle" @changeClipping="changeClipping"
					@updateRotate="updateRotate" @mouseLeftSetting="mouseLeftSetting"
					@mouseRightSetting="mouseRightSetting" @otherSelect="otherSelect"
					@onPlaneModeSelect="onPlaneModeSelect" />
				<HeaderTip :module-object="moduleObject"
					:style="{position: 'absolute', top: '50px', left: '0px'}" />
			</template>
		</GlobalHeader>

		<a-layout-sider class="project-sider" :width="siderWidth" ref="siderRef">
			<!-- 模具 panel -->
			<CollapseTree
				:style="{paddingTop: moduleObject.expired || moduleObject.zeroCount ? '40px' : '0'}"
				showLine title="模具Part" tip="保持出入口开放的模型文件，step格式" panelKey="part" defaultExpandAll
				disabledRightMenu>
				<template #content>
					<MoldSettings :dataList="partList" @onAllVisible="allVisibleHandler"
						@onAllColor="allColorHandler" @onAllOpacity="allOpacityHandler"
						@uploadCallback="uploadCallback" />
				</template>
			</CollapseTree>

			<!-- 参数配置 panel -->
			<CollapseTree showLine title="参数配置" panelKey="model" defaultExpandAll>
				<template #content>
					<MoldSettings v-if="settings.length" :dataList="settings"
						:nameList="bjPointNameList" @onColor="colorHandler"
						@onVisible="visibleHandler" @onOpacity="opacityHandler"
						@onActivate="onActivateHandler" @onCloseTag="closeTagHandler"
						@onCloseAllTag="closeAllTagHandler" @addYsLine="addYsLineHandler"
						@addCRKLine="addCRKLineHandler" @tagActivate="tagActivateHandler"
						@tagGroupActivate="tagGroupActivateHandler"
						@pointTagActivate="pointTagActivateHandler"
						@removeOneGroup="removeOneGroupHandler"
						@clearGroupObjValue="clearGroupObjValueHandler"
						@sliderValueChange="sliderValueChangeHandler"
						@switchValueChange="switchValueChangeHandler"
						@onCheckboxChange="onCheckboxChangeHandler"
						@removeOneGroupPoint="removeOneGroupPointHandler" />
					<a-divider />
					<a-affix :offset-bottom="0">
						<div class="submit">
							<a-button type="primary" :disabled="disabled && !canCreate"
								@click="($event) => submitTask($event)">提交计算</a-button>
						</div>
					</a-affix>
				</template>
			</CollapseTree>
		</a-layout-sider>

		<a-layout-content :style="{ background: DEFAULT_VIEW_BACKGROUND, position: 'relative' }">
			<!-- 蒙层 -->
			<div class="masking" v-if="solveLoading || disableSettings" />

			<!-- 视图渲染区 -->
			<MoldView ref="moldView" v-if="visibleView" :hasCover="hasCover"
				:copyByName="copyByName" :taskInfoLoading="taskInfoLoading" @tip="handleTip"
				@submit="submitTask" @clearDraw="onClearDraw" @clearCurve="onClearCurve"
				@onUnlock="onUnlockHandler" @updateCurve="onUpdateCurve"
				@updateCircle="onUpdateCircle" @updateBjPoint="updateBjPoint"
				@closeBoxSelect="closeBoxSelect" @boxSelectedHandler="boxSelectedHandler"
				@singleSelectedHandler="singleSelectedHandler"
				@updateDiyShapeList="updateDiyShapeList" @deleteDiyShapeList="deleteDiyShapeList"
				@closeActionControl="closeActionControl" @updateModeSelect="onPlaneModeSelect" />
		</a-layout-content>

		<div class="project-right">
			<MoldResult
				:style="{paddingTop: moduleObject.expired || moduleObject.zeroCount ? '40px' : '0'}"
				:dataList="results" @onColor="resultColorHandler" @onVisible="resultVisibleHandler"
				@onOpacity="resultOpacityHandler" />
		</div>
		<div class="tip-name">{{ tipName }}</div>
		<div class="network-status">网络状态：{{ online }}</div>
	</a-layout>
</template>

<script>
/* ===================================
 * 随行冷-任务详情
 * Created by cjking on 2022/02/20.
 * Copyright 2022, Inc.
 * =================================== */
import Config from '@/config/Config';
import { logger } from '@/utils/logger';
import { mapActions, mapGetters } from 'vuex';
import { mixinWebSocket, mixinModule } from '@/mixins';
import { dispersedLinesByFaceName, getProjectTaskDetail, makeDispersed, submitRhino, updateTaskCacheParams } from '@/api';
import { basename, debounce, deepCopy, downloadFileByNewWindow, noop, isArray, checkIfLoaded, difference, isEmpty, addEventHandler, getCustomId, isEmptyObject, sleep } from '@/utils/utils';
import {
	SelectModeEnum,
	DEFAULT_VIEW_BACKGROUND,
	ProgressStatusEnum,
	RenderModeEnum,
	WsTypeEnum,
	TaskStatusEnum,
	ModuleEnum,
	ColorEnum,
	RESULT_WATER_COOLING_GROUP,
	DISPERSED_LINE_GROUP,
	waterCoolingResultKeyMap,
	PlaneModeEnum,
	ViewEnum,
	DrawEnum,
} from '@/constants';
import { preventDefaults, sortByName } from '@/components/core/ViewUtils.js';
import MoldView from '@/components/core/MoldView';
import Navigation from '@/components/core/Navigation';
import GlobalHeader from '@/components/page/GlobalHeader';
import CollapseTree from '@/components/common/CollapseTree';
import JEllipsis from '@/components/common/JEllipsis.vue';
import HeaderTip from '@/components/common/HeaderTip';
import backHomeImg from '@/assets/img/project/navigation/backHome.png';
import { defaultJsonData, defaultPartData } from '@/views/module/mold/defaultJsonData';
import { defaultResultData } from '@/views/module/mold/defaultResultData';
import MoldResult from '@/views/module/mold/MoldResult';
import MoldSettings from '@/views/module/mold/MoldSettings';

const PanelEnum = {
	part: 'part',
	mesh: 'mesh',
	resolve: 'resolve',
};

const ActionTypeEnum = {
	submit: 'submit',
	saveData: 'saveData',
};

const check = (list, name, condition) => {
	return list.some(item => item.name === name && condition(item));
};

const getFirstItem = (list, defaultValue = undefined) => list?.length ? list[0] : defaultValue;

export default {
	name: 'TaskDetail',
	mixins: [
		mixinWebSocket, mixinModule,
	],
	components: {
		MoldView,
		Navigation,
		GlobalHeader,
		CollapseTree,
		MoldSettings,
		JEllipsis,
		MoldResult,
		HeaderTip,
	},
	data () {
		return {
			backHomeImg,
			ModuleEnum,
			PanelEnum,
			DEFAULT_VIEW_BACKGROUND,
			siderWidth: 320,
			curSelectMode: SelectModeEnum.solid,
			curRenderMode: RenderModeEnum.surfaceAndWireframe,
			curDraw: '',
			curAction: '',
			curPlaneMode: '',
			curViewMode: '',
			hasCover: false,
			visibleView: true,
			submitLoading: false,
			disabledSelectFace: false,
			disabledSelectSolid: false,
			taskInfoLoading: false,
			partList: [{ ...deepCopy(defaultPartData) }],
			modelList: [],
			curParam: undefined,        // 当前激活的参数
			selectTagExtra: undefined,  // 当前激活的预设线
			selectTag: '',				// 激活参数下的Tag
			taskInfo: {},
			fileName: '',
			calcParam: [],
			jsonData: [],
			curActiveObj: undefined,    // 当前激活的参数对象
			settings: [...deepCopy(defaultJsonData)],
			drawCurveParams: {},        // 绘制曲线参数（需提交保存，作为回显用）
			drawCircleParams: {},       // 绘制圆参数（需提交保存，作为回显用）
			online: '正常',
			downLink: '0 M/s',
			tipName: '',
			taskId: '',
			projectId: '',
			onceSubmit: true,
			calcParamMd5: null,
			solveLoading: false,
			disableSettings: false,
			results: [...deepCopy(defaultResultData)],
			selectedNames: [],
			bjPointNameList: [],
			pointMap: {},
			copyByName: '',
			diyShapeList: [], // 每个对象结构：key，value，type
		};
	},
	computed: {
		...mapGetters([
			'isLogin', 'userInfo', 'collapsed', 'solidNames', 'moduleList',
		]),
		taskName () {
			return this.taskInfo?.sysTask?.taskName || '';
		},
		path () {
			return `${this.userInfo.orgCode}/${this.projectId}/${this.taskId}`;
		},
		dispersePath () {
			return `${Config.staticDomainURL}/${this.userInfo.orgCode}/${this.projectId}/${this.taskId}/dispersed`;
		},
		canCreate () {
			return this.moduleObject.canCreate &&
				!this.solveLoading;
		},
		// canDownload () {
		// 	const status = this.results.find(i => i.key === 'result_make_pipe').status;
		// 	// logger.log('canDownload', this.moduleObject.canDownload,
		// 	// this.taskInfo?.sysTask?.status === TaskStatusEnum.success,
		// 	// 	[undefined, 0, 1, 8, 9].includes(status), !this.solveLoading);
		// 	return this.moduleObject.canDownload &&
		// 		(this.taskInfo?.sysTask?.status === TaskStatusEnum.success ||
		// 		[undefined, 0, 1, 8, 9].includes(status)) &&
		// 		!this.solveLoading;
		// },
		disabled () {
			return this.userInfo.id !== this.taskInfo?.sysTask?.createBy;
		},
	},
	async mounted () {
		this.userId = this.userInfo.id || '';
		this.projectId = this.$route.params.id || '';
		this.taskId = this.$route.params.taskId || '';

		// 监听离散或计算数据
		this.listenDispersedOrCompute(false);

		this.initEvents();

		await this.$refs.moldView.loadData();

		this.initProjectData();
	},
	methods: {
		...mapActions(['initPermissionList']),

		/**
		 * 初始化缓存数据
		 */
		initCacheData (taskInfo) {
			if (taskInfo?.viewParamSets) {
				const viewParamSets = taskInfo?.viewParamSets;
				if (viewParamSets.partList) this.partList = viewParamSets.partList;
				if (viewParamSets.settings) this.settings = viewParamSets.settings;
				// if (viewParamSets.results) this.results = viewParamSets.results;
				if (viewParamSets.hiddenObjectMap) {
					this.$refs.moldView.hiddenObjectMap = viewParamSets.hiddenObjectMap;
				}
			}
			if (this.fileName) {
				const part = this.partList[0];
				part.value = this.fileName ? [this.fileName] : [];
				part.submitLoading = !this.fileName ? false : part.submitLoading;
			}
		},

		/**
		 * 初始化树
		 */
		initTree () {
			if (this.partList?.length) {
				this.allVisibleHandler(this.partList[0]);
				this.allColorHandler(this.partList[0]);
				this.allOpacityHandler(this.partList[0]);
			}
			if (this.settings?.length) {
				const setting = this.settings.find(setting => setting.activate);
				if (setting) {
					this.visibleHandler(setting);
					this.onActivateHandler(setting, false);
					this.opacityHandler(setting);
				}
			}
		},

		/**
		 * 将任务缓存保存到服务器
		 */
		async saveTaskCacheToServer () {
			const params = {
				id: this.taskId,
				viewParamSets: { // 页面所有参数设置
					partList: this.partList, // 模具参数
					settings: this.settings, // 参数配置
					results: this.results,   // 结果参数
					hiddenObjectMap: this.$refs.moldView?.hiddenObjectMap,   // 隐藏对象参数
					diyShapeList: this.diyShapeList ?? [], // 绘图信息
				},
			};
			logger.log('更新任务缓存数据参数 params: ', params);
			const res = await updateTaskCacheParams(params, { loading: false });
			logger.log('更新任务缓存数据结果 res: ', res);
		},

		/**
		 * 提交任务
		 */
		submitTask: debounce(async function (e) {

			if (this.solveLoading || this.taskInfo?.sysTask?.status === TaskStatusEnum.waiting) {
				return;
			}

			if (e?.srcElement?.blur) {
				e.srcElement.blur();
			}

			await this.initPermissionList();

			if (!this.moduleObject.canCreate) {
				if (this.moduleObject.expired) {
					this.$message.warning('套餐已过期');
				} else if (this.moduleObject.zeroCount) {
					this.$message.warning('套餐剩余下载次数不足');
				}
				this.solveLoading = false;
				return;
			}

			if (this.disabled) {
				this.$message.warning('只有创建者可以提交');
				this.solveLoading = false;
				return;
			}

			this.solveLoading = true;
			this.openViewLoading('计算中...', true);

			// 去除所有选中状态
			this.resetOtherSettingActive({});
			this.resetOtherTagActive({});

			// 去除所有面和预设线的颜色
			this.resetAllYsLineColor();
			this.$refs.moldView.resetColorHandle();

			const { status, postParams } = this.submitValidate();

			if (!status) return;

			// 清除加载的结果数据
			this.$refs.moldView?.clearResult();

			this.results = [...deepCopy(defaultResultData)];

			const params = {
				id: this.taskId,
				paramSets: postParams,          // 计算值参数
				viewParamSets: {    // 页面所有参数设置
					partList: this.partList,    // 模具参数
					settings: this.settings,    // 参数配置
					results: [],                // 结果参数，需要清空服务器保存的缓存来同步页面显示
					diyShapeList: this.diyShapeList ?? [],    // 自定义画线需要生成文件 {key线名称, value文件内容, type类型}
				},
			};

			logger.log('提交任务参数 params: ', params);
			this.$refs.moldView.curActiveObj = null;
			const res = await submitRhino(params, { loading: false });
			logger.log('提交任务结果 res: ', res);

			if (res?.success && res.result) {
				if (res.result.execute === ActionTypeEnum.saveData) {
					this.closeViewLoading();
					this.solveLoading = false;
				} else {
					this.updateStatus();
				}
				this.$message.success(res.message);
			} else {
				this.closeViewLoading();
				this.solveLoading = false;
			}

		}, 10),

		/**
		 * 更新状态
		 */
		async updateStatus () {
			const res = await getProjectTaskDetail({ id: this.taskId });
			if (res.success) {
				const results = deepCopy(this.results);
				results.forEach(i => {
					['result_make_pipe', 'result_pipe_line_preview', 'result_include_water_model', 'result_pipe_line'].forEach(j => {
						if (j === i.key) {
							if (res.sysTask?.resultJson[j]) {
								i.status = res.sysTask?.resultJson?.[j + '_status'];
							} else {
								i.status = 2; // 未计算
							}
						}
					});
				});
				this.results = results;
			}
		},
		/**
		 * 上传离散文件
		 */
		async uploadCallback ({ file }) {

			const part = this.partList[0];
			part.submitLoading = true;

			const formData = new FormData();
			formData.append('taskId', this.taskId);
			formData.append('file', file);
			const res = await makeDispersed(formData).catch(() => part.submitLoading = false);
			logger.log('上传离散文件结果 res: ', res);

			// 必须用后端返回的名称，后缀被改了
			if (res?.result) this.fileName = res.result;

			this.openViewLoading();

			logger.time('离散数据时间');
		},

		/**
		 * 提交验证
		 */
		submitValidate () {
			const result = { status: false, postParams: undefined };

			const formatData = (type) => this.settings.filter(s => s.type === type).map(s => {
				return {
					key: s.key,
					value: s.value,
					extra: s.extra,
				};
			});
			const shapeList = formatData('shape');
			const sliderList = formatData('slider');
			const switchList = formatData('switch');

			const ysLineFormat = () => {
				const list = [];
				shapeList.filter(s => s.key === 'ys_line').forEach(s => {
					const ysLineNames = [];
					const lineNames = [];
					const typeObj = {};
					const textObj = {};
					s.extra.forEach(extraItem => {
						if (extraItem.ysLineName) {
							ysLineNames.push(extraItem.ysLineName);

							const obj = this.$refs.moldView.curvesMap[extraItem.ysLineName];

							logger.log('submitValidate', obj);
							textObj[extraItem.ysLineName] = obj?.clickPoints || [];
							// 所有线同样处理
							this.updateDiyShapeList({
								key: extraItem.ysLineName,
								value: obj?.clickPoints || [],
							});

						}
						if (extraItem.crkLineNames?.length) {
							const arr = [...extraItem.crkLineNames];
							arr.forEach(i => {
								const obj = this.$refs.moldView.curvesMap[i];
								if (obj) {
									// 处理自定义绘制的出入口线
									this.updateDiyShapeList({
										key: i,
										value: obj.clickPoints,
									});
								}
							});
							lineNames.push(arr);
						}
					});
					list.push({ name: 'ys_line', text: textObj });
					list.push({ name: 'b_line', text: lineNames.join(',') });
				});
				return list;
			};

			const bjPointFormat = () => {
				const list = [];
				const obj = shapeList.find(s => s.key === 'bj_point');
				if (obj) {
					const points = [];
					obj.extra?.forEach(extraItem => {
						if (extraItem.points?.length) {
							points.push(...extraItem.points);
						}
					});
					list.push({ name: obj.key, text: points });
				}
				return list;
			};

			const commonFormat = (list) => list.map(s => {
				if (!['ys_line', 'bj_point'].includes(s.key)) {
					return {
						name: s.key,
						text: isArray(s.value) ? s.value.join(',') : Number(s.value),
					};
				}
			});

			// 组装参数
			const postParams = [
				{
					name: 'path',
					text: this.path,
				},
				{
					name: this.partList[0].key,
					text: getFirstItem(this.partList[0].value, ''),
				},
				...ysLineFormat(),
				...bjPointFormat(),
				...commonFormat(shapeList),
				...commonFormat(sliderList),
				...commonFormat(switchList),
			].filter(p => p); // 过滤空值

			logger.log('提交任务参数：', postParams);

			const getValue = (key) => postParams.find(p => p.name === key).text;

			// 弹窗提示
			const toast = (msg, duration = 5) => {
				this.closeViewLoading();
				this.solveLoading = false;
				this.$message.config({
					top: `10%`,
					duration: duration,
					maxCount: 3,
				});
				this.$message.warning(msg, duration);
			};

			// 验证模型名称是否存在
			if (!getValue('model_part')) {
				toast('请先上传模型!');
				return result;
			}

			const firstValidate = () => {
				if (!getValue('jw_sur')) {
					toast('胶位面不能为空!');
					return false;
				}
				if (!getValue('crk_sur')) {
					toast('出入口面不能为空!');
					return false;
				}
				return true;
			};

			const secondValidate = () => {
				// 验证 预设线与出入口边线分组对应
				const ysLineValueObj = getValue('ys_line');
				const ysLineNames = Object.keys(ysLineValueObj);
				if (ysLineNames.length === 0) {
					toast(`请添加预设线!`);
					return false;
				}

				const ysLineSetting = this.getSetting('ys_line');
				if (!getValue('b_line')) {
					toast('出入口边线不能为空!');
					return false;
				}

				const list = [...ysLineSetting.extra];
				let index = 0;
				for (const extraItem of list) {
					index++;
					if (extraItem.ysLineName && !extraItem.crkLineNames?.length) {
						toast(`第${index}组数据，预设线${extraItem.ysLineName}缺少出入口边线，请完善!`);
						return false;
					}
					if (!extraItem.ysLineName && extraItem.crkLineNames?.length) {
						toast(`第${index}组数据，出入口边线${extraItem.crkLineNames.join(',')}缺少预设线，请完善!`);
						return false;
					}
				}

				// 变径点判断
				let bjPointList = getValue('bj_point');
				if (bjPointList?.length) {
					bjPointList = bjPointList.filter(p => p); // 过滤空值
					if (bjPointList.length % 2 !== 0) {
						toast(`一组变径点数据必须包含两个点!`);
						return false;
					}
				}

				// 管半径 + 管径调整量 - 变径量值，应大于等于1
				const pipeRadiusValue = Number(getValue('radius_of_pipe'));
				const gjItemValue = Number(getValue('num_of_gjtz'));
				const bjlItemValue = Number(getValue('bjl'));
				if (bjPointList?.length && pipeRadiusValue + gjItemValue + bjlItemValue < 1) {
					toast(`管半径+管径调整量-变径量值，应大于等于1，请修改相关参数值!`);
					return false;
				}

				return true;
			};

			// 验证第一开关必填项（设计空间开关）
			if (getValue('calc_design_space')) {
				if (!firstValidate()) {
					return result;
				}
			}

			// 验证第二、三开关必填项（管线开关、管(路)开关）
			if (getValue('make_pipe_line') || getValue('make_pipe')) {
				if (!secondValidate()) {
					return result;
				}
			}
			result.status = true;
			result.postParams = postParams;
			return result;
		},

		/**
		 * 更新参数设置
		 */
		updateSettings (key, attrName, attrValue) {
			const settings = [...this.settings];
			this.settings = [];
			settings.forEach(setting => {
				if (setting.key === key) {
					setting[attrName] = attrValue;
					if (attrName === 'checked') {
						setting['value'] = attrValue ? 1 : 0;
					}
				}
				if (isEmpty(key)) { // key为空时，更新所有
					setting[attrName] = attrValue;
				}
			});
			this.settings = settings;
		},

		/**
		 * 初始化项目数据
		 */
		async initProjectData () {
			try {
				await this.getTaskInfo();
			} catch (e) {
				logger.error('初始化项目数据 error: ', e);
			}
		},

		/**
		 * 获取任务详情
		 */
		async getTaskInfo () {
			const res = await getProjectTaskDetail({ id: this.taskId });
			logger.log('获取任务详情 res: ', res);

			this.taskInfoLoading = true;

			if (res?.success && res?.result) {
				this.taskInfo = res?.result || {};
				const sysTask = this.taskInfo.sysTask || {};

				// 模型名称
				this.fileName = this.taskInfo.fileName || '';

				// 是否有缩略图
				this.hasCover = !!sysTask.cover;

				this.initCacheData(this.taskInfo);

				// 解析离散数据
				if (sysTask.type) {
					logger.log('随行冷离散数据 dispersedResult: ', sysTask.dispersedResult);
					const dispersedResult = sysTask.dispersedResult;
					if (!dispersedResult && sysTask.status === ProgressStatusEnum.FAIL) {
						this.$modal.error({
							content: '任务离散失败，请重新创建任务',
							okText: '返回任务列表',
							onOk: () => this.$router.push(`/mold/project/detail/${this.projectId}`),
						});
						return;
					}
					if (dispersedResult?.filePath) {
						await this.$refs.moldView.loadData(dispersedResult.filePath);
					} else if (dispersedResult) {
						// await this.$refs.moldView.loadDataV2(dispersedResult);
						await this.$refs.moldView.loadDataV3(dispersedResult);
					}
				}

				// 解析求解数据
				let isUpdateGlobalOpacity = false;
				if (sysTask.type && sysTask.resultJson && !isEmptyObject(sysTask.resultJson)) {
					this.results = await this.$refs.moldView?.loadComputeData(sysTask, deepCopy(defaultResultData), this.taskInfo.viewParamSets.results);
					isUpdateGlobalOpacity = this.results.some(res => res && res.visible);
					logger.log('[getTaskInfo] isUpdateGlobalOpacity: ', isUpdateGlobalOpacity);
				}

				// 根据返回值初始化离散线
				if (sysTask.type && sysTask.faceResultPath) {
					logger.log('根据返回值初始化离散线 faceResultPath :', sysTask.faceResultPath);
					const crkSetting = this.getSetting('crk_sur');
					if (crkSetting?.value?.length) {
						await this.$refs.moldView?.loadLinesData();
					}
				}

				// 根据返回值绘制预设线
				const ysLineSetting = this.getSetting('ys_line');
				if (ysLineSetting?.extra?.length) {
					for (const extraItem of ysLineSetting.extra) {
						if (extraItem.ysLineName && extraItem.ysLineValue?.length) {
							ysLineSetting.value.push(extraItem.ysLineName);
							if (extraItem) {
								// 初始化通用预设线
								await this.$refs.moldView.drawCurve(extraItem.ysLineName, [...extraItem.ysLineValue], true);
							}
						}
					}
				} else {
					if (ysLineSetting?.extra) { // 添加一组默认选项
						ysLineSetting.extra.push({
							id: getCustomId(),
							ysLineName: '',         // 预设线名称
							ysLineValue: [],        // 预设线值（points）
							ysLineActivate: false,  // 预设线激活
							crkLineActivate: false, // 出入口边线激活
							crkLineNames: [],       // 出入口边线名称集合
						});
					}
				}

				// 根据返回值绘制变径点
				const bjPointSetting = this.getSetting('bj_point');
				// 初始化变径点名称集合
				const bjPointNameList = [];
				if (bjPointSetting?.extra?.length) {
					this.$refs.moldView.pointCount = 0;
					for (const extraItem of bjPointSetting.extra) {
						if (extraItem.groupName && extraItem.points?.length) {
							bjPointSetting.value.push(...extraItem.pointNames);
							await this.$refs.moldView.getSceneAllBjPoint();
							for (let i = 0; i < extraItem.points.length; i++) {
								const nickname = extraItem.pointNames[i];
								if (nickname) {
									this.$refs.moldView.pointCount = Math.max(this.$refs.moldView.pointCount, +nickname.replace(/^.*_/g, '')) + 1;
									bjPointNameList.push(nickname);
									if (extraItem.points[i]) {
										await this.$refs.moldView.drawBjPointSphere(extraItem.points[i], { nickname, groupName: extraItem.groupName });
									}
								}
							}
						}
					}
				}
				this.$nextTick(() => {
					this.bjPointNameList = bjPointNameList;
				});
				this.diyShapeList = this.taskInfo?.viewParamSets?.diyShapeList ?? [];
				// 初始化圆，单双螺旋线，曲线/折线
				this.diyShapeList.forEach(i => {
					if (i.key.startsWith('circle')) {
						this.$refs.moldView.drawCurve(i.key, i.value, true);
					}
				});

				// 初始化树
				this.initTree();

				const progressLoading = sysTask.status === ProgressStatusEnum.LOADING;
				// 离散中
				this.submitLoading = (sysTask.type === WsTypeEnum.dispersed && progressLoading) || (sysTask.type === WsTypeEnum.resolve && progressLoading);
				if (this.submitLoading) {
					this.openViewLoading();
				}

				// 计算中
				this.solveLoading = sysTask.type === WsTypeEnum.resolve && progressLoading;
				if (this.solveLoading) {
					this.openViewLoading();
				}

				// 更新全局设置必须放置在最后操作（防止修改过程中，别的地方也有修改）
				if (isUpdateGlobalOpacity) {
					this.partList[0].opacity = 50;
					this.updateAllOpacity(50);
				}

				// 默认参考平面
				this.onPlaneModeSelect(PlaneModeEnum.planeXY);

				// 处理右键隐藏的数据
				this.$nextTick(() => {
					const hiddenObjectMap = this.$refs.moldView?.hiddenObjectMap;
					if (hiddenObjectMap) {
						this.$refs.moldView.updateVisibleByList(Object.keys(hiddenObjectMap), false);
					}

					// 有结果时隐藏参考平面
					if (sysTask.type && sysTask.resultJson && !isEmptyObject(sysTask.resultJson) && this.$refs.moldView.gridHelper) {
						this.$refs.moldView.gridHelper.visible = false;
					}
				});

			}

			this.taskInfoLoading = false;
		},

		/**
		 * 更新部件数据
		 */
		updatePartListData ({ visible, color, opacity, value, submitLoading } = {}) {
			const part = this.partList[0];
			if (!isEmpty(visible)) {
				part.visible = visible;
			}
			if (!isEmpty(color)) {
				part.color = color;
			}
			if (!isEmpty(opacity)) {
				part.opacity = opacity;
			}
			if (!isEmpty(value)) {
				part.value = value;
			}
			if (!isEmpty(submitLoading)) {
				part.submitLoading = submitLoading;
			}
		},

		/**
		 * 更新结果数据
		 */
		updateResultsData ({ key, visible, color, opacity }) {
			const result = this.results.find(res => res.key === key);
			if (!result) return;
			if (!isEmpty(visible)) {
				result.visible = visible;
			}
			if (!isEmpty(color)) {
				result.color = color;
			}
			if (!isEmpty(opacity)) {
				result.opacity = opacity;
			}
		},

		/**
		 * 重置缓存数据
		 */
		async resetCacheData () {
			await Promise.all([
				this.$store.commit('savePartList', []),
				this.$store.commit('saveSettings', []),
				this.$store.commit('saveResults', []),
				this.$store.commit('saveHiddenObjectMap', {}),
			]);
			this.partList = [{ ...deepCopy(defaultPartData) }];
			this.settings = [...deepCopy(defaultJsonData)];
			this.results = [...deepCopy(defaultResultData)];
		},

		/**
		 * 监听离散或计算数据
		 */
		listenDispersedOrCompute (loading = true) {
			const params = {};
			this.listenWsStatus(params, {
				loading: loading,
				callback: async (res) => {
					logger.log('监听离散或计算数据: ', res);

					// 轮询检测，等待moldView页面加载完毕
					if (!loading) {
						await checkIfLoaded(!!this.$refs?.moldView);
					}

					this.solveLoading = false;
					const part = this.partList[0];
					part.value = this.fileName ? [this.fileName] : [];
					part.submitLoading = !this.fileName ? false : part.submitLoading;
					this.saveTaskCacheToServer().then();

					if (res.type === WsTypeEnum.dispersed) {
						logger.log('离散地址 filePath: ', res.filePath);
						logger.log('离散数据 dispersedResult: ', res.dispersedResult);
						if (res.filePath) {
							await this.$refs.moldView.loadData(res.filePath);
						} else {
							if (res.dispersedResult) {
								// await this.$refs.moldView.loadDataV2(res.dispersedResult);
								await this.$refs.moldView.loadDataV3(res.dispersedResult);
								this.partList[0].opacity = 100;
								this.updateAllOpacity(100);
								logger.timeEnd('离散数据时间');
							}
						}
						this.onPlaneModeSelect(PlaneModeEnum.planeXY);
					}

					// 处理通过面离散线返回的数据
					if (res.type === WsTypeEnum.dispersedFace) {
						const crkSetting = this.getSetting('crk_sur');
						logger.log('[listen] 离散面值: ', crkSetting?.value);
						if (crkSetting?.value.length) {
							await this.$refs.moldView?.loadLinesData();
						}
					}

					// 结果通知
					if (res.type === WsTypeEnum.resolve) {
						if (this.$refs.moldView.gridHelper) {
							this.$refs.moldView.gridHelper.visible = false;
						}
						this.results = await this.$refs.moldView.loadComputeData(res, deepCopy(defaultResultData));

						const isUpdateGlobalOpacity = this.results.some(i => i.visible);
						logger.log('[resolve] isUpdateGlobalOpacity: ', isUpdateGlobalOpacity);
						if (isUpdateGlobalOpacity) {
							this.partList[0].opacity = 50;
							this.updateAllOpacity(50);
							if (this.taskInfo?.sysTask?.status) {
								this.taskInfo.sysTask.status = TaskStatusEnum.success;
							}
						}
					}

					// 下载step通知
					if (res.type === WsTypeEnum.download) {
						logger.log('下载step通知 res: ', res);
						if (res.downloadUrl) {
							const filePath = Config.staticDomainURL + '/' + res.downloadUrl;
							downloadFileByNewWindow(filePath, basename(filePath));
						}
					}

					// 下载文件生成状态更新
					if (res.type === WsTypeEnum.resultExportStatus) {
						logger.log('下载step文件的状态更新 res:', res);
						if (res.resultJson) {
							const results = [...deepCopy(this.results)];
							results.forEach(i => {
								['result_make_pipe', 'result_pipe_line_preview', 'result_include_water_model', 'result_pipe_line'].forEach(j => {
									if (j === i.key) {
										i.status = res.resultJson[j + '_status'];
									}
								});
							});
							this.results = results;
						}
					}
				},
				errCallback: (res) => {
					this.solveLoading = false;
					this.partList[0].submitLoading = false;
					const action = res.type === WsTypeEnum.resolve ? '求解' : '离散';
					this.$modal.error({ content: res.errors || `${action}失败，请稍后重试!` });
				},
			});
		},

		/**
		 * 监听websocket状态
		 */
		async listenWsStatus (params, options) {
			const loading = options.loading ?? true;
			const loadingMsg = options.loadingMsg ?? '';
			const callback = options.callback || noop;
			const errCallback = options.errCallback || noop;
			const progressCallback = options.progressCallback || noop;

			// const key = Date.now();
			const key = this.taskId;
			if (loading) this.openViewLoading(loadingMsg);
			const socket = await this.initWebsocket(key);
			// socket.send(params);
			this.$root.$on(`${key}Callback`, res => {
				// status:状态 0:未开始，1:创建中，8:完成，9:失败
				// type:类型 dispersed:离散数据，resolve:求解器数据
				if (res.status === ProgressStatusEnum.LOADING) {
					progressCallback(res);
				}
				if (res.status === ProgressStatusEnum.SUCCESS) {
					this.closeViewLoading();
					// socket.onClose(res.type);
					callback(res);
				}
				if (res.status === ProgressStatusEnum.FAIL) {
					this.closeViewLoading();
					errCallback(res);
				}
			});
		},

		/**
		 * 切换折叠
		 */
		toggleCollapsed () {
			const collapsed = !this.collapsed;
			this.siderWidth = collapsed ? 0 : 320;
			this.$nextTick(() => {
				this.$store.dispatch('setCollapsed', collapsed);
			});
		},

		/**
		 * 切换到选择实体
		 */
		switchToChooseSolid (bool = true) {
			this.disabledSelectFace = !!bool;
			this.disabledSelectSolid = false;
			this.curSelectMode = SelectModeEnum.solid;
			this.$refs.moldView.selectModeHandle(SelectModeEnum.solid);
		},

		/**
		 * 切换到选择面
		 */
		switchToChooseFace (bool = true) {
			this.disabledSelectFace = false;
			this.disabledSelectSolid = !!bool;
			this.curSelectMode = SelectModeEnum.surface;
			this.$refs.moldView.selectModeHandle(SelectModeEnum.surface);
		},

		/**
		 * 取消选择禁用
		 */
		cancelChooseDisable () {
			this.disabledSelectFace = false;
			this.disabledSelectSolid = false;
			this.curSelectMode = SelectModeEnum.surface;
			this.$refs.moldView.selectModeHandle(SelectModeEnum.surface);
		},

		/**
		 * 右键点击事件
		 */
		onContextMenuHandle ({ header, groupName, type, data }) {
			logger.log('projectDetail 右键点击事件 header, type, data: ', header, type, data);
		},

		/**
		 * 模型选择处理
		 */
		async modelSelectHandle ({ header, groupName, data }) {
			logger.log('模型选择处理 header, groupName, data: ', header, groupName, data);
		},

		/**
		 * 更新左侧菜单树选择状态
		 */
		updateLeftMenuTreeData (list, handlerA, handlerB = undefined) {
			const tempList = deepCopy(list);
			tempList.forEach(item => {
				if (handlerA) {
					item.children.forEach(child => {
						child.selected = handlerA(child);
					});
				}
				item.selected = handlerB ? handlerB(item) : item.selected;
			});
			return tempList;
		},

		/**
		 * 模型增加处理
		 */
		async modelAddHandle ({ groupName }) {
			logger.log('模型增加处理 groupName: ', groupName);
		},

		/**
		 * 开启加载中
		 */
		openViewLoading (loadingMsg = '', resetCount) {
			this.submitLoading = true;
			this.$refs.moldView.openLoading(loadingMsg, resetCount);
		},

		/**
		 * 关闭加载中
		 */
		closeViewLoading () {
			this.submitLoading = false;
			this.$refs.moldView?.closeLoading();
		},

		/**
		 * 重置视图
		 */
		resetView () {
			if (this.checkAction()) return;
			this.$refs.moldView.resetView();
			this.curPlaneMode = PlaneModeEnum.planeXY;
			this.$refs.moldView.selectPlaneMode(PlaneModeEnum.planeXY);
		},

		/**
		 * 重置相机
		 */
		resetCamera () {
			this.$refs.moldView?.resetCamera();
		},

		/**
		 * 绘图
		 * type: 画点、画线、画圆
		 */
		onDraw (type) {
			if (this.checkAction()) return;
			if (this.curDraw && type && this.curDraw !== type) {
				this.$message.warn('请结束之前的绘制操作');
				const oldDraw = this.curDraw;
				this.curDraw = type;
				this.$nextTick(() => {
					this.curDraw = oldDraw;
				});
				return;
			}
			if (this.$refs.moldView.controlling) {
				this.$message.warn('请取消或完成当前控制操作后继续');
				return;
			}
			this.curDraw = type;
			this.$refs.moldView.onDraw(type);
		},

		/**
		 * 清除所有绘制
		 */
		clearAllDrawn () {
			if (this.checkAction()) return;
			this.$refs.moldView.clearAllDrawn();
			this.bjPointNameList = [];
			const bj_point = this.getSetting('bj_point');
			bj_point.extra.forEach(i => {
				i.points = [];
				i.pointNames = [];
			});
		},

		/**
		 * 控制操作
		 * type: 添加控制点、复制、移动
		 */
		onAction (type) {
			this.curAction = type;
			if (this.checkAction()) {
				this.$nextTick(() => {
					this.closeActionControl();
				});
				return;
			};
			if (this.$refs.moldView.curDraw) {
				this.$nextTick(() => {
					this.closeActionControl();
				});
				this.$message.warn('请取消或完成当前控制操作后继续');
				return;
			}
			this.$refs.moldView.onAction(type);
		},

		/**
		 * 框选取消
		 */
		closeBoxSelect () {
			this.curAction = '';
			this.$refs.moldView.onBoxSelect(null);
		},

		/**
		 * 更改投影模式
		 */
		changeCameraMode (mode) {
			if (this.checkAction()) return;
			this.$refs.moldView?.changeCameraMode(mode);
		},

		/**
		 * 更改渲染模式
		 * @param mode
		 */
		changeRenderMode (mode) {
			if (this.checkAction()) return;
			this.curRenderMode = mode;
			this.$refs.moldView?.changeRenderMode(mode);
		},

		/**
		 * 更新视图
		 */
		updateView (direction) {
			this.curViewMode = direction;
			if (this.checkAction()) {
				this.$nextTick(() => {
					this.curViewMode = '';
				});
				return;
			}
			;
			this.$refs.moldView?.updateView(direction);
			// 各视图状态下，参考平面重绘
			switch (direction) {
				case ViewEnum.front:
				case ViewEnum.back:
					this.curPlaneMode = PlaneModeEnum.planeXZ;
					this.$refs.moldView.selectPlaneMode(PlaneModeEnum.planeXZ);
					break;
				case ViewEnum.left:
				case ViewEnum.right:
					this.curPlaneMode = PlaneModeEnum.planeYZ;
					this.$refs.moldView.selectPlaneMode(PlaneModeEnum.planeYZ);
					break;
				case ViewEnum.top:
				case ViewEnum.bottom:
					this.curPlaneMode = PlaneModeEnum.planeXY;
					this.$refs.moldView.selectPlaneMode(PlaneModeEnum.planeXY);
					break;
				default:
					this.curPlaneMode = PlaneModeEnum.planeXY;
					this.$refs.moldView.selectPlaneMode(PlaneModeEnum.planeXY);
					break;
			}
			this.curPlaneMode;
		},

		/**
		 * 选择模型(点、线、面、体)
		 */
		selectModeHandle (selectMode) {
			if (this.checkAction()) return;
			this.curSelectMode = selectMode;
			this.$refs.moldView?.selectModeHandle(selectMode);
		},

		/**
		 * 检查动作
		 */
		checkAction ({ message = '正在执行拖动等动作，请先释放后再重试!', condition = () => true } = {}) {
			if (this.$refs.moldView?.currentActiveItem && condition()) {
				this.$message.warning(message);
				return true;
			}
			return false;
		},

		/**
		 * 更新颜色
		 */
		updateAllColor (value) {
			this.$refs.moldView?.updateAllColor(Number(value.replace(/^#/, '0x')));
		},

		/**
		 * 更新透明度
		 */
		updateAllOpacity (value) {
			this.$refs.moldView?.updateAllOpacity(value);
		},

		/**
		 * 更新颜色
		 */
		updateAllVisible (value, hiddenYsLine) {
			this.$refs.moldView?.updateAllVisible(!!value, hiddenYsLine);
		},

		/**
		 * 更新切面
		 * @params 对象{切面，反转xyz方向，反转正反方向}
		 */
		changeClipping ({ clip, clippingReverse = {} }) {
			this.$refs.moldView?.updateClipping(clip, clippingReverse);
		},

		/**
		 * 旋转视图
		 */
		updateRotate (obj) {
			this.$refs.moldView?.updateRotate(obj);
		},

		/**
		 * 鼠标左键设置
		 * @param value
		 */
		mouseLeftSetting (value) {
			this.$refs.moldView.mouseLeftSetting(value);
		},

		/**
		 * 鼠标右键设置
		 * @param value
		 */
		mouseRightSetting (value) {
			this.$refs.moldView.mouseRightSetting(value);
		},

		/**
		 * 其他选择更改
		 */
		otherSelect (value) {
			this.$refs.moldView.otherSelect(value);
		},

		/**
		 * 参考平面
		 */
		onPlaneModeSelect (value) {
			this.curPlaneMode = value;
			this.$refs.moldView.selectPlaneMode(value);
		},

		/**
		 * 下载结果文件
		 */
		downloadResultFile (event, data, groupName) {
			preventDefaults(event);
			logger.log('下载结果文件 groupName, data: ', groupName, data);
			const fileName = this.taskInfo.cad_models.replace(/(\.stp|\.step|\.stl|\.ige|\.iges)/, '.rmed');
			const rMedFileUrl = `${this.basePath}/resolve/${fileName}`;
			downloadFileByNewWindow(rMedFileUrl, basename(rMedFileUrl));
		},

		/**
		 * 框选结果处理
		 */
		async boxSelectedHandler (nicknames) {

			logger.log('框选结果处理: ', nicknames);

			if (this.checkAction()) return;
			const t1 = performance.now();

			if (!this.curActiveObj || !nicknames || !nicknames.length) return;

			const setting = this.settings.find(setting => setting.key === this.curActiveObj.key);
			// setting.value = Array.from(new Set([...setting.value, ...nicknames]));
			setting.value = [...nicknames];
			setting.value.sort(sortByName);
			this.curActiveObj.value = setting.value;

			const t2 = performance.now();
			logger.log(`  ${Math.round(t2 - t1)} ms: boxSelectedHandler (${nicknames ? nicknames.join(',') : null})`);
		},

		/**
		 * 更新设置属性和值
		 */
		getSetting (key) {
			return this.settings.find(setting => setting.key === key);
		},

		/**
		 * 重置所有预设线线颜色
		 */
		resetAllYsLineColor ({ selected = false, color = ColorEnum.black, onlyYsLine = false } = {}) {
			const allYsLine = this.getSceneAllYsLine().map(line => line && line.nickname);
			const allDispersedLine = this.getSceneAllDispersedLine().map(line => line && line.nickname);
			this.$refs.moldView.updateYsLineAttrByNameList([...allYsLine, ...allDispersedLine], { selected, color, onlyYsLine });
		},

		/**
		 * 选中/取消选中 图形
		 */
		async singleSelectedHandler ({ name, isAdd }) {
			logger.log('singleSelectedHandler name: ', name, isAdd);

			if (!this.curActiveObj || !name) return;

			const moldView = this.$refs.moldView;
			this.disableSettings = !!moldView.selectTarget;

			// 拾取选中拖动时，禁止单选操作
			if (moldView.selectTarget) return;

			const condition = () => this.curActiveObj.key === 'ys_line' && !this.curActiveObj.activate;
			if (this.checkAction({ condition })) return;

			let canUpdate = true;

			const colorUpdate = (nameList, selected, color = undefined) => {
				// 更新选中值的颜色
				moldView.updateYsLineAttrByNameList(nameList, { selected: selected, color: color });
			};

			const updateValueAndColor = () => {
				// update tmp value
				const existed = this.curActiveObj.value.includes(name);
				if (existed) {
					if (!isAdd) {
						this.curActiveObj.value = this.curActiveObj.value.filter(faceName => faceName !== name);
						const color = !existed ? this.curActiveObj.color : ColorEnum.black;
						colorUpdate([name], !existed, color);
					}
				} else {
					if (isAdd) {
						this.curActiveObj.value.push(name);
						const color = !existed ? this.curActiveObj.color : ColorEnum.black;
						colorUpdate([name], !existed, color);
					}
				}

				this.curActiveObj.value = Array.from(new Set(this.curActiveObj.value));

				// update color

			};
			if (this.curActiveObj.key === 'ys_line') { // 同步settings对应的预设线及出入口边线数据
				this.copyByName = name; // 保存选中名称，用于复制
				// 预设线及出入口边线菜单被激活，但是预设线或者出入口边线按钮没被单独激活，不允许操作
				if (this.curActiveObj.activate) {
					const allLineNotActivate = this.curActiveObj.extra.every(extraItem => !extraItem.ysLineActivate && !extraItem.crkLineActivate);
					if (allLineNotActivate) {
						// this.$message.warn(`请先激活预设线或出入口边线按钮`);
						updateValueAndColor();
						return;
					}
				}

				const ysLineExtraItem = this.curActiveObj.extra?.find(extraItem => extraItem.ysLineActivate);
				if (ysLineExtraItem) {
					canUpdate = false;
					let clearValue;

					if (ysLineExtraItem.ysLineName === name) {
						if (!isAdd) {
							this.resetAllYsLineColor();
							clearValue = name;
							this.copyByName = '';
							ysLineExtraItem.ysLineName = '';

							// clear points
							ysLineExtraItem.ysLineValue = [];
						}
					} else {
						this.resetAllYsLineColor();
						if (isAdd && this.isYsLineName(name)) {
							const checkUsed = this.curActiveObj.extra.some(extraItem => extraItem.ysLineName === name || extraItem.crkLineNames.includes(name));
							if (checkUsed) {
								this.$message.warn(`预设线【${name}】已被使用，请重新选择!`);
								return;
							}
							ysLineExtraItem.ysLineName = name;
							clearValue = ysLineExtraItem.ysLineName;

							// update points
							if (moldView.curvesMap[name]) {
								ysLineExtraItem.ysLineValue = [...moldView.curvesMap[name].clickPoints];
							} else {
								ysLineExtraItem.ysLineValue = [];
							}

							// 更新选中值的颜色
							colorUpdate([name], true, this.curActiveObj.color);
						}
					}
				}
				const crkLineExtraItem = this.curActiveObj.extra?.find(extraItem => extraItem.crkLineActivate);
				if (crkLineExtraItem) {
					canUpdate = false;
					if (crkLineExtraItem.crkLineNames.includes(name)) {
						if (!isAdd) {
							crkLineExtraItem.crkLineNames = crkLineExtraItem.crkLineNames.filter(value => value !== name);
							// 去除选定值的颜色
							colorUpdate([name], false);
						}
					} else {
						if (isAdd) {
							if (name.startsWith('curve_')) {
								this.$message.warn(`预设线不能被使用在出入口边线上，请重新选择!`);
								colorUpdate([name], false);
								return;
							}
							const checkUsed = this.curActiveObj.extra.some(extraItem => extraItem.ysLineName === name || extraItem.crkLineNames.includes(name));
							if (checkUsed) {
								this.$message.warn(`出入口边线【${name}】已被使用，请重新选择!`);
								colorUpdate([name], false);
								return;
							}
							crkLineExtraItem.crkLineNames.push(name);
							// 更新选中值的颜色
							colorUpdate([name], true, this.curActiveObj.color);
						}
					}
				}
			}

			let crkItemExisted = false;
			if (this.curActiveObj.key === 'crk_sur') {
				// 检测内存中数据是否存在
				crkItemExisted = this.curActiveObj.value.includes(name);
			}

			if (canUpdate) {
				updateValueAndColor();
			}

			if (this.curActiveObj.key === 'crk_sur') { // 选择出入口面时，同步离散线的数据
				if (crkItemExisted) {
					const group = moldView.getScene()?.getObjectByName(DISPERSED_LINE_GROUP);
					if (group) {
						logger.log(`删除【${name}】关联的离散线`);
						group.children = group.children.filter(child => child.linkName !== name);
						moldView.requestRender();
					}
				} else {
					await this.initLineByFaceNames(this.curActiveObj.value);
				}
			}
		},
		isYsLineName (name) {
			return name?.match(/^curve_|^brokenLine_|^singleSpiralLine_|^doubleHelixLine_|^zyx_/);
		},

		/**
		 * 位置数组 转 文本（Rhino grasshopper call）
		 * @param positionArray
		 * @return {string}
		 */
		positionArray2Txt (positionArray) {
			const points = [];
			for (let i = 0; i < positionArray.length; i += 3) {
				const x = positionArray[i];
				const y = positionArray[i + 1];
				const z = positionArray[i + 2];
				points.push({ x, y, z });
			}
			return JSON.stringify(points);
		},

		/**
		 * 按面名初始化线
		 */
		async initLineByFaceNames (faceNames, showMsg = true) {
			const params = {
				faceName: faceNames.join(','),
				taskId: this.taskId,
			};
			const res = await dispersedLinesByFaceName(params);
			logger.log('按面名初始化线 res: ', res);
			if (res?.success && showMsg) {
				this.$message.success('初始化线成功');
			}
		},

		/**
		 * 更新自定义绘制曲线参数
		 */
		updateDiyShapeList (item) {
			logger.log('更新自定义绘制曲线参数: ', item);

			let obj = this.diyShapeList.find(i => i.key === item.key);
			if (obj) {
				obj = item;
			} else {
				this.diyShapeList.push(item);
			}
			logger.log('更新自定义绘制曲线参数', this.diyShapeList);
		},

		/**
		 * 删除自定义绘制曲线参数
		 */
		deleteDiyShapeList (key) {
			logger.log('删除自定义绘制曲线参数: ', key);
			this.diyShapeList = this.diyShapeList.filter(i => i.key !== key) ?? [];
		},

		/**
		 * 取消控制操作
		 */
		closeActionControl () {
			this.curAction = '';
		},

		/**
		 * 接触锁定
		 */
		onUnlockHandler () {
			logger.log('解除锁定');
			this.disableSettings = false;
		},

		/**
		 * 预设线更新曲线
		 * @params { key: 参数名, nickname: 线名, value: 点的数组 }
		 */
		onUpdateCurve ({ key, nickname, value }) {

			logger.log('预设线更新曲线:', key, nickname, JSON.stringify(value));

			if (!key) return;

			const moldView = this.$refs.moldView;
			const { ysLines } = moldView.getAllYsLineAndSpheres();
			// 过滤后的预设线
			const filteredYsLines = ysLines.filter(ysLine => ysLine.nickname !== nickname);
			const names = filteredYsLines.map(ysLine => ysLine.nickname);
			this.updateYsLineAttrByNameList(names, false);
			const setting = this.getSetting(key);
			if (setting) {
				setting.extra = setting.extra ?? [];
				if (!value || !value.length) {
					setting.extra = setting.extra.map(item => {
						if (item.ysLineName === nickname) {
							nickname = '';
							item.ysLineName = '';
							item.ysLineValue = [];
						}
						return item;
					}).filter(item => item.ysLineName !== '' || item.crkLineNames.length);
				}
				setting.extra.forEach(extraItem => {
					const curveCacheData = moldView.curvesMap[extraItem.ysLineName];
					if (curveCacheData) {
						if (extraItem.ysLineName.startsWith('curve_') || extraItem.ysLineName.startsWith('brokenLine_')) {
							const points = curveCacheData.clickPoints.map(point => moldView.getPoint(point.clone()));
							extraItem.ysLineValue = [...points];
						} else {
							extraItem.ysLineValue = curveCacheData.clickPoints;
						}
					}
				});

				const activeId = Object.keys(setting.valueActiveMap).find(key => setting.valueActiveMap[key]);
				if (activeId) {
					const activatedExtraItem = setting.extra.find(extraItem => extraItem.id === activeId);
					const usedExtraItem = setting.extra.find(extraItem => extraItem.ysLineName === nickname);
					if (activatedExtraItem && (!usedExtraItem || usedExtraItem.id === activatedExtraItem.id)) {
						activatedExtraItem.ysLineName = nickname;
						activatedExtraItem.ysLineValue = [...(value || [])];
					}
				}
			}
		},

		/**
		 * 清除所有绘制线和点参数的数据
		 */
		onClearCurve () {
			const setting = this.getSetting('ys_line');
			const clearValues = [];
			setting?.extra?.forEach(extraItem => {
				clearValues.push(extraItem.ysLineName, ...extraItem.crkLineNames);
				extraItem.ysLineName = '';
				extraItem.ysLineValue = [];
				extraItem.crkLineNames = [];
			});
			setting.valueActiveMap = {};
			this.updateYsLineAttrByNameList(clearValues, false);
			this.$refs.moldView.removeSceneYsLineByNameList(clearValues);
		},

		/**
		 * 预设线更新圆
		 * @params { key: 参数名, nickname: 线名 }
		 */
		onUpdateCircle ({ key, nickname }) {

			const setting = this.getSetting(key);
			if (setting) {
				setting.extra.forEach(extraItem => {
					extraItem.crkLineNames = extraItem.crkLineNames.filter(i => i !== nickname);
				});
			}
		},

		/**
		 * 清除所有自制出入口线的数据
		 */
		onClearCircle () {
			const setting = this.getSetting('ys_line');
			const clearValues = [];
			setting?.extra?.forEach(extraItem => {
				clearValues.push(extraItem.ysLineName, ...extraItem.crkLineNames);
				extraItem.ysLineName = '';
				extraItem.ysLineValue = [];
				extraItem.crkLineNames = [];
			});
			setting.valueActiveMap = {};
			this.updateYsLineAttrByNameList(clearValues, false);
			this.$refs.moldView.removeSceneYsLineByNameList(clearValues);
		},

		/**
		 * 清除绘制模式
		 */
		onClearDraw () {
			this.curDraw = '';
			this.$refs.moldView.onDraw('');
		},

		/**
		 * 更新变径点
		 */
		updateBjPoint ({ nickname, groupName, value }) {
			logger.log('更新变径点：', nickname, groupName, value);
			this.bjPointNameList = this.$refs.moldView.getSceneAllBjPoint().map(c => c.nickname);

			this.pointMap[nickname] = value;
		},

		/**
		 * 所有显隐处理
		 */
		allVisibleHandler ({ key, visible }, isInit = false) {
			logger.log('所有显隐处理 key, visible: ', key, visible);

			if (this.checkAction()) return;

			const children = this.getSceneAllFaceAndAllWireframe();
			const moldView = this.$refs.moldView;
			if (!moldView) return;
			moldView.originVisible = visible;
			const ysLines = this.getSceneAllYsLine();
			const dispersedLines = this.getSceneAllDispersedLine();
			const spheres = [];
			ysLines.forEach(line => {
				spheres.push(...moldView.getSceneObjsByCondition(line.nickname, false));
			});

			[...children, ...ysLines, ...dispersedLines, ...spheres].forEach(child => {
				moldView.updateObjAttr(child, { visible });
			});
			moldView.requestRender();

			// 同步参数菜单所有显隐功能
			this.updateSettings(null, 'visible', visible);

			if (!isInit) this.updatePartListData({ visible });
		},

		/**
		 * 所有颜色处理
		 */
		allColorHandler ({ key, color }, isInit = false) {
			logger.log('所有颜色处理 key, color: ', key, color);

			if (this.checkAction()) return;

			const children = this.getSceneAllFaceAndAllWireframe();
			const moldView = this.$refs.moldView;
			if (!moldView) return;
			moldView.originColor = color;
			children.forEach(child => {
				if (!moldView.isLine(child)) {
					moldView.updateObjAttr(child, { color });
				}
			});
			moldView.requestRender();

			if (!isInit) this.updatePartListData({ color });
		},

		/**
		 * 所有透明度处理
		 */
		allOpacityHandler ({ key, opacity }, isInit = false) {
			logger.log('所有透明度处理 key, opacity: ', key, opacity);

			if (this.checkAction()) return;

			const children = this.getSceneAllFaceAndAllWireframe();
			const moldView = this.$refs.moldView;
			if (!moldView) return;
			moldView.originOpacity = opacity;
			children.forEach(child => {
				moldView.updateObjAttr(child, { opacity });
			});

			if (!isInit) this.updatePartListData({ opacity });
		},

		/**
		 * 显示/隐藏处理
		 */
		visibleHandler ({ key, visible }) {
			logger.log('显示/隐藏处理 key, visible: ', key, visible);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);

			// 激活当前功能菜单
			setting.activate = true;
			this.onActivateHandler(setting, true);

			// 重置其他菜单激活状态
			this.resetOtherSettingActive(setting);

			if (setting && setting.value?.length) {
				this.updateAttrByNameList([...setting.value], true, setting.color, visible);
			}

			if (key === 'ys_line') {
				const setting = this.getSetting(key);
				if (setting) {
					const ysLineNames = [];
					const crkLineNames = [];
					setting.extra.forEach(extraItem => {
						ysLineNames.push(extraItem.ysLineName);
						crkLineNames.push(...extraItem.crkLineNames);
						this.$refs.moldView?.getScene()?.children?.filter(c => c.groupName === extraItem.ysLineName).forEach(sphere => {
							sphere.visible = visible;
						});
					});
					this.updateYsLineAttrByNameList([...ysLineNames, ...crkLineNames], true, setting.color, visible);
				}
			}
		},

		/**
		 * 颜色处理
		 */
		colorHandler ({ key, color }) {
			logger.log('颜色处理 key, color: ', key, color);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);
			if (setting.value?.length && setting.activate) {
				this.$refs.moldView?.updateAttrByNameList([...setting.value], {
					selected: true,
					color,
				});
			}
		},

		/**
		 * 透明度处理
		 */
		opacityHandler ({ key, opacity }) {
			logger.log('透明度处理 key, opacity: ', key, opacity);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);
			if (setting.value?.length && setting.activate) {
				this.$refs.moldView?.updateAttrByNameList([...setting.value], {
					selected: true,
					color: setting.color,
					opacity,
				});
			}
		},

		/**
		 * 激活处理
		 */
		onActivateHandler ({ key, activate, mode }, clearAllTagActive = true) {
			logger.log('激活处理 key, activate, mode: ', key, activate, mode);

			if (this.checkAction()) return;
			this.selectModeHandle(mode);

			// 重置其他菜单激活状态
			this.resetOtherSettingActive({ key });

			// 重置所有标签激活状态
			if (clearAllTagActive) {
				this.selectModeHandle();
				this.resetSettingAllTagActive();
			}

			const data = this.getSetting(key);
			data.activate = activate;
			if (!activate) {
				data.valueActiveMap = {};
			}
			this.initCurActiveObj();

			const moldView = this.$refs.moldView;
			if (!moldView) return;

			// 重置视图颜色
			moldView.resetColorHandle();

			if (activate) {
				// 预设线及出入口边线 激活处理
				if (this.curActiveObj?.key === 'ys_line' && this.curActiveObj.extra?.length) {
					const activated = this.curActiveObj?.activate;
					const color = activated ? this.curActiveObj.color : ColorEnum.black;
					this.curActiveObj.extra.forEach(extraItem => {
						if (extraItem.ysLineName) {
							this.updateYsLineAttrByNameList([extraItem.ysLineName], true, color);
						}
						if (extraItem.crkLineNames?.length) {
							this.updateYsLineAttrByNameList(extraItem.crkLineNames, true, color);
						}
					});
				} else if (this.curActiveObj?.key === 'bj_point' && this.curActiveObj.extra?.length) {
					this.curActiveObj.extra.forEach(extraItem => {
						if (extraItem.pointNames?.length) {
							this.updateAttrByNameList(extraItem.pointNames, true, this.curActiveObj.color);
						}
					});
				} else {
					if (this.curActiveObj?.activate && this.curActiveObj.value?.length) {
						this.updateAttrByNameList([...this.curActiveObj.value], true, this.curActiveObj.color);
					} else if (!this.curActiveObj || !this.curActiveObj.activate) {
						this.onDraw(null);
					}
				}
			} else {
				this.onDraw(null);
			}
		},

		/**
		 * 初始化当前激活对象
		 */
		initCurActiveObj () {
			this.curActiveObj = this.settings.find(setting => setting.activate);
			if (this.curActiveObj) {
				this.curActiveObj.value = Array.from(new Set(this.curActiveObj.value));
			}
			const moldView = this.$refs.moldView;
			if (moldView) {
				moldView.curActiveObj = this.curActiveObj;
			}
		},

		/**
		 * 关闭标签处理
		 */
		closeTagHandler ({ data, name }) {
			logger.log('关闭标签处理 data, name: ', data, name);

			if (this.checkAction()) return;

			const setting = this.settings.find(setting => setting.key === data.key);
			if (setting) {
				// 若是出入口面，需要同步清理离散的线
				if (setting.key === 'crk_sur') {
					const moldView = this.$refs.moldView;
					const group = moldView.getScene()?.getObjectByName(DISPERSED_LINE_GROUP);
					if (group) {
						logger.log(`删除【${name}】关联的离散线`);
						group.children = group.children.filter(child => child.linkName !== name);
						moldView.requestRender();
					}
				}

				if (setting.key !== 'ys_line') { // 非预设线及出入口边线
					this.updateAttrByNameList([name], false);
				}
			}
		},

		/**
		 * 关闭所有标签
		 */
		closeAllTagHandler ({ data, clearValues }) {
			logger.log('关闭所有标签 data, clearValues: ', data, clearValues);

			if (this.checkAction()) return;

			const moldView = this.$refs.moldView;
			const setting = this.settings.find(setting => setting.key === data.key);
			if (setting) {
				// 若是出入口面，需要同步清理离散的线
				if (setting.key === 'crk_sur') {
					clearValues.forEach(crkFaceName => {
						const group = moldView.getScene()?.getObjectByName(DISPERSED_LINE_GROUP);
						if (group) {
							logger.log(`删除【${crkFaceName}】关联的离散线`);
							group.children = group.children.filter(child => child.linkName !== crkFaceName);
							moldView.requestRender();
						}
					});
				}

				if (setting.key !== 'ys_line') { // 非预设线及出入口边线
					moldView.updateAttrByNameList(clearValues, { selected: false });
				}
			}
		},

		/**
		 * 添加预设线
		 */
		addYsLineHandler ({ name, data }) {
			logger.log('添加预设线 name, data: ', name, data);

			if (this.checkAction()) return;

			// 重置其他菜单激活状态
			this.resetOtherSettingActive(data);

			// 重置其他tag激活状态
			this.resetOtherTagActive(data);
		},

		/**
		 * 添加出入口边线
		 */
		addCRKLineHandler ({ name, data }) {
			logger.log('添加出入口边线 name, data: ', name, data);

			if (this.checkAction()) return;

			// 重置其他菜单激活状态
			this.resetOtherSettingActive(data);

			// 重置其他tag激活状态
			this.resetOtherTagActive(data);
		},

		/**
		 * 标签激活处理
		 */
		tagActivateHandler ({ name, data }) {
			logger.log('标签激活处理 name, data: ', name, data);

			if (this.checkAction()) return;

			if (!data.activate) {
				// 重置其他菜单激活状态
				this.resetOtherSettingActive(data);

				// 重置其他tag激活状态
				this.resetOtherTagActive(data);

				data.activate = true;
				this.onActivateHandler(data, false);
			}

			const attrName = data.key + '_' + name;
			const activated = data.valueActiveMap[attrName];
			const color = activated ? ColorEnum.cyanBlue : data.color;
			if (data.mode === SelectModeEnum.line) {
				this.updateYsLineAttrByNameList([name], true, color);
			} else {
				this.updateAttrByNameList([name], true, color);
			}
		},

		/**
		 * 标签组激活处理
		 */
		tagGroupActivateHandler ({ name, data, extraItem }) {
			logger.log('标签组激活处理 name, data, extraItem: ', name, data, extraItem);

			if (this.checkAction()) return;

			// 若菜单未激活，需要先激活
			if (!data.activate) {
				data.activate = true;
				this.onActivateHandler(data, false);
			} else {
				// 重置视图颜色
				this.$refs.moldView?.resetColorHandle();
			}

			if (extraItem.ysLineActivate || extraItem.crkLineActivate) {
				this.$refs.moldView.chooseLine = true;
			} else {
				this.$refs.moldView.chooseLine = false;
			}

			if (!extraItem.ysLineActivate) {
				delete data.valueActiveMap[extraItem.id];
			}

			if (extraItem.ysLineName && extraItem.ysLineActivate) {

				this.resetAllYsLineColor({ selected: false });
				const activated = extraItem.ysLineActivate;
				const color = activated ? ColorEnum.cyanBlue : data.color;
				this.updateYsLineAttrByNameList([extraItem.ysLineName], true, color);
			}
			if (extraItem.crkLineActivate) {
				const activated = extraItem.crkLineActivate;
				const color = activated ? ColorEnum.cyanBlue : data.color;
				if (extraItem.crkLineNames?.length) {
					this.updateYsLineAttrByNameList(extraItem.crkLineNames, true, color);
				}
				this.updateYsLineAttrByNameList([extraItem.ysLineName], true, color);
			}
		},

		/**
		 * 点标签激活处理
		 */
		pointTagActivateHandler ({ data, extraItem }) {
			logger.log('点标签激活处理 data, extraItem: ', data, extraItem);

			if (this.checkAction()) return;

			// 若菜单未激活，需要先激活
			if (!data.activate) this.onActivateHandler(data, false);

			const activateId = Object.keys(data.valueActiveMap).find(key => data.valueActiveMap[key]);
			const color = activateId ? ColorEnum.cyanBlue : data.color;
			this.updateYsLineAttrByNameList([extraItem.ysLineName], true, color);

			// 重置其他tag激活状态
			this.resetOtherTagActive(data, false);
		},

		/**
		 * 重置所有标签激活状态
		 */
		resetSettingAllTagActive () {
			const settings = [...this.settings];
			this.settings = [];
			settings.forEach(item => {
				if (item.valueActiveMap) {
					item.valueActiveMap = {};
				}
				if (item.key === 'ys_line' && item.extra?.length) {
					item.extra.forEach(extraItem => {
						extraItem.ysLineActivate = false;
						extraItem.crkLineActivate = false;
					});
				}
			});
			this.settings = settings;
		},

		/**
		 * 重置其他菜单激活状态
		 */
		resetOtherSettingActive (data) {
			this.$refs.moldView.curDraw = false;
			this.settings.forEach(item => {
				if (item.key === data.key) {
					item.activate = true;
				} else {
					item.activate = false;
				}
			});
		},

		/**
		 * 重置其他tag激活状态
		 */
		resetOtherTagActive (data, clearExtra = true) {
			const settings = [...this.settings];
			this.settings = [];
			this.$refs.moldView.chooseLine = false;
			this.$refs.moldView?.selectModeHandle('');
			settings.forEach(item => {
				if (item.key !== data.key) {
					if (item.valueActiveMap) {
						item.valueActiveMap = {};
					}
					if (clearExtra && item.key === 'ys_line' && item.extra?.length) {
						item.extra.forEach(extraItem => {
							extraItem.ysLineActivate = false;
							extraItem.crkLineActivate = false;
						});
					}
				}
			});
			this.settings = settings;
		},

		/**
		 * 删除一组
		 */
		removeOneGroupHandler ({ data, extraItem, clearValues }) {
			logger.log('删除一组 data, extraItem: ', data, extraItem, clearValues);

			if (this.checkAction()) return;

			// 删除预设线
			this.updateYsLineAttrByNameList(clearValues, false);
			// this.$refs.moldView?.removeSceneYsLineByNameList(clearValues);

			const setting = this.getSetting(data.key);
			if (setting) {
				setting.extra = setting.extra.filter(item => item.id !== extraItem.id);
			}
		},

		/**
		 * 清除组单个对象值
		 */
		clearGroupObjValueHandler ({ key, data, extraItem, clearValues }) {
			logger.log('清除组单个对象值 key, data, extraItem: ', key, data, extraItem, clearValues);

			if (this.checkAction()) return;

			this.updateYsLineAttrByNameList(clearValues, false);
			// this.$refs.moldView?.removeSceneYsLineByNameList(clearValues);

			const setting = this.getSetting(data.key);
			if (setting) {
				setting.value = difference(setting.value, clearValues);
			}
		},

		/**
		 * 按名称列表更新预设线属性(多个)
		 */
		updateYsLineAttrByNameList (lineNameList, selected, color = undefined, visible = undefined) {
			this.$refs.moldView?.updateYsLineAttrByNameList(lineNameList, {
				selected,
				color,
				visible,
			});
		},

		/**
		 * 删除一组变径点
		 */
		removeOneGroupPointHandler ({ data, extraItem, clearValues }) {
			logger.log('删除一组 data, extraItem: ', data, extraItem, clearValues);

			if (this.checkAction()) return;

			// 删除变径点
			this.updateBjPointAttrByNameList(clearValues, false);
			this.$refs.moldView?.removeSceneBjPointByNameList(clearValues);

			const setting = this.getSetting(data.key);
			if (setting) {
				setting.extra = setting.extra.filter(item => item.id !== extraItem.id);
			}
			this.bjPointNameList = [...this.bjPointNameList].filter(i => [...clearValues].every(j => j !== i));
			// logger.log(1935, this.bjPointNameList);
		},

		/**
		 * 按名称列表更新变径点属性(多个)
		 */
		updateBjPointAttrByNameList (pointNameList, selected, color = undefined, visible = undefined) {
			this.$refs.moldView?.updateBjPointAttrByNameList(pointNameList, {
				selected,
				color,
				visible,
			});
		},

		/**
		 * 按名称列表更新对象属性(多个)
		 */
		updateAttrByNameList (nameList, selected, color = undefined, visible = undefined) {
			this.$refs.moldView?.updateAttrByNameList(nameList, {
				selected,
				color,
				visible,
			});
		},

		/**
		 * 滑块值更改处理
		 */
		sliderValueChangeHandler (data) {
			logger.log('滑块值更改处理 data: ', data);
			const setting = this.settings.find(setting => setting.key === data.key);
			if (setting) {
				setting.value = data.value;
			}
		},

		/**
		 * 开关值变化处理
		 */
		switchValueChangeHandler (data) {
			logger.log('开关值变化处理 data: ', data);
			const setting = this.settings.find(setting => setting.key === data.key);
			if (setting) {
				setting.value = data.value;
			}

			// 1、管线开关开启：需同步开启 设计空间开关
			if (data.key === 'make_pipe_line') {
				this.updateSettings('calc_design_space', 'checked', true);
			}

			// 2、管(路)开关开启：需同步开启证 管线开关 及 设计空间开关
			if (data.key === 'make_pipe') {
				this.updateSettings('calc_design_space', 'checked', true);
				this.updateSettings('make_pipe_line', 'checked', true);
			}
		},

		/**
		 * 复选框值更新
		 */
		onCheckboxChangeHandler ({ checkedValues, data, extraItem }) {
			logger.log('复选框值更新：', checkedValues, data, extraItem);

			// 重置所有变径点选中样式
			const bjPointNames = this.$refs.moldView?.getSceneAllBjPoint().map(child => child.nickname);
			this.updateBjPointAttrByNameList(bjPointNames, true, ColorEnum.yellow);

			// 更新指定变径点选中样式
			this.updateBjPointAttrByNameList(checkedValues, true, ColorEnum.cyanBlue);

			// 更新值
			const setting = this.getSetting('bj_point');
			setting.extra.forEach(extraSource => {
				if (extraSource.groupName === extraItem.groupName) {
					extraSource.points = [];
					extraItem.pointNames.forEach((name) => {
						extraSource.points.push(this.pointMap[name]);
					});
				}
			});
		},

		/**
		 * 结果显/隐处理
		 */
		resultVisibleHandler ({ key, visible }) {
			logger.log('结果显/隐处理 key, visible: ', key, visible);
			// if (key === 'result_include_water_model') { // 如果是含水路模型，则：显示模具+生成管路，其他生成结果所有透明度为50%，并且隐藏
			// 	// 隐藏模具
			// 	this.partList[0].opacity = 50;
			// 	this.partList[0].visible = false;
			// 	this.updateAllVisible(false);
			//
			// 	// 隐藏所有预设线
			// 	this.$refs.moldView.updateYsLineAllVisible(false);
			//
			// 	// 隐藏所有变径点
			// 	this.$refs.moldView.updateBjPointAllVisible(false);
			//
			// 	// 隐藏所有离散线
			// 	this.$refs.moldView.updateDispersedLineAllVisible(false);
			//
			// 	// 更新含水路模型视图
			// 	const resultGroup = this.$refs.moldView.getGroupByName(RESULT_WATER_COOLING_GROUP);
			// 	if (resultGroup) {
			// 		const children = resultGroup.children.filter(child => child.groupName.includes(waterCoolingResultKeyMap.waterwayModel));
			// 		if (children?.length) {
			// 			children.forEach(child => {
			// 				child.visible = visible;
			// 			});
			// 		}
			// 	}
			// } else {
			// 	this.updateResultAttrByName(key, { visible });
			// 	this.updateResultsData({ key, visible });
			// }

			if (key === 'result_include_water_model') { // 如果是含水路模型，则：显示含水路模型+含水路模型线框
				// 更新含水路模型视图
				const resultGroup = this.$refs.moldView.getGroupByName(RESULT_WATER_COOLING_GROUP);
				if (resultGroup) {
					const children = resultGroup.children.filter(child => child.groupName.includes(waterCoolingResultKeyMap.waterwayModel));
					if (children?.length) {
						children.forEach(child => {
							child.visible = visible;
						});
					}
				}
			} else {
				this.updateResultAttrByName(key, { visible });
				this.updateResultsData({ key, visible });
			}
		},

		/**
		 * 结果颜色处理
		 */
		resultColorHandler ({ key, color }) {
			logger.log('结果颜色处理 key, color: ', key, color);
			this.updateResultAttrByName(key, { color });
			this.updateResultsData({ key, color });
		},

		/**
		 * 结果透明度处理
		 */
		resultOpacityHandler ({ key, opacity }) {
			logger.log('结果透明度处理 key, opacity: ', key, opacity);
			this.updateResultAttrByName(key, { opacity });
			this.updateResultsData({ key, opacity });
		},

		/**
		 * 按名称列表更新结果属性
		 * @param name
		 * @param color
		 * @param opacity
		 * @param visible
		 */
		updateResultAttrByName (name, { color, opacity, visible }) {
			const children = this.getResultObjs();
			for (const child of children) {
				if (child.groupName === name) {
					this.$refs.moldView?.updateObjAttr(child, { color, opacity, visible });
				}
			}
		},

		/**
		 * 获取场景所有面和线框对象
		 * @returns {Object3D[]|*[]}
		 */
		getSceneAllFaceAndAllWireframe () {
			return this.$refs.moldView?.getSceneAllFaceAndAllWireframe();
		},

		/**
		 * 获取结果对象
		 * @param {String} key nickname前缀，默认undefined，查询所有
		 * @returns {Object3D[]|*[]}
		 */
		getResultObjs (key = undefined) {
			return this.$refs.moldView?.getSceneChildrenByCondition(RESULT_WATER_COOLING_GROUP, child => {
				if (key) return child.nickname?.includes(key);
				return child;
			});
		},

		/**
		 * 获取场景所有预设线对象
		 * @returns {Object3D[]|*[]}
		 */
		getSceneAllYsLine () {
			return this.$refs.moldView?.getSceneAllYsLine();
		},

		/**
		 * 获取场景所有离散线对象
		 * @returns {Object3D[]|*[]}
		 */
		getSceneAllDispersedLine () {
			return this.$refs.moldView?.getSceneAllDispersedLine();
		},

		/**
		 * 名称提示
		 * @param object
		 */
		handleTip (object) {
			this.tipName = '';
			if (object) {
				if (object.nickname) {
					this.tipName = `名称：${object.nickname}`;
				} else if (object.groupName) {
					this.tipName = `组名称：${object.groupName}`;
				}
			}
		},

		updateOnline () {
			this.online = navigator.onLine ? '正常' : '断开';
		},

		// measureBW () {
		// 	this.downLink = (navigator.connection.downlink / 8).toFixed(2) + 'M/s';
		// },

		goToView (path) {
			this.saveTaskCacheToServer();
			this.$refs.moldView.reset();
			this.$nextTick(() => {
				this.destroy();
				this.$router.push(path);
			});
		},

		initEvents () {
			window.addEventListener('online', this.updateOnline);
			window.addEventListener('offline', this.updateOnline);

			// 浏览器刷新事件
			const beforeunloadHandler = (event) => {
				this.saveTaskCacheToServer();
				// // 按标准取消事件
				// event.preventDefault();
				// // Chrome 需要设置 returnValue
				// event.returnValue = 'stop';
			};
			addEventHandler(window, 'beforeunload', (e) => beforeunloadHandler(e));

			// 浏览器返回事件
			if (window.history && window.history.pushState) {
				window.onpopstate = () => {
					// 后退按钮触发事件
					this.saveTaskCacheToServer();
				};
			}
		},

		/**
		 * 销毁视图
		 */
		destroy () {
			this.onClose(); // close ws
			this.visibleView = false;
		},
	},
	beforeDestroy () {
		this.destroy();
	},
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/base.less';
@import '~@/assets/styles/layout.less';
@import '~@/assets/styles/mixins/grid.less';
@import '~@/assets/styles/mixins/variables.less';

#layout-basic {
	position: relative;
	background: @primary-background;
	height: 100vh;

	.logo {
		font-size: 16px;
		font-weight: 500;
		color: #111111;
		margin-left: 20px;
	}

	.header-title {
		display: flex;
		width: 100%;

		span {
			font-size: 16px;
			font-weight: bold;
			text-align: center;
			width: 260px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			margin: 0 auto;
		}
	}

	.ant-layout-sider {
		margin-top: 52px;
	}

	.ant-layout-content {
		margin-top: 52px;
	}

	.project-sider {
		overflow: auto;
		height: 100%;
		max-height: calc(100vh - 50px - 2px);
		overflow-x: hidden;
		background: transparent;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			left: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.project-right {
		background: transparent;
		position: absolute;
		right: 0;
		top: 52px;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			right: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.ant-empty-normal {
		margin: 0 0;
	}

	.list {
		img {
			width: 20px;

			&.w18 {
				width: 18px;
				height: 18px;
			}
		}

		.item-meta {
			display: flex;
			align-items: center;
			cursor: pointer;

			&:hover {
				color: @primary;
			}

			.active {
				color: @primary;
			}
		}
	}

	.progress-item {
		.progress-bar {
			max-width: 160px;
			margin-left: 10px;
			min-width: 160px;
		}

		.ant-list-item-action {
			margin-left: 10px;
		}

		.ant-list-item-meta-title {
			display: flex;
			align-items: center;
		}

		.progress-title {
			display: flex;
			align-items: center;
			height: 22px;

			img {
				height: inherit;
				margin-right: 5px;
			}
		}

		.ant-progress {
			line-height: 0;
		}
	}

	.masking {
		position: fixed;
		top: 52px;
		left: 0;
		width: 315px;
		height: calc(100vh - 50px - 2px);
		background: rgba(0, 0, 0, 0.05);
		z-index: 999;
		cursor: not-allowed;
	}

	.tip-name {
		position: fixed;
		right: 200px;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.network-status {
		position: fixed;
		right: 0;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.submit {
		height: 52px;
		width: 296px;
		padding: 10px 4px;
		background-color: white;

		.ant-btn {
			width: 280px;
			height: 32px;
		}
	}
}
</style>
