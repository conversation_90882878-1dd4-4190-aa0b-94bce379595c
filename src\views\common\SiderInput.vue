<template>
	<a-row>
		<a-col :span="colLeftSpan">
			<a-slider :value="form[valueKey] | ToNumber" :min="inputMin" :max="siderMax" :step="step" @change="onSiderChange" />
		</a-col>
		<a-col :span="colCenterSpan" :style="colCenterStyle">
			<i-input
				:min="inputMin"
				:max="inputMax"
				:value="form[valueKey]"
				:disabled="disabled"
				:isNumber="isNumber"
				:maxLength="maxLength"
				:placeholder="placeholder"
				:limitMinMax="limitMinMax"
				:limitLength="limitLength"
				:isExponential="isExponential"
				:fractionDigits="fractionDigits"
				:scientificReservedDigits="scientificReservedDigits"
				@change="onInputChange"
				@blur="onInputBlur"
			/>
		</a-col>
		<a-col :span="colRightSpan" :style="colRightStyle">
			<slot name="extra"></slot>
		</a-col>
	</a-row>
</template>

<script>
/* ===================================
 * 含滑块的input输入框
 * Created by cjking on 2021/11/17.
 * Copyright 2021, Inc.
 * =================================== */
import IInput from '@/components/common/IInput';
import { isEmpty } from '@/utils/utils';

const separator = '.';
const addSeparator = '+';
const minusSeparator = '-';

export default {
	name: 'SiderInput',
	components: {
		IInput,
	},
	props: {
		form: {
			type: Object,
			required: true,
		},
		valueKey: {
			type: String,
			required: true,
		},
		min: {
			type: Number,
			required: false,
			default: 0,
		},
		max: {
			type: Number,
			required: false,
			default: 999999999,
		},
		limitMinMax: {
			type: Boolean,
			required: false,
			default: true,
		},
		step: {
			type: Number,
			required: false,
			default: 1,
		},
		maxLength: {
			type: Number,
			required: false,
			default: 20,
		},
		colLeftSpan: {
			type: Number,
			required: false,
			default: 8,
		},
		colCenterSpan: {
			type: Number,
			required: false,
			default: 8,
		},
		colRightSpan: {
			type: Number,
			required: false,
			default: 4,
		},
		colCenterStyle: {
			type: Object,
			required: false,
			default: () => {
				return { marginLeft: '10px' };
			},
		},
		colRightStyle: {
			type: Object,
			required: false,
			default: () => {
				return { marginLeft: '5px' };
			},
		},
		limitLength: { // 值的长度大等于6时，开始转换为科学计数法
			type: Number,
			required: false,
			default: 6,
		},
		placeholder: {
			type: String,
			required: false,
			default: '',
		},
		disabled: {
			type: Boolean,
			required: false,
			default: undefined,
		},
		isNumber: { // 是否转换为Number数字
			type: Boolean,
			required: false,
			default: false,
		},
		isExponential: { // 是否转换为科学计数法
			type: Boolean,
			required: false,
			default: false,
		},
		fractionDigits: { // 小数位数
			type: Number,
			required: false,
			default: undefined,
		},
		scientificReservedDigits: { // 科学计数法保留位数
			type: Number,
			required: false,
			default: 3,
		},
	},
	data () {
		return {
			siderMax: undefined,
			inputMin: undefined,
			inputMax: undefined,
		};
	},
	watch: {
		min: {
			immediate: true,
			handler () {
				this.inputMin = this.min;
			},
		},
		max: {
			immediate: true,
			handler () {
				this.inputMax = this.max;
				this.siderMax = this.max;
			},
		},
	},
	methods: {
		onSiderChange (value) {
			this.$emit('siderChange', this.valueHandle(value));
		},

		onInputChange (event) {
			const value = this.valueHandle(event.target.value);
			event.target.value = value;
			this.$emit('change', event);
		},

		onInputBlur (event) {
			const value = this.valueHandle(event.target.value);
			event.target.value = value;
			this.$emit('blur', event);
		},

		valueHandle (value) {
			value = String(value).replace(/[^\d\\.?\-+]/g, '');
			if (!isEmpty(value) && !value.endsWith(separator) && !value.endsWith(addSeparator) && !value.endsWith(minusSeparator)) {
				value = Number(value || 0);
				if (this.limitMinMax && !isEmpty(value) && !isEmpty(this.min) && !isEmpty(this.max)) {
					value = value < this.min ? this.min : value;
					value = value > this.max ? this.max : value;
				}
			}
			return value;
		},
	},
};
</script>
