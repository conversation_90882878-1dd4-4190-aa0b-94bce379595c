<template>
	<div class="user-layout-register">
		<a-form-model ref="ruleForm" :model="form" :rules="rules" @keyup.enter.native="handleSubmit">
			<div class="user-layout-register-title"><h1>注册</h1></div>

			<a-form-model-item prop="username">
				<a-input type="text" size="large" v-model.trim="form.username" placeholder="用户名">
					<a-icon slot="prefix" type="user" :style="{ color: inputColor }" />
				</a-input>
			</a-form-model-item>

			<a-form-model-item prop="phone">
				<a-input type="text" size="large" v-model.trim="form.phone" placeholder="手机号" :max-length="11">
					<a-icon slot="prefix" type="mobile" :style="{ color: inputColor }" />
				</a-input>
			</a-form-model-item>
			<a-row :gutter="24">
				<a-col :span="14">
					<a-form-model-item prop="captcha">
						<a-input type="text" size="large" v-model.trim="form.captcha" placeholder="验证码">
							<a-icon slot="prefix" type="message" :style="{ color: inputColor }" />
						</a-input>
					</a-form-model-item>
				</a-col>
				<a-col :span="9">
					<img style="margin-top: 2px; cursor: pointer;" :src="randCodeImage" alt @click="getCaptcha">
				</a-col>
			</a-row>

			<a-form-model-item prop="password">
				<a-input type="password" size="large" v-model.trim="form.password" placeholder="密码">
					<a-icon slot="prefix" type="lock" :style="{ color: inputColor }" />
				</a-input>
			</a-form-model-item>

			<a-form-model-item prop="confirmPassword">
				<a-input type="password" size="large" v-model.trim="form.confirmPassword" placeholder="确认密码">
					<a-icon slot="prefix" type="lock" :style="{ color: inputColor }" />
				</a-input>
			</a-form-model-item>

			<a-form-model-item prop="email">
				<a-input type="text" size="large" v-model.trim="form.email" placeholder="邮箱">
					<a-icon slot="prefix" type="mail" :style="{ color: inputColor }" />
				</a-input>
			</a-form-model-item>

			<a-row :gutter="24">
				<a-col :span="24">
					<a-checkbox :checked="form.iAgree" @change="onCheckChange">
						我已阅读并同意 <a v-href @click="$message.info('功能开发中...')">fzCloud用户注册协议.</a>
					</a-checkbox>
				</a-col>
			</a-row>

			<a-row :gutter="24" style="margin-top: 10px;">
				<a-col :span="24">
					<a-button size="large" type="primary" class="forget-button" :loading="btnLoading" @click.stop.prevent="handleSubmit">注册</a-button>
				</a-col>
			</a-row>
		</a-form-model>
	</div>
</template>

<script>
/* ===================================
 * 注册页
 * Created by cjking on 2021/04/07.
 * Copyright 2021, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { getSmSCode, register } from '@/api';
import { formValidate, PasswordReg } from '@/utils/validate';
import { clearUserData, deleteNullAttr } from '@/utils/utils';
import checkCode from '@/assets/img/check-code.png';

export default {
	name: 'Register',
	data () {
		return {
			btnLoading: false,
			labelCol: {
				xs: { span: 24 },
				sm: { span: 5 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			inputColor: 'rgba(0,0,0,.25)',
			form: {
				username: '',           // 用户名
				phone: '',              // 手机号
				captcha: '',            // 验证码
				password: '',           // 密码
				confirmPassword: '',    // 确认密码
				email: '',              // 邮箱
				iAgree: false,          // 我已阅读并同意
			},
			rules: {
				username: formValidate.noEmpty('请输入用户名'),
				phone: formValidate.noEmpty('请输入手机号'),
				captcha: formValidate.noEmpty('请输入验证码'),
				password: formValidate.customCondition({
					required: true,
					message: '请输入密码，格式：长度为8~16位，数字、字母、特殊字符至少包含两种！',
					condition: () => PasswordReg.test(this.form.password),
				}),
				confirmPassword: formValidate.compare({
					required: true,
					message: '确认密码',
					lastValue: () => this.form.password,
				}),
				email: formValidate.email('请输入邮箱账号'),
				iAgree: formValidate.customCondition({
					required: true,
					message: '请阅读并勾选fzCloud用户注册协议',
					condition: () => this.iAgree.value,
				}),
			},
			inputCodeContent: '',
			randCodeImage: checkCode,
			currDateTime: null,
		};
	},
	created () {
		clearUserData();
		this.getCaptcha();
	},
	methods: {

		/**
		 * 获取验证码
		 */
		getCaptcha () {
			this.currDateTime = Date.now();
			getSmSCode(this.currDateTime).then(res => {
				if (res?.success && res.result) {
					this.randCodeImage = res.result.base64 ? `data:image/jpeg;base64,${ res.result.base64 }` : res.result;
				} else {
					this.randCodeImage = checkCode;
					this.$message.error(res?.message);
				}
			}).catch(() => {
				this.randCodeImage = checkCode;
			});
		},

		/**
		 * 我已阅读并同意
		 */
		onCheckChange (e) {
			this.form.iAgree = !!e.target?.checked;
		},

		/**
		 * 注册(提交)
		 */
		handleSubmit () {
			logger.log('注册(提交): ', this.form);
			this.$refs.ruleForm.validate(async valid => {
				if (!valid) return false;
				if (!this.form.iAgree) {
					this.$message.info('请阅读并勾选fzCloud用户注册协议');
					return false;
				}
				const params = {
					username: this.form.username,               // 用户名
					phone: this.form.phone,                     // 手机号
					password: this.form.password,               // 密码
					email: this.form.email,                     // 邮箱
					captcha: this.form.captcha,                 // 验证码
					checkKey: this.currDateTime,                 // 验证码key
				};
				try {
					this.btnLoading = true;
					logger.log('注册参数 params: ', params);
					const res = await register(deleteNullAttr(params));
					logger.log('注册成功 res: ', res);
					if (!res) {
						this.btnLoading = false;
						this.getCaptcha();
						return;
					}
					this.$router.push('/');
				} catch (e) {
					this.btnLoading = false;
					this.getCaptcha();
				}
			});
		},
	},
};
</script>

<style lang="less" scoped>
.user-layout-register {
	width: 315px;

	&-title {
		text-align: center;
		> h1 {
			color: #6362FF;
		}
	}

	&-tip {
		text-align: center;
	}

	button.forget-button {
		padding: 0 15px;
		font-size: 16px;
		height: 40px;
		width: 100%;
		margin-top: 5px;
	}

	.a-center {
		display: flex;
		justify-content: center;
		margin-top: 5px;
	}
}
</style>
