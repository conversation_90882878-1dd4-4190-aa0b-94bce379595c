/* ===================================
 * 倒计时
 * Created by cjking on 2020/10/13.
 * Copyright 2020, Inc.
 * =================================== */
import Config from '@/config/Config';

export const mixinCountdown = {
	data () {
		return {
			timer: null,
			defaultCountDown: 30, // 默认倒计时秒数,可通过子类重写覆盖
			countDown: 0,
		};
	},
	created () {
		this.countDown = this.defaultCountDown;
	},
	methods: {
		/**
		 * 开启倒计时
		 * @param {Function=} callback 执行回调
		 */
		openCountDown (callback) {
			if (!Config.openCountDown) return;
			this.timer = setInterval(() => {
				this.countDown--;
				if (this.countDown < 0) {
					callback && callback();
					this.countDown = this.defaultCountDown;
				}
			}, 1000);
		},

		/**
		 * 取消倒计时
		 */
		closeCountDown () {
			clearInterval(this.timer);
			this.timer = null;
		},
	},
	beforeDestroy () {
		this.closeCountDown();
	},
};
