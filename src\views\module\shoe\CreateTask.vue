<template>
	<a-modal
		:title="isEdit ? '编辑任务' : '新建任务'"
		:visible="visible"
		:mask-closable="false"
		@cancel="closeWindow"
	>
		<a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
			<a-form-model-item label="任务名称" prop="taskName">
				<a-input type="text" v-model="form.taskName" placeholder="请输入任务名称" :max-length="25" />
			</a-form-model-item>
		</a-form-model>
		<template slot="footer">
			<a-button type="default" @click="closeWindow">取消</a-button>
			<a-button type="primary" @click="handleSubmit" :loading="loading">确定</a-button>
		</template>

	</a-modal>
</template>

<script>
/* ===================================
 * 新建/编辑任务弹窗
 * Created by cjking on 2022/02/17.
 * Copyright 2022, Inc.
 * =================================== */
import { mapGetters } from 'vuex';
import { logger } from '@/utils/logger';
import { formValidate } from '@/utils/validate';
import { createProjectTask, editProjectTask } from '@/api';
// import NewTag from '@/views/module/shoe/NewTag';

export default {
	name: 'CreateProject',
	// components: { NewTag },
	data () {
		return {
			visible: false,
			labelCol: { span: 5 },
			wrapperCol: { span: 16 },
			form: {
				id: '',          // 任务ID
				projectId: '',   // 项目ID
				taskName: '',    // 任务名称
			},
			rules: {
				taskName: formValidate.noEmpty('请输入任务名称，25个字以内'),
			},
			isEdit: false,
			taskInfo: {},    // 任务信息
			loading: false,
		};
	},
	computed: {
		...mapGetters(['userInfo']),
	},
	methods: {
		/**
		 * 显示弹窗
		 */
		showModal (projectId) {
			this.form.projectId = projectId;
			this.form.taskName = '任务' + new Date().getTime();
			this.isEdit = false;
			this.visible = true;
			this.loading = false;
		},

		/**
		 * 编辑弹窗
		 */
		async editModal (record, projectId) {
			this.form.projectId = projectId;
			this.form.id = record.id;
			this.form.taskName = record.taskName;
			this.isEdit = true;
			this.visible = true;
			this.loading = false;
		},
		/**
		 * 关闭弹窗
		 */
		closeWindow () {
			this.$emit('close');
			this.visible = false;
			this.$refs.ruleForm.clearValidate();
		},

		/**
		 * 确定
		 */
		handleSubmit () {
			this.$refs.ruleForm.validate(async valid => {
				if (!valid) return false;
				const params = {
					...this.form.id && { id: this.form.id }, // 任务ID
					projectId: this.form.projectId,          // 项目ID
					taskName: this.form.taskName,            // 任务名称
				};

				this.loading = true;
				if (this.isEdit) {
					logger.log('编辑任务参数 params: ', params);
					const res = await editProjectTask(params);
					logger.log('编辑任务成功 res: ', res);
					if (res?.success) {
						this.$message.success('编辑任务成功');
						this.confirmAndCloseWin();
					}
				} else {
					logger.log('创建任务参数 params: ', params);
					const res = await createProjectTask(params);
					logger.log('创建任务成功 res: ', res);
					if (res?.success) {
						this.$message.success('创建任务成功');
						this.taskInfo = res?.result || {};
					}
					this.confirmAndCloseWin();
				}
				this.loading = false;
			});
		},

		/**
		 * 确认并关闭弹窗
		 */
		confirmAndCloseWin () {
			this.$emit('ok', this.isEdit);
			this.$refs.ruleForm.clearValidate();
			this.visible = false;
		},
	},
};
</script>

<style lang="less" scoped>
.tag {
	height: 32px;
	line-height: 30px;
}
</style>
