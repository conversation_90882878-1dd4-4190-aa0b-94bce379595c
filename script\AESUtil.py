import base64
import hashlib
# pip install pycryptodome
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad, unpad


class AESUtil(object):
	ENCODING = 'UTF-8'

	@staticmethod
	def get_signature(signature_dict):
		values = list(signature_dict.values())  # 使用 list() 转换为列表
		values.sort()  # 按ascii排序

		# 生成签名
		sha1 = hashlib.sha1()
		sha1.update(''.join(values).encode(AESUtil.ENCODING))
		return sha1.hexdigest()[16:32]

	@staticmethod
	def encrypt(key, text):
		"""
		AES的ECB模式加密方法
		:param key: 密钥
		:param text:被加密字符串（明文）
		:return:密文
		"""
		key = key.encode(AESUtil.ENCODING)
		# 字符串补位
		text = pad(text.encode(AESUtil.ENCODING), AES.block_size, style='pkcs7')
		cipher = AES.new(key, AES.MODE_ECB)
		# 加密后得到的是bytes类型的数据，使用Base64进行编码,返回byte字符串
		result = cipher.encrypt(text)
		encrypted_bytes = base64.b64encode(result)
		encrypted_text = encrypted_bytes.decode(AESUtil.ENCODING)

		# base64二次加密
		encrypted_text = str(base64.encodebytes(encrypted_text.encode(AESUtil.ENCODING)), encoding=AESUtil.ENCODING)
		encrypted_text = encrypted_text.replace('\n', '')
		return encrypted_text

	@staticmethod
	def decrypt(key, text):
		"""
		:param key: 密钥
		:param text: 加密后的数据（密文）
		:return:明文
		"""
		key = key.encode(AESUtil.ENCODING)
		text = base64.b64decode(text)
		# base64二次解密
		text = base64.b64decode(text)
		cipher = AES.new(key, AES.MODE_ECB)

		# 补位
		text_decrypted = unpad(cipher.decrypt(text), AES.block_size, style='pkcs7')
		text_decrypted = text_decrypted.decode(AESUtil.ENCODING)
		return text_decrypted


if __name__ == '__main__':
	import json

	signature_data = {
		'x-asyt-version': '0.1.0',
		'x-asyt-timestamp': '1639377895659',
		'x-asyt-expire': '10000',
		'x-asyt-nonce': 'i4qi43',
	}

	body = {
		"code": "eyuv",
		"password": "123qwe!@#",
		"username": "testcj"
	}

	signature = AESUtil.get_signature(signature_data)
	encoded = AESUtil.encrypt(signature, json.dumps(body))
	print("encoded:", encoded)

	decoded = AESUtil.decrypt(signature, encoded)
	print("decoded:", decoded)
	print("json decoded2:", json.loads(decoded))
