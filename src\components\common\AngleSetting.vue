<template>
	<a-card :title="title" :bodyStyle="{width: '220px'}" @keydown.8.stop>
		<div
			class="base-wrapper"
		>
			<label for=""><b>角度</b></label>
			<a-input-number ref="number" :value="angle" placeholder="请输入" class="m-input" :max="360" :min="-360" @change="changeValue"></a-input-number>
		</div>
	</a-card>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			required: true,
		},
	},
	data () {
		return {
			angle: 0
		};
	},
	methods: {
		changeValue (value) {
			this.angle = value > 360 ? 360 : value < -360 ? -360 : value;
			this.$refs.number.blur();
			this.$refs.number.focus();
		}
	},
	watch: {
		angle (value) {
			this.$emit('change', value);
		}
	}
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/base.less';
@import '~@/assets/styles/mixins/variables.less';

.ant-card {
	/deep/ .ant-card-head {
		padding: 0 20px;
	}
	/deep/ .ant-card-body {
		padding: 10px 20px
	}

	/deep/ .ant-card-head-title {
		padding-top: 12px;
		padding-bottom: 12px;
	}
}
.base-wrapper {
	display:flex;
	justify-content:space-between;
	align-items:center;
	margin-top:10px;
	margin-bottom:10px;
}

.m-input {
	flex: 1;
	margin-left: 8px;
}
</style>
