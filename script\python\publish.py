#!/usr/bin/python3
# -*- coding: UTF-8 -*-
###################################
# 发布类
# Created by cjking on 2020/07/06.
###################################
import os
import time
import utils
import logger
import propertiesUtil
from server import getServer

logger = logger.Logger()
config = propertiesUtil.Properties(f'{os.getcwd()}/script/python/config.properties').getProperties()
username = config.get('username')
password = config.get('password')
filePath = config.get('filePath')
server_port = config.get('server_port')


async def publish(env, g_server=None, g_intranet=None, version='latest'):
	try:
		# 1、获取库名称、tag版本
		repository_name = config.get('repository_name')

		# 服务器地址
		server = g_server
		if server is None:
			server = await getServer(env)
		logger.choose("您选择的服务器地址是: %s" % server, "blue")

		repository_name = repository_name if env == 'prod' else ''.join([repository_name, '-', env])
		logger.choose('仓库名称: %s' % repository_name)

		# 获取tag版本
		tag_list = utils.getTagList(repository_name, env)
		if utils.isEmpty(tag_list):
			logger.choose('未查询到版本号!', 'red')
			exit(0)
		else:
			last_version = tag_list[0]
			if version in tag_list:
				last_version = version
			logger.choose('仓库最新版本号: %s' % last_version)
			# 开始发布
			startPublish(env, repository_name, last_version, server, g_intranet)
	except KeyboardInterrupt:
		print('退出程序')
		exit(0)


def startPublish(env, repository_name, last_version, server, g_intranet):
	"""
	开始发布
	"""
	try:
		ssh_port = config.get('ssh_port_prod') if env == 'prod' else config.get('ssh_port')
		# 如果存在旧容器，则删除
		logger.info("docker容器停止中...")
		utils.quickCmd(f'ssh -p {ssh_port} root@{server} "docker stop {repository_name}"')
		logger.info("docker容器停止完成。")

		logger.info("docker容器删除中...")
		utils.quickCmd(f'ssh -p {ssh_port} root@{server} "docker rm {repository_name}"')
		logger.info("docker容器删除完成。")

		# 如果存在旧镜像，则删除
		logger.info("docker镜像删除中...")
		rmDockerImage = "docker rmi \$(docker images | grep %s | awk \'{print \$3}\')" % repository_name
		if utils.isWin():
			rmDockerImage = "docker rmi $(docker images | grep %s | awk \'{print $3}\')" % repository_name
		logger.info(rmDockerImage)
		utils.quickCmd(f'ssh -p {ssh_port} root@{server} "{rmDockerImage}"')
		logger.info("docker镜像删除完成。")

		logger.info("docker镜像启动中...")
		registry_url = config.get('api_out_network') if env == 'prod' else config.get('api_in_network')
		registry = registry_url.replace('https://', '').replace('http://', '')
		command = f"docker run --name {repository_name} -p {server_port}:80 -idt {registry}/{repository_name}:{last_version}"
		logger.info(command)
		utils.quickCmd(f'ssh -p {ssh_port} root@{server} {command}')
		if env == 'prod':
			utils.quickCmd(f'ssh -p {ssh_port} root@{server} "docker login --username={username} -p {password} {registry_url}"')
			utils.quickCmd(f'ssh -p {ssh_port} root@{server} "sh {filePath}/sync-{repository_name}-docker-container.sh {g_intranet}"')
			logger.info("docker镜像同步完成...")
		logger.info("docker镜像启动完成。")

		logger.info("开始清理本地编译文件...")
		if os.path.exists('dist'):
			utils.quickCmd('rm -rf dist')
		logger.info("清理本地编译文件完成。")

		logger.info("服务器部署完成。")
		time.sleep(2)
		utils.quickCmd(f'ssh -p {ssh_port} root@{server} "docker ps -a"')
	except KeyboardInterrupt:
		print('退出程序')
		exit(0)
