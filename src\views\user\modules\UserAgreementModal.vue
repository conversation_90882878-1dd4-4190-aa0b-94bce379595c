<template>
	<a-modal
		:width="1200"
		:visible="visible"
		:confirmLoading="confirmLoading"
		@cancel="handleCancel"
		cancelText="关闭"
		:title="null"
		:footer="null"
	>
		<a-spin :spinning="confirmLoading">
			<div class="doc">
				<h1>服务条款</h1>
				<p>欢迎使用创成云平台（以下简称“平台”或“我们”）</p>
				<p>这是一款面向企业和团队用户的在线协同软件，该平台即对应域名为“http://www.generativethings.com”及其相关二级域名均由安世亚太科技股份有限公司（以下简称“安世亚太”）提供。
				</p>
				<p>本服务条款是您与安世亚太之间的协议。此条款在必要时将进行修订， 且毋须另行通知。</p>
				<p>修订后的条款一旦在网页上公布即有效代替原来的服务条款。</p>
				<p>________________________________________</p>
				<h3>1. 帐户</h3>
				<p>您在登录使用创成云平台之前需要注册专属于创成云平台的帐户，同时您需要仔细阅读平台的
					《隐私协议》和《服务条款》，并在充分阅读和理解了相关协议的约定后提交必要的个人及团队信息完成注册。安世亚太因此获取的与您个人及您团队相关的任何信息均将依照《隐私协议》中的相关条款安全使用。
				</p>
				<p>您注册的帐户由您管理和使用，但该帐户仍然归属于安世亚太。安世亚太将永久保留您对所注册帐户的使用权。</p>
				<p>请您妥善保管您的帐户名和密码，当您使用完毕后，应安全退出。因您保管不善可能导致遭受盗号或密码失窃，责任由您自行承担。</p>
				<h3>2. 使用本服务</h3>
				<p>使用平台上相关的试用版产品或服务是免费的，但不包括我们针对高级用户推出的产品或服务付费版本和功能。</p>
				<p>如果您有意愿使用平台上的某些付费服务，这将表示您同意支付其中的所有费用。在您提交使用付费产品的申请后，安世亚太将提供经我们认可的第三方在线服务机构的支付方式，并要求您支付相关费用。成功支付后，表明您已经获得使用付费服务的权利并且已经达成此项交易，除非因安世亚太的原因导致服务无法正常提供，否则我们将不退还您已经支付的服务费。
				</p>
				<p>此外，由于您违反了《隐私协议》和《服务条款》的相关规定而导致帐户不可用，安世亚太将不会退还付费产品在未使用期间的服务费。</p>
				<p>以下行为是我们坚决反对和禁止的：</p>
				<ul>
					<li>（1）以恶意目的对本网站进行任何形式的反向工程、反向编译、反汇编，或在竞争产品抄袭模仿本网站的设计。</li>
					<li>（2）使用平台的通信功能发送垃圾信息、频繁骚扰其他用户和造成用户反感的行为。</li>
					<li>（3）对网站服务器进行恶意攻击，或者采取恶意手段登录平台或使用平台上的服务或产品并由此造成服务器异常。</li>
					<li>（4）向第三方直接或间接出售、转售平台上的产品或者服务。</li>
					<li>（5）使用平台从事非法活动或者为非法活动提供帮助。</li>
				</ul>
				<p>如果您采取了上述行为，我们将该视行为引起后果的严重性追究责任，并保留通过法律途径追偿合理损失的权利。</p>
				<h3>3. 隐私保护</h3>
				<p>我们为用户提供了完备的隐私保护机制，具体请参阅《隐私协议》中的相关约定。</p>
				<h3>4. 数据、内容和知识产权</h3>
				<p>平台的所有知识产权均归属安世亚太所有。安世亚太在本服务中所使用的“创成云”、“工作台”
					LOGO等商业标识，其著作权或商标权归安世亚太所有。上述及其他任何本服务包含的内容的知识产权均受到法律保护，未经安世亚太、用户或相关权利人书面许可，
					任何人不得以任何形式进行使用或创造相关衍生作品。</p>
				<h3>5. 不可抗力及免责</h3>
				<p>您理解并同意，在使用本服务的过程中，可能会遇到不可抗力等风险因素，使本服务发生中断。
					不可抗力是指不能预见、不能克服并不能避免且对一方或双方造成重大影响的客观事件，
					包括但不限于自然灾害如洪水、地震、瘟疫流行和风暴等以及社会事件如战争、动乱、政府行为等。出现上述情况时，安世亚太将努力在第一时间与相关单位配合，及时进行修复，但是由此给您造成的损失，安世亚太在法律允许的范围内免责。
				</p>
				<p>在法律允许的范围内，安世亚太对以下情形导致的服务中断或受阻不承担责任：</p>
				<ul>
					<li>（1）受到计算机病毒、木马或其他恶意程序、黑客攻击的破坏；</li>
					<li>（2）用户的电脑软件、系统、硬件和通信线路出现故障；</li>
					<li>（3）用户操作不当；</li>
					<li>（4）用户通过非安世亚太授权的方式使用本服务；</li>
					<li>（5）其他安世亚太无法控制或合理预见的情形；</li>
				</ul>
				<p>您理解并同意，本服务并非为某些特定目的而设计，包括但不限于核设施、军事用途、
					医疗设施、交通通讯等重要领域。如果因为软件或服务的原因导致上述操作失败而带来的人员伤亡、财产损失和环境破坏等，安世亚太不承担法律责任。</p>
				<h3>6. 生效与终止</h3>
				<p>使用安世亚太的服务即视为您已阅读本协议并接受本协议的约束。安世亚太有权在必要时修改本协议条款，您可以在相关服务页面查阅最新版本的协议条款。本协议条款变更后，如果您继续使用安世亚太提供的软件或服务，即视为您已接受修改后的协议。如果您不接受修改后的协议，应当停止使用安世亚太提供的软件或服务。
				</p>
				<p>安世亚太可能会对服务内容进行变更，也可能会中断、中止或终止服务。</p>
				<p>如发生下列任何一种情形，安世亚太有权不经通知而中断或终止向您提供的服务：</p>
				<ul>
					<li>（1）根据法律规定您应提交真实信息，而您提供的个人资料不真实、或与注册时信息不一致又未能提供合理证明；</li>
					<li>（2）您违反相关法律法规或本协议的约定；</li>
					<li>（3）按照法律规定或主管部门的要求；</li>
					<li>（4）出于安全的原因或其他必要的情形。</li>
				</ul>
				<p>安世亚太有权按本协议第2条的约定进行收费。若您未按时足额付费，安世亚太有权中断、中止或终止提供服务。</p>
				<h3>7. 管辖与法律适用</h3>
				<ul>
					<li>本协议的成立、生效、履行、解释及纠纷解决，适用中华人民共和国大陆地区法律（不包括冲突法）。若您和安世亚太之间发生任何纠纷或争议，首先应友好协商解决；协商不成的，您同意将纠纷或争议提交北京仲裁委员会提请仲裁。双方同意选择适用北京仲裁委员会仲裁规则，仲裁裁决对双方均有约束力。
					</li>
					<li>本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。</li>
					<li>本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。</li>
				</ul>
				<h3>8. 其他</h3>
				<p>如果您对本协议或本服务有意见或建议，可与安世亚太客户服务部门联系，联系方式：<EMAIL>，我们会给予您必要的帮助。</p>
				<h3>9. 特别声明</h3>
				<p>除本协议有明确规定外，本协议并未对利用本软件访问的安世亚太或合作单位的其他服务规定相关的服务条款，对于这些服务有可能有单独的服务条款加以规范，请用户在使用有关服务时另行了解与确认。
				</p>
				<p>若您使用平台中包含的第三方软件或技术，其中包括但不限于开源代码或开源库或任何其他组建，请您遵循该第三方软件或技术的权利人对您的使用、拷贝、修改和再发布等的合法权利及应当遵守的约定。
				</p>
				<p>如果您没有遵守上述服务条款或第三方权利人的相关知识产权约定，该第三方可能会对您提起诉讼或采取其他制裁措施，并要求安世亚太给予协助，您应当自行承担法律责任。</p>

			</div>
		</a-spin>
	</a-modal>
</template>

<script>
export default {
	name: 'DocModal',
	components: {},
	data () {
		return {
			title: '用户协议',
			drawerWidth: 1200,
			visible: false,
			confirmLoading: false,
			model: {},
		};
	},
	methods: {
		resetScreenSize () {
			const screenWidth = document.body.clientWidth;
			if (screenWidth < 500) {
				this.drawerWidth = screenWidth;
			} else {
				this.drawerWidth = 1200;
			}
		},
		// 窗口最大化切换
		toggleScreen () {
			if (this.modaltoggleFlag) {
				this.modalWidth = window.innerWidth;
			} else {
				this.modalWidth = 1200;
			}
			this.modaltoggleFlag = !this.modaltoggleFlag;
		},
		handleCancel () {
			this.close();
		},
		open () {
			this.visible = true;
		},
		close () {
			this.$emit('close');
			this.visible = false;
			this.disableSubmit = false;
		},
	},
};
</script>
<style lang="less" scoped>
.doc {
    padding: 20px;
    h3 {
        font-weight: 600;
    }
}
</style>
