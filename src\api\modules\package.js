/* ===================================
* 套餐管理 API
* Created by zhangzheng on 2022/07/21.
* Copyright 2022, Inc.
* =================================== */
import * as http from '@/http';
/**
 * 查询部门(通过ID)
 * @param params
 * @returns {Promise}
 */
export const queryDepartById = (params) => http.get(`/sys/sysDepart/queryById`, params);

/**
 * 企业模块套餐管理-通过企业id查询列表
 * @param params
 * @returns {Promise}
 */
export const queryDepartModulePackageList = (params) => http.get(`/syspackage/sysDepartModulePackage/listByDepartId`, params);

/**
 * 企业模块套餐管理-通过id查询列表
 * @param params
 * @returns {Promise}
 */
export const queryDepartModulePackageById = (params) => http.get(`/syspackage/sysDepartModulePackage/queryById`, params);

/**
 * 企业模块套餐管理-日志
 * @param params
 * @returns {Promise}
 */
export const queryDepartPowerLog = (params) => http.get(`/syspackage/sysDepartPowerLog/list`, params);
