<template>
	<a-layout class="index-layout">
		<GlobalHeader />
		<a-layout-content :style="{ padding: '0', marginTop: '50px' }">
			<div class="home-container">
				<div class="bannerBox">
					<div class="title">新一代仿真技术</div>
					<div class="subTitle">基于web的在线仿真服务平台</div>
					<a-button type="primary" @click="visit">进入平台</a-button>
					<img v-lazy="bannerBgImg">
				</div>
				<div class="box1">
					<div class="title">Pera.SimOnline 提供一站式在线仿真服务</div>
					<img class="bg" v-lazy="bannerBg2Img">
				</div>
				<div class="box2">
					<div class="content">
						<div class="title">典型应用</div>
						<div class="tabs">
							<div
								:class="['item', currentTab === index ? 'active' : '']"
								v-for="index in Object.keys(ImgTitle).map(Number)"
								@click="switchTab(index)"
								:key="index"
							>
								{{ ImgTitle[index] }}
							</div>
						</div>
						<div class="tabContent">
							<img v-if="[1, 2, 4].includes(currentTab)" v-lazy="curImg">
							<video v-if="currentTab === 3" :src="videoSrc" class="video" width="800" height="500" autoplay controlsList="nodownload">
								您的浏览器不支持 video 标签。
							</video>
						</div>
					</div>
				</div>
				<div class="box3">
					<div class="title">合作伙伴</div>
					<div class="company">
						<img v-lazy="company1Img">
						<img v-lazy="company2Img">
						<img v-lazy="company3Img">
						<img v-lazy="company4Img">
						<img v-lazy="company5Img">
						<img v-lazy="company6Img">
					</div>
				</div>
			</div>
		</a-layout-content>
		<global-footer />
	</a-layout>
</template>

<script>
/* ===================================
 * 首页
 * Created by cjking on 2021/04/07.
 * Copyright 2021, Inc.
 * =================================== */
import Config from '@/config/Config';
import GlobalHeader from '@/components/page/GlobalHeader';
import GlobalFooter from '@/components/page/GlobalFooter';

import bannerBgImg from '@/assets/img/bannerBg.png';
import bannerBg2Img from '@/assets/img/bannerBg2.png';

import box0Img from '@/assets/img/box0.png';
import box1Img from '@/assets/img/box1.png';
import box2Img from '@/assets/img/box2.png';
import box3Img from '@/assets/img/box3.png';
import box4Img from '@/assets/img/box4.png';
import fluidImg from '@/assets/img/fluid.gif';
import structure from '@/assets/img/structure.gif';
import multiPhysics from '@/assets/img/multiPhysics.gif';

import company1Img from '@/assets/img/company/company1.png';
import company2Img from '@/assets/img/company/company2.png';
import company3Img from '@/assets/img/company/company3.png';
import company4Img from '@/assets/img/company/company4.png';
import company5Img from '@/assets/img/company/company5.png';
import company6Img from '@/assets/img/company/company6.png';
import { mapGetters } from 'vuex';

const ImgMap = {
	1: fluidImg,
	2: structure,
	4: multiPhysics,
};

const ImgTitle = {
	1: '流体',
	2: '结构',
	3: '电磁',
	4: '多物理场',
};

export default {
	name: 'Index',
	components: {
		GlobalHeader,
		GlobalFooter,
	},
	data () {
		return {
			bannerBgImg,
			bannerBg2Img,
			box0Img,
			box1Img,
			box2Img,
			box3Img,
			box4Img,
			fluidImg,
			structure,
			multiPhysics,
			company1Img,
			company2Img,
			company3Img,
			company4Img,
			company5Img,
			company6Img,
			ImgTitle,
			videoSrc: `${ Config.staticDomainURL }/videos/electromagnetism.mp4`,
			curImg: fluidImg,
			currentTab: 1,
		};
	},
	computed: {
		...mapGetters(['isLogin', 'userInfo', 'moduleList']),
	},
	methods: {
		visit () {
			if (this.isLogin) {
				this.$router.push(this.moduleList[0].url);
			} else {
				this.$router.push('/user/login');
			}
		},
		switchTab (index) {
			this.currentTab = index || 1;
			this.curImg = ImgMap[this.currentTab];
		},
	},
};
</script>

<style lang="less">
@import "~@/assets/styles/mixins/grid.less";
@import "~@/assets/styles/mixins/variables.less";

.index-layout {
	.text-center-h2() {
		.text-center {
			text-align: center;
		}

		h2 {
			font-size: 36px;
			margin: 0;
			padding: 0 0 50px;
			line-height: 48px;
			color: #3A3A3A;
		}

		h2:after {
			content: "";
			display: block;
			width: 30px;
			background-color: @blue-primary;
			border: 1px solid @blue-primary;
			margin: 5px auto;
		}
		.more {
			.flex-display-align-items-justify-content();
		}
	}

	.ant-carousel /deep/ {
		.slick-slide {

			h3 {
				color: @white;
			}
		}
		.custom-slick-arrow {
			width: 25px;
			height: 25px;
			font-size: 25px;
			color: @white;
			background-color: rgba(31, 45, 61, 0.11);
			opacity: 0.3;
		}
		.custom-slick-arrow:before {
			display: none;
		}
		.custom-slick-arrow:hover {
			opacity: 0.5;
		}
	}
}

.home-container {
	background: @white;
	min-height: 100vh;
	min-width: 1200px;
	> .bannerBox {
		width: 100%;
		height: calc(100vh - 50px);
		background: linear-gradient(180deg, #D5E6F0 0%, #BBD3E1 18%, #E1E6EC 65%, #E1E6EC 100%);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		.title {
			font-size: calc((100vh - 50px) * 0.05);
			font-weight: 600;
			color: @title-color;
			letter-spacing: 3px;
		}
		.subTitle {
			font-size: 18px;
			color: @title-color;
			margin: 5px 0 12px;
		}
		button {
			width: 180px;
			height: calc((100vh - 50px) * 0.06);
			max-height: calc((100vh - 50px) * 0.06);
			min-height: calc((100vh - 50px) * 0.06);
			background: @primary;
			border-radius: 2px;
			font-size: 16px;
			margin-bottom: 10px;
		}
		> img {
			height: calc((100vh - 50px) * 0.65);
			max-height: calc((100vh - 50px) * 0.65);
			min-height: calc((100vh - 50px) * 0.65);
		}
	}

	> .box1 {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		height: calc(100vh - 50px);
		.title {
			font-size: 32px;
			font-weight: 600;
			color: @title-color;
			line-height: 32px;
		}
		.bg {
			margin-top: 30px;
			display: block;
			height: calc((100vh - 50px) * 0.65);
			max-height: calc((100vh - 50px) * 0.65);
			min-height: calc((100vh - 50px) * 0.65);
		}
		.boxs {
			display: flex;
			flex-wrap: wrap;
			width: 1200px;
			margin: 0 auto;
			justify-content: space-between;
			position: relative;
			> .item {
				color: @title-color;
				margin-top: 40px;
				position: relative;
				img {
					width: 580px;
					height: 415px;
				}
				.desc {
					position: absolute;
					left: 230px;
					bottom: 30px;
					font-size: 20px;
				}
			}
			> .middle {
				width: 320px;
				height: 260px;
				background: @white;
				box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.06);
				border-radius: 20px;
				border: 1px solid #ACE1F2;
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				top: 339px;
				left: 439px;
				z-index: 99;
			}
		}
	}

	> .box2 {
		width: 100%;
		background-color: #F2F8FC;
		> .content {
			width: 800px;
			margin: 0 auto;
			padding: 50px 0;
			.title {
				font-size: 32px;
				font-weight: 600;
				color: @title-color;
				line-height: 32px;
				text-align: center;
			}
			.tabs {
				width: 600px;
				margin: 30px auto 20px;
				height: 44px;
				border: 1px solid #E5E5E5;
				background-color: @white;
				display: flex;
				align-items: center;
				justify-content: space-around;
				.item {
					max-width: 80px;
					height: 44px;
					font-size: 16px;
					font-weight: 400;
					color: @title-color;
					line-height: 16px;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;

					&.active {
						color: @primary;
						border-bottom: 2px solid @primary;
					}
				}
			}
			.tabContent {
				width: 800px;
				height: 500px;
				position: relative;
				img {
					width: 800px;
					height: 500px;
				}
				video {
					width: 800px;
					height: 500px;
				}
				.shadow {
					position: absolute;
					height: 30px;
					width: 100%;
					bottom: 20px;
					z-index: 9;
					background-color: #F5F5F5;
				}
			}
		}
	}

	> .box3 {
		width: 100%;
		padding: 50px 0;
		.title {
			font-size: 32px;
			font-weight: 600;
			color: @title-color;
			line-height: 32px;
			text-align: center;
		}
		.company {
			width: 1200px;
			margin: 30px auto;
			display: flex;
			align-items: center;
			justify-content: space-around;
			> img {
				width: 182px;
				height: 112px;

			}
		}
	}
}
</style>
