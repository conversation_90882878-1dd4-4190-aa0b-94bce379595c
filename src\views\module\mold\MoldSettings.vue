<template>
	<div class="params-settings" v-if="!loading">
		<template v-for="(data, index) in dataList">

			<div class="box" :key="index" v-if="isParts(data)">
				<div class="column upload" v-if="data.value && data.value.length">
					<div class="content">
						<div class="item percent-65">
							<span class="label">
								<a-tooltip placement="top" :title="data.value.join(',')">
									{{ data.value.join(',') | Ellipsis(8) }}
								</a-tooltip>
							</span>
						</div>

						<div class="item right">

							<div class="mr-10" @click="allVisibleHandler(data)">
								<svg-icon :type="data.visible ? 'param-visible' : 'param-hidden'" />
							</div>

							<div class="mr-10">
								<input type="color" v-model="data.color" @input="allColorHandler(data)">
							</div>

							<a-popover :title="null" placement="topRight">
								<template #content>
									<div class="display-flex-center">
										<a-slider
											:min="0"
											:max="100"
											:step="1"
											v-model="data.opacity"
											:style="{ width: '100px', margin: '10px 0 0 10px' }"
											@change="allOpacityHandler(data)"
										/>
										<div class="opacity">{{ data.opacity }}%</div>
									</div>
								</template>
								<svg-icon type="param-opacity" />
							</a-popover>
						</div>
					</div>
				</div>
				<div class="column upload" v-else>
					<div class="select-box">
						<FileLoader
							class="upload-btn"
							:disabled="data.disabled"
							:loading="data.submitLoading"
							:exts="['3dm', 'stp', 'step', 'iges', 'igs', 'stl', 'pdf', 'ai']"
							@ok="uploadCallbackHandler($event, data)"
						/>
					</div>
				</div>
			</div>

			<div class="box" :key="index" v-if="isShape(data)">
				<div class="column">
					<div class="content">
						<div class="item percent-55">
							<span class="label" :class="{ 'required': data.required }">{{ data.label }}</span>
							<span class="colon">:</span>
							<a-tooltip v-if="data.tip" placement="top" :title="data.tip">
								<a-icon type="question-circle" style="cursor: pointer" />
							</a-tooltip>
						</div>

						<div class="item right">

							<div class="mr-10" @click="visibleHandler(data)">
								<svg-icon :type="data.visible ? 'param-visible' : 'param-hidden'" />
							</div>

							<div class="mr-10">
								<input type="color" v-model="data.color" @input="colorHandler(data)">
							</div>

							<a-popover :title="null" placement="topRight">
								<template #content>
									<div class="display-flex-center">
										<a-slider
											:min="0"
											:max="100"
											:step="1"
											v-model="data.opacity"
											:style="{ width: '100px', margin: '10px 0 0 10px' }"
											@change="opacityHandler(data)"
										/>
										<div class="opacity">{{ data.opacity }}%</div>
									</div>
								</template>
								<svg-icon type="param-opacity" />
							</a-popover>

							<div :class="{ 'action': true, 'activate': data.activate }" @click="activateHandler(data)">
								<svg-icon :type="getIcon(data)" style="width: 20px; height: 20px;" />
							</div>

						</div>
					</div>
				</div>

				<div class="column group" v-if="isSurface(data)">
					<div class="select-box">
						<a-tooltip placement="top" :title="data.value.join(',')" :overlayStyle="{ maxHeight: '240px', overflowY: data.value.join(',').length > 200 ? 'auto' : 'hidden' }">
							<div class="select-item">
								<template v-if="data.value && data.value.length && data.valueActiveMap">
									<template v-for="(name, idx) in data.value">
										<a-tag
											closable
											:key="idx"
											:class="{ 'activate': getActive(name, data) }"
											@close="closeTag(data, name)"
											@click="commonTagActivateHandler(name, data)"
										>
											{{ name }}
										</a-tag>
									</template>
								</template>
							</div>
						</a-tooltip>
						<div class="select-item u-22">
							<span class="icon">
								<svg-icon className="clear" type="clear" @click="closeAllTag(data)" />
							</span>
						</div>
					</div>
				</div>

				<div class="column group" v-if="isLine(data)">
					<div class="group-container">
						<template v-if="!data.extra || !data.extra.length">
							<div class="select-box btn-add" @click="addGroupObj(data)">
								<a-button icon="plus" size="large">添加一组</a-button>
							</div>
						</template>
						<template v-else>
							<div class="select-box" v-for="(extraItem, idx) in data.extra" :key="idx">
								<div class="select-item" :class="{'h22': extraItem.ysLineName || (extraItem.crkLineNames && extraItem.crkLineNames.length)}">
									<template v-if="extraItem.ysLineName">
										<a-tag
											closable
											:class="{ 'activate': extraItem.ysLineActivate }"
											@close="clearGroupObjValue('ysLineName', data, extraItem)"
											@click="tagActivateHandler('ysLineName', data, extraItem)"
										>
											<JEllipsis :value="extraItem.ysLineName" :length="6" :divStyle="{ maxWidth: '82px' }" />
										</a-tag>
									</template>
									<a-button v-else :class="{ 'activate': extraItem.ysLineActivate }" icon="plus" size="small" @click="addYsLine('ysLineName', data, extraItem)">添加预设线</a-button>

									<span style="margin: 0 3px;">-</span>

									<template v-if="extraItem.crkLineNames && extraItem.crkLineNames.length">
										<a-tooltip placement="top" :title="extraItem.crkLineNames.join(',')">
											<a-tag
												closable
												class="extra-tag"
												:class="{ 'activate': extraItem.crkLineActivate }"
												@close="clearGroupObjValue('crkLineNames', data, extraItem)"
												@click="tagActivateHandler('crkLineNames', data, extraItem)"
											>
												{{ extraItem.crkLineNames.join(',') | Ellipsis(5) }}
											</a-tag>
										</a-tooltip>
									</template>
									<a-button v-else :class="{ 'activate': extraItem.crkLineActivate }" icon="plus" size="small" @click="addCRKLine('crkLineNames', data, extraItem)">添加出入口边线</a-button>
								</div>
								<div class="select-item u-22">
									<span class="icon">
										<svg-icon className="clear" type="clear" @click="removeOneGroup(data, extraItem)" />
									</span>
								</div>
							</div>
							<div class="select-box btn-add" @click="addGroupObj(data)">
								<a-button icon="plus" size="large">添加一组</a-button>
							</div>
						</template>
					</div>
				</div>
				<!-- <div todo v-if="isPoint(data)">
					data.extra: {{ data.extra }} <br>
					names: {{ names }} <br>
					nameMap: {{ nameMap }} <br>
				</div> -->
				<div class="column group" v-if="isPoint(data)">
					<div class="group-container">
						<template v-if="!data.extra || !data.extra.length">
							<div class="select-box btn-add" @click="addOneGroupPoint(data)">
								<a-button icon="plus" size="large">添加一组</a-button>
							</div>
						</template>
						<template v-else>
							<div class="select-box" v-for="(extraItem, idx) in data.extra" :key="idx">
								<div class="select-item h22">
									<a-tag
										:class="{ 'point': true, 'activate': data.valueActiveMap[extraItem.id] }"
										@click="pointActivate(data, extraItem)"
									>
										{{ extraItem.groupName }}
									</a-tag>

									<span style="margin: 0 3px;">-</span>

									<a-popover :title="null" trigger="click" v-if="names.length">
										<template #content>
											<a-checkbox-group
												class="extra-checkbox-group"
												v-model="extraItem.pointNames"
												@change="onCheckboxChange($event, data, extraItem)"
											>
												<a-row>
													<a-col :span="8" v-for="(name, pIdx) in names" :key="pIdx">
														<a-checkbox
															:value="name"
															:disabled="(nameMap[name] && nameMap[name] !== extraItem.id)
																|| (!extraItem.pointNames.includes(name) && extraItem.pointNames.length >= 2 )"
														>
															{{ name }}
														</a-checkbox>
													</a-col>
												</a-row>
											</a-checkbox-group>
										</template>
										<template v-if="extraItem.pointNames && extraItem.pointNames.length > 0">
											<a-button icon="select" size="small" @click="chooseChangePoint(data, extraItem)">
												{{ extraItem.pointNames.join(',') | Ellipsis(5) }}
											</a-button>
										</template>
										<template v-else>
											<a-button icon="select" size="small" @click="chooseChangePoint(data, extraItem)">
												选择变径点
											</a-button>
										</template>
									</a-popover>
									<template v-else>
										<a-button icon="select" size="small" @click="chooseChangePoint(data, extraItem)">选择变径点</a-button>
									</template>
								</div>

								<div class="select-item u-22">
									<span class="icon">
										<svg-icon className="clear" type="clear" @click="removeOneGroupPoint(data, extraItem)" />
									</span>
								</div>
							</div>
							<div class="select-box btn-add">
								<a-button icon="plus" size="large" @click="addOneGroupPoint(data)">添加一组</a-button>
							</div>
						</template>
					</div>
				</div>
			</div>

			<div class="box" :key="index" v-if="isSlider(data)">
				<div class="column">
					<div class="content">
						<div class="item percent-55">
							<span class="label" :class="{ 'required': data.required }">{{ data.label }}</span>
							<span class="colon">:</span>
							<a-tooltip v-if="data.tip" placement="top" :title="data.tip">
								<a-icon type="question-circle" style="cursor: pointer" />
							</a-tooltip>
						</div>
					</div>
				</div>

				<div class="column slider-wrapper">
					<div class="select-box">
						<div class="select-item slider">
							<a-slider
								:value="data.value"
								:min="data.min"
								:max="data.max"
								:step="data.step"
								:marks="{ [data.min]: data.min, [data.max]: data.max }"
								@change="sliderValueChange($event, data)"
							/>
						</div>
						<div class="select-item u-60">
							<a-input-number
								size="small"
								:value="data.value"
								:min="data.min"
								:max="data.max"
								:step="data.step"
								@change="sliderValueChange($event, data)"
							/>
						</div>
					</div>
				</div>

			</div>

			<div class="box" :key="index" v-if="isSwitch(data)">
				<div class="column">
					<div class="content">
						<div class="item percent-55">
							<span class="label" :class="{ 'required': data.required }">{{ data.label }}</span>
							<span class="colon">:</span>
							<a-tooltip v-if="data.tip" placement="top" :title="data.tip">
								<a-icon type="question-circle" style="cursor: pointer" />
							</a-tooltip>
						</div>

						<div class="item right">
							<a-switch
								v-model="data.checked"
								checked-children="开启"
								un-checked-children="关闭"
								:disabled="data.disabled"
								@change="switchValueChange($event, data)"
							/>
						</div>
					</div>
				</div>

			</div>

		</template>
	</div>
</template>

<script>
/* ===================================
 * 随形冷却-参数设置页
 * Created by cjking on 2022/05/16.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { difference, getCustomId, isArray, isEmptyObject, deepCopy } from '@/utils/utils';
import JEllipsis from '@/components/common/JEllipsis';
import FileLoader from '@/components/core/FileLoader';

export default {
	name: 'MoldSettings',
	components: {
		JEllipsis,
		FileLoader,
	},
	props: {
		activeKey: {
			type: Array,
			required: false,
			default: () => ['1'],
		},
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
		nameList: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	data () {
		return {
			loading: false,
			names: [],
			nameMap: {},
		};
	},
	watch: {
		nameList: {
			immediate: true,
			handler (names) {
				if (names?.length) {
					this.names = [...names];
					// const nameMap = {};

					const bj_point = this.dataList.find(i => i.key === 'bj_point');
					bj_point?.extra?.forEach(i => {
						i.pointNames?.forEach(j => {
							this.nameMap[j] = i.id;
						});
					});

					this.nameMap = deepCopy(this.nameMap);
				} else {
					this.names = [];
					this.nameMap = {};
				}
			},
		},
	},
	methods: {
		isEmptyObject,
		isParts (data) {
			return data.type === 'parts';
		},

		isShape (data) {
			return data.type === 'shape';
		},

		isSlider (data) {
			return data.type === 'slider';
		},

		isSwitch (data) {
			return data.type === 'switch';
		},

		isSurface (data) {
			return data.mode === 'surface';
		},

		isLine (data) {
			return data.mode === 'line';
		},

		isPoint (data) {
			return data.mode === 'point';
		},

		getIcon (data) {
			if (this.isSurface(data)) {
				return 'param-select-face';
			}
			if (this.isLine(data)) {
				return 'param-select-line';
			}
			if (this.isPoint(data)) {
				return 'param-select-point';
			}
		},

		getActive (name, data) {
			return !!data.valueActiveMap[data.key + '_' + name];
		},

		/**
		 * 上传回调处理
		 */
		uploadCallbackHandler (file, data) {
			this.$emit('uploadCallback', { file, data });
		},

		/**
		 * 所有显隐处理
		 */
		allVisibleHandler (data) {
			data.visible = !data.visible;
			this.$emit('onAllVisible', data);
		},

		/**
		 * 所有颜色处理
		 */
		allColorHandler (data) {
			this.$emit('onAllColor', data);
		},

		/**
		 * 所有透明度处理
		 */
		allOpacityHandler (data) {
			this.$emit('onAllOpacity', data);
		},

		/**
		 * 显示/隐藏处理
		 */
		visibleHandler (data) {
			data.visible = !data.visible;
			this.$emit('onVisible', data);
		},

		/**
		 * 颜色处理
		 */
		colorHandler (data) {
			this.$emit('onColor', data);
		},

		/**
		 * 透明度处理
		 */
		opacityHandler (data) {
			this.$emit('onOpacity', data);
		},

		/**
		 * 激活处理
		 */
		activateHandler (data, activate = undefined) {
			data.activate = activate ?? !data.activate;
			this.dataList.forEach(item => {
				if (item.key === data.key) {
					item.activate = data.activate;
				} else {
					item.activate = false;
				}
			});
			this.$emit('onActivate', data);
		},

		/**
		 * 关闭标签
		 */
		closeTag (data, name) {
			const item = this.dataList.find(item => item.key === data.key);
			if (item) {
				item.value = [...item.value].filter(val => val && val !== name);
			}
			this.$emit('onCloseTag', { data, name });
		},

		/**
		 * 关闭所有标签
		 */
		closeAllTag (data) {
			let clearValues = [];
			const item = this.dataList.find(item => item.key === data.key);
			if (item) {
				clearValues = [...item.value];
				item.value = [];
			}
			this.$emit('onCloseAllTag', { data, clearValues });
		},

		/**
		 * 标签激活处理
		 */
		tagActivateHandler (name, data, extraItem) {
			let attrName;
			let otherAttrName;
			const attrNames = ['ysLineActivate', 'crkLineActivate'];
			if (name === 'ysLineName') {
				attrName = attrNames[0];
				otherAttrName = attrNames[1];
			} else {
				attrName = attrNames[1];
				otherAttrName = attrNames[0];
			}

			extraItem[attrName] = !extraItem[attrName];
			extraItem[otherAttrName] = extraItem[attrName];

			// 清除所有标签激活(不含自己)
			this.dataList.forEach((item) => {
				if (item.key === data.key) {
					item.valueActiveMap = {};
				}
				item.extra?.forEach(extraSource => {
					if (extraSource && extraSource.id !== extraItem.id) {
						extraSource[attrNames[0]] = false;
						extraSource[attrNames[1]] = false;
					}
				});
			});

			// 激活指定标签
			const item = this.dataList.find(item => item.key === data.key);
			if (item) {
				item.valueActiveMap = {};
				const extraSource = item.extra.find(extraSource => extraSource.id === extraItem.id);
				if (extraSource) {
					item.valueActiveMap = {
						[extraSource.id]: true,
					};
					const curBool = extraItem[attrName];
					extraSource[attrName] = curBool;
					if (curBool) {
						extraSource[otherAttrName] = !curBool;
					}
				}
			}

			this.$emit('tagGroupActivate', { name, data, extraItem });
		},

		/**
		 * 点标签激活处理
		 */
		pointTagActivateHandler (data, extraItem) {
			data.valueActiveMap = {}; // 先重置
			data.valueActiveMap[extraItem.id] = true;
			this.$emit('pointTagActivate', { data, extraItem });
		},

		/**
		 * 通用标签激活处理程序
		 */
		commonTagActivateHandler (name, data) {
			const attrName = data.key + '_' + name;
			const valueActiveMap = { ...data.valueActiveMap };
			data.valueActiveMap = null;
			valueActiveMap[attrName] = !valueActiveMap[attrName];

			const item = this.dataList.find(item => item.key === data.key);
			if (item) {
				item.valueActiveMap = valueActiveMap;
				data.valueActiveMap = valueActiveMap;
			}
			this.$emit('tagActivate', { name, data });
		},

		/**
		 * 添加组单个对象
		 */
		addGroupObj (data) {
			logger.log('添加组单个对象 data: ', data);
			const item = this.dataList.find(item => item.key === data.key);
			if (item && item.extra) {
				item.extra.push(
					{
						id: getCustomId(),
						ysLineName: '',         // 预设线名称
						ysLineValue: [],        // 预设线值（points）
						ysLineActivate: false,  // 预设线激活
						crkLineActivate: false, // 出入口边线激活
						crkLineNames: [],       // 出入口边线名称集合
					},
				);
			}
		},

		/**
		 * 清除组单个对象值
		 */
		clearGroupObjValue (key, data, extraItem) {
			logger.log('清除组单个对象值 key, data, extraItem: ', key, data, extraItem);
			const checkIsArray = isArray(extraItem[key]);
			let clearValues = [];
			const item = this.dataList.find(item => item.key === data.key);
			if (item && item.extra) {
				const extraSource = item.extra.find(extraSource => extraSource.id === extraItem.id);
				if (extraSource) {
					clearValues = checkIsArray ? [...extraSource[key]] : [extraSource[key]];
					if (!checkIsArray && extraSource.ysLineValue) {
						extraSource.ysLineValue = [];
					}
					extraSource[key] = checkIsArray ? [] : '';
					extraItem[key] = checkIsArray ? [] : '';
					item.value = difference(item.value, clearValues);
				}
			}
			this.$emit('clearGroupObjValue', { key, data, extraItem, clearValues });
		},

		/**
		 * 删除一组
		 */
		removeOneGroup (data, extraItem) {
			logger.log('删除一组 data, extraItem: ', data, extraItem);
			let clearValues = [];
			const item = this.dataList.find(item => item.key === data.key);
			if (item) {
				item.extra.forEach(extraSource => {
					if (extraSource.id === extraItem.id) {
						clearValues.push(extraItem.ysLineName, ...extraItem.crkLineNames);
					}
				});
				item.extra = item.extra.filter(extraSource => extraSource.id !== extraItem.id);
				data.extra = data.extra.filter(extraSource => extraSource.id !== extraItem.id);
				clearValues = Array.from(new Set(clearValues.filter(v => v)));
				item.value = difference(item.value, clearValues);
			}
			this.$emit('removeOneGroup', { data, extraItem, clearValues });
		},

		/**
		 * 添加预设线
		 */
		addYsLine (name, data, extraItem) {
			this.tagActivateHandler(name, data, extraItem);
			this.$emit('addYsLine', { name, data, extraItem });
		},

		/**
		 * 添加出入口边线
		 */
		addCRKLine (name, data, extraItem) {
			this.tagActivateHandler(name, data, extraItem);
			this.$emit('addCRKLine', { name, data, extraItem });
		},

		/**
		 * 滑块值更改
		 */
		sliderValueChange (value, data) {
			if (!isNaN(value)) {
				if (value < data.min) {
					data.value = data.min;
				} else if (value > data.max) {
					data.value = data.max;
				} else {
					data.value = value;
				}
			}
			this.$emit('sliderValueChange', data);
		},

		/**
		 * 开关值变化
		 */
		switchValueChange (value, data) {
			data.value = value ? 1 : 0;
			this.$emit('switchValueChange', data);
		},

		/**
		 * 选择变径点
		 */
		chooseChangePoint (data, extraItem) {
			if (!this.names?.length) {
				this.$message.warn('请先绘制变径点!');
				return;
			}
			this.pointActivate(data, extraItem);
		},

		/**
		 * 复选框值更新
		 */
		onCheckboxChange (checkedValues, data, extraItem) {

			this.loading = true;
			const item = this.dataList.find(item => item.key === data.key);
			if (item) {
				const nameMap = {};
				item.extra.forEach(i => {
					i.pointNames.forEach(j => {
						nameMap[j] = i.id;
					});
				});
				this.nameMap = nameMap;
				data.value = Array.from(new Set([...data.value, ...checkedValues]));
			}
			this.loading = false;
			this.$emit('onCheckboxChange', { checkedValues, data, extraItem });
		},

		/**
		 * 添加一组点
		 */
		addOneGroupPoint (data) {
			logger.log('添加一组点 data: ', data);
			this.loading = true;
			this.activateHandler(data, true);
			const item = this.dataList.find(item => item.key === data.key);
			let count;
			if (!item.extra || item.extra.length === 0) {
				item.extra = [];
				count = 1;
			} else {
				count = +item.extra[item.extra.length - 1].groupName.replace(/.*_/g, '') + 1;
			}
			const extraItem = {
				id: getCustomId(),
				pointNames: [], // 变径点名称集合
				points: [],     // 变径点值（points）
				groupName: 'points_group_' + count, // 变径点单个tag组名称
			};
			item.extra.push(extraItem);
			this.loading = false;
		},

		/**
		 * 删除一组点
		 */
		removeOneGroupPoint (data, extraItem) {
			logger.log('删除一组点 data, extraItem: ', data, extraItem);
			this.loading = true;
			let clearValues = [];
			const item = this.dataList.find(item => item.key === data.key);
			if (item) {
				item.extra.forEach(extraSource => {
					if (extraSource.id === extraItem.id) {
						clearValues.push(...extraItem.pointNames);
						extraItem.pointNames.forEach(i => {
							delete this.nameMap[i];
						});
					}
				});
				item.extra = item.extra.filter(extraSource => extraSource.id !== extraItem.id);
				data.extra = data.extra.filter(extraSource => extraSource.id !== extraItem.id);
				clearValues = Array.from(new Set(clearValues.filter(v => v)));
				item.value = difference(item.value, clearValues);
			}
			this.loading = false;
			this.$emit('removeOneGroupPoint', { data, extraItem, clearValues });
		},

		/**
		 * 点激活
		 */
		pointActivate (data, extraItem) {
			data.valueActiveMap = {}; // 重置
			data.valueActiveMap[extraItem.id] = true;
			this.$emit('onCheckboxChange', { checkedValues: extraItem.pointNames, data, extraItem });
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.gutter-example /deep/ .ant-row > div {
	background: transparent;
	border: 0;
}

.gutter-box {
	background: #00A0E9;
	padding: 5px 0;
}

.params-settings {
	.label {
		height: 20px;
		line-height: 20px;
		font-size: 14px;
		font-weight: bold;
		color: #333333;
	}

	.required:before {
		display: inline-block;
		margin-right: 4px;
		color: #F5222D;
		font-size: 14px;
		font-family: SimSun, sans-serif;
		line-height: 1;
		content: "*";
		margin-left: -11px;
	}

	.colon {
		display: inline-block;
		margin-right: 5px;
		font-weight: bold;
	}

	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}

	.action {
		margin-left: 10px;
		border-radius: 2px;

		&.activate {
			svg {
				border: 1px solid @primary;
				border-radius: 2px;
			}
		}
	}

	.box {
		margin-left: 4px;

		&.mtb-16 {
			margin: 16px 0;
		}

		.content {
			display: flex;
			width: 100%;

			.item {
				flex: 1;

				&.percent-55 {
					flex: 0 0 55%;
				}

				&.percent-65 {
					flex: 0 0 65%;
				}

				svg {
					width: 20px;
					height: 20px;
					color: @primary;
				}

				.icon {
					padding: 0 12px;
					color: @primary;
					cursor: pointer;

					&.right {
						text-align: right;
					}
				}

				&.right {
					display: flex;
					justify-content: flex-end;
					align-items: center;
					margin-left: -10px;

					svg, input[type="color"] {
						cursor: pointer;
					}

					.mr-10 {
						margin-right: 10px;
					}
				}
			}
		}

		.column {
			padding: 10px 0;

			&.upload {
				padding: 0;

				.select-box {
					padding: 0;
				}

				.upload-btn {
					width: 280px;

					/deep/ .ant-btn {
						border-radius: 3px;
						background-color: #6362FF;
					}
				}
			}

			&.group {

				/deep/ .ant-btn {
					font-size: 12px;

					&:focus {
						color: #333333;
						background-color: #FFF;
						border-color: #E5E5E5;
					}
				}

				.group-container {
					width: 100%;
					display: flex;
					flex-direction: column;

					.activate {
						color: #8E8AFF;
						border-color: #8E8AFF;
					}

					.select-box {
						padding: 0 10px 5px 10px;

						&:first-child {
							padding: 10px 10px 5px 10px;
						}
					}

					.select-item {
						overflow: hidden;
						display: flex;

						&.h22 {
							height: 24px;
							line-height: 22px;
						}

						.ant-btn {
							min-width: 102px;
						}
					}

					.btn-add {
						padding: 10px;

						.ant-btn {
							width: 100%;
							height: 35px;
							min-height: 35px;
							border-radius: 3px;
						}
					}

					.ant-tag {
						margin-right: 0;
						margin-bottom: 0;
						height: 24px;
						line-height: 22px;
						cursor: pointer;
						min-width: 102px;
						max-width: 102px;
						text-align: center;
						overflow: hidden;

						display: flex;
						align-items: center;
						justify-content: center;
					}

					.ant-btn-sm {
						padding: 0 5px;
					}

					.extra-tag {
						width: 115px;
						min-width: 115px;
						overflow: hidden;
						/*文本不会换行*/
						white-space: nowrap;
					}
				}

				.point {
					&.ant-tag {
						min-width: 116px;
					}
				}
			}

			&.slider-wrapper {
				padding: 5px 0;

				.select-box {
					background: transparent;
					height: 46px;
					line-height: 46px;

					/deep/ .slider {
						.ant-slider {
							width: 140px;
							margin-left: 10px;
						}
					}

					.select-item {
						overflow: hidden;
					}
				}
			}

			.select-box {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				background: #F9F9F9;
				padding: 10px 8px;

				.select-item {
					flex: 1;
					min-height: 22px;
					max-height: 60px;
					overflow: auto;
				}

				.select-item.u-22 {
					flex: 0 0 22px;
					margin: 0 0 0 5px;
				}

				/deep/ .select-item.u-60 {
					flex: 0 0 60px;

					.ant-input-number {
						width: 60px;
					}
				}

				/deep/ .ant-tag {
					margin-right: 5px;
					max-width: 72px;
					padding: 0 5px;
					cursor: pointer;
					color: #333333;
					background: #FAFAFA;
					border: 1px solid #E5E5E5;

					&:nth-child(n+3) {
						margin-bottom: 5px;
					}

					&:last-child {
						margin-bottom: 0;
					}

					&.activate {
						color: #8E8AFF;
						border-color: #8E8AFF;
					}

					.anticon-close {
						margin-left: 0;
					}
				}

				.icon {
					color: @primary;
					cursor: pointer;

					.clear {
						width: 20px;
						height: 20px;
						vertical-align: -5px;
					}
				}
			}
		}
	}
}

.extra-checkbox-group {
	min-width: 256px;
	max-width: 356px;
}
</style>
