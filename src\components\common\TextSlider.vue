<template>
	<div class="attr-item">
		<div class="attr-item-title" v-if="label">
			<div>
				<label style="margin-right: 6px;">
					<b><span
						v-if="required"
						style="margin-left: -6px;color:red"
					>*</span>{{ label }}{{ label ? ':' : '' }}</b>
				</label>
				<a-tooltip v-if="tip" placement="top">
					<template slot="title">
						{{ tip }}
					</template>
					<a-icon type="question-circle"></a-icon>
				</a-tooltip>
			</div>
		</div>
		<div class="attr-item-content">
			<div class="text-slider">
				<a-icon
					type="left"
					@click="(e) => { handleValueChange(e, fieldValue)}"
				/>
				<div class="text-card">
					<h4>{{ selectedValue.title }}</h4>
				</div>
				<a-icon
					type="right"
					@click="(e) => { handleValueChange(e, fieldValue)}"
				/>
			</div>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/4/26.
 * Copyright 2022, Inc.
 * =================================== */
export default {
	components: {
	},
	props: {
		required: {
			type: Boolean,
			required: false,
			default: false,
		},
		title: {
			type: String,
			required: false,
			default: '',
		},
		label: {
			type: String,
			required: false,
			default: '',
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
		name: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: Number,
			required: false,
			default: 0,
		},
		options: {
			type: Array,
			required: true,
		}
	},
	data () {
		return {
			fieldValue: 0,
			selectedValue: {

			}
		};
	},
	watch: {
		value: {
			immediate: true,
			handler (newVal) {
				this.fieldValue = newVal;
				if (!this.fieldValue) {
					this.fieldValue = 0;
				}
				this.selectedValue = this.options.find(i => i.value === this.fieldValue);
			},
		},
	},
	created () {
		this.selectedValue = this.options[0];
	},
	methods: {
		handleValueChange (e, value) {
			value = this.options[0].value === value ? 1 : 0;
			this.$emit('valueChange', { value, key: this.name });
		}
	},
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/layout.less';
@import '~@/assets/styles/mixins/variables.less';

.attr-item {
	/deep/ .ant-slider-mark-text {
		top: -34px;
	}
	&-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	&-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 5px;
		margin-bottom: 16px;
		min-height: 22px;
		border-radius: 1px;
		border: 1px solid #CCCCCC;
	}
}
.text-slider {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	align-content: center;
	align-items: center;
	margin: auto auto;
}
.text-card {
	width: 230px;
	height: 32px;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-content: center;
	align-items: center;
	h4 {
		font-weight: 600;
		margin-bottom: 0 !important;
	}
}
</style>
