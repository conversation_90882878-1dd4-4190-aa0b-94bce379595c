<template>
	<div id="userLayout" :class="['user-layout-wrapper']">
		<div class="container">
			<route-view></route-view>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 登录页用到
 * Updated by cjking on 2020/06/16.
 * Copyright 2020, Inc.
 * =================================== */
import { mixinNoScrollbar } from '@/mixins';
import RouteView from '@/components/layouts/RouteView';

export default {
	name: 'UserLayout',
	components: { RouteView },
	mixins: [mixinNoScrollbar],
	data () {
		return {};
	},
	mounted () {
		// document.body.classList.add('userLayout');
		// // 通过hook监听组件销毁钩子函数
		// this.$once('hook:beforeDestroy', () => {
		// 	document.body.classList.remove('userLayout');
		// });
	},
};
</script>

<style lang="less" scoped>
#userLayout.user-layout-wrapper {
	height: 100%;
	background: url(~@/assets/img/bg.png) no-repeat center center;
	background-size: cover;

	.container {
		height: 100vh;
		display: flex;
		flex-direction: row;
		justify-content: center;
		margin: 0 auto;

		a {
			text-decoration: none;
		}

		.top {
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont,
			"Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
			"Helvetica Neue", Helvetica, Arial, sans-serif,
			"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
			color: rgba(255, 255, 255, 1);

			.slider {
				height: 6em;
				font-size: 6em;
				font-weight: bold;
				line-height: 6em;
				padding: 0.1em;
			}

			.header {
				font-size: 3.2em;
				font-weight: bold;
				margin-top: 0.2em;

				.badge {
					position: absolute;
					display: inline-block;
					line-height: 1;
					vertical-align: middle;
					margin-left: -12px;
					margin-top: -10px;
					opacity: 0.8;
				}

				.logo {
					height: 44px;
					vertical-align: top;
					margin-right: 16px;
					border-style: none;
				}

				.title {
					font-size: 33px;
					font-weight: 600;
					position: relative;
					top: 2px;
				}
			}

			.desc {
				font-size: 1.25em;
			}
		}

		.main {
			align-self: center;
			padding: 35px 50px;
			background: rgba(255, 255, 255, 1);
			opacity: 0.95;
			border-radius: 4px;
		}
	}
}
</style>
