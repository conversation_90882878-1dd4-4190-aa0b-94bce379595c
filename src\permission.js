/* ===================================
 * 权限及路由控制
 * Created by cjking on 2021/04/09.
 * Copyright 2021, Inc.
 * =================================== */
import router from './router';
import store from './store';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import { ACCESS_TOKEN } from '@/constants';
import { storage } from '@/utils/storage';

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = [
	'/',
	'/user/login',
	'/user/register',
	'/user/forgetPwd',
	'/user/updatePwd',
]; // no redirect whitelist

router.beforeEach(async (to, from, next) => {
	NProgress.start(); // start progress bar

	if (to.path === '/middle') {
		next();
		NProgress.done();
		return;
	}

	/* has token */
	if (storage.get(ACCESS_TOKEN)) {
		if (to.path === '/user/login') {
			next({ path: '/' });
			NProgress.done();
		} else if (to.path === '/base/blank') {
			next();
			NProgress.done();
		} else {
			const moduleList = store.getters.moduleList;
			if (!moduleList?.length) {
				await store.dispatch('initPermissionList');
				if (!store.getters.moduleList?.length) {
					next({ path: '/base/blank' });
					NProgress.done();
					return;
				}
				const redirect = decodeURIComponent(
					from.query.redirect || to.path
				);
				if (to.path === redirect) {
					// hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
					next({ ...to, replace: true });
				} else {
					// 跳转到目的路由
					next({ path: redirect });
				}
			} else {
				if (to.path.match(/^\/$/)) {
					if (moduleList.find((i) => i.moduleId === '1')) {
						next({ path: '/mold' });
						NProgress.done();
					} else if (moduleList.find((i) => i.moduleId === '2')) {
						next({ path: '/texture' });
						NProgress.done();
					} else if (moduleList.find((i) => i.moduleId === '3')) {
						next({ path: '/shoe' });
						NProgress.done();
					}
				} else if (
					(to.path.match(/^\/mold/) &&
						!moduleList.find((i) => i.moduleId === '1')) ||
					(to.path.match(/^\/texture/) &&
						!moduleList.find((i) => i.moduleId === '2')) ||
					(to.path.match(/^\/shoe/) &&
						!moduleList.find((i) => i.moduleId === '3'))
				) {
					next({ path: '/base/blank' });
					NProgress.done();
				} else {

					next();
					NProgress.done();
				}
			}
			// } else {
			// 	if (
			// 		to.path.startsWith('/mold/project/detail') &&
			// 		to.params?.taskId
			// 	) {
			// 		// 检测是否正确路由(此处已检测任务是否存在为例)
			// 		const bool = await store.dispatch(
			// 			'checkTaskExisted',
			// 			to.params.taskId
			// 		);
			// 		if (bool) {
			// 			next();
			// 		} else {
			// 			next({
			// 				path: '/404',
			// 				query: { redirect: to.fullPath },
			// 			});
			// 			NProgress.done();
			// 		}
			// 	} else {
			// 		next();
			// 	}
			// }
		}
	} else {
		// 在免登录白名单，直接进入
		if (whiteList.includes(to.path)) {
			next();
			NProgress.done();
		} else {
			next({ path: '/user/login', query: { redirect: to.fullPath } });
			NProgress.done(); // if current page is login will not trigger afterEach hook, so manually handle it
		}
	}
	next();
});

router.afterEach(() => {
	NProgress.done(); // finish progress bar
});
