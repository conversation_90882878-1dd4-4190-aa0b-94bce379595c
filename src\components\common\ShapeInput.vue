<template>
	<div>
		<div class="attr-item">
			<div class="attr-item-title">
				<div>
					<label style="margin-right: 6px;">
						<b><span
							v-if="config.required"
							style="margin-left: -6px;color:red"
						>*</span>{{ label }}{{ label ? ':' : '' }}</b>
					</label>
					<a-tooltip v-if="config.tip" placement="top">
						<template slot="title">
							{{ config.tip }}
						</template>
						<a-icon type="question-circle"></a-icon>
					</a-tooltip>
				</div>

				<div class="attr-item-title-toolbar">
					<div v-if="!config.disableVisible" @click="handleVisibleChange">
						<svg-icon
							:type="config.visible ? 'param-visible' : 'param-hidden'"
							:class-name="config.visible ? 'param-visible' : 'param-hidden'"
							:vStyle="{width: '20px', height: '20px'}"
						/>
					</div>
					<input
						type="color"
						v-if="!config.disableColor"
						v-model="config.color"
						@input="handleColorChange"
					>
					<a-popover v-if="!config.disableOpacity" :title="null">
						<template #content>
							<div class="display-flex-center">
								<a-slider
									:min="0"
									:max="100"
									:step="1"
									v-model="config.opacity"
									:style="{ width: '100px', margin: '10px 0 0 10px' }"
									@change="handleOpacityChange"
								/>
								<div class="opacity">{{ config.opacity }}%</div>
							</div>
						</template>
						<svg-icon
							type="param-opacity"
							class-name="param-opacity"
							:vStyle="{width:'20px', height:'20px'}"
						/>
					</a-popover>
					<div :class="{'param-unactive': active !== name, 'param-active': active === name}" @click="handleParamActiveChange">
						<svg-icon
							v-if="isSurface"
							type="param-select-face"
							class-name="param-select-face"
							:vStyle="{width: '20px', height: '20px'}"
						/>
						<svg-icon
							v-if="isLine"
							type="param-select-line"
							class-name="param-select-line"
							:vStyle="{width: '20px', height: '20px'}"
						/>
						<svg-icon
							v-if="isPoint"
							type="param-select-point"
							class-name="param-select-point"
							:vStyle="{width: '20px', height: '20px'}"
						/>
					</div>
				</div>
			</div>

			<template v-if="name === 'ys_line'">
				<div
					v-for="(item, index) in config.b_line"
					:key="name + index"
					class="attr-item-content"
				>
					<div style="display:flex; justify-content:flex-start;align-items:center">
						<Tags
							v-if="item.key"
							:closable="false"
							:data-list="[item.key]"
							:active-color="config.color"
							:active-tag="activeTag === item.key ? activeTag : undefined"
							@click="(e)=> {handleClickTag(item.key, { index, type: 'ys_line'})}"
							@remove="handleRemoveTag"
						/>
						<a-button
							v-else
							type="default"
							size="small"
							icon="plus"
							@click="$event => {handleClickTag('', { index, type: 'ys_line'})}"
							:style="{fontSize: '12px', margin: '2px'}"
						>
							添加预设线
						</a-button>
						&nbsp;-&nbsp;
						<!-- <a-tag v-if="config.b_line && config.b_line[key] && config.b_line[key].length > 0">
							<JEllipsis
								:length="7"
								:value="(config.b_line && config.b_line[key] || []).join(',')"
							/>
						</a-tag> -->
						<Tags
							v-if="item.value"
							:dataList="[item.value.join(',')]"
							:active-tag="selectTag"
							@click="(e)=> {handleClickTag(item.value, { index, type: 'b_line'})}"
							:closable="false"
							:fullCharNum="7"
						/>
						<a-button
							v-else
							type="default"
							size="small"
							icon="plus"
							@click="()=>handleClickTag('', { index, type: 'b_line'})"
							:style="{fontSize: '12px', margin: '2px'}"
						>添加出入口边线</a-button>
					</div>
					<div class="attr-item-toolbar">
						<div @click="handleRemoveTag(key)">
							<svg-icon
								type="clear"
								class-name="clear"
								:vStyle="{width:'20px', height:'20px'}"
							/>
						</div>
					</div>
				</div>
				<div
					:key="name + 'n'"
					class="attr-item-content"
					:style="{justifyContent: 'center', alignItems: 'center'}"
				>
					<div>

						<a-button
							type="link"
							size="small"
							icon="plus"
							@click="$event => {handleAdd()}"
							:style="{fontSize: '12px', margin: '2px'}"
						>
							添加一组
						</a-button>

					</div>

					<div class="attr-item-toolbar"></div>
				</div>
			</template>
			<template v-else>
				<div class="attr-item-content">
					<div>
						<Tags
							v-if="config.tag !== false"
							:dataList="value || []"
							:active-tag="selectTag"
							@click="handleClickTag"
							@remove="handleRemoveTag"
						/>
						<Tags
							v-else-if="value && value.length > 0"
							:dataList="[value.join(',')]"
							:active-tag="selectTag"
							@click="handleClickTag"
							:closable="false"
							:fullCharNum="15"
						/>
					</div>
					<div class="attr-item-toolbar">
						<div @click="handleClear">
							<svg-icon
								type="clear"
								class-name="clear"
								:vStyle="{width:'20px',height:'20px'}"
							/>
						</div>
					</div>
				</div>
			</template>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/3/2.
 * Copyright 2022, Inc.
 * =================================== */
import { deepCopy } from '@/utils/utils';
import JEllipsis from './JEllipsis.vue';
import Tags from '@/components/common/Tags';

export default {
	components: {
		Tags,
		// JEllipsis,
	},
	props: {
		label: {
			type: String,
			required: true,
		},
		config: {
			type: Object,
			required: false,
			default: () => {},
		},
		name: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: [Array, Object, String, Boolean, Number, null],
			required: false,
			default: null,
		},
		active: {
			type: String,
			required: false,
			default: '',
		},
		activeTag: {
			type: String,
			required: false,
			default: '',
		},
	},
	data () {
		return {
			selectTag: '',
			fieldValue: undefined,
			fieldConfig: {},
		};
	},
	computed: {
		isSwitch () {
			return this.config.type === 'switch';
		},
		isSlider () {
			return this.config.type === 'slider';
		},
		isShape () {
			return this.config.type === 'shape';
		},
		isSurface () {
			return this.config.mode === 'surface';
		},
		isLine () {
			return this.config.mode === 'line';
		},
		isPoint () {
			return this.config.mode === 'point';
		},
	},
	watch: {
		value: {
			deep: true,
			immediate: true,
			handler (newVal) {
				this.fieldValue = newVal;
			},
		},
		activeTag: {
			immediate: true,
			handler (newVal) {
				// 只有当前参数激活的时候才显示激活tag
				if (this.active === this.name) {
					this.selectTag = newVal;
				} else {
					this.selectTag = '';
				}
			},
		},
		active: {
			immediate: true,
			handler (newVal) {
				// 只有当前参数激活的时候才显示激活tag
				if (newVal !== this.name) {
					this.selectTag = '';
				}
			},
		},
		config: {
			deep: true,
			immediate: true,
			handler (newVal) {
				this.fieldConfig = newVal;
			},
		},
	},
	methods: {
		handleColorChange (e) {
			this.config.color = e.target.value;
			if (this.config.type === 'shape') {
				this.$emit('configChange', this.name);
			}
		},
		handleOpacityChange (newVal) {
			this.config.opacity = newVal;
			if (this.config.type === 'shape') {
				this.$emit('configChange', this.name);
			}
		},
		handleVisibleChange () {
			this.config.visible = !this.config.visible;
			if (this.config.type === 'shape') {
				this.$emit('configChange', this.name);
			}
		},
		handleParamActiveChange () {
			this.$emit('activeChange', this.name);
		},
		handleValueChange (value) {
			let val;
			if (typeof value === 'number' || typeof value === 'boolean') {
				val = value;
			} else if (typeof value === 'string') {
				val = Number(value);
			} else if (value?.target?.value) {
				val = Number(value?.target?.value);
			}
			this.$emit('valueChange', { value: val, name: this.name });
		},
		handleClear () {
			const oldValue = deepCopy(this.fieldValue);
			this.fieldValue = [];
			this.$emit('valueChange', { value: this.fieldValue, name: this.name });
		},
		handleClickTag (tag, extra) {
			this.selectTag = tag;
			this.$emit('clickTag', { name: this.name, nickname: tag, extra });
		},
		handleRemoveTag (tag) {
			this.$emit('removeTag', { name: this.name, nickname: tag });
		},
		handleAdd () {
			this.$emit('addYsLineGroup');
		}
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.attr-item {
	/deep/ .ant-slider-mark-text {
		top: -34px
	}
	&-title, &-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	&-title-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		> * {
			margin-left: 10px;
		}
	}
	&-content {
		background: #F8F8F8;
		padding: 10px;
		min-height: 40px;
	}
	&-content2 {
		padding: 10px;
		height: 40px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}
	&-icon {
		width: 20px;
		height: 20px;
	}
	&-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.svg-icon, input {
			margin-left: 10px;
			cursor: pointer;
		}
	}
	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}
	.svg-icon {
		color: @primary;
	}
}

.param-unactive {
	border: solid rgba(0, 0, 0, 0) 1px;
}

.param-active {
	border: 1px solid @primary;
	border-radius: 2px;
}

.ant-tag {
	border-radius: 2px;
	margin-top: 2px;
	margin-bottom: 2px;
	margin-right: 4px;
	height: 24px;
	line-height: 24px;
}
</style>
