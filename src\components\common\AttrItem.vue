<template>
	<div>
		<div class="attr-item">

			<label><b>{{ label }}</b></label>

			<a-switch v-if="isSwitch" v-model="fieldValue" />

			<div v-else-if="isSlider" class="isSlider">
				<a-slider
					v-model="fieldValue"
					:min="config.min"
					:max="config.max"
					:step="config.step"
					:marks="{
						[config.min+'']: config.min,
						[config.max+'']: config.max
					}"
					:style="{ width: '100px', margin: '14px 14px 0 0' }"
				/>
				<a-input-number
					style="width:60px;"
					:min="config.min"
					:max="config.max"
					:step="config.step"
					v-model="fieldValue"
				/>
			</div>

			<input type="color" v-else-if="isColor" v-model="fieldValue">

			<div v-else-if="isText">
				<JEllipsis v-if="fieldValue" :value="fieldValue" :length="10" />
			</div>

			<div v-else-if="isObject" class="attr-item-toolbar">
				<div @click="handleDownload">
					<svg-icon
						v-if="downloadUrl"
						type="download"
						class-name="download"
						:vStyle="{width:'20px', height:'20px'}"
					/>
				</div>
				<div @click="handleVisibleChange">
					<svg-icon
						:type="fieldValue.visible ? 'param-visible' : 'param-hidden'"
						:class-name="fieldValue.visible ? 'param-visible' : 'param-hidden'"
						:vStyle="{width:'20px', height:'20px'}"
					/>
				</div>
				<input type="color" v-model="fieldValue.color">
				<a-popover :title="null" placement="topRight">
					<template #content>
						<div class="display-flex-center">
							<a-slider
								:min="0"
								:max="100"
								:step="1"
								v-model="fieldValue.opacity"
								:style="{ width: '100px', margin: '10px 0 0 10px' }"
							/>
							<div class="opacity">{{ fieldValue.opacity }}%</div>
						</div>
					</template>
					<svg-icon
						type="param-opacity"
						class-name="param-opacity"
						:vStyle="{width:'20px',height:'20px'}"
					/>
				</a-popover>
			</div>

		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/3/2.
 * Copyright 2022, Inc.
 * =================================== */
import JEllipsis from '@/components/common/JEllipsis';
import { downloadFileByNewWindow } from '@/utils/utils';

const defaultConfig = {
	min: 0,
	max: 100,
	step: 1,
};

export default {
	components: {
		JEllipsis,
	},
	props: {
		label: {
			type: String,
			required: true,
		},
		type: {
			type: String,
			required: false,
			default: 'switch',
		},
		url: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: [Array, Object, String, Boolean, Number],
			required: true,
		},
		config: {
			type: Object,
			required: false,
			default: () => {
				return defaultConfig;
			},
		},
	},
	data () {
		return {
			fieldValue: 0,
			downloadUrl: '',
		};
	},
	computed: {
		isSwitch () {
			return this.type === 'switch';
		},
		isSlider () {
			return this.type === 'slider';
		},
		isText () {
			return this.type === 'text';
		},
		isObject () {
			return this.type === 'object';
		},
		isColor () {
			return this.type === 'color';
		},
	},
	created () {
		this.fieldValue = this.value;
	},
	model: {
		event: 'change',
	},
	watch: {
		value: {
			deep: true,
			immediate: true,
			handler (val) {
				this.fieldValue = val;
			},
		},
		fieldValue: {
			deep: true,
			handler (val) {
				this.$emit('change', val);
			},
		},
		url: {
			immediate: true,
			handler (val) {
				this.downloadUrl = val;
			},
		},
	},
	methods: {
		/**
		 * 可见处理
		 */
		handleVisibleChange () {
			this.fieldValue.visible = !this.fieldValue.visible;
		},

		/**
		 * 文件下载处理
		 */
		handleDownload () {
			downloadFileByNewWindow(this.downloadUrl, this.label + this.downloadUrl.replace(/.*\./g, '.'));
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.attr-item {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin-top: 10px;
	margin-bottom: 10px;
	/deep/ .ant-slider-mark-text {
		top: -34px
	}
	&-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.svg-icon, input[type="color"] {
			margin-left: 10px;
			color: @primary;
			cursor: pointer;
		}
	}
	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}

	.isSlider {
		width: 180px;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
	}
}
</style>
