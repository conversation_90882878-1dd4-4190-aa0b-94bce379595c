/* ===================================
 * WebSocket
 * Created by cjking on 2021/05/10.
 * Copyright 2021, Inc.
 * =================================== */
import Config from '@/config/Config';
import { isObject } from '@/utils/utils';
import { logger } from '@/utils/logger';
import { storage } from '@/utils/storage';
import { ACCESS_TOKEN } from '@/constants';

const WsStatusEnum = {
	CONNECTING: 0,  // 值为0，表示正在连接；
	OPEN: 1,        // 值为1，表示连接成功，可以通信了；
	CLOSING: 2,     // 值为2，表示连接正在关闭；
	CLOSED: 3,      // 值为3，表示连接已经关闭，或者打开连接失败。
};

export const mixinWebSocket = {
	data () {
		return {
			wsInstance: null,
			wsHeartFlag: false, // 心跳标记
			reconnectCount: 0,  // 重连次数
			MAX_CONNECT: 3,     // 最大重连次数
			wsUrl: Config.wsUrl,
		};
	},
	methods: {
		async initWebsocket (key) { // 初始化WebSocket
			return new Promise(resolve => {
				if (window.WebSocket) {
					const url = this.wsUrl + '/webSocket/' + key + '/' + storage.get(ACCESS_TOKEN);
					this.wsInstance = new WebSocket(url);
					this.wsInstance.__CB_KEY__ = key;
					this.wsInstance.onopen = () => this.onOpen(resolve);
					this.wsInstance.onmessage = this.onMessage;
					this.wsInstance.onerror = this.onError;
					this.wsInstance.onclose = this.onClose;
					this.timingHeart();
				}
			});
		},
		onOpen (resolve) {
			logger.log(`Websocket [CLIENT] OPEN`);
			// 状态判断，readyState等于1的时候建立链接成功
			if (this.wsInstance?.readyState === WsStatusEnum.OPEN) {
				this.wsHeartFlag = true;
				this.reconnectCount = 0;
				resolve(this);
			}
		},
		send (params) {
			// 状态判断，当为OPEN时，发送消息
			if (this.wsInstance?.readyState === WsStatusEnum.OPEN) {
				if (isObject(params)) {
					params = JSON.stringify(params);
				}
				this.wsInstance.send(params);
			}
		},
		timingHeart () {
			// 心跳检测，每1分钟发送一次
			if (this.wsHeartFlag) {
				this.wsInstance.send('Heart');
			}
			this.wsHeart = setTimeout(() => {
				this.timingHeart();
			}, 50 * 1000);
		},
		onMessage (evt) { // 数据接收
			let receivedData;
			try {
				receivedData = JSON.parse(evt?.data);
			} catch (e) {
				receivedData = evt;
			}
			try {
				if (receivedData.success && receivedData.result) {
					receivedData.result = JSON.parse(receivedData.result);
				}
			} catch (e) {}
			logger.log('[CLIENT] Received: ', receivedData);

			if (this.wsInstance?.__CB_KEY__) {
				this.$root.$emit(`${ this.wsInstance.__CB_KEY__ }Callback`, receivedData);
			}
		},
		onError () {
			// 链接失败，进行重连
			clearTimeout(this.wsHeart);
			this.wsHeartFlag = false;
			if (this.reconnectCount <= this.MAX_CONNECT) {
				setTimeout(() => {
					this.onOpen();
					this.reconnectCount += 1;
				}, 5000);
			} else {
				this.$message.error('抱歉，暂时无法连接到服务器，请稍后再试');
				this.onClose();
			}
		},
		onClose (type = '') {
			this.wsHeartFlag = false;
			clearTimeout(this.wsHeart);
			if (this.wsInstance) {
				this.wsInstance?.close();
				this.wsInstance = null;
				logger.log('[CLIENT] Close [', type, ']');
			}
		},
	},
};
