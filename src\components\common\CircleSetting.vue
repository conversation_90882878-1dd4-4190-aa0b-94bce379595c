<template>
	<a-card :title="title" :bodyStyle="{width: '240px'}" @keydown.8.stop>
		<div class="base-wrapper">
			<label for=""><b>半径</b></label>
			<a-input-number v-model="formData.radius" placeholder="请输入" :min="0" :max="1000000" class="m-input"></a-input-number>
		</div>
	</a-card>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			required: true,
		},
		radius: {
			type: Number,
			default: 10
		},
	},
	data () {
		return {
			formData: {
				radius: 10,
			}
		};
	},
	mounted () {
		this.formData = Object.assign(this.formData, {
			radius: Math.abs(this.radius),
		});
		this.$emit('change', this.formData);
	},
	watch: {
		formData: {
			handler (val) {
				this.$emit('change', val);
			},
			deep: true,
		},
	}
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/base.less';
@import '~@/assets/styles/mixins/variables.less';

.ant-card {
	/deep/ .ant-card-head {
		padding: 0 20px;
	}
	/deep/ .ant-card-body {
		padding: 10px 20px
	}

	/deep/ .ant-card-head-title {
		padding-top: 12px;
		padding-bottom: 12px;
	}
}

.base-wrapper {
	display:flex;
	justify-content:space-between;
	align-items:center;
	margin-top:10px;
	margin-bottom:10px;
}

.m-input {
	flex: 1;
	margin-left: 8px;
}

.ml-0 {
	margin-left: 0
}
</style>
