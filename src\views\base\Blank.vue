<template>
	<div class="flex-center">
		<h2>您的企业没有授权模块</h2>
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
	components: {},
	data () {
		return {

		};
	},
	computed: {
		...mapGetters(['userInfo', 'moduleList']),
	},
	async created () {
		if (!this.userInfo) {
			this.$router.push('/user/login');
			return;
		}
		await this.initPermissionList();
		if (this.moduleList.length > 0) {
			this.$confirm({
				title: '提示',
				content: '是否刷新页面跳转到其他模块?',
				okText: '确认',       // 确认按钮文字
				cancelText: '取消',   // 取消按钮文字
				onOk: () => {
					if (this.moduleList[0].moduleId === '1') {
						this.$router.push('/mold');
					} else if (this.moduleList[0].moduleId === '2') {
						this.$router.push('/texture');
					} else if (this.moduleList[0].moduleId === '3') {
						this.$router.push('/shoe');
					}
				},
			});

		}
	},
	methods: {
		...mapActions(['initPermissionList']),
	},
};
</script>

<style lang="less" scoped>
.big-text {
	font-size: 18px;
    font-weight: 500;
}

.red-text {
	color: red;
	margin-left: 40px;
}

.flex-center {
    height: calc(100vh - 80px);
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
