<template>
	<a-layout-header class="global-header" :class="size === 'lg' ? 'active' : ''">
		<div class="header-box">
			<div class="grid">
				<div class="grid-cell" style="flex: 0 0 auto; display: flex; justify-content: flex-start;align-items: left;">
					<slot name="leftAction"></slot>
					<slot name="logo">
						<div class="logo">
							<img :src="logoImg" @click="toHome">
							<span style="display: inline-block; font-weight: bold; font-size: 16px; margin-left: 30px;">{{ userInfo.departName }}工作台</span>
						</div>
					</slot>
				</div>
				<div class="grid-cell" :style="{ display: 'flex', 'align-items': 'center', 'justify-content': justifyContent, 'margin-left':'112px'}">
					<slot name="title">
						<div class="navigation-container">
							<div class="navigation-container-item" v-if="hasModule(1)" :class="{ 'disabled': false }">
								<div class="menu" :class="{'active': curMenu === ModuleEnum.mold}" @click="jumpView(ModuleEnum.mold, '/mold')">
									<svg-icon type="head-mold" class-name="head-mold"></svg-icon>
									<span class="text">模具制造</span>
								</div>
							</div>
							<div class="navigation-container-item" v-if="hasModule(2)" :class="{ 'disabled': false }">
								<div class="menu" :class="{'active': curMenu === ModuleEnum.texture}" @click="jumpView(ModuleEnum.texture, '/texture')">
									<svg-icon type="head-texture" class-name="head-texture"></svg-icon>
									<span class="text">数字纹理</span>
								</div>
							</div>
							<div class="navigation-container-item" v-if="hasModule(3)" :class="{ 'disabled': false }">
								<div class="menu" :class="{'active': curMenu === ModuleEnum.shoe}" @click="jumpView(ModuleEnum.shoe, '/shoe')">
									<svg-icon type="head-shoe" class-name="head-shoe"></svg-icon>
									<span class="text">客制化鞋</span>
								</div>
							</div>
							<!-- <div class="navigation-container-item" :class="{ 'disabled': false }">
								<div class="menu">
									<svg-icon type="head-workspace" class-name="head-workspace"></svg-icon>
									<span class="text">工作空间</span>
								</div>
							</div>
							<div class="navigation-container-item" :class="{ 'disabled': false }">
								<div class="menu">
									<svg-icon type="head-setting" class-name="head-setting"></svg-icon>
									<span class="text">企业管理</span>
								</div>
							</div>
							<div class="navigation-container-item" :class="{ 'disabled': false }">
								<div class="menu">
									<svg-icon type="head-stat" class-name="head-stat"></svg-icon>
									<span class="text">使用量统计</span>
								</div>
							</div> -->
						</div>
					</slot>
				</div>
				<div class="grid-cell" style="flex: 0 0 380px;">
					<slot name="content"></slot>
					<div class="right">
						<div class="btn-group" v-if="!isLogin">
							<a-button type="primary" @click="toRegister">立即注册</a-button>
							<a-button type="default" @click="toLogin">登录</a-button>
						</div>
						<template v-else>
							<a-dropdown overlay-class-name="header_dropdown" placement="bottomCenter">
								<div class="user" style="display: flex;">
									<icon-menu :img-src="userImg" :v-style="{ width: '40px', height: '40px' }" />
									<div style="margin-left: 10px; font-size: 16px">{{ userInfo.username }}</div>
								</div>
								<a-menu slot="overlay">
									<!-- <a-menu-item key="0">
										<div style="padding: 0 20px;">
											<span>{{ userInfo.username }}</span>
										</div>
									</a-menu-item>
									<a-menu-divider />
									<a-menu-item key="4" v-href="'/mold/project/list'">
										<div style="padding: 0 20px;">
											<a-icon type="user" style="margin-right: 5px;" />
											<span>个人中心</span>
										</div>
									</a-menu-item> -->

									<a-menu-item key="5" @click="showUpdatePwdModal">
										<div style="padding: 0 20px;">
											<a-icon type="setting" style="margin-right: 5px;" />
											<span>修改密码</span>
										</div>
									</a-menu-item>
									<a-menu-item key="6" v-if="userInfo.roleCodes === 'customerAdmin'" @click="showAuthorizeModal">
										<div style="padding: 0 20px;">
											<a-icon type="wallet" style="margin-right: 5px;" />
											<span>企业套餐</span>
										</div>
									</a-menu-item>
									<a-menu-item key="7" @click="handleLogout">
										<div style="padding: 0 20px;">
											<a-icon type="logout" style="margin-right: 5px;" />
											<span>退出登录</span>
										</div>
									</a-menu-item>
								</a-menu>
							</a-dropdown>
						</template>
					</div>
				</div>
			</div>
			<slot name="navigation"></slot>
		</div>

		<!-- 修改密码弹窗 -->
		<UpdatePwdModal
			ref="updatePwdModal"
			v-if="updatePwdModalVisible"
			@close="() => this.updatePwdModalVisible = false"
			@ok="() => this.updatePwdModalVisible = false"
		/>

	</a-layout-header>
</template>

<script>
/* ===================================
 * 全局-公共页头
 * Created by cjking on 2021/04/08.
 * Copyright 2021, Inc.
 * =================================== */
import { mapActions, mapGetters } from 'vuex';
import { storage } from '@/utils/storage';
import { logger } from '@/utils/logger';
import router from '@/router';
import userImg from '@/assets/img/user.png';
import logoImg from '@/assets/img/logo2.png';
import IconMenu from '@/components/common/IconMenu';
import UpdatePwdModal from '@/views/user/UpdatePwdModal';
import { ModuleEnum, USER_INFO } from '@/constants';

export default {
	name: 'GlobalHeader',
	components: {
		IconMenu,
		UpdatePwdModal,
	},
	props: {
		size: {
			type: String,
			required: false,
			default: 'lg',
		},
		menu: {
			type: String,
			required: false,
			default: '',
		},
		justifyContent: {
			type: String,
			required: false,
			default: 'flex-start',
		}
	},
	data () {
		return {
			userImg,
			logoImg,
			ModuleEnum,
			curMenu: '',
			notifyDataList: [],     // 消息列表
			notifyCount: 0,         // 消息通知数量
			notifyVisible: false,    // 是否显示消息信息
			updatePwdModalVisible: false,    // 是否显示修改密码弹窗
		};
	},
	watch: {
		menu: {
			immediate: true,
			handler () {
				this.curMenu = this.menu;
			},
		},
	},
	computed: {
		...mapGetters(['isLogin', 'userInfo', 'moduleList']),
	},
	async mounted () {
		await this.initPermissionList();
		if (this.moduleList.length === 0) {
			this.$router.push('/base/blank');
		}
		window.oldUsername = this.userInfo.username;
		document.addEventListener('visibilitychange', () => {
			if (document.visibilityState === 'visible') {
				const newUsername = storage.get(USER_INFO)?.username;
				if (window.oldUsername !== newUsername) {
					// logger.log('visibilitychange', window.oldUsername, newUsername);
					this.$router.push('/');
					location.reload();
				}
			}
		});
	},
	methods: {
		...mapActions(['Logout', 'initPermissionList']),

		/**
		 * 去登录页
		 */
		toLogin () {
			this.$router.push('/user/login');
		},

		/**
		 * 去首页
		 */
		toHome () {
			this.$router.push('/');
		},

		/**
		 * 去注册页
		 */
		toRegister () {
			this.$router.push('/user/register');
		},

		/**
		 * 显示更新密码弹窗
		 */
		showUpdatePwdModal () {
			this.updatePwdModalVisible = true;
			this.$nextTick(() => {
				this.$refs.updatePwdModal.showModal();
			});
		},

		showAuthorizeModal () {
			this.$router.push('/base/authorize');
		},

		/**
		 * 注销登录
		 */
		handleLogout () {
			this.$confirm({
				title: '提示',
				content: '真的要注销登录吗 ?',
				okText: '确认',       // 确认按钮文字
				cancelText: '取消',   // 取消按钮文字
				onOk: () => {
					this.Logout().then(() => {
						router.push('/user/login');
					}).catch(err => {
						this.$message.error({
							title: '错误',
							description: err?.message,
						});
					});
				},
			});
		},

		/**
		 * 跳转视图
		 * @param menu
		 * @param path
		 */
		jumpView (menu, path) {
			this.curMenu = menu;
			this.$router.push(path);
		},
		/**
		 * 判断模块权限
		 * @param id
		 */
		hasModule (id) {
			return this.moduleList.some(i => i.moduleId === id + '');
		}
	},
	beforeDestroy () {
		document.removeEventListener('visibilitychange', () => {});
	}
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/grid.less";
@import "~@/assets/styles/mixins/variables.less";

.global-header {
	position: fixed;
	z-index: 1;
	width: 100%;
	height: 50px !important;
	padding: 0 !important;
	background: #FFFFFF !important;
	box-shadow: 0 2px 10px 0 rgba(19, 42, 111, 0.16);

	&.active {
		height: 70px !important;
		.header-box {
			height: 70px;
			line-height: 70px;

			.btn-group {
				height: 70px;
				line-height: 70px;
			}
		}
		.grid {
			.grid-cell {
				height: 70px;
				line-height: 70px;
			}
		}
	}

	.header-box {
		height: 50px;
		line-height: 50px;
		max-width: 1600px;
		margin: 0 auto;

		.btn-group {
			height: 50px;
			line-height: 50px;

			button {
				width: 100px;
				height: 32px;
				&:nth-child(2) {
					margin-left: 10px;
				}
			}
		}
	}

	.grid {
		.grid-cell {
			height: 50px;
			line-height: 50px;

			.logo {
				margin-left: 16px;
				img {
					height: 36px;
					cursor: pointer;
				}
			}

			.right {
				display: flex;
				justify-content: flex-end;
				margin-right: 16px;
				width: auto;
				/deep/ .ant-btn {
					height: 40px;
				}
			}
		}
	}

	.navigation {
		&-container {
			display: flex;

			&-item:nth-child(n+2) {
				margin-left: 40px;
			}

			.menu {
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				align-items: center;
				cursor: pointer;
				.mold {
					color: #000;
					top: -1px;
					font-size: 16px;
					position: relative;
				}
				.text {
					font-size: 16px;
					font-weight: bold;
					margin-left: 2px;
					white-space: nowrap;
				}
				&:hover, &.active {
					.text {
						color: @primary;
					}
					.svg-icon {
						color: @primary;
					}
				}
				&.active {
					.text {
						color: @primary;
					}
					.svg-icon {
						color: @primary;
					}
				}
			}
		}

		&.disabled {
			.menu {
				cursor: not-allowed;
				.text {
					color: @disabled-text;
				}
			}
		}
	}
}

.header_dropdown {
	min-width: 246px !important;

	/deep/ .ant-divider-horizontal {
		margin: 5px 0 !important;
	}

	/deep/ .ant-dropdown-menu-item {
		div, span {
			color: #333333;
		}
		&:first-child:hover {
			background: none;
			cursor: default;
		}
		&:nth-child(n+2):hover {
			div, span {
				color: @primary;
			}
		}
	}
}

.more {
	.flex-display();
	.flex-justify-content(flex-end);
}

.divider {
	margin: 10px 0 !important;
}

.icon-menu {
	height: auto !important;
}
</style>
