<template>
	<a-modal v-model="modalVisible" class="clipping-card-modal" :width="360" :title="title" @cancel="close" :footer="null">
		<!-- 内容区域 -->

		<!-- <attr-item label="显示/隐藏" type="switch" v-model="visible" />
					<attr-item label="透明度" type="slider" v-model="opacity" />
					<attr-item label="颜色" type="color" v-model="color" /> -->
		<attr-item label="X方向切面" type="slider" :config="{min: 0, max: 100, step: 1}" v-model="clipping.clippingX" />
		<attr-item label="Y方向切面" type="slider" :config="{min: 0, max: 100, step: 1}" v-model="clipping.clippingY" />
		<attr-item label="Z方向切面" type="slider" :config="{min: 0, max: 100, step: 1}" v-model="clipping.clippingZ" />

		<div style="display:flex;justify-content:space-between;align-items:center;margin-top:10px;margin-bottom:10px">
			<label for=""><b>法向量</b></label>
			<a-radio-group
				v-model="clippingValue.direction"
				button-style="solid"
				size="small"
				@change="handleRadioChange"
			>
				<a-radio-button value="x">
					X
				</a-radio-button>
				<a-radio-button value="y">
					Y
				</a-radio-button>
				<a-radio-button value="z">
					Z
				</a-radio-button>
			</a-radio-group>
			<label for=""><b>反转</b></label>
			<a-switch v-model="clippingValue.reverse" @change="handleReverseChange"></a-switch>
		</div>
		<attr-item ref="attrItemX" label="X" type="slider" :config="{min: -1, max: 1, step: 0.01}" v-model="clippingValue.x" @change="handleXChange" />
		<attr-item ref="attrItemY" label="Y" type="slider" :config="{min: -1, max: 1, step: 0.01}" v-model="clippingValue.y" @change="handleYChange" />
		<attr-item ref="attrItemZ" label="Z" type="slider" :config="{min: -1, max: 1, step: 0.01}" v-model="clippingValue.z" @change="handleZChange" />
		<div style="display:flex;justify-content:center">
			<a-button type="primary" @click="reset">重置参数</a-button>
		</div>
	</a-modal>
</template>

<script>
/* ===================================
 * 属性面板
 * Created by zhangzheng on 2022/3/2.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { deepCopy, flatten, isBase64, noop } from '@/utils/utils';
import AttrItem from '@/components/common/AttrItem.vue';

export default {
	name: 'CollapseTree',
	components: {
		AttrItem
	},
	props: {
		title: {
			type: String,
			required: true,
		},
		activeKeys: {
			type: Array,
			required: false,
			default: () => [],
		},
		panelKey: {
			type: String,
			required: true,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabledRightMenu: {
			type: Boolean,
			required: false,
			default: false,
		},
	},
	data () {
		return {
			modalVisible: false,
			visible: true,
			opacity: 100,
			color: '#999999',
			clipping: {
				clippingX: 100,
				clippingY: 100,
				clippingZ: 100,
				xx: -1,
				xy: 0,
				xz: 0,
				yx: 0,
				yy: -1,
				yz: 0,
				zx: 0,
				zy: 0,
				zz: -1,
			},
			clippingReverse: {
				x: false,
				y: false,
				z: false,
			},
			clippingValue: {
				direction: 'x',
				reverse: false,
				x: -1,
				y: 0,
				z: 0
			},
			innerDataList: [],
			innerActiveKeys: [],
		};
	},
	watch: {
		activeKeys: {
			immediate: true,
			handler (keys) {
				this.$nextTick(() => {
					if (this.defaultExpandAll) {
						this.innerActiveKeys = [this.panelKey];
					} else {
						this.innerActiveKeys = keys?.length ? keys : [];
					}
				});
			},
		},
		visible: {
			immediate: true,
			handler (newVal) {
				logger.log('changeVisible: ', newVal);
				this.$emit('changeVisible', newVal);
			},
		},
		opacity: {
			immediate: true,
			handler (newVal) {
				logger.log('changeOpacity: ', newVal);
				this.$emit('changeOpacity', newVal);
			},
		},
		color: {
			immediate: true,
			handler (newVal) {
				logger.log('changeColor: ', newVal);
				this.$emit('changeColor', newVal);
			},
		},
		clipping: {
			immediate: true,
			deep: true,
			handler (newVal) {
				// logger.log('changeClipping: ', { clip: newVal });
				this.$emit('changeClipping', { clip: newVal, clippingReverse: this.clippingReverse });
			}
		},
		clippingReverse: {
			deep: true,
			handler (newVal) {
				// logger.log('changeClipping: ', { clip: this.clipping, clippingReverse: newVal });
				this.$emit('changeClipping', { clip: this.clipping, clippingReverse: newVal });
			}
		}
	},
	methods: {
		open () {
			this.modalVisible = true;
		},
		close () {
			this.modalVisible = false;
		},
		/**
		 * 初始化数据
		 */
		initData (sourceList) {
			const tmpSourceList = deepCopy(sourceList);
			this.innerDataList = tmpSourceList;
		},
		/**
		 * 防止默认值
		 */
		preventDefaults (e) {
			e?.preventDefault();
			e?.stopPropagation();
		},
		/**
		 * 选择法向量
		 */
		handleRadioChange ({ target: { value } }) {
			this.$nextTick(() => {
				if (value) {
					this.clippingValue = Object.assign(this.clippingValue, {
						x: this.clipping[value + 'x'],
						y: this.clipping[value + 'y'],
						z: this.clipping[value + 'z'],
						reverse: this.clippingReverse[value],
					});
					this.$refs.attrItemX.fieldValue = this.clipping[value + 'x'];
					this.$refs.attrItemY.fieldValue = this.clipping[value + 'y'];
					this.$refs.attrItemZ.fieldValue = this.clipping[value + 'z'];

				}
			});
		},
		/**
		 * 反转
		 */
		handleReverseChange (value) {
			this.$nextTick(() => {
				this.clippingReverse[this.clippingValue.direction] = value;
			});
		},
		/**
		 * X值
		 */
		handleXChange (val) {
			this.$nextTick(() => {
				if (this.clippingValue.direction) {
					this.clipping[this.clippingValue.direction + 'x'] = val;
				}
			});
		},
		/**
		 * Y值
		 */
		handleYChange (val) {
			this.$nextTick(() => {
				if (this.clippingValue.direction) {
					this.clipping[this.clippingValue.direction + 'y'] = val;
				}
			});
		},
		/**
		 * Z值
		 */
		handleZChange (val) {
			this.$nextTick(() => {
				if (this.clippingValue.direction) {
					this.clipping[this.clippingValue.direction + 'z'] = val;
				}
			});
		},
		/**
		 * 重置
		 */
		reset () {
			this.$nextTick(() => {
				this.clipping = {
					clippingX: 100,
					clippingY: 100,
					clippingZ: 100,
					xx: -1,
					xy: 0,
					xz: 0,
					yx: 0,
					yy: -1,
					yz: 0,
					zx: 0,
					zy: 0,
					zz: -1,
				};
				this.clippingReverse = {
					x: false,
					y: false,
					z: false,
				};
				this.clippingValue = {
					direction: 'x',
					reverse: false,
					x: -1,
					y: 0,
					z: 0
				};
			});

		}
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/mixins/variables.less";

.collapse-attr {

	/deep/ .ant-collapse > .ant-collapse-item {
		border-bottom: 3px solid #E5E5E5;
		.ant-collapse-header {
			border-left: 2px solid @primary;
			border-radius: 0;
			background: #F9F9FF;
			padding: 9px 16px;
			padding-right: 40px;
		}
	}

	.collapse-panel {

		.disable-selection();

		img {
			width: 20px;
			.disable-selection();

			&.w18 {
				width: 18px;
				height: 18px;
			}
		}

		.collapse-panel-header {
			display: flex;
			align-items: center;
			width: 280px;
			.icon {
				flex: 0 0 20px;
			}
			.title {
				flex: 1;
				margin-left: 8px;
				font-size: 16px;
				font-weight: bold;
				.disable-selection();
			}
			.extra {
				flex: 0 0 70px;
				text-align: end;
				.disable-selection();
			}
		}

		.tree-node-custom {
			display: flex;

			&.parent {
				width: 257px;
			}

			&.child {
				width: 231px;
			}

			.icon {
				flex: 0 0 20px;
			}
			.title {
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				&.ml8 {
					margin-left: 8px;
				}

				span {
					display: block;
					cursor: pointer;
					color: #000000;
					.disable-selection();
				}

				.opacity65 {
					color: rgba(0, 0, 0, 0.65);
				}

				.selected {
					color: @primary;
				}
			}
			.icon-actions {
				flex: 0 0 70px;
				text-align: end;

				img {
					margin-top: -2px;
					margin-right: 5px;
					cursor: pointer;

					&:last-child {
						margin-right: 0;
					}
				}
			}
		}
	}

	.line-vertical {
		position: absolute;
		left: 12px;
		top: -4px;
		width: 1px;
		height: 32px;
		background: rgba(58, 58, 58, 0.5);
		&.last {
			top: -8px;
			height: 20px;
		}
	}
	.line-horizontal {
		position: absolute;
		left: 12px;
		top: 12px;
		width: 14px;
		height: 1px;
		background: rgba(58, 58, 58, 0.5);
	}
}
</style>
