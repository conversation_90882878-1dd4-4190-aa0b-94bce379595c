<template>
	<div class="attr-item">
		<div class="attr-item-title" v-if="label">
			<div>
				<label style="margin-right: 6px;">
					<b><span
						v-if="required"
						style="margin-left: -6px;color:red"
					>*</span>{{ label }}{{ label ? ':' : '' }}</b>
				</label>
				<a-tooltip v-if="tip" placement="top">
					<template slot="title">
						{{ tip }}
					</template>
					<a-icon type="question-circle"></a-icon>
				</a-tooltip>
			</div>
		</div>
		<div class="attr-item-content">
			<div class="image-slider">
				<a-icon
					v-show="fieldValue > 0"
					type="left"
					@click="(e) => { handleValueChange(e, fieldValue - 1)}"
				/>
				<a-icon
					v-show="fieldValue === 0"
					type="left"
					:style="{color: '#cccccc'}"
				/>
				<a-popover v-model="visible" :destroyTooltipOnHide="true" :arrowPointAtCenter="true" trigger="click" :overlayStyle="{maxWidth: '960px'}" placement="right">
					<template slot="title">
						<div style="display:flex;justify-content:space-between;align-content:center;align-items:center;line-height: 36px">
							<b>请选择单元变种</b>
							<a-icon type="close" @click="visible=false" :style="{color: '#aaaaaa'}"></a-icon>
						</div>
					</template>
					<template slot="content">
						<div class="dropdown-content">
							<a-list :grid="{ gutter: 0, column: 3 }" :pagination="false" :data-source="dataSource">
								<a-list-item slot="renderItem" slot-scope="item">
									<div
										:class="{ 'image-card2': true, 'image-card2-active': item.title === selectedValue.title }"
										@click="(e) => { if (!item.disabled)handleValueChange(e, item.value) }"
									>
										<img :src="item.url" alt="" :style="{ opacity: item.disabled ? 0.5 : 1}">
										<div :style="{ opacity: item.disabled ? 0.5 : 1}">{{ item.title }}</div>
										<!-- <section v-if="item.disabled" class="image-card2-disabled" @click="visible = !visible;(e) => { handleValueChange(e, item.value) }">
											<a-icon type="stop" :style="{'font-size':'64px'}" />
										</section> -->
									</div>
								</a-list-item>
							</a-list>
						</div>
					</template>
					<div class="image-card">
						<div>
							<img :src="selectedValue.url" alt="" :style="{ opacity: selectedValue.disabled ? 0.5 : 1}">
						</div>
						<div>
							<h4 :style="{ opacity: selectedValue.disabled ? 0.5 : 1}">{{ selectedValue.title }}</h4>
							<span :title="selectedValue.desc">{{ selectedValue.desc }}</span>
						</div>
						<!-- <div v-if="selectedValue.disabled" class="image-card-disabled">
							<a-icon type="stop" :style="{'font-size':'64px'}" />
						</div> -->
					</div>
				</a-popover>
				<a-icon
					v-show="fieldValue < dataSource.length - 1"
					type="right"
					@click="(e) => { handleValueChange(e, fieldValue + 1)}"
				/>
				<a-icon
					v-show="fieldValue === dataSource.length - 1"
					type="right"
					:style="{color: '#cccccc'}"
				/>
			</div>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/4/26.
 * Copyright 2022, Inc.
 * =================================== */
import	B01	from '@/assets/img/texture/B01.png';
import	B02	from '@/assets/img/texture/B02.png';
import	B03	from '@/assets/img/texture/B03.png';
import	B04	from '@/assets/img/texture/B04.png';
import	B05	from '@/assets/img/texture/B05.png';
import	B06	from '@/assets/img/texture/B06.png';
import	B07	from '@/assets/img/texture/B07.png';
import	B08	from '@/assets/img/texture/B08.png';
import { logger } from '@/utils/logger';

export default {
	components: {
	},
	props: {
		required: {
			type: Boolean,
			required: false,
			default: false,
		},
		title: {
			type: String,
			required: false,
			default: '',
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
		label: {
			type: String,
			required: false,
			default: '',
		},
		name: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: Number,
			required: false,
			default: null,
		},
		mold: {
			type: Number,
			required: false,
			default: null,
		},
		invalid: {
			type: Boolean,
			required: false,
			default: false,
		}
	},
	data () {
		return {
			visible: false,
			fieldValue: 0,
			fieldMold: 0,
			dataSource: [
				{
					url: B01,
					name: 'B01',
					value: 0,
					title: '矩阵分布',
					desc: '按照矩阵方式进行纹理分布',
					disabled: false,
				},
				{
					url: B02,
					name: 'B02',
					value: 1,
					title: '菱形分布',
					desc: '按照菱形方式进行纹理分布',
					disabled: false,
				},
				{
					url: B03,
					name: 'B03',
					value: 2,
					title: '菱形细分',
					desc: '按照菱形细分方式进行纹理分布',
					disabled: false,
				},
				{
					url: B04,
					name: 'B04',
					value: 3,
					title: '镜像分布',
					desc: '按照矩阵镜像分布方式进行纹理分布',
					disabled: false,
				},
				{
					url: B05,
					name: 'B05',
					value: 4,
					title: '连续细分',
					desc: '按照矩阵连续细分方式进行纹理分布',
					disabled: false,
				},
				{
					url: B06,
					name: 'B06',
					value: 5,
					title: '三角分布',
					desc: '按照三角分布方式进行纹理分布',
					disabled: false,
				},
				{
					url: B07,
					name: 'B07',
					value: 6,
					title: '三角细分',
					desc: '按照三角细分方式进行纹理分布',
					disabled: false,
				},
				{
					url: B08,
					name: 'B08',
					value: 7,
					title: '六边分布',
					desc: '按照六边分布方式进行纹理分布',
					disabled: false,
				},
			],
			selectedValue: {

			},
			disabledMap: {
				0: [],
				1: [7],
				2: [3, 4],
				3: [0, 4],
				4: [1, 2, 3, 4, 6],
				5: [1, 7],
				6: [4],
				7: [4],
				8: [3, 4],
				9: [3, 4],
				10: [1, 4],
				11: [2, 4, 5, 6, 7],
				12: [],
				13: [4],
				14: [4],
				15: [4, 6],
				16: [4],
				17: [1, 4],
			},
		};
	},
	watch: {
		'value': {
			immediate: true,
			handler (newVal) {
				this.fieldValue = newVal;
				if (!this.fieldValue) {
					this.fieldValue = 0;
				}
				this.computDisable();
				this.selectedValue = this.dataSource.find(i => i.value === this.fieldValue);
			},
		},
		'mold': {
			immediate: true,
			deep: true,
			handler (newVal) {
				this.fieldMold = newVal;
				this.computDisable();
				this.selectedValue = this.dataSource.find(i => i.value === this.fieldValue);
				this.$emit('valueChange', {
					value: this.selectedValue.value,
					key: this.name,
					invalid: this.selectedValue.disabled
				});
			}
		}
	},
	methods: {
		handleValueChange (e, value) {
			this.invalid = this.dataSource.find(i => i.value === value).disabled;
			this.$emit('valueChange', { value, key: this.name, invalid: this.invalid });
			this.visible = false;
		},
		computDisable () {
			this.dataSource.forEach((i, index) => {
				i.disabled = this.disabledMap[this.mold || 0].includes(index);
			});
		}
	},
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/layout.less';
@import '~@/assets/styles/mixins/variables.less';

.attr-item {
	/deep/ .ant-slider-mark-text {
		top: -34px;
	}
	&-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	&-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 1px;
		border: 1px solid #cccccc;
		padding: 0 10px;
		margin-bottom: 16px;
		min-height: 40px;
	}
}
.image-slider {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	align-content: center;
	align-items: center;
}
.dropdown-content {
	max-width: 388px;
	max-height: 288px;
	padding: 4px 0;
	.image-card2 {
		cursor: pointer;
		&:hover {
			border-color: #FF7020;
		}
	}
	.image-card2-active {
		border-color: #FF7020;
	}
	/deep/ .ant-list-pagination {
		text-align: center;
	}
}
.image-card {
	width: 230px;
	height: 70px;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-content: center;
	align-items: center;
	position: relative;
	div {
		margin: 4px;
		img {
			width: 75px;
			height: 50px;
			border: solid rgba(0,0,0,0) 1px;
			z-index: 100;
			&:hover {
				cursor: pointer;
				border: solid #FF7020 1px;
			}
		}
		h4 {
			font-weight: 600;
		}
		span {
			display: block;
			width: 140px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
	&-disabled {
		position: absolute;
		top: 0px;
		left: 0px;
		z-index: 300;
		display: flex;
		justify-content: center;
		align-content: center;
		pointer-events: none;
	}
}

.image-card2 {
	position: relative;
	width: 120px;
	height: 80px;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-content: center;
	align-items: center;
	margin: 0 4px;
	border: solid #CCCCCC 1px;
	border-radius: 1px;
	img {
		width: 120px;
		height: 80px;
		z-index: 100;
	}
	div {
		position: absolute;
		left: 0px;
		bottom: 0px;
		width: 118px;
		height: 24px;
		text-align: center;
		z-index: 200;
		color: #FFFFFF;
		background-color: rgba(0, 0, 0, 0.6);
	}
	&-disabled {
		position: absolute;
		z-index: 300;
		top: 8px;
		left: 26px;
		display: flex;
		justify-content: center;
		align-content: center;
		pointer-events: none;
	}
}
</style>
