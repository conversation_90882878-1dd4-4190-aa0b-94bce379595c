#!/usr/bin/python3
# -*- coding: UTF-8 -*-
###################################
# cli工具
# 安装依赖: pip install -r requirements.txt
# Created by cjking on 2020/07/06.
###################################
import os
import tag
import build
import utils
import logger
import asyncio
import publish
import argparse

logger = logger.Logger()

# 设置参数
ap = argparse.ArgumentParser()
ap.add_argument("-e", "--env", required=False, type=str, help="The execution environment")
ap.add_argument("-a", "--action", required=False, type=str, help="Perform operations")
ap.add_argument("-s", "--server", required=False, type=str, help="Server address")
ap.add_argument("-ss", "--static_server", required=False, type=str, help="Static server address")
ap.add_argument("-c", "--clean", required=False, type=str, help="Clean up the compile cache")
ap.add_argument("-i", "--intranet", required=False, type=str, help="Access the server API Intranet address")
ap.add_argument("-v", "--version", required=False, type=str, help="Tag version")
args = vars(ap.parse_args())
# 获取参数
g_env = args['env']
g_action = args['action']
g_server = args['server']
g_static_server = args['static_server']
g_clean = args['clean']
g_intranet = args['intranet']
g_version = args['version']

g_clean = True if g_clean == 'true' else False


async def main():
	env = '' if g_env is None else g_env.replace('\'', '')
	if utils.isEmpty(env):
		envs = ['dev', 'test', 'prod']
		index = await utils.getSelectValue(envs, '请选择环境？')
		env = envs[index - 1]
	logger.choose("您选择的环境是: %s" % env, "blue")

	act = g_action
	if act is None:
		actions = ['build', 'queryTags', 'delTag', 'delTagAll', 'reservedLastSevenDays', 'publish']
		index = await utils.getSelectValue(actions, '请选择操作？')
		act = actions[index - 1]
	logger.choose("您选择的操作是: %s" % act, "blue")

	if act == "build":
		logger.choose('是否清理缓存: %s' % g_clean)
		await build.startBuild(env, g_server, g_clean, g_intranet, g_static_server)
	if act == "queryTags":
		await tag.queryTags(env)
	if act == "delTag":
		await tag.delTag(env)
	if act == "delTagAll":
		await tag.delTagAll(env)
	if act == "reservedLastSevenDays":
		await tag.reservedLastSevenDaysTags(env, g_server)
	if act == "publish":
		await publish.publish(env, g_server, g_intranet, g_version)


if __name__ == '__main__':
	asyncio.run(main())
