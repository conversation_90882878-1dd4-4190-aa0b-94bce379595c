import { storage } from '@/utils/storage';
import { logger } from '@/utils/logger';
import { IMessage } from '@/http/message';
import { getProjectTaskDetail } from '@/api';

const task = {
	state: {
		partList: [],
		settings: [],
		lastTaskId: '',
		results: [],
		hiddenObjectMap: {},
		oneWeek: 7 * 24 * 60, // 一周(分钟)
	},
	mutations: {
		saveResults (state, value) {
			state.results = value || [];
			storage.set('results', value, state.oneWeek);
		},
		savePartList (state, value) {
			state.partList = value || [];
			storage.set('partList', value, state.oneWeek);
		},
		saveSettings (state, value) {
			state.settings = value || [];
			storage.set('settings', value, state.oneWeek);
		},
		saveHiddenObjectMap (state, value) {
			state.hiddenObjectMap = value || [];
			storage.set('hiddenObjectMap', value, state.oneWeek);
		},
		setLastTaskId (state, value) {
			state.lastTaskId = value || [];
			storage.set('lastTaskId', value, state.oneWeek);
		},
	},
	actions: {
		saveResults ({ commit, state }, value) {
			commit('saveResults', value);
		},
		savePartList ({ commit, state }, value) {
			commit('savePartList', value);
		},
		saveSettings ({ commit, state }, value) {
			commit('saveSettings', value);
		},
		saveHiddenObjectMap ({ commit, state }, value) {
			commit('saveHiddenObjectMap', value);
		},
		setLastTaskId ({ commit, state }, value) {
			commit('setLastTaskId', value);
		},

		/**
		 * 检测任务是否存在
		 */
		checkTaskExisted ({ commit, state }, taskId) {
			return new Promise(async (resolve, reject) => {
				try {
					if (!taskId) {
						return reject(false);
					}
					const res = await getProjectTaskDetail({ id: taskId });
					const existed = !!(res?.code === IMessage.OK.code && res.result?.sysTask);
					logger.log('检测任务是否存在 : ', existed);
					return resolve(existed);
				} catch (error) {
					return reject(false);
				}
			});
		},
	},
};

export default task;
