/* ===================================
 * vue-cli 3.x配置文件
 * Created by cjking on 2020/05/02.
 * Copyright 2020, Inc.
 * =================================== */
const os = require("os");
const path = require("path");
const webpack = require("webpack");
const CompressionPlugin = require("compression-webpack-plugin"); // Gzip
const BundleAnalyzerPlugin =
	require("webpack-bundle-analyzer").BundleAnalyzerPlugin; // Webpack包文件分析器
const env = process.env.VUE_APP_ENV; // 环境变量(简写)
const debug = process.env.VUE_APP_DEBUG === "true"; // 是否开启debug
const report = process.env.VUE_APP_REPORT === "true"; // 是否输出编译统计报告
const openLog = process.env.VUE_APP_LOGGER === "true"; // 是否开启日志输出
const production = ["production", "test"].includes(process.env.VUE_APP_MODE); // 是否生产环境
// eslint-disable-next-line no-console
console.log("production: ", production);

const resolve = (dir) => path.join(__dirname, dir);

const publicPath = production ? process.env.VUE_APP_BASE : "/"; // 资源路径，不同环境切换控制

/**
 * 获取客户端IP
 */
const getClientIP = () => {
	const interfaceAddresses = os.networkInterfaces();
	for (const prop in interfaceAddresses) {
		if (Object.prototype.hasOwnProperty.call(interfaceAddresses, prop)) {
			const interfaceAddress = interfaceAddresses[prop];
			for (const alias of interfaceAddress) {
				if (
					alias &&
					alias.family === "IPv4" &&
					alias.address !== "127.0.0.1" &&
					!alias.internal
				) {
					return alias.address;
				}
			}
		}
	}
};

/**
 * 包装代理配置
 * @param proxyOption       代理配置
 * @param prefix            代理前缀
 * @param url               代理地址
 * @param removePrefix      是否移除代理前缀
 * @param openWebSocket     是否开启webSocket
 */
const packOption = (
	proxyOption,
	prefix,
	url,
	removePrefix = false,
	openWebSocket = false
) => {
	proxyOption[prefix] = {
		target: url,
		secure: false,
		changeOrigin: true,
		debug: true,
		pathRewrite: {
			[`^${prefix}`]: "",
		},
	};
	if (openWebSocket) {
		proxyOption[prefix].ws = true; // 开启ws
	}
	if (removePrefix) {
		delete proxyOption[prefix].pathRewrite;
	}
	return proxyOption;
};

/**
 * 获取代理配置
 * @param {Array<{ prefix: String,  url: String, removePrefix: Boolean=, openWebSocket: Boolean=}>} list
 */
const getProxyOption = (list = []) => {
	const proxyOption = {};
	list.forEach((item) => {
		if (Array.isArray(item.prefix)) {
			for (const key of item.prefix) {
				packOption(
					proxyOption,
					key,
					item.url,
					item.removePrefix,
					item.openWebSocket
				);
			}
		} else {
			packOption(
				proxyOption,
				item.prefix,
				item.url,
				item.removePrefix,
				item.openWebSocket
			);
		}
	});
	// eslint-disable-next-line no-console
	// console.log('代理配置:\n', proxyOption);
	return proxyOption;
};

/**
 * cdn开关
 * 开启cdn时就关闭dll，或者用dll时就不用cdn
 */
const OPEN_CDN = false;

const webpackHtmlOptions = {
	// dns预加载，优化接口请求
	dnsPrefetch: [
		// 'https://aaa.exmaple.com'
	],
	externals: {
		vue: "Vue",
		"vue-router": "VueRouter",
		vuex: "Vuex",
		axios: "axios",
		"crypto-js": "CryptoJS",
		nprogress: "NProgress",
		"ant-design-vue": "antd",
		three: "THREE",
	},
	cdn: {
		// 生产环境
		build: {
			css: [
				"https://lf9-cdn-tos.bytecdntp.com/cdn/expire-100-y/nprogress/0.2.0/nprogress.min.css",
				"https://lf26-cdn-tos.bytecdntp.com/cdn/expire-100-y/ant-design-vue/1.7.6/antd.min.css",
			],
			js: [
				// 字节跳动静态资源公共库 https://cdn.bytedance.com
				"https://lf3-cdn-tos.bytecdntp.com/cdn/expire-100-y/vue/2.6.14/vue.min.js",
				"https://lf26-cdn-tos.bytecdntp.com/cdn/expire-100-y/vue-router/3.5.1/vue-router.min.js",
				"https://lf9-cdn-tos.bytecdntp.com/cdn/expire-100-y/vuex/3.6.2/vuex.min.js",
				"https://lf26-cdn-tos.bytecdntp.com/cdn/expire-100-y/axios/0.21.1/axios.min.js",
				"https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-y/crypto-js/4.0.0/crypto-js.min.js",
				"https://lf3-cdn-tos.bytecdntp.com/cdn/expire-100-y/nprogress/0.2.0/nprogress.min.js",
				"https://lf9-cdn-tos.bytecdntp.com/cdn/expire-100-y/ant-design-vue/1.7.6/antd.min.js",
				"https://lf26-cdn-tos.bytecdntp.com/cdn/expire-100-y/three.js/r125/three.min.js",
			],
			backupCss: [
				"https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css",
				"https://cdn.bootcdn.net/ajax/libs/ant-design-vue/1.7.6/antd.min.css",
			],
			backupJs: [
				"https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js",
				"https://cdn.bootcdn.net/ajax/libs/vue-router/3.5.1/vue-router.min.js",
				"https://cdn.bootcdn.net/ajax/libs/vuex/3.6.2/vuex.min.js",
				"https://cdn.bootcdn.net/ajax/libs/axios/0.21.1/axios.min.js",
				"https://cdn.bootcdn.net/ajax/libs/crypto-js/4.0.0/crypto-js.min.js",
				"https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js",
				"https://cdn.bootcdn.net/ajax/libs/ant-design-vue/1.7.6/antd.min.js",
				"https://cdn.bootcdn.net/ajax/libs/three.js/r125/three.min.js",
			],
		},
	},
	// 插件
	libs: {
		css: [],
		js: [
			// 'https://cdn.jsdelivr.net/npm/rhino3dm@7.11.1/rhino3dm.min.js',
			// `https://public-pre.fangzhenxiu.com/pera-cloud/npm/rhino3dm@7.11.1/rhino3dm.min.js`,
			"/npm/rhino3dm@7.11.1/rhino3dm.min.js",
			// 'https://files.mcneel.com/rhino3dm/js/latest/compute.rhino3d.js'
			// '/npm/rhino3dm@7.11.1/compute.rhino3d.js'
		],
	},
};

module.exports = {
	// 基本路径
	publicPath, // 资源路径，不同环境切换控制
	// 输出文件目录
	outputDir: "dist",
	// eslint-loader 是否在保存的时候检查
	lintOnSave: true,
	// 放置生成的静态资源 (js、css、img、fonts) 的 (相对于 outputDir 的) 目录。
	assetsDir: "static",
	// 以多页模式构建应用程序
	pages: undefined,
	// 是否使用包含运行时编译器的 Vue 构建版本
	runtimeCompiler: false,
	// 是否为 Babel 或 TypeScript 使用 thread-loader。该选项在系统的 CPU 有多于一个内核时自动启用，仅作用于生产构建，在适当的时候开启几个子进程去并发的执行压缩
	parallel: require("os").cpus().length > 1,
	// parallel: false,

	// webpack配置
	chainWebpack: (config) => {
		// 修复HMR
		config.resolve.symlinks(true);
		// 优化moment 去掉国际化内容
		config
			.plugin("ignore")
			.use(
				new webpack.ContextReplacementPlugin(
					/moment[/\\]locale$/,
					/zh-cn$/
				)
			);

		config.module.rules.delete("svg"); // 重点:删除默认配置中处理svg
		// config.module
		// 	.rule('html-loader')
		// 	.test(/\.html$/)
		// 	.include.add(resolve('public')) // 表示public目录中的文件需要进行html-loader
		// 	.end()
		// 	.use('html-loader')
		// 	.loader('html-loader');
		/**
		 * svg设置
		 */
		config.module
			.rule("svg-sprite-loader")
			.test(/\.svg$/)
			.include.add(resolve("src/assets/svg")) // 表示src/assets/svg目录中的文件需要进行svg-sprite-loader
			.end()
			.use("svg-sprite-loader")
			.loader("svg-sprite-loader")
			.options({
				symbolId: "icon-[name]",
			});
		config.module
			.rule("images")
			.test(/\.(png|jpe?g|gif|webp|svg)(\?.*)?$/)
			.exclude.add(resolve("src/assets/svg")) // 表示src/assets/svg目录中的文件不要进行url-loader
			.end()
			.use("url-loader")
			.loader("url-loader")
			.options({
				limit: 10240, // 设置小于10k的就base64
				name: "img/[name].[hash:7].[ext]",
				publicPath: publicPath,
			});
		// config.module
		// 	.rule('wtk-worker-loader')
		// 	.test(/\.worker\.js$/)
		// 	.include.add(/vtk\.js/) // 表示vtk.js目录中的文件需要进行worker-loader
		// 	.end()
		// 	.use('worker-loader')
		// 	.loader('worker-loader');
		/**
		 * vtk设置
		 */
		config.module
			.rule("shader-loader")
			.test(/\.glsl$/i)
			.include.add(/vtk\.js[/\\]Sources/) // 表示vtk.js/Sources目录中的文件需要进行shader-loader
			.end()
			.use("shader-loader")
			.loader("shader-loader");
		/**
		 * 删除懒加载模块的 prefetch preload，降低带宽压力
		 */
		config.plugins.delete("prefetch").delete("preload");
		// 清除警告
		config.performance.set("hints", false);
		// 设置路径别名
		config.resolve.alias
			.set("public", resolve("public"))
			.set("@", resolve("src"));
		/**
		 * 添加CDN参数到htmlWebpackPlugin配置中， 修改 public/index.html
		 */
		config.plugin("html").tap((args) => {
			// 生产环境将cdn写入webpackHtmlOptions，在public/index.html应用
			if (OPEN_CDN) {
				args[0].inject = true; // 开启/关闭自动注入
				args[0].OPEN_CDN = OPEN_CDN;
				args[0].cdn = webpackHtmlOptions.cdn.build;
				// dns预加载
				args[0].dnsPrefetch = webpackHtmlOptions.dnsPrefetch;
			}
			// 插件加载
			args[0].libs = webpackHtmlOptions.libs;
			return args;
		});
	},

	// 调整 webpack 配置
	configureWebpack: (config) => {
		// config.module.rules.push({
		// 	test: /\.worker\.js$/i,
		// 	use: {
		// 		loader: 'worker-loader',
		// 		// 允许将内联的 web worker 作为 BLOB
		// 		// 具有 fallback 值的内联模式将为不支持Web Worker的浏览器创建文件，要禁用此行为，只需使用 no-fallback 值
		// 		// options: { inline: 'no-fallback' },
		// 		options: { inline: 'no-fallback', esModule: true, filename: 'my-worker01-' + Date.now() },
		// 	},
		// });

		// 由于没有map文件，就不会对应vue文件的代码
		config.devtool =
			process.env.NODE_ENV === "development"
				? "eval-cheap-module-source-map"
				: "none";

		// 开启cdn状态：externals里的模块不打包
		if (OPEN_CDN) {
			config.externals = webpackHtmlOptions.externals;
		}

		// 生产环境取消 console.log
		if (production) {
			config.optimization.minimizer[0].options.terserOptions.compress.drop_console =
				!openLog;
			config.optimization.minimizer[0].options.terserOptions.compress.drop_debugger =
				!openLog;
		}

		const compressPlugin = new CompressionPlugin({
			// 文件开启Gzip，也可以通过服务端(如：nginx)(https://github.com/webpack-contrib/compression-webpack-plugin)
			filename: "[path][base].gz",
			algorithm: "gzip",
			test: new RegExp(
				"\\.(" + ["js", "css", "html", "json"].join("|") + ")$"
			),
			threshold: 8192,
			minRatio: 0.8,
		});
		// 生产and测试环境
		const pluginsPro = [compressPlugin];
		// 开发环境
		const pluginsDev = [compressPlugin];
		if (report) {
			// Webpack包文件分析器(https://github.com/webpack-contrib/webpack-bundle-analyzer)
			pluginsPro.push(new BundleAnalyzerPlugin());
		}
		if (debug) {
			// pluginsDev.push(
			// 	// 移动端模拟开发者工具(https://github.com/diamont1001/vconsole-webpack-plugin  https://github.com/Tencent/vConsole)
			// 	new vConsolePlugin({
			// 		filter: [], // 需要过滤的入口文件
			// 		enable: !production // 发布代码前记得改回 false
			// 	})
			// );
		}
		if (production) {
			// 为生产环境修改配置...process.env.NODE_ENV !== 'development'
			config.plugins = [...config.plugins, ...pluginsPro];
		} else {
			// 为开发环境修改配置...
			config.plugins = [...config.plugins, ...pluginsDev];
		}

		if (production) {
			// 优化打包chunk-vendor.js文件体积过大
			// 当我们运行项目并且打包的时候，会发现chunk-vendors.js这个文件非常大，
			// 那是因为webpack将所有的依赖全都压缩到了这个文件里面，这时我们可以将其拆分，将所有的依赖都打包成单独的js。
			// 开启分离js
			config.optimization = {
				runtimeChunk: "single",
				splitChunks: {
					chunks: "all",
					maxInitialRequests: Infinity,
					minSize: 20000,
					cacheGroups: {
						vendor: {
							test: /[\\/]node_modules[\\/]/,
							name(module) {
								// get the name. E.g. node_modules/packageName/not/this/part.js
								// or node_modules/packageName
								const packageName = module.context.match(
									/[\\/]node_modules[\\/](.*?)([\\/]|$)/
								)[1];
								// npm package names are URL-safe, but some servers don't like @ symbols
								return `npm.${packageName.replace("@", "")}`;
							},
						},
					},
				},
			};
		}
	},
	css: {
		extract: production ? { ignoreOrder: true } : false, // ignoreOrder fix:css warning Conflicting order. Following module has been added
		sourceMap: false, // 是否构建样式地图，false将提高构建速度，一般不建议开启
		loaderOptions: {
			// css预设器配置项
			less: {
				modifyVars: {
					/* less 变量覆盖，用于自定义 ant design 主题 */
					// @error-color: #E5252F;
					// @warning-color: #DD9524;
					// @success-color: #17AE45;
					// @link-color: @primary-color;
					// @home-bg-color: #FFFFFF;

					"primary-color": "#6362FF", // 全局主色
					"link-color": "#6362FF", // 链接色
					"border-radius-base": "4px", // 组件/浮层圆角
					"text-color": "#333333", // 主文本色
					"text-color-secondary": "#CCCCCC", // 次文本色
					"success-color": "#17AE45", // 成功/完成
					"warning-color": "#DD9524", // 提示提醒
					"error-color": "#E5252F", // 失败/警告
					"border-color-base": "#E5E5E5", // 线框边框颜色
				},
				javascriptEnabled: true,
			},
		},
	},
	// webpack-dev-server 相关配置 https://webpack.js.org/configuration/dev-server/
	devServer: {
		// host: getClientIP(),
		port: 8102, // 端口号
		hotOnly: true, // 热更新
		inline: true, // 实时刷新
		/**
		 * 配置跨域处理,多个代理
		 */
		proxy: getProxyOption([
			{
				prefix: "/dev/static",
				url: "http://*************:8901",
				// url: 'http://dev-static.fmock.cn',
			},
			{
				openWebSocket: true,
				prefix: "/dev/socket",
				url: "ws://127.0.0.1:8098",
				// url: "ws://ws.fmock.cn",
			},
			{
				prefix: "/dev",
				url: "http://127.0.0.1:8098",
				// url: 'http://api.fmock.cn',
			},
			{
				prefix: "/test/static",
				url: "http://*************:8901",
			},
			{
				openWebSocket: true,
				prefix: "/test/socket",
				url: "ws://*************:8098",
			},
			{
				prefix: "/test",
				url: "http://*************:8098",
			},
			// {
			// 	prefix: '/prod/static',
			// 	url: 'http://static-client.generativethings.com',
			// },
			// {
			// 	openWebSocket: true,
			// 	prefix: '/prod/socket',
			// 	url: 'ws://ws.generativethings.com',
			// },
			// {
			// 	prefix: '/prod',
			// 	url: 'http://api-client.generativethings.com',
			// },
			{
				prefix: "/local/static",
				url: "http://dev-static.fmock.cn",
			},
			{
				openWebSocket: true,
				prefix: "/local/socket",
				url: "ws://ws.fmock.cn",
			},
			{
				prefix: "/local",
				url: "http://api.fmock.cn",
			},
		]),
	},

	// pwa配置
	pwa: {
		iconPaths: {
			favicon32: "favicon.ico",
			favicon16: "favicon.ico",
			appleTouchIcon: "favicon.ico",
			maskIcon: "favicon.ico",
			msTileImage: "favicon.ico",
		},
	},

	// 默认情况下 babel-loader 会忽略所有 node_modules 中的文件。
	// 如果你想要通过 Babel 显式转译一个依赖，可以在这个选项中列出来（可通过此，把以下最新的语法编译为es5通用语法）
	transpileDependencies: ["jszip"],
};
