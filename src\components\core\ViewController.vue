<template>
	<div class="grid wrapper">
		<template v-if="config.match(/画点/)">
			<div :class="['grid-cell']">
				<a-tooltip placement="top" title="画点">
					<div :class="{'active-draw': curDrawMode === DrawEnum.point}">
						<img :src="drawPointImg" @click="onDraw(curDrawMode === DrawEnum.point ? '' : DrawEnum.point)">
					</div>
				</a-tooltip>
			</div>
			<a-divider v-if="config.match(/画点\|/)" type="vertical" />
		</template>

		<!--<template v-if="config.match(/绘制/)">
			<div :class="['grid-cell']">
				<a-tooltip placement="top" title="画线">
					<div :class="{'active-draw': curDrawMode === DrawEnum.curve}">
						<img :src="drawCurveImg" @click="onDraw(curDrawMode === DrawEnum.curve ? '' : DrawEnum.curve)">
					</div>
				</a-tooltip>
			</div>
			<a-divider v-if="config.match(/绘制\|/)" type="vertical" />
		</template>-->

		<template v-if="config.match(/绘制/)">
			<!-- 下拉 -->
			<NavigationDropdown title="绘制模式" :dataList="DrawEnumList" v-model="curDrawMode" @onSelect="drawHandle">
				<template #selectIcon>
					<img class="render" :src="getIcon(DrawEnumList, curDrawMode) || DrawEnumList[0].icon">
					<!--<img class="render sub fix1" :src="subDownImg">-->
					<img class="render sub fix1" :src="downTriangleImg">
				</template>
			</NavigationDropdown>
			<a-divider v-if="config.match(/绘制\|/)" type="vertical" />
		</template>

		<template v-if="config.match(/控制/)">
			<!-- 下拉 -->
			<NavigationDropdown title="控制模式" :dataList="ActionEnumList" v-model="curAction" @onSelect="actionHandle">
				<template #selectIcon>
					<img class="render" :src="getIcon(ActionEnumList, curAction) || ActionEnumList[0].icon">
					<!--<img class="render sub fix1" :src="subDownImg">-->
					<img class="render sub fix1" :src="downTriangleImg">
				</template>
			</NavigationDropdown>
			<a-divider v-if="config.match(/控制\|/)" type="vertical" style="margin-left: 5px;" />
		</template>

		<template v-if="config.match(/清除/)">
			<div :class="['grid-cell']">
				<a-tooltip placement="top" title="清除">
					<span style="display: block;" @click="clearDrawn">
						<img :src="clearImg">
					</span>
				</a-tooltip>
			</div>
			<a-divider v-if="config.match(/清除\|/)" type="vertical" />
		</template>

		<!--<template v-if="config.match(/框选/)">
			<div :class="['grid-cell']">
				<a-tooltip id="boxSelectIcon" placement="top" title="框选">
					<span :class="{'svg-icon-overwrite': true, 'action-active': curAction === ActionEnum.boxSelect}" @click="onBoxSelect(ActionEnum.boxSelect)">
						<svg-icon
							type="boxSelect"
							:vStyle="{ fontSize: '24px', color: '#6362FF' }"
						/>
					</span>
				</a-tooltip>
			</div>
			<a-divider v-if="config.match(/框选\|/)" type="vertical" />
		</template>-->

		<template v-if="config.match(/重置视图/)">
			<div :class="['grid-cell']" style="margin-right: -5px; margin-top: 4px;">
				<a-tooltip placement="top" title="重置视图">
					<img :src="selfAdaptionImg" @click="resetView()">
				</a-tooltip>
			</div>
			<a-divider v-if="config.match(/重置视图\|/)" type="vertical" />
		</template>

		<template v-if="config.match(/视图模式/)">
			<!-- 下拉 -->
			<NavigationDropdown title="视图模式" :dataList="ViewEnumList" v-model="curView" @onSelect="updateView" />
			<a-divider v-if="config.match(/视图模式\|/)" type="vertical" style="margin-left: 5px;" />
		</template>

		<template v-if="config.match(/选择模式/)">
			<!-- 下拉 -->
			<NavigationDropdown title="选择模式" :dataList="SelectModeEnumList" v-model="curSelectMode" @onSelect="selectModeHandle" />
			<a-divider v-if="config.match(/选择模式\|/)" type="vertical" style="margin-left: 5px;" />
		</template>

		<template v-if="config.match(/渲染模式/)">
			<!-- 下拉 -->
			<NavigationDropdown title="渲染模式" :dataList="RenderModeEnumList" v-model="curRenderMode" @onSelect="changeRenderMode" />
			<a-divider v-if="config.match(/渲染模式\|/)" type="vertical" style="margin-left: 5px;" />
		</template>

		<template v-if="config.match(/摄像机/)">
			<!-- 下拉 -->
			<NavigationDropdown title="摄像机" :dataList="CameraModeEnumList" v-model="curCameraMode" @onSelect="changeCameraMode" />
			<a-divider v-if="config.match(/摄像机\|/)" type="vertical" style="margin-left: 5px;" />
		</template>

		<template v-if="config.match(/平面裁切/)">
			<div :class="['grid-cell']" @click="preventDefaults($event)">
				<!-- <a-popover placement="bottom" :title="null" :mouseLeaveDelay="1">
					<div slot="content"> -->
				<ClippingCard
					ref="clippingCard"
					v-dialogDrag="{dragEl: '.ant-modal-header', dragDom: '.ant-modal'}"
					showLine
					title="平面裁切"
					panelKey="attr"
					disabledRightMenu
					@changeClipping="updateClipping"
				>
				</ClippingCard>
				<!-- </div> -->
				<div style="position: relative;" title="平面裁切">
					<img class="render" :src="clippingImg" @click="handleClippingModal">
					<!--<img class="render sub fix1" :src="subDownImg">-->
					<!-- <img class="render sub fix1" :src="downTriangleImg"> -->
				</div>
				<!-- </a-popover> -->
			</div>
			<a-divider v-if="config.match(/平面裁切\|/)" type="vertical" style="margin-left: 5px;" />
		</template>

		<!--<template v-if="config.match(/视图旋转/)">
			&lt;!&ndash; 下拉 &ndash;&gt;
			<div :class="['grid-cell']">
				<a-dropdown placement="bottomCenter" :disabled="disabledNav">
					<a-menu class="menu-dropdown" slot="overlay">
						<a-menu-item key="-1" style="text-align: center;">
							视图旋转
						</a-menu-item>
						<a-divider />
						<a-menu-item key="1">
							<div :class="['grid-cell']" @click="preventDefaults($event)">
								<span><svg-icon type="x-rotate-left" :vStyle="{ fontSize: '26px', color: '#6362FF' }" />左右旋转</span>
								<a-slider :defaultValue="0" :min="-360" :max="360" :step="1" style="width: 100px; margin-left: 15px; margin-right: 15px;" @change="value => updateRotate('y', value)"></a-slider>
							</div>
						</a-menu-item>
						<a-divider />
						<a-menu-item key="2">
							<div :class="['grid-cell']" @click="preventDefaults($event)">
								<span><svg-icon type="arrow-up-down" :vStyle="{ fontSize: '26px', color: '#6362FF' }" />上下旋转</span>
								<a-slider :defaultValue="0" :min="-360" :max="360" :step="1" style="width: 100px; margin-left: 15px;" @change="value => updateRotate('x', value)"></a-slider>
							</div>
						</a-menu-item>
					</a-menu>
					<div style="position: relative;" title="视图旋转">
						<svg-icon type="rotate" :vStyle="{ fontSize: '26px', marginTop: '16px', marginLeft: '8px', color: '#6362FF' }" />
					</div>
				</a-dropdown>
			</div>
			<a-divider v-if="config.match(/视图旋转\|/)" type="vertical" />
		</template>-->

		<template v-if="config.match(/参考平面/)">
			<!-- 下拉 -->
			<div :class="['grid-cell']">
				<a-dropdown placement="bottomCenter" :disabled="disabledNav" :trigger="['hover']">
					<a-menu class="menu-dropdown" slot="overlay">
						<a-menu-item key="-1" style="text-align: center;">
							参考平面
						</a-menu-item>
						<a-divider />
						<a-sub-menu key="sub1" class="controller">
							<span slot="title">
								<span class="grid-cell" :style="{marginLeft: '-10px'}">
									<img class="render" :src="PlaneEnumList[0].icon"><span>默认参考平面</span>
								</span>
							</span>
							<a-menu-item key="11">
								<div :style="{marginRight: '20px'}" :class="['grid-cell', curPlaneMode === PlaneEnumList[0].key ? 'active' : '', PlaneEnumList[0].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[0].key)">
									<img class="render" :src="PlaneEnumList[0].icon">{{ PlaneEnumList[0].desc }}
								</div>
							</a-menu-item>
							<a-menu-item key="12">
								<div :style="{marginRight: '20px'}" :class="['grid-cell', curPlaneMode === PlaneEnumList[1].key ? 'active' : '', PlaneEnumList[1].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[1].key)">
									<img class="render" :src="PlaneEnumList[1].icon">{{ PlaneEnumList[1].desc }}
								</div>
							</a-menu-item>
							<a-menu-item key="13">
								<div :style="{marginRight: '20px'}" :class="['grid-cell', curPlaneMode === PlaneEnumList[2].key ? 'active' : '', PlaneEnumList[2].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[2].key)">
									<img class="render" :src="PlaneEnumList[2].icon">{{ PlaneEnumList[2].desc }}
								</div>
							</a-menu-item>
						</a-sub-menu>
						<a-divider />
						<a-sub-menu key="sub2" class="controller">
							<span slot="title">
								<span class="grid-cell" :style="{marginLeft: '-10px'}">
									<img class="render" :src="PlaneEnumList[3].icon"><span>自定义参考平面</span>
								</span>
							</span>
							<a-menu-item key="21">
								<div :style="{marginRight: '20px'}" :class="['grid-cell', curPlaneMode === PlaneEnumList[3].key ? 'active' : '', PlaneEnumList[3].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[3].key)">
									<img class="render" :src="PlaneEnumList[3].icon">{{ PlaneEnumList[3].desc }}
								</div>
							</a-menu-item>
							<a-menu-item key="22">
								<div :style="{marginRight: '20px'}" :class="['grid-cell', curPlaneMode === PlaneEnumList[4].key ? 'active' : '', PlaneEnumList[4].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[4].key)">
									<img class="render" :src="PlaneEnumList[4].icon">{{ PlaneEnumList[4].desc }}
								</div>
							</a-menu-item>
							<a-menu-item key="23">
								<div :style="{marginRight: '20px'}" :class="['grid-cell', curPlaneMode === PlaneEnumList[5].key ? 'active' : '', PlaneEnumList[5].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[5].key)">
									<img class="render" :src="PlaneEnumList[5].icon">{{ PlaneEnumList[5].desc }}
								</div>
							</a-menu-item>
						</a-sub-menu>
						<a-divider />
						<a-menu-item key="3">
							<div :class="['grid-cell', curPlaneMode === PlaneEnumList[6].key ? 'active' : '', PlaneEnumList[6].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[6].key)">
								<img class="render" :src="PlaneEnumList[6].icon">{{ PlaneEnumList[6].desc }}
							</div>
						</a-menu-item>
						<a-divider />
						<a-menu-item key="4">
							<div :class="['grid-cell', curPlaneMode === PlaneEnumList[7].key ? 'active' : '', PlaneEnumList[7].disabled ? 'disabled' : '']" @click="onPlaneModeSelect(PlaneEnumList[7].key)">
								<img class="render" :src="PlaneEnumList[7].icon">{{ PlaneEnumList[7].desc }}
							</div>
						</a-menu-item>
					</a-menu>
					<div style="position: relative;" title="参考平面">
						<img class="render" :src="curPlaneModeIcon">
						<img class="render sub fix1" :src="downTriangleImg">
					</div>
				</a-dropdown>
			</div>
			<a-divider v-if="config.match(/参考平面\|/)" type="vertical" />
		</template>

		<template v-if="config.match(/鼠标按键/)">
			<!-- 下拉 -->
			<div :class="['grid-cell']">
				<a-dropdown v-model="mouseDropdownVisible" placement="bottomCenter" :disabled="disabledNav">
					<a-menu class="menu-dropdown" slot="overlay">
						<a-menu-item key="-1" style="text-align: center;">
							鼠标按键
						</a-menu-item>
						<a-divider />
						<a-menu-item key="1">
							<div :class="['grid-cell']">
								<span style="margin-right: 20px;">鼠标左键</span>
								<a-radio-group name="mouseLeftRadioGroup" v-model="mouseLeftButton">
									<a-radio :value="MOUSE.PAN" @click="mouseLeftSetting($event, MOUSE.PAN)">平移</a-radio>
									<a-radio :value="MOUSE.ROTATE" @click="mouseLeftSetting($event, MOUSE.ROTATE)">旋转</a-radio>
								</a-radio-group>
							</div>
						</a-menu-item>
						<a-divider />
						<a-menu-item key="2">
							<div :class="['grid-cell']">
								<span style="margin-right: 20px;">鼠标右键</span>
								<a-radio-group name="mouseRightRadioGroup" v-model="mouseRightButton">
									<a-radio :value="MOUSE.PAN" @click="mouseRightSetting($event, MOUSE.PAN)">平移</a-radio>
									<a-radio :value="MOUSE.ROTATE" @click="mouseRightSetting($event, MOUSE.ROTATE)">旋转</a-radio>
								</a-radio-group>
							</div>
						</a-menu-item>
					</a-menu>
					<div title="鼠标按键">
						<svg-icon type="mouse" :vStyle="{ fontSize: '28px', marginTop: '16px', color: '#6362FF' }" />
					</div>
				</a-dropdown>
			</div>
			<a-divider v-if="config.match(/鼠标按键\|/)" type="vertical" style="margin-left: -5px;" />
		</template>

		<template v-if="config.match(/其他操作/)">
			<!-- 下拉 -->
			<NavigationDropdown title="其他操作" :dataList="OtherEnumList" v-model="curOtherChoose" :startIndex="1" @onSelect="otherSelect">
				<template #selectIcon>
					<img class="render" :src="OtherEnumList[0].icon">
					<!--<img class="render sub fix1" :src="subDownImg">-->
					<img class="render sub fix1" :src="downTriangleImg">
				</template>
			</NavigationDropdown>
		</template>
	</div>
</template>

<script>
/* ===================================
 * 视图控制器
 * Created by cjking on 2021/06/17.
 * Copyright 2021, Inc.
 * =================================== */
import { MOUSE } from 'three';

import frontImg from '@/assets/img/project/navigation/front.png';
import backImg from '@/assets/img/project/navigation/back.png';
import topImg from '@/assets/img/project/navigation/top.png';
import bottomImg from '@/assets/img/project/navigation/bottom.png';
import leftImg from '@/assets/img/project/navigation/left.png';
import rightImg from '@/assets/img/project/navigation/right.png';

import defaultViewImg from '@/assets/img/project/navigation/defaultView.png';
import selfAdaptionImg from '@/assets/img/project/navigation/selfAdaption.png';
// import leftRotateImg from '@/assets/img/project/navigation/leftRotate.png';
// import rightRotateImg from '@/assets/img/project/navigation/rightRotate.png';

import subDownImg from '@/assets/img/project/navigation/subDown.png';
import downTriangleImg from '@/assets/img/project/navigation/down-triangle.png';
import solidImg from '@/assets/img/project/navigation/solid.png';
import wireframeImg from '@/assets/img/project/navigation/wireframe.png';
import translucentImg from '@/assets/img/project/navigation/translucent.png';
import solidAddEdgeImg from '@/assets/img/project/navigation/solidAddEdge.png';

import chooseFaceImg from '@/assets/img/project/navigation/chooseFace.png';
import chooseLineImg from '@/assets/img/project/navigation/chooseLine.png';
import choosePointImg from '@/assets/img/project/navigation/choosePoint.png';
import chooseSolidImg from '@/assets/img/project/navigation/chooseSolid.png';

import perspectiveImg from '@/assets/img/project/navigation/perspective.png';
import orthographicImg from '@/assets/img/project/navigation/orthographic.png';

import drawPointImg from '@/assets/img/project/navigation/draw-point.png';
import drawCurveImg from '@/assets/img/project/navigation/draw-curve.png';
// import drawCircleImg from '@/assets/img/project/navigation/draw-circle.png';
import clearImg from '@/assets/img/project/navigation/clear.png';

import otherImg from '@/assets/img/project/navigation/other.png';
import showImg from '@/assets/img/project/navigation/show.png';
// import copyImg from '@/assets/img/project/navigation/copy.png';
// import moveImg from '@/assets/img/project/navigation/move.png';
// import rotateImg from '@/assets/img/project/navigation/rotate.png';
// import hiddenImg from '@/assets/img/project/navigation/hide.png';
import hideFaceImg from '@/assets/img/project/navigation/hideFace.png';
import hideLineImg from '@/assets/img/project/navigation/hideLine.png';
import hideWireframeImg from '@/assets/img/project/navigation/hideWireframe.png';

import clippingImg from '@/assets/img/project/navigation/clipping.png';
import screenshotImg from '@/assets/img/project/navigation/screenshot.png';

// draw
import curveImg from '@/assets/img/project/navigation/draw/curve.png';
import brokenLineImg from '@/assets/img/project/navigation/draw/brokenLine.png';
import circleImg from '@/assets/img/project/navigation/draw/circle.png';
import singleSpiralLineImg from '@/assets/img/project/navigation/draw/singleSpiralLine.png';
import doubleHelixLineImg from '@/assets/img/project/navigation/draw/doubleHelixLine.png';

// action
import boxSelect from '@/assets/img/project/navigation/action/box-select.png';
import addControlPointsImg from '@/assets/img/project/navigation/action/add-control-points.png';
import moveCopyImg from '@/assets/img/project/navigation/action/move-copy.png';
import mirrorCurveImg from '@/assets/img/project/navigation/action/mirror-curve.png';
import rotateCopyImg from '@/assets/img/project/navigation/action/rotate-copy.png';
import circularArrayImg from '@/assets/img/project/navigation/action/circular-array.png';
import curveZoomImg from '@/assets/img/project/navigation/action/curve-zoom.png';

import ClippingCard from '@/components/common/ClippingCard';

import {
	SelectModeEnum, ViewEnum, RenderModeEnum,
	CameraModeEnum, DrawEnum, ActionEnum, PlaneModeEnum,
} from '@/constants';
import { preventDefaults } from '@/components/core/ViewUtils';
import NavigationDropdown from '@/components/common/NavigationDropdown';

import planeXYImg from '@/assets/img/project/navigation/draw/plane-xy.png';
import planeYZImg from '@/assets/img/project/navigation/draw/plane-yz.png';
import planeXZImg from '@/assets/img/project/navigation/draw/plane-xz.png';
import planeDiyXYImg from '@/assets/img/project/navigation/draw/plane-diy-xy.png';
import planeDiyYZImg from '@/assets/img/project/navigation/draw/plane-diy-yz.png';
import planeDiyXZImg from '@/assets/img/project/navigation/draw/plane-diy-xz.png';
import curveNormalPlaneImg from '@/assets/img/project/navigation/draw/curve-normal-plane.png';
import curveTangentPlaneImg from '@/assets/img/project/navigation/draw/curve-tangent-plane.png';

// 绘制枚举
const DrawEnumList = [
	{ key: 'curve', icon: curveImg, desc: '曲线', disabled: false },
	{ key: 'brokenLine', icon: brokenLineImg, desc: '折线', disabled: false },
	{ key: 'singleSpiralLine', icon: singleSpiralLineImg, desc: '单螺旋线', disabled: false },
	{ key: 'doubleHelixLine', icon: doubleHelixLineImg, desc: '双螺旋线', disabled: false },
	{ key: 'circle', icon: circleImg, desc: '圆', disabled: false },
];

// 操作枚举
const ActionEnumList = [
	{ key: 'boxSelect', icon: boxSelect, desc: '框选', disabled: false },
	{ key: 'addControlPoints', icon: addControlPointsImg, desc: '添加控制点', disabled: false },
	{ key: 'moveCopy', icon: moveCopyImg, desc: '移动复制', disabled: false },
	{ key: 'mirrorCurve', icon: mirrorCurveImg, desc: '镜像曲线', disabled: false },
	{ key: 'rotateCopy', icon: rotateCopyImg, desc: '旋转复制', disabled: false },
	{ key: 'circularArray', icon: circularArrayImg, desc: '圆周阵列', disabled: false },
	{ key: 'curveZoom', icon: curveZoomImg, desc: '曲线缩放', disabled: false },
];

// 视图枚举
const ViewEnumList = [
	// { key: 'default', icon: defaultViewImg, desc: '默认视图', disabled: false },
	{ key: 'default', icon: defaultViewImg, desc: '轴视图', disabled: false },
	{ key: 'front', icon: frontImg, desc: '前视图', disabled: false },
	{ key: 'back', icon: backImg, desc: '后视图', disabled: false },
	{ key: 'left', icon: leftImg, desc: '左视图', disabled: false },
	{ key: 'right', icon: rightImg, desc: '右视图', disabled: false },
	{ key: 'top', icon: topImg, desc: '俯视图', disabled: false },
	{ key: 'bottom', icon: bottomImg, desc: '仰视图', disabled: false },
];

// 选择模式枚举
const SelectModeEnumList = [
	{ key: 'solid', icon: chooseSolidImg, desc: '选择体', disabled: false },
	{ key: 'surface', icon: chooseFaceImg, desc: '选择面', disabled: false },
	{ key: 'line', icon: chooseLineImg, desc: '选择线', disabled: false },
	{ key: 'point', icon: choosePointImg, desc: '选择点', disabled: false },
];

// 渲染模式枚举
const RenderModeEnumList = [
	{ key: 'surface', icon: solidImg, desc: '体', disabled: false },
	{ key: 'wireframe', icon: wireframeImg, desc: '线框', disabled: false },
	{ key: 'surfaceAndWireframe', icon: solidAddEdgeImg, desc: '体+线框', disabled: false },
	// { key: 'translucent', icon: translucentImg, desc: '体+线框+半透明', disabled: false },
];

// 摄像机枚举
const CameraModeEnumList = [
	{ key: 'perspective', icon: perspectiveImg, desc: '透视摄像机', disabled: false },
	{ key: 'orthographic', icon: orthographicImg, desc: '正交摄像机', disabled: false },
];

// 其他操作模式
const PlaneEnumList = [
	{ key: 'planeXY', icon: planeXYImg, desc: 'XY平面', disabled: false },
	{ key: 'planeYZ', icon: planeYZImg, desc: 'YZ平面', disabled: false },
	{ key: 'planeXZ', icon: planeXZImg, desc: 'XZ平面', disabled: false },
	{ key: 'planeDiyXY', icon: planeDiyXYImg, desc: '自设XY平面', disabled: false },
	{ key: 'planeDiyYZ', icon: planeDiyYZImg, desc: '自设YZ平面', disabled: false },
	{ key: 'planeDiyXZ', icon: planeDiyXZImg, desc: '自设XZ平面', disabled: false },
	{ key: 'curveTangentPlane', icon: curveTangentPlaneImg, desc: '曲面切平面', disabled: false },
	{ key: 'curveNormalPlane', icon: curveNormalPlaneImg, desc: '曲线法平面', disabled: false },
];

// 其他操作模式
const OtherEnumList = [
	{ key: 'default', icon: otherImg, desc: '默认', disabled: false },
	{ key: 'show', icon: showImg, desc: '全部显示', disabled: false },
	// { key: 'hidden', icon: hiddenImg, desc: '隐藏', disabled: false },
	{ key: 'hiddenAllFace', icon: hideFaceImg, desc: '隐藏所有面', disabled: false },
	{ key: 'hiddenWireframe', icon: hideWireframeImg, desc: '隐藏线框', disabled: false },
	{ key: 'hiddenAllYsLine', icon: hideLineImg, desc: '隐藏所有自由线', disabled: false, showDivider: true },
	// { key: 'copy', icon: copyImg, desc: '复制', disabled: false },
	// { key: 'move', icon: moveImg, desc: '移动', disabled: false },
	// { key: 'rotate', icon: rotateImg, desc: '翻转', disabled: false },
	{ key: 'screenshot', icon: screenshotImg, desc: '屏幕截图', disabled: false },
];

const Icons = {
	selfAdaptionImg,
	subDownImg,
	downTriangleImg,
	drawPointImg,
	drawCurveImg,
	clippingImg,
	clearImg,
};

export default {
	name: 'ViewController',
	components: {
		ClippingCard,
		NavigationDropdown,
	},
	props: {
		selectMode: { // 点选模式
			type: String,
			required: false,
			default: SelectModeEnum.solid,
		},
		action: { // 动作: 框选等
			type: String,
			required: false,
			default: '',
		},
		selectDraw: { // 绘图模式
			type: String,
			required: false,
			default: '',
		},
		renderMode: { // 渲染模式
			type: String,
			required: false,
			default: RenderModeEnum.surfaceAndWireframe,
		},
		planeMode: { // 参考平面模式
			type: String,
			required: false,
			default: '',
		},
		viewMode: {
			type: String,
			required: false,
			default: 'default',
		},
		disabledSelectFace: { // 禁用选择面
			type: Boolean,
			required: false,
			default: false,
		},
		disabledSelectSolid: { // 禁用选择体
			type: Boolean,
			required: false,
			default: false,
		},
		disabled: { // 整体禁用
			type: Boolean,
			required: false,
			default: false,
		},
		config: {
			type: String,
			required: true,
		},
	},
	data () {
		return {
			MOUSE,
			...Icons,
			DrawEnum,
			ActionEnum,

			DrawEnumList,
			ViewEnumList,
			OtherEnumList,
			ActionEnumList,
			SelectModeEnumList,
			RenderModeEnumList,
			CameraModeEnumList,
			PlaneEnumList,

			lastDrawType: '',
			lastActionType: '',
			curAction: '',
			curDrawMode: '',
			curChoose: 'default',
			curOtherChoose: 'default',
			curView: ViewEnum.default,
			curSelectMode: SelectModeEnum.solid,
			curCameraMode: CameraModeEnum.orthographic,
			curRenderMode: RenderModeEnum.surfaceAndWireframe,
			curPlaneMode: '',
			disabledNav: false,
			mouseDropdownVisible: false,
			mouseLeftButton: MOUSE.ROTATE,
			mouseRightButton: MOUSE.PAN,
		};
	},
	computed: {
		curPlaneModeIcon () {
			return this.PlaneEnumList.find(i => i.key === this.curPlaneMode)?.icon ?? this.PlaneEnumList[0].icon;
		},
	},
	watch: {
		selectMode: {
			immediate: true,
			handler () {
				this.curChoose = this.selectMode;
			},
		},
		renderMode: {
			immediate: true,
			handler () {
				this.curRenderMode = this.renderMode;
			},
		},
		planeMode: {
			immediate: true,
			handler () {
				this.curPlaneMode = this.planeMode;
			},
		},
		viewMode: {
			immediate: true,
			handler () {
				this.curView = this.viewMode;
			}
		},
		action: {
			immediate: true,
			handler (curAction) {
				this.curAction = curAction;
				if (!curAction) {
					this.lastActionType = '';
				}
			},
		},
		selectDraw: {
			immediate: true,
			handler (mode) {
				this.curDrawMode = mode;
				if (!mode) {
					this.lastDrawType = '';
				}
			},
		},
		disabledSelectFace: {
			immediate: true,
			handler () {
				this.disabledFace = this.disabledSelectFace;
				this.checkCurChoose();
			},
		},
		disabledSelectSolid: {
			immediate: true,
			handler () {
				this.disabledSolid = this.disabledSelectSolid;
				this.checkCurChoose();
			},
		},
		disabled: {
			immediate: true,
			handler () {
				this.disabledNav = this.disabled;
			},
		},
	},
	methods: {
		preventDefaults,

		/**
		 * 检查当前选择
		 */
		checkCurChoose () {
			if (this.disabledFace && this.disabledSolid) {
				this.curChoose = '';
			}
		},

		/**
		 * 更新视图
		 */
		updateView (data) {
			this.curView = data.key;
			this.$emit('updateView', data.key);
		},

		drawHandle (data) {
			this.onDraw(data.key);
		},

		actionHandle (data) {
			if (this.lastActionType && this.lastActionType === data.key) {
				this.lastActionType = '';
				this.curAction = '';
			} else {
				this.lastActionType = data.key;
				this.curAction = data.key;
			}
			this.$emit('onAction', this.curAction);
		},

		/**
		 * 选择模型(点、线、面、体)
		 */
		selectModeHandle (data) {
			if (data.key === SelectModeEnum.surface && this.disabledSelectFace) {
				const selectModeItem = this.SelectModeEnumList.find(selectModeItem => selectModeItem.key === SelectModeEnum.surface);
				if (selectModeItem) {
					selectModeItem.disabled = true;
				}
				return;
			}
			if (data.key === SelectModeEnum.solid && this.disabledSelectSolid) {
				const selectModeItem = this.SelectModeEnumList.find(selectModeItem => selectModeItem.key === SelectModeEnum.solid);
				if (selectModeItem) {
					selectModeItem.disabled = true;
				}
				return;
			}
			this.$emit('selectModeHandle', data.key);
		},

		/**
		 * 重置相机
		 */
		resetCamera () {
			this.$emit('resetCamera');
		},

		/**
		 * 更改相机投影模式
		 */
		changeCameraMode (data) {
			this.$emit('changeCameraMode', data.key);
		},

		/**
		 * 更改渲染模式
		 */
		changeRenderMode (data) {
			this.$emit('changeRenderMode', data.key);
		},

		/**
		 * 重置视图
		 */
		resetView () {
			this.curView = 'default';
			this.$emit('resetView');
		},

		/**
		 * 绘图
		 * type: 画点、画线、画圆
		 */
		onDraw (type) {
			if (this.lastDrawType && this.lastDrawType === type) {
				this.lastDrawType = '';
				this.curDrawMode = '';
			} else {
				this.lastDrawType = type;
				this.curDrawMode = type;
			}
			this.$emit('onDraw', this.curDrawMode);
		},

		/**
		 * 清除绘制
		 */
		clearDrawn () {
			this.curDrawMode = '';
			this.$emit('clearDrawn');
		},

		/**
		 * 框选
		 */
		onBoxSelect (action) {
			this.curAction = action;
			this.$emit('onBoxSelect', action);
		},

		/**
		 * 参考平面选择
		 */
		onPlaneModeSelect (action) {
			this.curPlaneMode = action;
			this.$emit('onPlaneModeSelect', action);
		},

		/**
		 * 裁切
		 */
		updateClipping (value) {
			this.$emit('changeClipping', value);
		},

		/**
		 * 旋转视图
		 */
		updateRotate (axis, angle) {
			this.$emit('updateRotate', { axis, angle });
		},

		/**
		 * 鼠标左键设置
		 * @param event
		 * @param value
		 */
		mouseLeftSetting (event, value) {
			preventDefaults(event);
			this.mouseLeftButton = value;
			this.mouseDropdownVisible = true;
			if (value === MOUSE.PAN) {
				this.mouseRightButton = MOUSE.ROTATE;
			}
			if (value === MOUSE.ROTATE) {
				this.mouseRightButton = MOUSE.PAN;
			}
			this.$emit('mouseLeftSetting', value);
		},

		/**
		 * 鼠标右键设置
		 * @param event
		 * @param value
		 */
		mouseRightSetting (event, value) {
			preventDefaults(event);
			this.mouseRightButton = value;
			this.mouseDropdownVisible = true;
			if (value === MOUSE.PAN) {
				this.mouseLeftButton = MOUSE.ROTATE;
			}
			if (value === MOUSE.ROTATE) {
				this.mouseLeftButton = MOUSE.PAN;
			}
			this.$emit('mouseRightSetting', value);
		},

		/**
		 * 其他选择更改
		 */
		otherSelect (data) {
			this.$emit('otherSelect', data.key);
		},

		getIcon (list, key) {
			const item = list.find(item => item.key === key);
			return item ? item.icon : '';
		},
		handleClippingModal () {
			if (!this.$refs.clippingCard.modalVisible) {
				this.$refs.clippingCard.open();
			} else {
				this.$refs.clippingCard.close();
			}
		}
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/mixins/variables.less";

.grid {
	display: flex;
	height: 46px;
	line-height: 46px;
	justify-content: flex-start;

	&.wrapper {
		width: auto;
		margin: 1px auto 0;
	}

	/deep/ .ant-divider-vertical {
		margin: 0 0 0 0;
		top: 15px;
		height: 20px;
		left: 10px;
	}
}

.grid-cell {
	flex: 1;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-items: center;

	img, .text {
		margin-left: 5px;
		margin-right: 5px;
		width: 20px;
		height: 20px;
		cursor: pointer;
	}

	.p3 {
		padding: 3px;
	}

	&:before {
		opacity: 0;
		content: "\2714";
		color: @primary;
	}

	&.active {
		img, .svg-icon {
			background: @primary-assist;
			border-radius: 4px;
			border: 1px solid @primary;
		}

		&:before {
			content: "\2714";
			color: @primary;
			opacity: 1;
		}
	}

	.active-draw {
		img, .svg-icon {
			border-radius: 3px;
			border: 1px solid @primary;
			background: @primary-assist;
		}
	}

	.svg-icon-overwrite {
		display: block;
		margin-left: 8px;
		margin-top: 6px;
	}

	.action-active {
		/deep/ svg {
			border-radius: 3px;
			border: 1px solid @primary;
			background: @primary-assist;
		}
	}

	&.disabled {
		img, .svg-icon {
			background: @disabled-bg;
			cursor: not-allowed;
		}
	}

	.svg-icon {
		font-size: 20px;
		padding: 4px;
		cursor: pointer;
	}

	.mt {
		margin-top: 12px;
	}

	.sub {
		//width: 5px;
		//height: 5px;
		width: 8px;
		height: 4px;
		position: absolute;
		right: 5px;
		bottom: 12px;

		&.fix1 {
			//right: 0;
			//bottom: 10px;
			right: -10px;
			bottom: 19px;
		}

		&.fix2 {
			right: 0;
			bottom: 0;
		}
	}

	.nav-clear {
		font-size: 24px;
		margin-left: 5px;
		color: @primary;
	}
}

.ant-dropdown-menu {
	padding: 0 4px;

	.ant-divider-horizontal {
		margin: 0;
		left: 0;
	}

	.ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title {
		padding: 5px 2px;
	}

	.ant-dropdown-menu-item:hover {
		background: none;

		img, .svg-icon {
			background: @primary-assist;
			border-radius: 4px;
			border: 1px solid @primary;
		}
	}
}

.controller {
	/deep/ .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow {
		top: 4px;
	}
}
</style>
