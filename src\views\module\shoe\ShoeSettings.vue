<template>
	<div class="params-settings">
		<template v-for="(data, index) in dataList">

			<div class="box" :key="'param_' + data.type + index" v-if="isParts(data)">
				<div class="column upload">
					<div class="content">
						<div class="item percent-65">
							<h3 style="margin-bottom: 0">
								<b>{{ data.title }}:</b>
							</h3>
						</div>
						<div class="item right" style="margin-left: 40px;">
							<FileLoader
								showType="icon"
								class="upload-btn"
								:disabled="data.disabled"
								:loading="data.submitLoading"
								:exts="data.exts"
								@ok="uploadCallbackHandler($event, data)"
							/>
						</div>
					</div>
				</div>
				<div class="column upload" style="margin-top: 10px;" v-if="data.value.length !== 0">
					<div class="content">
						<div class="item percent-65">
							<span class="label">
								<a-tooltip placement="top" :title="data.value.join(',')">
									<a-icon type="file" />{{ data.value.join(',') | Ellipsis(8) }}
								</a-tooltip>
							</span>
						</div>

						<div class="item right">
							<div class="mr-10" @click="allVisibleHandler(data)">
								<svg-icon :type="data.visible ? 'param-visible' : 'param-hidden'" />
							</div>

							<div class="mr-10">
								<input type="color" v-model="data.color" @input="allColorHandler(data)">
							</div>

							<a-popover :title="null" placement="topRight">
								<template #content>
									<div class="display-flex-center">
										<a-slider
											:min="0"
											:max="100"
											:step="1"
											v-model="data.opacity"
											:style="{ width: '100px', margin: '10px 0 0 10px' }"
											@change="allOpacityHandler(data)"
										/>
										<div class="opacity">{{ data.opacity }}%</div>
									</div>
								</template>
								<svg-icon type="param-opacity" />
							</a-popover>
						</div>
					</div>
				</div>
				<a-divider v-if="index !== dataList.length - 1" style="margin-top: 16px;margin-bottom: 16px"></a-divider>
			</div>
			<div class="box" :key="'param_' + data.type + index" v-if="isSlider(data) && data.show !== false">
				<SliderInput
					:label="data.label"
					:min="data.min"
					:max="data.max"
					:step="data.step"
					:name="data.key"
					:tip="data.tip"
					:value="data.value"
					@valueChange="handleValueChange"
				/>
			</div>

			<div class="box" :key="'param_' + data.type + index" v-if="isSwitch(data) && data.show !== false">
				<SwitchInput
					:label="data.label"
					:name="data.key"
					:value="data.value"
					:tip="data.tip"
					@valueChange="handleValueChange"
				/>
			</div>
			<div class="box" :key="'param_' + data.type + index" v-if="isTitle(data)">
				<LabelTitle
					:label="data.label"
					:tip="data.tip"
				/>
			</div>
			<div class="box" :key="'param_' + data.type + index" v-if="isRadioInput(data) && data.show !== false">
				<RadioInput
					:label="data.label"
					:name="data.key"
					:value="data.value"
					:tip="data.tip"
					:options="data.options"
					@valueChange="handleValueChange"
				/>
			</div>

			<div class="box" :key="'param_' + data.type + index" v-if="isDivider(data)">
				<a-divider v-if="isDivider(data)" style="margin-top: 10px; margin-bottom: 10px;" />
			</div>
		</template>
	</div>
</template>

<script>
/* ===================================
 * 鞋-参数设置页
 * Created by zhangzheng on 2022/05/23.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { difference, getCustomId, isArray } from '@/utils/utils';
import SliderInput from '@/components/common/SliderInput';
import SwitchInput from '@/components/common/SwitchInput';
import RadioInput from '@/components/common/RadioInput';
import LabelTitle from '@/components/common/LabelTitle';
import FileLoader from '@/components/core/FileLoader';

export default {
	name: 'ShoeSettings',
	components: {
		SliderInput,
		SwitchInput,
		RadioInput,
		LabelTitle,
		FileLoader,
	},
	props: {
		activeKey: {
			type: Array,
			required: false,
			default: () => ['1'],
		},
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	data () {
		return {};
	},
	methods: {
		isParts (data) {
			return data.type === 'parts';
		},

		isTitle (data) {
			return data.type === 'title';
		},

		isSlider (data) {
			return data.type === 'slider';
		},

		isSwitch (data) {
			return data.type === 'switch';
		},

		isDivider (data) {
			return data.type === 'divider';
		},

		isRadioInput (data) {
			return data.type === 'radio';
		},

		/**
		 * 上传回调处理
		 */
		uploadCallbackHandler (file, data) {
			this.$emit('uploadCallback', { file, data });
		},

		/**
		 * 所有显隐处理
		 */
		allVisibleHandler (data) {
			data.visible = !data.visible;
			this.$emit('onAllVisible', data);
		},

		/**
		 * 所有颜色处理
		 */
		allColorHandler (data) {
			this.$emit('onAllColor', data);
		},

		/**
		 * 所有透明度处理
		 */
		allOpacityHandler (data) {
			this.$emit('onAllOpacity', data);
		},

		/**
		 * 显示/隐藏处理
		 */
		visibleHandler (data) {
			data.visible = !data.visible;
			this.$emit('onVisible', data);
		},

		/**
		 * 颜色处理
		 */
		colorHandler (data) {
			this.$emit('onColor', data);
		},

		/**
		 * 透明度处理
		 */
		opacityHandler (data) {
			this.$emit('onOpacity', data);
		},

		/**
		 * 值更改
		 */
		handleValueChange (e) {
			this.$emit('valueChange', e);
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.params-settings {
	.big-label {
		height: 24px;
		line-height: 24px;
		font-size: 18px;
		font-weight: 600;
		color: #111111;
	}

	.label {
		height: 20px;
		line-height: 20px;
		font-size: 14px;
		font-weight: bold;
		color: #333333;
	}

	.required:before {
		display: inline-block;
		margin-right: 4px;
		color: #F5222D;
		font-size: 14px;
		font-family: SimSun, sans-serif;
		line-height: 1;
		content: "*";
		margin-left: -11px;
	}

	.colon {
		display: inline-block;
		margin-right: 5px;
		font-weight: bold;
	}

	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}

	.action {
		margin-left: 10px;
		border-radius: 2px;

		&.activate {
			svg {
				border: 1px solid @primary;
				border-radius: 2px;
			}
		}
	}

	.box {
		margin-left: 4px;
		&.mtb-16 {
			margin: 16px 0;
		}

		.content {
			display: flex;
			width: 100%;

			.item {
				flex: 1;

				&.percent-55 {
					flex: 0 0 55%;
				}

				&.percent-65 {
					flex: 0 0 65%;
				}

				svg {
					width: 20px;
					height: 20px;
					color: @primary;
				}

				.icon {
					padding: 0 12px;
					color: @primary;
					cursor: pointer;

					&.right {
						text-align: right;
					}
				}

				&.right {
					display: flex;
					justify-content: flex-end;
					align-items: center;
					margin-left: -10px;

					svg, input[type="color"] {
						cursor: pointer;
					}

					.mr-10 {
						margin-right: 10px;
					}
				}
			}
		}

		.column {
			padding: 10px 0;

			&.upload {
				padding: 0;

				.select-box {
					padding: 0;
				}

				.upload-btn {
					width: 280px;

					/deep/ .ant-btn {
						border-radius: 3px;
						background-color: #6362FF;
					}
				}
			}

			&.group {
				/deep/ .ant-btn {
					font-size: 12px;
				}

				.group-container {
					width: 100%;
					display: flex;
					flex-direction: column;

					.activate {
						color: #8E8AFF;
						border-color: #8E8AFF;
					}

					.select-box {
						padding: 0 10px 5px 10px;

						&:first-child {
							padding: 10px 10px 5px 10px;
						}
					}

					.select-item {
						overflow: hidden;
						display: flex;

						&.h22 {
							height: 24px;
							line-height: 22px;
						}
					}

					.btn-add {
						padding: 10px;

						.ant-btn {
							width: 100%;
							height: 35px;
							min-height: 35px;
							border-radius: 3px;
						}
					}

					.ant-tag {
						margin-right: 0;
						margin-bottom: 0;
						height: 24px;
						line-height: 22px;
						overflow: hidden;
						cursor: pointer;

						min-width: 96px;
						text-align: center;
					}

					.extra-tag {
						width: 120px;
						min-width: 120px;
						overflow: hidden;
						/*文本不会换行*/
						white-space: nowrap;
					}
				}
			}

			&.slider-wrapper {
				padding: 5px 0;

				.select-box {
					background: transparent;
					height: 46px;
					line-height: 46px;

					/deep/ .slider {
						.ant-slider {
							width: 140px;
							margin-left: 10px;
						}
					}

					.select-item {
						overflow: hidden;
					}
				}
			}

			.select-box {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				background: #F9F9F9;
				padding: 10px;

				.select-item {
					flex: 1;
					min-height: 22px;
					max-height: 60px;
					overflow: auto;
				}

				.select-item.u-22 {
					flex: 0 0 22px;
					margin: 0 0 0 5px;
				}

				/deep/ .select-item.u-60 {
					flex: 0 0 60px;

					.ant-input-number {
						width: 60px;
					}
				}

				/deep/ .ant-tag {
					margin-right: 5px;
					max-width: 72px;
					padding: 0 5px;
					cursor: pointer;
					color: #333333;
					background: #FAFAFA;
					border: 1px solid #E5E5E5;

					&:nth-child(n+3) {
						margin-bottom: 5px;
					}

					&:last-child {
						margin-bottom: 0;
					}

					&.activate {
						color: #8E8AFF;
						border-color: #8E8AFF;
					}

					.anticon-close {
						margin-left: 0;
					}
				}

				.icon {
					color: @primary;
					cursor: pointer;

					.clear {
						width: 20px;
						height: 20px;
						vertical-align: -5px;
					}
				}
			}
		}
	}
}
</style>
