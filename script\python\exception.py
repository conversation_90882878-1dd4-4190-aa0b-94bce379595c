# 定义基础的用户异常类
class UserException(Exception):
	code = 400
	msg = 'sorry,you make a mistake(^_^)!'
	error_code = 999

	def __init__(self, msg=None, code=None, error_code=None):
		if msg is not None:
			self.msg = msg
		if code is not None:
			self.code = code
		if error_code is not None:
			self.error_code = error_code


# 定义用户输入异常，继承自用户异常
class ParameterException(UserException):
	code = 400
	msg = 'User Input Wrong'
	error_code = 1000


# 还可以定义其他异常类
# 资源未找到异常
class NotFound(UserException):
	code = 404
	msg = 'the resource is not found'
	error_code = 1001


# 认证异常类
class AuthFailed(UserException):
	code = 401
	msg = 'authorization failed'
	error_code = 1005


def defaultFun():
	pass


# 调用示例
# raise ParameterException(msg="Input param must be Digital Number！")
# raise Exception('x 不能大于 5。x 的值为: {}'.format(x))

# 捕获异常
def capture_except(success_Callback=defaultFun, error_callback=defaultFun, except_type=None):
	if except_type is None:
		except_type = KeyboardInterrupt
	try:
		success_Callback()
	except except_type:
		error_callback()
