<template>
	<a-modal
		:title="isEdit ? '编辑项目' : '创建项目'"
		:visible="visible"
		:mask-closable="false"
		@cancel="closeWindow"
	>
		<a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
			<a-form-model-item label="项目名称" prop="projectName">
				<a-input type="text" v-model="form.projectName" placeholder="请输入项目名称" :max-length="25" />
			</a-form-model-item>

			<!-- <a-form-model-item label="客户名称" prop="clientName">
				<NewTag :dataList="form.clientTags" btnText="客户名称" @change="(tags) => this.form.clientTags = tags" />
			</a-form-model-item>

			<a-form-model-item label="产品名称" prop="clientName">
				<NewTag :dataList="form.productTags" btnText="产品名称" @change="(tags) => this.form.productTags = tags" />
			</a-form-model-item> -->

			<a-form-model-item label="项目介绍" prop="content">
				<a-input type="textarea" v-model="form.content" placeholder="请输入项目介绍，80个字以内" :max-length="80" />
			</a-form-model-item>
		</a-form-model>

		<template slot="footer">
			<a-button type="default" @click="closeWindow">取消</a-button>
			<a-button type="primary" @click="handleSubmit" :loading="loading">确定</a-button>
		</template>

	</a-modal>
</template>

<script>
/* ===================================
 * 新建/编辑项目弹窗
 * Created by cjking on 2022/02/17.
 * Copyright 2022, Inc.
 * =================================== */
import { mapGetters } from 'vuex';
import { logger } from '@/utils/logger';
import { formValidate } from '@/utils/validate';
import { createProject, editProject, getProjectInfo } from '@/api';
// import NewTag from '@/views/module/mold/NewTag';

export default {
	name: 'CreateProject',
	// components: { NewTag },
	data () {
		return {
			visible: false,
			labelCol: { span: 5 },
			wrapperCol: { span: 16 },
			form: {
				projectName: '',    // 项目名称
				content: '',        // 项目介绍
				moduleId: '',			// 模块ID
				// clientTags: '',     // 客户标签列表
				// productTags: '',    // 产品标签列表
			},
			rules: {
				projectName: formValidate.noEmpty('请输入项目名称，25个字以内'),
				content: formValidate.noEmpty('请输入项目介绍，80个字以内'),
			},
			isEdit: false,
			projectInfo: {},    // 项目信息
			loading: false,
		};
	},
	computed: {
		...mapGetters(['userInfo']),
	},
	methods: {
		/**
		 * 显示弹窗
		 */
		showModal (moduleId) {
			this.form.moduleId = moduleId;
			this.isEdit = false;
			this.visible = true;
			this.loading = false;
		},

		/**
		 * 编辑弹窗
		 */
		async editModal (moduleId, id) {
			this.isEdit = true;
			await this.getProjectInfo(id);
			this.form.moduleId = moduleId;
			this.visible = true;
			this.loading = false;
		},

		/**
		 * 获取项目详情
		 */
		async getProjectInfo (id) {
			const res = await getProjectInfo({ id });
			logger.log('获取项目详情 res: ', res);
			if (res?.success && res?.result) {
				this.form = Object.assign(this.form, res?.result || {});
			}
		},

		/**
		 * 关闭弹窗
		 */
		closeWindow () {
			this.$emit('close');
			this.visible = false;
			this.$refs.ruleForm.clearValidate();
		},

		/**
		 * 确定
		 */
		handleSubmit () {
			this.$refs.ruleForm.validate(async valid => {
				if (!valid) return false;
				const params = {
					...this.form.id && { id: this.form.id }, // 项目ID
					projectName: this.form.projectName,     // 项目名称
					content: this.form.content,             // 项目描述
					moduleId: this.form.moduleId,			// 模块ID
					// clientTags: this.form.clientTags,    // 客户标签列表
					// productTags: this.form.productTags,  // 产品标签列表
				};

				this.loading = true;
				if (this.isEdit) {
					logger.log('编辑项目参数 params: ', params);
					const res = await editProject(params);
					logger.log('编辑项目成功 res: ', res);
					if (res?.success) {
						this.$message.success('编辑项目成功');
						this.confirmAndCloseWin();
					}
				} else {
					logger.log('创建项目参数 params: ', params);
					const res = await createProject(params);
					logger.log('创建项目成功 res: ', res);
					if (res?.success) {
						this.$message.success('创建项目成功');
					}
					this.confirmAndCloseWin();
				}
				this.loading = false;
			});
		},

		/**
		 * 确认并关闭弹窗
		 */
		confirmAndCloseWin () {
			this.$emit('ok', this.projectInfo);
			this.$refs.ruleForm.clearValidate();
			this.visible = false;
		},
	},
};
</script>

<style lang="less" scoped>
.tag {
	height: 32px;
	line-height: 30px;
}
</style>
