<template>
	<svg v-if="type" :class="svgClass" :style="vStyle" aria-hidden="true" @click="onClick">
		<use :xlink:href="icon" />
	</svg>
</template>

<script>
/* 注意：需要把svg图片文件的(需要修改的)fill以及stroke修改成currentColor */
export default {
	name: 'SvgIcon',
	props: {
		type: {
			type: String,
			required: true,
		},
		className: {
			type: String,
			required: false,
			default: '',
		},
		vStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
		spin: {
			type: Boolean,
			required: false,
			default: false,
		},
	},
	data () {
		return {
			icon: '',
			svgClass: '',
		};
	},
	watch: {
		type: {
			immediate: true,
			handler () {
				this.icon = `#icon-${ this.type }`;
			},
		},
		className: {
			immediate: true,
			handler () {
				if (this.className) {
					this.svgClass = 'svg-icon ' + this.className;
				} else {
					this.svgClass = 'svg-icon';
				}
			},
		},
		spin: {
			immediate: true,
			handler (bool) {
				if (bool) {
					this.svgClass = this.svgClass + ` icon-spin`;
				} else {
					this.svgClass = this.svgClass.replace('icon-spin', '');
				}
			},
		},
	},
	methods: {
		onClick (e) {
			this.$emit('click', e);
		},
	},
};
</script>

<style lang="less" scoped>
.svg-icon {
	width: 1em;
	height: 1em;
	vertical-align: -0.25em;
	fill: currentColor; /** 此属性为更改svg颜色属性设置 **/
	stroke: currentColor;
	overflow: hidden;
}

.icon-spin {
	animation: spin .8s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
</style>
