build-dev: #开发环境
	python3 $(shell pwd)/script/python/cli.py --env "dev" --action build --server ************* --intranet ************* --static_server ************* --clean true
build-test: #测试环境
	python3 $(shell pwd)/script/python/cli.py --env "test" --action build --server ************* --intranet ************* --static_server ************* --clean true
build-prod: #生产环境
	python3 $(shell pwd)/script/python/cli.py --env "prod" --action build --server ************ --intranet ************ --static_server ************ --clean true
run:
	build-dev #默认执行dev环境

publish-dev:
	python3 $(shell pwd)/script/python/cli.py --action publish --env "dev" --version latest --server ************* --intranet *************
publish-test:
	python3 $(shell pwd)/script/python/cli.py --action publish --env "test" --version latest --server ************* --intranet *************
publish-prod:
	python3 $(shell pwd)/script/python/cli.py --action publish --env "prod" --version latest --server ************ --intranet ************
