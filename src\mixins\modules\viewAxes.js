/* ===================================
 * vtkView坐标轴
 * Created by cjking on 2022/03/11.
 * Copyright 2022, Inc.
 * =================================== */
import * as THREE from 'three';
import { ColorEnum } from '@/constants';
import { DynamicTexture } from '@/components/core/DynamicTexture';

export const mixinViewAxes = {
	methods: {
		/**
		 * 初始化轴
		 */
		initAxes () {
			// Make simple orientation arrows and box - REF: http://jsfiddle.net/b97zd1a3/16/
			const arrowCanvas = document.querySelector('#arrowCanvas');
			const arrowRenderer = new THREE.WebGLRenderer({
				alpha: true,
				antialias: true,
				canvas: arrowCanvas,
			}); // clear
			arrowRenderer.setClearColor(ColorEnum.black, 0);
			arrowRenderer.setSize(arrowCanvas.clientWidth * window.devicePixelRatio, arrowCanvas.clientHeight * window.devicePixelRatio, false);
			const arrowScene = new THREE.Scene();
			const arrowCamera = new THREE.PerspectiveCamera(50, arrowCanvas.clientWidth / arrowCanvas.clientHeight, 1, 500);
			const perspectiveCamera = this.getPerspectiveCamera();
			if (perspectiveCamera) arrowCamera.up = perspectiveCamera.up; // important!
			const arrowPos = new THREE.Vector3(0, 0, 0);
			arrowScene.add(new THREE.ArrowHelper(new THREE.Vector3(1, 0, 0), arrowPos, 50, 0x7F2020, 15, 10));
			arrowScene.add(new THREE.ArrowHelper(new THREE.Vector3(0, 1, 0), arrowPos, 50, 0x207F20, 15, 10));
			arrowScene.add(new THREE.ArrowHelper(new THREE.Vector3(0, 0, 1), arrowPos, 50, 0x20207F, 15, 10));
			arrowScene.add(new THREE.Mesh(
				new THREE.BoxGeometry(40, 40, 40),
				new THREE.MeshLambertMaterial({
					color: 0xaaaaaa,
					// flatShading: false
				}),
			));
			arrowScene.add(new THREE.HemisphereLight(0xC7E8FF, 0xFFE3B3, 1.2));

			this.setArrowCanvas(arrowCanvas);
			this.setArrowRenderer(arrowRenderer);
			this.setArrowScene(arrowScene);
			this.setArrowCamera(arrowCamera);

			const userSetting = {
				fontStyle: 'bold',
				fontFamily: 'Arial',
				faceColor: 'Gainsboro',
				edgeColor: 'LightSlateGray',
				fontSizeScale: (res) => res / 2,
				resolution: 150,
				xPlusFaceProperty: {
					text: 'X+',
					fontColor: 'Tomato',
				},
				xMinusFaceProperty: {
					text: 'X-',
					fontColor: 'Tomato',
				},
				yPlusFaceProperty: {
					text: 'Y+',
					fontColor: 'SeaGreen',
				},
				yMinusFaceProperty: {
					text: 'Y-',
					fontColor: 'SeaGreen',
				},
				zPlusFaceProperty: {
					text: 'Z+',
					fontColor: 'DeepSkyBlue',
				},
				zMinusFaceProperty: {
					text: 'Z-',
					fontColor: 'DeepSkyBlue',
				},
			};
			const fontList = [
				{
					text: 'X+',
					faceName: 'xPlus',
					fontColor: 'Tomato',
				},
				{
					text: 'X-',
					faceName: 'xMinus',
					fontColor: 'Tomato',
				},
				{
					text: 'Y+',
					faceName: 'yPlus',
					fontColor: 'DeepSkyBlue',
				},
				{
					text: 'Y-',
					faceName: 'yMinus',
					fontColor: 'DeepSkyBlue',
				},
				{
					text: 'Z+',
					faceName: 'zPlus',
					fontColor: 'SeaGreen',
				},
				{
					text: 'Z-',
					faceName: 'zMinus',
					fontColor: 'SeaGreen',
				},
			];
			const face_textures = [];

			function createFaceTextures () {
				for (let i = 0; i < 6; i++) {
					const dynamicTexture = new DynamicTexture(512, 512);
					dynamicTexture.clear('LightSlateGray').updateFaceTexture(fontList[i].faceName, userSetting);
					face_textures.push(dynamicTexture);
				}
			}

			createFaceTextures();
			const materials = [
				new THREE.MeshBasicMaterial({ map: face_textures[0].texture }),
				new THREE.MeshBasicMaterial({ map: face_textures[1].texture }),
				new THREE.MeshBasicMaterial({ map: face_textures[2].texture }),
				new THREE.MeshBasicMaterial({ map: face_textures[3].texture }),
				new THREE.MeshBasicMaterial({ map: face_textures[4].texture }),
				new THREE.MeshBasicMaterial({ map: face_textures[5].texture }),
			];
			arrowScene.add(new THREE.Mesh(new THREE.BoxGeometry(40, 40, 40), materials));
		},
	},
};
