/* ===================================
 * 自定义拦截器
 * Created by cjking on 2021/04/07.
 * Copyright 2021, Inc.
 * =================================== */
import { firstUpperCase, formatDate, getStaticUrl, toExponential, toFileSize } from '@/utils/utils';

/**
 * 将number数值转化成为货币格式
 * @param num
 * @param places 保留小数位数
 * @param symbol 货币符号 ($、￥、€、￡、₣、¥、₩)
 * @param thousand 整数部分千位分隔符
 * @param decimal 小数分隔符
 * @returns {string}
 */
export const FormatMoney = (num, places, symbol, thousand, decimal) => {
	num = num || 0;
	places = !isNaN(places = Math.abs(places)) ? places : 2;
	symbol = symbol || '';
	thousand = thousand || ',';
	decimal = decimal || '.';
	let j;
	const negative = num < 0 ? '-' : '';
	const i = parseInt(num = Math.abs(+num || 0).toFixed(places), 10) + '';
	j = (j = i.length) > 3 ? j % 3 : 0;
	return symbol + negative + (j ? i.substr(0, j) + thousand : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, '$1' + thousand) + (places ? decimal + Math.abs(num - i).toFixed(places).slice(2) : '');
};

/**
 * 时间转换管道
 * @param time
 * @param format
 * @returns {string}
 */
export const DatePipe = (time, format) => formatDate(time, format);

/**
 * 清理空值，对象
 * @param children
 * @returns {*[]}
 */
export const FilterEmpty = (children = []) => children.filter(c => c.tag || (c.text && c.text.trim() !== ''));

/**
 * 字符串超长截取省略号显示
 * @param value
 * @param length 全角字符个数
 * @param showEllipsis 是否显示省略号
 * @returns {string}
 */
export const Ellipsis = (value, length = 25, showEllipsis = true) => {
	function getBytesLength (str) {
		return str.length + escape(str).split('%u').length - 1;
	}

	function removeCharAt (str, index) {
		let retStr = '';
		if (index <= str.length) {
			if (index === 0) {
				retStr = str.substring(1);
			} else {
				if (index === str.length - 1) {
					retStr = str.substring(0, str.length - 1);
				} else {
					retStr = str.substring(0, index) + str.substring(index + 1);
				}
			}
		}
		return retStr;
	}

	function intercept (str, length) {
		let len = getBytesLength(str);
		while (len > length) {
			str = removeCharAt(str, str.length - 1);
			len = getBytesLength(str);
		}
		return str;
	}

	if (!value) {
		return '';
	}
	if (value.length > length) {
		const oldValue = value;
		value = intercept(value, length * 2);
		if (value !== oldValue) {
			return value + (showEllipsis ? '...' : '');
		}
	}
	return value;
};

/**
 * 字符串处理(将整数部分逢三一断)
 * @param value
 * @returns {string}
 */
export const NumberFormat = (value) => {
	if (!value) {
		return '0';
	}
	return value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
};

/**
 * json字符串输出
 * @param {Object|Array} value
 * @returns {String|*}
 */
export const Json = (value) => {
	if (typeof value !== 'object') {
		return value;
	}
	return JSON.stringify(value);
};

/**
 * join字符串拼接
 * @param {Array|*} list
 * @param {String} separator    分隔符
 * @returns {String}
 */
export const Join = (list, separator = ',') => {
	if (!Array.isArray(list)) {
		return list;
	}
	return list.join(separator);
};

/**
 * 回显字典
 * @param {String} fieldValue   比较(条件)字段值
 * @param {Array} list          字典列表
 * @param {String} fieldKey     比较(条件)字段key
 * @param {String} echoKey      回显字段key
 * @returns {String|Number}
 */
export const EchoDict = (fieldValue, list = [], fieldKey = 'id', echoKey = 'name') => {
	const item = list.find(item => item && String(item[fieldKey]) === String(fieldValue));
	return item ? item[echoKey] : '';
};

/**
 * 文件大小显示(单位：M)
 * @returns {string}
 */
export const ToFileSize = toFileSize;

/**
 * url格式化
 * @param {String} url
 * @returns {String}
 */
export const StaticUrlFormat = (url) => getStaticUrl({ url });

/**
 * 首字母大写
 * @param {String} str
 * @returns {String}
 */
export const FirstUpperCase = (str) => firstUpperCase(str);

/**
 * 转换为数字
 * @param {String} str
 * @returns {Number}
 */
export const ToNumber = (str) => Number(str);

/**
 * 转换为科学计数法
 * @param {String | Number} value
 * @param {Number} fractionDigits
 * @param {Number} limitLength 长度大等于6时，开始转换为科学计数法
 * @param {Boolean} isToExponential 是否转换问科学计数法
 * @returns {String}
 */
export const ToExponential = (value, fractionDigits = null, limitLength = 6, isToExponential = true) => {
	if (String(value).length < limitLength || !isToExponential) {
		return value;
	}
	return toExponential(value, fractionDigits);
};
