/* ===================================
 * 请求动作二次封装 API
 * Created by cjking on 2020/05/18.
 * Copyright 2020, Inc.
 * =================================== */
import * as http from '@/http';

/**
 * 动态http请求动作
 * @param url
 * @param params
 * @param method
 * @returns {*}
 */
export const httpAction = (url, params, method) => http[method](url, params);

/**
 * 获取请求动作
 * @param {String} url
 * @param {Object} [params=] params
 * @param {Object} [options=] options
 * @returns {Promise}
 */
export const getAction = (url, params, options) => http.get(url, params, options);

/**
 * 删除请求动作
 * @param {String} url
 * @param {Object} [params=] params
 * @param {Object} [options=] options
 * @returns {Promise}
 * @returns {Promise}
 */
export const deleteAction = (url, params, options) => http.del(url, params, options);

/**
 * 提交请求动作
 * @param {String} url
 * @param {Object} [params=] params
 * @param {Object} [options=] options
 * @returns {Promise}
 */
export const postAction = (url, params, options) => http.post(url, params, options);

/**
 * 更新请求动作
 * @param {String} url
 * @param {Object} [params=] params
 * @param {Object} [options=] options
 * @returns {Promise}
 */
export const putAction = (url, params, options) => http.put(url, params, options);

/**
 * 获取文件数据流
 * @param {String} url
 * @param {String} responseType
 * @param {Boolean} loading
 * @returns {Promise}
 */
export const loadFile = (url, responseType = 'blob', loading = true) => http.get(url, null, {
	loading,
	localUrl: true,
	interceptRes: false,
	// printResponse: true,
	clipResponseData: true,
	responseType: responseType,
});

/**
 * 上传请求动作
 * @param {String} url
 * @param {Object} [params=] params
 * @param {Object} [options=] options
 * @returns {Promise}
 */
export const uploadAction = (url, params, options) => {
	const formData = new FormData();
	for (const key in params) {
		formData.append(key, params[key]);
	}
	options = Object.assign(options, { headers: { 'Content-Type': 'multipart/form-data' } });
	return http.post(url, params, options);
};
