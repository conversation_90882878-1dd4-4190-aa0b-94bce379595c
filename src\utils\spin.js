/* ===================================
 * 全局spin(加载中)
 * Created by cjking on 2021/04/07.
 * Copyright 2021, Inc.
 * =================================== */
import store from '@/store';

export const spinService = {
	/**
	 * 开启加载中
	 * @param {Object} options 参数 [可选]
	 * @param {Object} [options.tip='Loading...'] options.tip 描述文案, 默认 "Loading..." [可选]
	 * @param {Object} [options.size='default'] options.size 组件大小, 默认 "default"。可选值为 small default large [可选]
	 * @param {Object} [options.delayTime=200] options.delayTime 延迟加载时间, 默认 200 [可选]
	 */
	async open (options = {}) {
		if (Object.keys(options).length) {
			await store.dispatch('initSpin', options);
		}
		let spinCount = store.state?.spin?.spinCount || 0;
		spinCount++;
		await store.dispatch('updateSpinCount', spinCount);
		await store.dispatch('openSpin', true);
	},

	/**
	 * 关闭加载中
	 */
	async close () {
		let spinCount = store.state?.spin?.spinCount || 0;
		spinCount--;
		await store.dispatch('updateSpinCount', spinCount);
		if (spinCount <= 0) {
			spinCount = 0;
			await store.dispatch('openSpin', false);
		}
	},
};
