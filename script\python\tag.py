#!/usr/bin/python3
# -*- coding: UTF-8 -*-
###################################
# tag管理类
# Created by cjking on 2020/07/06.
###################################
import os
import utils
import logger
import version
import propertiesUtil
from server import getServer

logger = logger.Logger()
config = propertiesUtil.Properties(f'{os.getcwd()}/script/python/config.properties').getProperties()


# 选择仓库
async def getRepository(env):
	repositories = utils.getRepositories(env, like=config.get('repository_name'))
	if utils.isEmpty(repositories):
		return None
	index = await utils.getSelectValue(repositories, '请选择仓库？')
	logger.choose("您选择的仓库index: %s" % index, "blue")
	repository = repositories[index - 1]
	logger.choose("您选择的仓库是: %s" % repository, "blue")
	return repository


# 查询所有版本号
async def queryTags(env):
	repository = config.get('repository_name') if env == 'prod' else config.get('repository_name') + '-' + env
	tag_list = utils.getTagList(repository, env)
	utils.jsonFormatPrint(tag_list)


# 删除版本号
async def delTag(env):
	repository = config.get('repository_name') if env == 'prod' else config.get('repository_name') + '-' + env
	tag_version = await version.chooseVersion(repository, env)
	if utils.notEmpty(tag_version):
		tag_digest = utils.getTagDigest(repository, tag_version, env)
		logger.info('tag_digest: %s' % tag_digest)

		del_status = utils.delTagByDigest(repository, tag_digest, env)
		if utils.includes('Accepted', del_status):
			logger.info('del_status: %s' % del_status)
		else:
			logger.warning('del_status: %s' % del_status)

	tag_list = utils.getTagList(repository, env)
	utils.jsonFormatPrint(tag_list)


# 删除所有版本号
async def delTagAll(env):
	repository = config.get('repository_name') if env == 'prod' else config.get('repository_name') + '-' + env
	tag_list = utils.getTagList(repository, env)

	if utils.notEmpty(tag_list):

		for i, chooseVersion in enumerate(tag_list):
			logger.choose("%s）%s" % (i + 1, chooseVersion))

			tag_digest = utils.getTagDigest(repository, chooseVersion, env)
			logger.info('tag_digest: %s' % tag_digest)

			del_status = utils.delTagByDigest(repository, tag_digest, env)
			if utils.includes('Accepted', del_status):
				logger.info('del_status: %s' % del_status)
			else:
				logger.warning('del_status: %s' % del_status)

	tag_list = utils.getTagList(repository, env)
	utils.jsonFormatPrint(tag_list)


# 保留最后七天版本号
async def reservedLastSevenDaysTags(env, g_server):
	repository = config.get('repository_name') if env == 'prod' else config.get('repository_name') + '-' + env
	logger.choose("repository: %s" % (repository))

	# 服务器地址
	server = g_server
	if server is None:
		server = await getServer(env)
	logger.choose("您选择的服务器地址是: %s" % server, "blue")

	tag_list = utils.getTagList(repository, env)

	if utils.notEmpty(tag_list):

		tag_list = tag_list[7:]  # 从第8个元素(包含8)开始截取列表

		for i, chooseVersion in enumerate(tag_list):
			logger.choose("删除版本%s" % (chooseVersion))

			tag_digest = utils.getTagDigest(repository, chooseVersion, env)
			logger.info('tag_digest: %s' % tag_digest)

			del_status = utils.delTagByDigest(repository, tag_digest, env)
			if utils.includes('Accepted', del_status):
				logger.info('del_status: %s' % del_status)
			else:
				logger.warning('del_status: %s' % del_status)

			# 删除docker容器及镜像
			await delete_docker_container_and_image(env, server, repository, chooseVersion)

	tag_list = utils.getTagList(repository, env)
	utils.jsonFormatPrint(tag_list)


# 删除docker容器及镜像
async def delete_docker_container_and_image(env: str, server: str, repository: str, chooseVersion: str):
	basePath = config.get('api_out_network') if env == 'prod' else config.get('api_in_network')
	registry_url = basePath.replace('https://', '')

	del_container_command = f'docker stop {repository}:{chooseVersion} && docker rm -f {repository}:{chooseVersion}'
	logger.choose("删除容器: %s" % del_container_command)
	utils.popCmd(del_container_command)

	del_image_command = f'docker rmi -f {repository}:{chooseVersion}'
	logger.choose("删除镜像: %s" % del_image_command)
	utils.popCmd(del_image_command)

	del_image_command2 = f'docker rmi -f {registry_url}/{repository}:{chooseVersion}'
	logger.choose("删除镜像2: %s" % del_image_command2)
	utils.popCmd(del_image_command2)

	# 清理远程服务器资源
	func = lambda string: string.replace(f':{chooseVersion}', '')
	logger.choose("删除字符串中所有版本: %s" % func(del_container_command))
	ssh_port = config.get('ssh_port_prod') if env == 'prod' else config.get('ssh_port')
	utils.quickCmd(f'ssh -p {ssh_port} root@{server} {func(del_container_command)}')
	utils.quickCmd(f'ssh -p {ssh_port} root@{server} {del_image_command}')
	utils.quickCmd(f'ssh -p {ssh_port} root@{server} {del_image_command2}')
