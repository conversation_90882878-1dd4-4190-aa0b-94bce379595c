<template>
	<a-modal title="下载结果" :visible="visible" :mask-closable="false" @cancel="close">
		<HeaderTip :module-object="moduleObject" />
		<a-table
			:row-key="record => record.key"
			:data-source="dataSource"
			:row-selection="{
				selectedRowKeys: selectedRowKeys,
				getCheckboxProps: record => ({
					props: {
						name: record.key,
						disabled: record.status !== TaskStatusEnum.success, // Column configuration not to be checked
					},
				}),
				onSelect: onSelect,
				onChange: onSelectChange
			}"
			:columns="columns"
			:pagination="false"
		>
			<span slot="statusSlot" slot-scope="record">
				<span v-if="!record.status" class="sc-status-default"><a-icon type="question-circle" /> 未生成</span>
				<span v-if="record.status === 2" class="sc-status-default"><a-icon type="question-circle" /> 未计算</span>
				<span v-if="record.status === TaskStatusEnum.waiting" class="sc-status-primary"><a-icon type="loading" /> 生成中</span>
				<span v-if="record.status === TaskStatusEnum.success" class="sc-status-success"><a-icon type="check-circle" /> 已生成</span>
				<span v-if="record.status === TaskStatusEnum.error" class="sc-status-error"><a-icon type="close-circle" /> 生成失败</span>
			</span>
		</a-table>

		<template slot="footer">
			<a-button type="primary" @click="handleSubmit" :disabled="!moduleObject.canDownload || !this.selectedRowKeys.length">下载</a-button>
		</template>
	</a-modal>
</template>

<script>
/* ===================================
* 随行冷结果下载弹窗
* Created by cjking on 2022/02/17.
* Copyright 2022, Inc.
* =================================== */
import { mapActions } from 'vuex';
import { downloadZip } from '@/api';
import { mixinModule } from '@/mixins';
import { TaskStatusEnum } from '@/constants';
import { arrayBufferToJson, formatDate, saveArrayBuffer } from '@/utils/utils';
import HeaderTip from '@/components/common/HeaderTip';

export default {
	name: 'DownloadModal',
	components: {
		HeaderTip,
	},
	mixins: [mixinModule],
	props: {
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	data () {
		return {
			TaskStatusEnum,
			visible: false,
			loading: false,
			dataSource: [],
			columns: [
				{
					title: '文件名',
					align: 'center',
					dataIndex: 'label',

				},
				{
					title: '生成状态',
					align: 'center',
					scopedSlots: { customRender: 'statusSlot' },
				},
			],
			taskId: '',
			selectionRows: [],
			selectedRowKeys: [],
		};
	},
	mounted () {
		this.taskId = this.$route.params.taskId || '';
	},
	watch: {
		dataList: {
			immediate: true,
			deep: true,
			handler (val) {
				this.dataSource = val.filter(i => ['result_pipe_line', 'result_make_pipe', 'result_include_water_model'].includes(i.key));
			},
		},
	},
	methods: {
		...mapActions(['initPermissionList']),
		/**
		 * 显示弹窗
		 */
		async open () {
			this.visible = true;
			this.loading = false;
			this.selectionRows = [];
			this.selectedRowKeys = [];
		},

		/**
		 * 关闭弹窗
		 */
		close () {
			this.visible = false;
		},

		/**
		 * 确定
		 */
		async handleSubmit () {
			const params = {
				id: this.taskId,
				typeName: this.selectedRowKeys.map(i => i + '_export.stp').join(','),
			};

			const res = await downloadZip(params, {
				interceptRes: false,
				responseType: 'arraybuffer',
			});
			if (res?.data && res.data.byteLength > 200) { // 错误消息时，返回数据长度约123
				saveArrayBuffer(res.data, this.taskId + '_' + formatDate(new Date(), 'YYYYMMDDHHmmss') + '.zip');
				this.initPermissionList();
			} else {
				const data = arrayBufferToJson(res.data);
				if (data) {
					this.$notification.error({ message: '温馨提示', description: data.message });
				}
			}
		},

		/**
		 * 确认并关闭弹窗
		 */
		onSelectChange (selectedRowKeys) {
			this.selectedRowKeys = selectedRowKeys;
		},

		onSelect (record, selected) {
			if (selected) {
				this.selectionRows.push(record);
			} else {
				this.selectionRows.forEach(function (item, index, arr) {
					if (item.key === record.key) {
						arr.splice(index, 1);
					}
				});
			}
		},
	},
};
</script>

<style lang="less" scoped>
.tag {
	height: 32px;
	line-height: 30px;
}

.sc-status {
	&-default {
		color: #404040;

		i {
			color: #DD9524;
		}
	}

	&-primary {
		color: #6362FF;

		i {
			color: #6362FF;
		}
	}

	&-success {
		color: #17AE45;

		i {
			color: #17AE45;
		}
	}

	&-error {
		color: #E5252F;

		i {
			color: #E5252F;
		}
	}
}
</style>
