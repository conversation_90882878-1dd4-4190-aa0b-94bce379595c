import base64

import compute_rhino3d.Util
import requests

compute_rhino3d.Util.url = "http://192.168.87.18:8081/"
post_url = compute_rhino3d.Util.url + "grasshopper"

data_bytes = open("D:/input_and_output/test.gh", mode="rb").read()
encoded = base64.b64encode(data_bytes)
decoded = encoded.decode("utf-8")

data = {
	"algo": decoded,
	"pointer": None,
	"values": [
		{
			"ParamName": "wireName",
			"InnerTree": {
				"0": [
					{
						"data": "wire.3dm"
					}
				],
			}
		}
	]
}

response = requests.post(post_url, json=data)
res = response.content
print(res)
