/* ===================================
 * 用户相关API
 * Created by cjking on 2020/05/18.
 * Copyright 2020, Inc.
 * =================================== */
import * as http from '@/http';

/**
 * 获取验证码
 * @param key
 * @returns {Promise}
 */
export const getSmSCode = (key) => http.get(`/sys/get_code`, null, { loading: false });

/**
 * 登录
 * @param {Object} params
 * @return {Promise}
 */
export const login = (params) => http.post(`/sys/login`, params, { isEncrypt: true, loading: false });

/**
 * 注册
 * @param {Object} params
 * @return {Promise}
 */
export const register = (params) => http.post(`/sys/register`, params, { isEncrypt: true, loading: false });

/**
 * 退出(注销)
 * @return {Promise}
 */
export const logout = () => http.post(`/sys/logout`);

/**
 * 更新密码
 * @param params
 * @returns {Promise}
 */
export const updatePassword = (params) => http.put(`/sys/user/updatePassword`, params);

/**
 * 忘记密码
 * @param params
 * @returns {Promise}
 */
export const forgotPassword = (params) => http.post(`/sys/user/forgotPassword`, params);

/**
 * 通过邮件修改密码
 * @param params
 * @returns {Promise}
 */
export const updatePasswordByEmail = (params) => http.post(`/sys/user/update_pwd_once`, params, { isEncrypt: true });

/**
 * 查询企业授权的业务模块
 * @param params
 * @returns {Promise}
 */
export const listGrantModule = (params) => http.get(`/module/sysModule/listGrantModule`, params, { loading: false });
