import A01 from '@/assets/img/texture/A01.png';
import A02 from '@/assets/img/texture/A02.png';
import A03 from '@/assets/img/texture/A03.png';
import A04 from '@/assets/img/texture/A04.png';
import A05 from '@/assets/img/texture/A05.png';
import A06 from '@/assets/img/texture/A06.png';
import A07 from '@/assets/img/texture/A07.png';
import A08 from '@/assets/img/texture/A08.png';
import A09 from '@/assets/img/texture/A09.png';
import A10 from '@/assets/img/texture/A10.png';
import A11 from '@/assets/img/texture/A11.png';
import A12 from '@/assets/img/texture/A12.png';
import A13 from '@/assets/img/texture/A13.png';
import A14 from '@/assets/img/texture/A14.png';
import A15 from '@/assets/img/texture/A15.png';
import A16 from '@/assets/img/texture/A16.png';
import A17 from '@/assets/img/texture/A17.png';
import A18 from '@/assets/img/texture/A18.png';
import B01 from '@/assets/img/texture/B01.png';
import B02 from '@/assets/img/texture/B02.png';
import B03 from '@/assets/img/texture/B03.png';
import B04 from '@/assets/img/texture/B04.png';
import B05 from '@/assets/img/texture/B05.png';
import B06 from '@/assets/img/texture/B06.png';
import B07 from '@/assets/img/texture/B07.png';
import B08 from '@/assets/img/texture/B08.png';

export const defaultPartData = {
	type: 'parts',
	key: 'model_part',
	value: [],
	visible: true,
	color: '#CCCCCC',
	opacity: 100,
	disabled: false,
	submitLoading: false,
};

/* 类型选择 */
const typeSource = [
	{
		url: A01,
		name: 'A01',
		value: 0,
		title: '波浪结构',
		desc: '菱形线网到波浪线网',
	},
	{
		url: A02,
		name: 'A02',
		value: 1,
		title: '线网结构',
		desc: '菱形线网到四边形线网',
	},
	{
		url: A03,
		name: 'A03',
		value: 2,
		title: '孔状结构',
		desc: '圆孔阵列到星形阵列',
	},
	{
		url: A04,
		name: 'A04',
		value: 3,
		title: '花形结构',
		desc: '闭合到绽放',
	},
	{
		url: A05,
		name: 'A05',
		value: 4,
		title: '脑纹结构',
		desc: '仅跟随纹理边长变化',
	},
	{
		url: A06,
		name: 'A06',
		value: 5,
		title: '波点结构',
		desc: '圆点到圆角方点',
	},
	{
		url: A07,
		name: 'A07',
		value: 6,
		title: '拼花结构',
		desc: '元素拼花分布',
	},
	{
		url: A08,
		name: 'A08',
		value: 7,
		title: '网格结构',
		desc: '四边形变化到菱形',
	},
	{
		url: A09,
		name: 'A09',
		value: 8,
		title: '渐消结构',
		desc: '元素渐消分布',
	},
	{
		url: A10,
		name: 'A10',
		value: 9,
		title: '风车结构1',
		desc: '静态网格到旋转造型',
	},
	{
		url: A11,
		name: 'A11',
		value: 10,
		title: '风车结构2',
		desc: '旋转网格到四边形结构',
	},
	{
		url: A12,
		name: 'A12',
		value: 11,
		title: '风车结构3',
		desc: '十字网格到旋转造型',
	},
	{
		url: A13,
		name: 'A13',
		value: 12,
		title: '风车结构4',
		desc: '十字网格到龟裂纹',
	},
	{
		url: A14,
		name: 'A14',
		value: 13,
		title: '风车结构5',
		desc: '旋转造型到菱形网格',
	},
	{
		url: A15,
		name: 'A15',
		value: 14,
		title: '风车结构6',
		desc: '旋转变化到菱形网格',
	},
	{
		url: A16,
		name: 'A16',
		value: 15,
		title: '风车结构7',
		desc: '十字造型开始旋转',
	},
	{
		url: A17,
		name: 'A17',
		value: 16,
		title: '风车结构8',
		desc: '菱形进行旋转变换',
	},
	{
		url: A18,
		name: 'A18',
		value: 17,
		title: '风车结构9',
		desc: '旋转造型回缩至菱形网格',
	},
];

/* 单元变种 */
const variantSource = [
	{
		url: B01,
		name: 'B01',
		value: 0,
		title: '矩阵分布',
		desc: '按照矩阵方式进行纹理分布',
		disabled: false,
	},
	{
		url: B02,
		name: 'B02',
		value: 1,
		title: '菱形分布',
		desc: '按照菱形方式进行纹理分布',
		disabled: false,
	},
	{
		url: B03,
		name: 'B03',
		value: 2,
		title: '菱形细分',
		desc: '按照菱形细分方式进行纹理分布',
		disabled: false,
	},
	{
		url: B04,
		name: 'B04',
		value: 3,
		title: '镜像分布',
		desc: '按照矩阵镜像分布方式进行纹理分布',
		disabled: false,
	},
	{
		url: B05,
		name: 'B05',
		value: 4,
		title: '连续细分',
		desc: '按照矩阵连续细分方式进行纹理分布',
		disabled: false,
	},
	{
		url: B06,
		name: 'B06',
		value: 5,
		title: '三角分布',
		desc: '按照三角分布方式进行纹理分布',
		disabled: false,
	},
	{
		url: B07,
		name: 'B07',
		value: 6,
		title: '三角细分',
		desc: '按照三角细分方式进行纹理分布',
		disabled: false,
	},
	{
		url: B08,
		name: 'B08',
		value: 7,
		title: '六边分布',
		desc: '按照六边分布方式进行纹理分布',
		disabled: false,
	},
];

export const defaultJsonData = [
	{
		type: 'slider',
		group: {
			label: '单元边长',
			tip: '单元边长应基于实际范围输入，过大或过小会影响生成结果，且边长越小计算耗时越长，建议最后调整边长值',
			visible: true,
		},
		label: '',
		key: 'bc_of_texture',
		value: 10,
		tip: '',
		min: 2,
		max: 20,
		step: 1,
		visible: true,
		showDivider: true,
	},
	{
		type: 'carousel',
		group: {
			label: '图案选择',
			tip: '',
			visible: true,
		},
		label: '类型选择',
		key: 'mold',
		value: 0,
		tip: '纹理主要类型选择',
		mode: 'popover',
		popoverTitle: '请选择纹理类型',
		dataList: [...typeSource],
		pagination: {
			pageSize: 6,
		},
		visible: false,
		showDivider: false,
	},
	{
		type: 'carousel',
		group: {
			label: '图案选择',
			tip: '',
			visible: false,
		},
		label: '单元变种',
		key: 'partition',
		value: 0,
		tip: '下属变种样式选择',
		mode: 'popover',
		popoverTitle: '请选择单元变种',
		dataList: [...variantSource],
		pagination: false,
		visible: false,
		invalid: false,
		showDivider: true,
	},
	{
		type: 'slider',
		group: {
			label: '变化调整',
			tip: '',
			visible: true,
		},
		label: '变化强度',
		key: 'strength_change',
		value: 0,
		tip: '使均匀纹理图案产生渐变',
		min: 0,
		max: 1,
		step: 0.001,
		visible: true,
		showDivider: false,
	},
	{
		type: 'slider',
		group: {
			label: '变化调整',
			tip: '',
			visible: false,
		},
		label: '额外增强',
		key: 'increase_in_addition',
		value: 1,
		tip: '若纹理交叉可减小取值',
		min: 1,
		max: 5,
		step: 0.001,
		visible: true,
		showDivider: false,
	},
	{
		type: 'slider',
		group: {
			label: '变化调整',
			tip: '',
			visible: false,
		},
		label: '变化抹匀',
		key: 'spreads_wipes_evenly',
		value: 0,
		tip: '减弱纹理变化的突兀感',
		min: 0,
		max: 5,
		step: 0.001,
		visible: true,
		showDivider: true,
	},
	{
		type: 'carousel',
		group: {
			label: '变化方向',
			tip: '',
			visible: true,
		},
		label: '方向选择',
		key: 'oneway_change',
		value: 0,
		tip: '',
		mode: 'text',
		dataList: [
			{
				value: 0,
				title: '单向变化',
			},
			{
				value: 1,
				title: '周期变化',
			},
		],
		children0: [
			{
				type: 'switch',
				group: {
					label: '变化方向',
					tip: '',
					visible: false,
				},
				label: '引导点',
				key: 'guided_point',
				value: 0,
				checked: !!0,
				tip: '',
				visible: true,
				disabled: false,
				showDivider: false,
			},
			{
				type: 'slider',
				group: {
					label: '变化方向',
					tip: '',
					visible: false,
				},
				label: '单向方向',
				key: 'guided_onway',
				value: 0,
				tip: '引导点基于首个被选中表面边缘区间滑动',
				min: 0,
				max: 1,
				step: 0.001,
				visible: true,
				showDivider: false,
			},
		],
		children1: [
			{
				type: 'carousel',
				group: {
					label: '变化方向',
					tip: '',
					visible: false,
				},
				label: '周期方向',
				key: 'period_direction',
				value: 0,
				tip: '切换以获取合适方向',
				mode: 'text',
				dataList: [
					{
						value: 0,
						title: '周期方向1',
					},
					{
						value: 1,
						title: '周期方向2',
					},
				],
				pagination: false,
				visible: false,
				showDivider: false,
			},
			{
				type: 'slider',
				group: {
					label: '变化方向',
					tip: '',
					visible: false,
				},
				label: '周期偏移',
				key: 'cycle_shift',
				value: 0,
				tip: '纹理整体偏移',
				min: 0,
				max: 360,
				step: 1,
				visible: false,
				showDivider: false,
			},
		],
		pagination: false,
		visible: true,
		showDivider: false,
	},
];
