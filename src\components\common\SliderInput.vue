<template>
	<div class="attr-item">
		<div class="attr-item-title" v-if="label">
			<div>
				<label style="margin-right: 6px;">
					<b><span
						v-if="required"
						style="margin-left: -6px;color:red"
					>*</span>{{ label }}{{ label ? ':' : '' }}</b>
				</label>
				<a-tooltip v-if="tip" placement="top">
					<template slot="title">
						{{ tip }}
					</template>
					<a-icon type="question-circle"></a-icon>
				</a-tooltip>
			</div>
		</div>
		<div class="attr-item-content" v-if="!hideSlider">
			<a-slider
				v-model="fieldValue"
				:min="min"
				:max="max"
				:step="step"
				:marks="{ [min]: min, [max]: max }"
				:style="{ width: '140px', margin: '14px 0 0 10px' }"
				@change="handleValueChange"
			/>
			<a-input-number
				size="small"
				:value="fieldValue"
				:min="min"
				:max="max"
				:step="step"
				:style="{ width: '60px', marginLeft: '10px' }"
				@change="handleValueChange"
			/>
		</div>
		<div class="attr-item-content" v-else>
			<a-input-number
				size="small"
				:value="fieldValue"
				:min="min"
				:max="max"
				:step="step"
				:style="{ width: '240px', marginLeft: '10px' }"
				@change="handleValueChange"
			/>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/4/26.
 * Copyright 2022, Inc.
 * =================================== */
export default {
	components: {},
	props: {
		required: {
			type: Boolean,
			required: false,
			default: false,
		},
		title: {
			type: String,
			required: false,
			default: '',
		},
		label: {
			type: String,
			required: false,
			default: '',
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
		name: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: Number,
			required: false,
			default: null,
		},
		hideSlider: {
			type: Boolean,
			required: false,
			default: false,
		},
		min: {
			type: Number,
			required: false,
			default: null,
		},
		max: {
			type: Number,
			required: false,
			default: null,
		},
		step: {
			type: Number,
			required: false,
			default: null,
		}
	},
	data () {
		return {
			fieldValue: undefined,
			bakValue: undefined,
		};
	},
	watch: {
		value: {
			deep: true,
			immediate: true,
			handler (newVal) {
				this.fieldValue = newVal <= 0 ? 0 : newVal;
			},
		},
	},
	created () {
		this.bakValue = this.value;
	},
	methods: {
		handleValueChange (value) {
			if (value <= 0) {
				value = value <= 0 ? 0 : value;
				this.fieldValue = value;
			}

			if (typeof value === 'number') {
				if ((this.step + '').match(/0\./)) {
					const len = (this.step + '').replace(/.*\./, '').length;
					value = +value.toFixed(len);
				} else {
					value = Math.round(value);
				}
			}
			if (typeof this.min === 'number' && value < this.min) {
				value = this.min;
			} else if (typeof this.max === 'number' && value > this.max) {
				value = this.max;
			} else if (isNaN(value)) {
				value = this.bakValue;
			}
			this.bakValue = value;
			this.$emit('valueChange', { value, key: this.name });
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.attr-item {
	width: 100%;
	/deep/ .ant-slider-mark-text {
		top: -34px
	}

	&-title, &-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	&-title-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;

		> * {
			margin-left: 10px;
		}
	}

	&-content {
		padding: 10px;
		min-height: 40px;
	}

	&-content2 {
		padding: 10px;
		height: 40px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	&-icon {
		width: 20px;
		height: 20px;
	}

	&-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.svg-icon, input {
			margin-left: 10px;
			cursor: pointer;
		}
	}

	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}

	.svg-icon {
		color: @primary;
	}

	/deep/ .ant-slider-track {
		background-color: @primary;
	}

	/deep/ .ant-slider-handle {
		background-color: @primary;
	}
}
</style>
