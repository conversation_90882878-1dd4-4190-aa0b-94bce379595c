{"name": "client-frontend", "description": "创客云客户端", "version": "0.1.1", "private": true, "scripts": {"commit": "git-cz", "serve": "vue-cli-service serve", "dll": "webpack --progress --config ./webpack.dll.js", "itk": "webpack --progress --config ./webpack.itk.js", "lint": "vue-cli-service lint", "lint:fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/", "inspect": "vue-cli-service inspect", "version": "node ./script/writeVersion.js", "build:itk": "cd itk && npx itk-js build .", "prepare": "if exist .husky\\pre-commit del /Q .husky\\pre-commit && husky install && npx husky add .husky/pre-commit \"npm run lint\"", "build:dev": "vue-cli-service build --max_old_space_size=4096 --report --mode development", "build:test": "vue-cli-service build --max_old_space_size=4096 --report --mode test", "build:prod": "vue-cli-service build --max_old_space_size=4096 --report --mode production", "publish:dev": "python3 ./script/python/cli.py --action publish --env 'dev' --version latest --server ************* --intranet *************", "publish:test": "python3 ./script/python/cli.py --action publish --env 'test' --version latest --server ************* --intranet *************", "publish:prod": "python3 ./script/python/cli.py --action publish --env 'prod' --version latest --server ************ --intranet ************"}, "dependencies": {"ant-design-vue": "1.7.8", "axios": "0.21.4", "comlink": "4.3.1", "core-js": "3.21.0", "crypto-js": "4.1.1", "dayjs": "1.10.7", "html2canvas": "1.4.1", "jszip": "3.10.0", "moment": "2.29.3", "mousetrap": "1.6.5", "nprogress": "0.2.0", "three": "0.129.0", "vmath": "1.4.8", "vue": "2.6.14", "vue-lazyload": "1.3.3", "vue-router": "3.5.2", "vuex": "3.6.2"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@commitlint/cli": "12.1.4", "@commitlint/config-conventional": "12.1.4", "@vue/cli-plugin-babel": "4.5.15", "@vue/cli-plugin-eslint": "4.5.15", "@vue/cli-plugin-router": "4.5.15", "@vue/cli-plugin-vuex": "4.5.15", "@vue/cli-service": "4.5.15", "@vue/eslint-config-standard": "5.1.2", "babel-eslint": "8.2.6", "babel-plugin-import": "1.13.3", "clean-webpack-plugin": "3.0.0", "commitizen": "4.2.4", "compression-webpack-plugin": "6.1.1", "conventional-changelog-cli": "2.2.2", "copy-webpack-plugin": "5.1.2", "cz-conventional-changelog": "3.3.0", "eslint": "6.8.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "eslint-plugin-vue": "6.2.2", "expose-loader": "0.7.5", "husky": "6.0.0", "less": "3.13.1", "less-loader": "5.0.0", "resize-observer-polyfill": "1.5.1", "svg-sprite-loader": "6.0.11", "url-loader": "4.1.1", "vue-template-compiler": "2.6.14", "webpack": "4.46.0", "webpack-bundle-analyzer": "3.9.0", "webpack-cli": "4.9.2", "worker-loader": "3.0.8", "write-file-webpack-plugin": "4.5.1"}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}