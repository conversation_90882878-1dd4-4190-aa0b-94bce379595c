/* ===================================
 * http请求封装
 * Created by cjking on 2021/04/07.
 * Copyright 2021, Inc.
 * =================================== */
import axios from 'axios';
import router from '../router';
import Config from '@/config/Config';
import { IMessage } from './message';
import { spinService } from '@/utils/spin';
import { Crypt } from '@/utils/crypt/crypt';
// import { configureScope } from '@sentry/browser';
import { ACCESS_TOKEN, TOKEN_TYPE } from '@/constants';
import { notification, Modal } from 'ant-design-vue';
import { randomString, toUrlString, isEmpty, isEmptyObject, isNumber, isObject, isString, clearUserData } from '@/utils/utils';
import { logger } from '@/utils/logger';
import { storage } from '@/utils/storage';
import { Base64Crypt } from '@/utils/crypt';

let isLoginWarned = false;
let timestampServer = Date.now();
let timestampClient = Date.now();
const headerEncryptStr = 'Asyt';

/**
 * 默认选项类型
 * @typedef DefaultOptionsType
 * @property {string} [method=] method - 请求方法类型
 * @property {string} [url=] url - 请求地址
 * @property {number} [timeout=] timeout - 请求超时时间(毫秒)60s
 * @property {object} [headers=] headers - 头信息
 * @property {boolean} [localUrl=] localUrl - 是否本地url，false: 根据配置文件，url会被重写，true: url保持原样不变
 * @property {object} [params=] params - 参数(get类型)
 * @property {object} [data=] data - 参数(body类型)
 * @property {boolean} [interceptRes=] interceptRes - 是否开启统一错误处理
 * @property {boolean} [isEncrypt=] isEncrypt - 是否加密传输数据
 * @property {boolean} [cache=] cache - 是否开启缓存
 * @property {boolean} [loading=] loading - 是否开启全局loading状态
 * @property {boolean} [withCredentials=] withCredentials - 是否携带cookie信息
 * @property {boolean} [printResponse=] printResponse - 是否打印response
 * @property {boolean} [clipResponseData=] clipResponseData - 修剪请求返回的数据(即：去掉最外层的data包装,直接返回内部数据)
 * @property {string} [responseType=] responseType - 响应格式: 可选项 'arraybuffer', 'blob', 'document', 'json', 'text', 'stream'
 * @property {function} [onUploadProgress=] onUploadProgress - 上传进度条回调函数，onUploadProgress: (progressEvent) => {}
 */

/**
 * 默认选项
 */
const defaultOptions = {
	method: 'get',              // 请求方法类型
	url: '',                    // 请求地址
	timeout: 60000,             // 请求超时时间（毫秒）60s
	headers: {},                // 头信息
	localUrl: false,            // 是否本地url，false: 根据配置文件 url会被重写, true: url保持原样不变
	params: null,               // 参数(get类型)
	data: null,                 // 参数(body类型)
	interceptRes: true,         // 是否开启统一错误处理
	isEncrypt: false,  	        // 是否加密传输数据
	cache: false,               // 是否开启缓存
	loading: true,		        // 是否开启全局loading状态
	withCredentials: false,	    // 是否携带cookie信息
	printResponse: false,       // 是否打印response
	clipResponseData: false,    // 是否修剪请求返回的数据(即：去掉最外层的data包装,直接返回内部数据)
	responseType: 'json',	    // 响应格式: 可选项 'arraybuffer', 'blob', 'document', 'json', 'text', 'stream'
	onUploadProgress: () => {}, // 上传进度条
	// onUploadProgress: (progressEvent) => {
	// 	if (progressEvent.lengthComputable) {
	// 		const val = (progressEvent.loaded / progressEvent.total * 100).toFixed(0);
	// 		this.showProgress = true;
	// 		this.progress = parseInt(val);
	// 	}
	// }
};

/**
 * 格式化url
 */
export function formatUrl (url) {
	let baseUrl = '';
	if (Config.basePath && !/^https?/.test(url)) {
		url = url.replace(/\/\//g, '/');
		baseUrl = Config.basePath;
		if (baseUrl.endsWith('/')) { // 判断是否作为结尾
			baseUrl = baseUrl.substring(0, baseUrl.length - 1);
		}
	}
	if (url && !url.startsWith('/') && !/^https?/.test(url)) { // 判断当前字符串是否以 "/" 作为开头
		baseUrl += '/';
	}
	baseUrl += url;
	return baseUrl;
}

/**
 * 获取token
 * @returns {string}
 */
const getToken = () => storage.get(ACCESS_TOKEN) || '';

/**
 * 获取头信息
 * @param {Object} [options=] options
 */
const getHeaders = (options = {}) => {
	const defaultHeaders = {
		// 'Authorization': storage.get(TOKEN_TYPE) + ' ' + getToken(),
		'X-Access-Token': getToken(),
		'X-From-Source': 'pc',  // 固定来源
	};
	if (options && options.headers && typeof options.headers === 'object') {
		const headers = options.headers;
		for (const key in headers) {
			if (key && Object.prototype.hasOwnProperty.call(headers, key)) {
				defaultHeaders[key] = headers[key];
			}
		}
	}
	return defaultHeaders;
};

/**
 * 处理请求结果
 * @param {Object} result
 * @param {Boolean} publicCall 是否外部(公共)调用
 */
export const handleResponse = (result, publicCall = false) => {
	result = publicCall && result && result.data ? result.data : result;
	let message = '';
	if (!result) {
		message = '网络繁忙,未返回数据';
	} else {
		message = result.message ? result.message : (result.msg ? result.msg : (result?.code ? '网络繁忙,未返回MSG' : '网络繁忙,未返回CODE'));
	}
	if (Array.isArray(message) && message.length) {
		const list = [];
		for (const msgItem of message) {
			if (msgItem.loc?.length && msgItem.loc?.length >= 2) {
				list.push(`${ msgItem.loc[0] } ${ msgItem.loc[1] } missing, ${ msgItem.msg }`);
			}
		}
		message = list.join('\n');
	}
	if ((result && result.code && [IMessage.UserNotLogin.code].includes(result.code)) || ([IMessage.TokenExpired.code].includes(result.code) && result.message === IMessage.TokenExpired.msg)) {
		if (!isLoginWarned) noLoginIntercept();
	} else {
		notification?.error({ message: '温馨提示', description: message });
	}
};

/**
 * 拦截非200请求响应
 * @param result
 */
const interceptResponse = (result) => {
	if (result && isNumber(result.code)) {
		result.code = Number(result.code);
	}
	if (!result || (result.code !== IMessage.OK.code && result.code !== IMessage.Success.code)) {
		handleResponse(result);
		return null;
	}
	return result;
};

/**
 * 未登录拦截
 * @param {function} [cancelCallback=] cancelCallback   自定义取消回调函数
 * @param {function} [confirmCallback=] confirmCallback 自定义确认回调函数
 * @param {Object} [options=] options 可选参数
 */
export const noLoginIntercept = (cancelCallback, confirmCallback, options = {}) => {
	isLoginWarned = true;
	setTimeout(() => isLoginWarned = false, 500);

	if (Modal?.destroyAll) {
		Modal.destroyAll();
	}

	if (window.location.pathname !== '/user/login') {
		Modal.confirm({
			title: '温馨提示',
			content: '登录已过期，请重新登录',
			okText: '重新登录',
			cancelText: '取消',
			cancelButtonProps: {
				props: { disabled: options?.disabled ?? true },
			},
			onCancel: () => {
				if (cancelCallback) {
					if (typeof cancelCallback === 'function') {
						cancelCallback();
					}
				}
			},
			onOk: async () => {
				if (confirmCallback) {
					if (typeof confirmCallback === 'function') {
						confirmCallback();
					}
				} else {
					clearUserData();
					return router.push('/user/login');
				}
			},
		});
	} else {
		clearUserData();
	}
};

/**
 * 处理请求错误
 * @param error
 */
export const handleError = (error) => {
	let message = isString(error) ? error : (error?.response ? error.response?.data?.message : (error?.message ?? ''));
	if (error?.status === 400) {
		message = '请求无效 (Bad request)';
	}
	if (error?.status === 404) {
		message = '请检查URL，以确保路径正确';
	}
	if (message?.includes('Unknown Error')) {
		message = '';
	}
	// 超时处理
	if (String(error)?.includes('timeout')) {
		message = '请求超时';
	}
	if (
		(message === IMessage.TokenExpired.msg && error.response?.data?.code === IMessage.TokenExpired.code) ||
		(error.response?.data?.code === IMessage.Unauthorized.code)
	) {
		if (!isLoginWarned) noLoginIntercept();
		return;
	}
	notification?.error({ message: '温馨提示', description: message || IMessage.NetWorkError.msg });
};

const request = (method, url, params, options = {}) => {
	const sourceUrl = url || '';
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async (resolve, reject) => {
		params = params ?? {};
		url = (/^https?/.test(url) || options.localUrl) ? url : formatUrl(url);
		options.headers = getHeaders(options);
		options = Object.assign({ ...defaultOptions }, options, { method, url });
		if (options.isEncrypt) {
			const { encryptData, signatureData } = await getEncryptData(params);
			params = encryptData;
			options.headers = Object.assign(options.headers, signatureData);
		}
		if (isObject(params) && !options.cache) {
			params._ = Date.now();
		}
		if (method === 'delete' || method === 'get') {
			if (!isEmptyObject(params)) {
				options.params = params;
			}
		} else {
			if (!isObject(params) || !isEmptyObject(params)) {
				const contentType = options.headers['Content-Type'];
				if (contentType && contentType.includes('application/x-www-form-urlencoded')) {
					options.data = isObject(params) ? toUrlString(params) : params;
				} else {
					options.data = params;
				}
			}
		}
		if (options.loading) await spinService.open();
		axios.request(options).then(async response => {
			if (options.printResponse) {
				logger.log(`[${ url }] Response: `, response);
			}
			if (options.loading) await spinService.close();
			if (response && response.data) {
				if (isNumber(response.data.code)) {
					response.data.code = parseInt(response.data.code, 10);
				}
				if (isNumber(response.data.errcode)) {
					response.data.errcode = parseInt(response.data.errcode, 10);
				}
			}

			updateTimestamp(response);

			// 对返回结果统一处理
			if (options.interceptRes) {
				if (interceptResponse(response.data)) {
					return resolve(options.clipResponseData ? (response.data && !isEmpty(response.data?.result) ? response.data.result : response.data) : response.data);
				} else {
					setSentryExtra(sourceUrl, params, response);
					return reject('Response Error');
				}
			} else {
				if (response?.data?.code !== IMessage.OK.code) {
					setSentryExtra(sourceUrl, params, response);
				}
				return resolve(options.clipResponseData ? response.data : response);
			}
		}).catch(error => {
			logger.error('NetWork error: ', error);
			setSentryExtra(sourceUrl, params, error?.response);
			if (options.loading) spinService.close();
			if (options.interceptRes) handleError(error);
			reject(error?.response);
		});
	});
};

/**
 * 保存最近一次服务器时间
 * @param res
 */
const updateTimestamp = (res) => {
	if (res && res.headers) {
		timestampServer = new Date(res?.headers?.date).getTime();
		timestampClient = new Date().getTime();
	}
};

/**
 * 获取加密数据
 */
const getEncryptData = async (bodyParams) => {
	const timestamp = timestampServer + (new Date().getTime() - timestampClient);
	const pkgInfo = await import('../../package.json'); // 有缓存机制，只会加载一次
	const signatureData = {
		[`X-${ headerEncryptStr }-Version`]: pkgInfo.version,
		[`X-${ headerEncryptStr }-Timestamp`]: timestamp.toString(),
		[`X-${ headerEncryptStr }-Expire`]: '10000', // 10s, headers 的值必须为字符串类型
		[`X-${ headerEncryptStr }-Nonce`]: randomString(6),
	};
	const values = Object.values(signatureData).sort();
	let str = '';
	for (const value of values) {
		str += value;
	}
	const signature = Crypt.sha1(str).slice(16, 32);
	signatureData[`X-${ headerEncryptStr }-Check`] = '1'; // 是否进行加密处理, '0' : 表示未加密，'1' : 表示已加密
	return { signatureData, encryptData: { cipherText: Base64Crypt.encrypt(Crypt.encrypt(bodyParams, signature)) } };
};

/**
 * 设置附加数据（日志记录）
 * @param {String} sourceUrl
 * @param {Object} params
 * @param {Object} [res=] res
 */
const setSentryExtra = (sourceUrl, params, res) => {
	if (Config.sentry?.open) {
		// configureScope(scope => {
		// 	// 设置附加数据
		// 	scope.setExtra('request', params);
		// 	if (res) {
		// 		scope.setExtra('response', res);
		// 	}
		// 	const userInfo = storage.get(USER_INFO) || {};
		// 	scope.setUser({
		// 		userId: String(userInfo.id),
		// 		username: userInfo.name,
		// 		email: userInfo.email,
		// 		avatar: userInfo.avatar,
		// 		createTime: userInfo.createTime,
		// 		phone: userInfo.phone,
		// 	});
		// });
	}
};

/**
 * 下载zip文件
 * @param url
 * @param options
 * @returns {Promise<unknown>}
 */
export function downloadZipFile (url, options = {}) {
	return new Promise((resolve, reject) => {
		const xhr = new XMLHttpRequest();

		if (options.headers) {
			Object.entries(options.headers).forEach(([key, value]) =>
				xhr.setRequestHeader(key, String(value)),
			);
		}

		if (options.progressCallback) {
			xhr.addEventListener('progress', options.progressCallback);
		}

		xhr.onreadystatechange = (e) => {
			if (xhr.readyState === 4) {
				if (xhr.status === 200 || xhr.status === 0) {
					resolve(xhr.response);
				} else {
					reject(xhr, e);
				}
			}
		};

		// Make request
		xhr.open('GET', url, true);
		xhr.responseType = 'arraybuffer';
		xhr.send();
	});
}

/**
 * get方法
 * @param {String} url 接口地址
 * @param {Object} [params=] params 参数 [可选]
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const get = (url, params, options) => request('get', url, params, options);

/**
 * post请求
 * @param {String} url 接口地址
 * @param {Object} [params=] params 参数 [可选]
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const post = (url, params, options) => request('post', url, params, options);

/**
 * patch请求
 * @param {String} url 接口地址
 * @param {Object} [params=] params 参数 [可选]
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const patch = (url, params, options) => request('patch', url, params, options);

/**
 * put请求
 * @param {String} url 接口地址
 * @param {Object} [params=] params 参数 [可选]
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const put = (url, params, options) => request('put', url, params, options);

/**
 * delete请求
 * @param {String} url 接口地址
 * @param {Object} [params=] params 参数 [可选]
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const del = (url, params, options) => request('delete', url, params, options);
