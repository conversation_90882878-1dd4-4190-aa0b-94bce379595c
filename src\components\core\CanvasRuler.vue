<template>
	<canvas id="rulerCanvas"></canvas>
</template>

<script>
/* ===================================
 * 画布标尺
 * Created by cjking on 2022/03/08.
 * Copyright 2022, Inc.
 * =================================== */
/**
 * canvas 绘制标尺
 *
 * params:{*}
 *      axisWidth:Number,轴线的宽度，单位px
 *      lineColor:String,轴线的颜色
 *      gridWidth:Number,大网格的宽度
 *      gridHeight:Number,大网格的高度
 *
 * dom:Dom元素,canvas的元素
 */
function CanvasRuler (params, dom) {
	let LONG = 0;
	let AXIS_WIDTH = 1;
	let LINE_COLOR = '#000000';
	let GRID_WIDTH = 100;
	let GRID_HEIGHT = 20;

	const canvas = dom;

	const DRAW_TYPE = {
		HORIZONTAL: 1,
		VERTICAL: 2,
		ALL: 3,
		NONE: 4,
	};

	this.setLong = function (long) {
		LONG = Number(long) ? Number(long) : 0;
	};
	this.getLong = function () {
		return LONG;
	};

	this.getDrawType = function () {
		return DRAW_TYPE;
	};

	this.setAxisWidth = function (axisWidth) {
		AXIS_WIDTH = Number(axisWidth) ? Number(axisWidth) : 1;
	};
	this.getAxisWidth = function () {
		return AXIS_WIDTH;
	};

	this.setLineColor = function (lineColor) {
		LINE_COLOR = lineColor;
	};
	this.getLineColor = function () {
		return LINE_COLOR;
	};

	this.setGridWidth = function (gridWidth) {
		GRID_WIDTH = Number(gridWidth) ? Number(gridWidth) : 1;
	};
	this.getGridWidth = function () {
		return GRID_WIDTH;
	};

	this.setGridHeight = function (gridHeight) {
		GRID_HEIGHT = Number(gridHeight) ? Number(gridHeight) : 1;
	};
	this.getGridHeight = function () {
		return GRID_HEIGHT;
	};

	this.getDomElement = function () { // 清空canvas
		return canvas;
	};

	this.clearCanvas = function () {
		// canvas.width = canvas.width;
	};

	this.setAxisWidth(params.axisWidth);
	this.setLineColor(params.lineColor);
	this.setGridWidth(params.gridWidth);
	this.setGridHeight(params.gridHeight);
	this.setLong(params.long);
}

CanvasRuler.prototype = {
	constructor: CanvasRuler,
	init: function (type) {
		this.clearCanvas();
		const ctx = this.getDomElement().getContext('2d');
		ctx.lineWidth = this.getAxisWidth();
		ctx.strokeStyle = this.getLineColor();
		const DRAW_TYPE = this.getDrawType();
		switch (type) {
			case DRAW_TYPE.HORIZONTAL:
				this.drawHorizontalAxis(ctx);
				break;
			case DRAW_TYPE.VERTICAL:
				this.drawVerticalAxis(ctx);
				break;
			case DRAW_TYPE.ALL:
				this.drawHorizontalAxis(ctx);
				this.drawVerticalAxis(ctx);
				break;
			default:
				break;
		}
	},

	// 绘制水平轴
	drawHorizontalAxis: function (ctx) {
		const canvas = this.getDomElement();
		const gridWidth = this.getGridWidth();
		const gridHeight = this.getGridHeight();
		const long = this.getLong();
		const start = (this.getDomElement().width / 2) - gridWidth;
		const posH = this.getDomElement().height - gridHeight - 10;
		const lastItem = start + gridWidth * 2;

		let middlePos = 0;
		let endPos = 0;

		for (let i = start; i < gridWidth * 2 + start; i += gridWidth) {
			ctx.beginPath();
			ctx.moveTo(i, posH);
			ctx.lineTo(i, posH + gridHeight / 2);
			ctx.lineTo(i + gridWidth, posH + gridHeight / 2);
			if (i === gridWidth * 2 + start - gridWidth) {
				middlePos = i;
			}
			let count = 0;
			let finished = false;
			for (let j = i + gridWidth / 10; j <= i + gridWidth; j += gridWidth / 10) {
				count++;
				if (count % 10 === 0 && j !== lastItem) {
					continue;
				}
				const h = posH - gridHeight / 2;
				ctx.moveTo(j, h + gridHeight);

				if (j === lastItem) {
					endPos = j;
					finished = true;
					ctx.lineTo(j, h + gridHeight / 2);
					continue;
				}

				// 每5个刻度区分
				// if (j === i + gridWidth / 10 * 5) {
				// 	ctx.lineTo(j, h + gridHeight / 2);
				// } else {
				// 	ctx.lineTo(j, h + gridHeight * 2 / 3);
				// }

				// 每10个刻度区分
				ctx.lineTo(j, h + gridHeight * 2 / 3);
			}
			ctx.stroke();

			if (finished) {
				this.drawText(canvas, ctx, start, posH, middlePos, endPos, long);
			}
		}
	},

	// 绘制垂直轴
	drawVerticalAxis: function (ctx) {
		const gridWidth = this.getGridWidth();
		const gridHeight = this.getGridHeight();
		ctx.beginPath();
		ctx.moveTo(gridHeight, gridHeight + 0.5);
		ctx.lineTo(gridHeight, gridWidth + 0.5);
		for (let i = 0.5; i < gridWidth; i = i + gridWidth / 10) {
			ctx.moveTo(gridHeight, i);
			if (i < gridHeight) continue;
			if (i === gridWidth / 10 * 5) {
				ctx.lineTo(gridHeight / 2, i);
			} else {
				ctx.lineTo(gridHeight * 2 / 3, i);
			}
		}
		ctx.stroke();
		for (let i = gridWidth + 0.5; i < this.getDomElement().height; i = i + gridWidth) {
			ctx.beginPath();
			ctx.moveTo(0, i);
			ctx.lineTo(gridHeight, i);
			ctx.lineTo(gridHeight, i + gridWidth);
			for (let j = i + gridWidth / 10; j < i + gridWidth; j = j + gridWidth / 10) {
				ctx.moveTo(gridHeight, j);
				if (j === i + gridWidth / 10 * 5) {
					ctx.lineTo(gridHeight / 2, j);
				} else {
					ctx.lineTo(gridHeight * 2 / 3, j);
				}
			}
			ctx.stroke();
		}
	},

	// 绘制文本
	drawText: function (canvas, ctx, startPos, posH, middlePos, endPos, long) {
		ctx.font = '11px Verdana';
		ctx.fillText('0', startPos - 3, posH + 22);

		ctx.font = '11px Verdana';
		ctx.fillText(String((long || 0) / 2), middlePos - 3, posH + 22);

		ctx.font = '11px Verdana';
		ctx.fillText((long || 0) + 'mm', endPos - 3, posH + 22);
	},
};

let canvasRuler;

export default {
	name: 'CanvasRuler',
	props: {
		parentRef: {
			type: Element,
			required: true,
		},
	},
	methods: {
		init (long) {
			const canvas = document.getElementById('rulerCanvas');
			canvas.width = this.parentRef.clientWidth;
			canvas.height = 30;

			canvasRuler = new CanvasRuler({
				long: long,
				axisWidth: 1,
				lineColor: '#000',
				gridWidth: 100,
				gridHeight: 20,
			}, canvas);

			canvasRuler.init(canvasRuler.getDrawType().HORIZONTAL);
		},
	},
};
</script>

<style lang="less" scoped>

</style>
