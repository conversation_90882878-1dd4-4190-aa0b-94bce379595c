<template>
	<a-card :title="title" @keydown.8.stop>
		<div class="base-wrapper">
			<label for=""><b>平面</b></label>
			<a-radio-group
				v-model="direction"
				button-style="solid"
				size="small"
			>
				<a-radio-button value="planeXY">XY</a-radio-button>
				<a-radio-button value="planeYZ">YZ</a-radio-button>
				<a-radio-button value="planeXZ">XZ</a-radio-button>
			</a-radio-group>
		</div>
		<div class="input-wrapper">
			<div class="input-list">
				<div>起始点坐标</div>
				<div class="base-wrapper">
					<label for=""><b>X</b></label>
					<a-input-number v-model="formData.u" placeholder="请输入" class="m-input" :disabled="!canEdit"></a-input-number>
				</div>
				<div class="base-wrapper">
					<label for=""><b>Y</b></label>
					<a-input-number v-model="formData.v" placeholder="请输入" class="m-input" :disabled="!canEdit"></a-input-number>
				</div>
				<div class="base-wrapper">
					<label for=""><b>Z</b></label>
					<a-input-number v-model="formData.w" placeholder="请输入" class="m-input" :disabled="!canEdit"></a-input-number>
				</div>
			</div>

			<div class="input-list">
				<div>结束点坐标</div>
				<div class="base-wrapper">
					<label for=""><b>X</b></label>
					<a-input-number v-model="formData.x" placeholder="请输入" class="m-input" :disabled="!canEdit"></a-input-number>
				</div>
				<div class="base-wrapper">
					<label for=""><b>Y</b></label>
					<a-input-number v-model="formData.y" placeholder="请输入" class="m-input" :disabled="!canEdit"></a-input-number>
				</div>
				<div class="base-wrapper">
					<label for=""><b>Z</b></label>
					<a-input-number v-model="formData.z" placeholder="请输入" class="m-input" :disabled="!canEdit"></a-input-number>
				</div>
			</div>
		</div>

	</a-card>
</template>

<script>
import * as THREE from 'three';
import {
	PlaneModeEnum
} from '@/constants';
export default {
	props: {
		title: {
			type: String,
			required: true,
		},
		position: {
			type: Array,
			default: () => []
		},
		oDirection: {
			type: String,
			default: 'planeXY'
		},
		canEdit: Boolean
	},
	data () {
		return {
			direction: 'planeXY',
			formData: {
				x: 0,
				y: 0,
				z: 0,
				u: 0,
				v: 0,
				w: 0,
			}
		};
	},
	mounted () {
		if ([PlaneModeEnum.planeXY, PlaneModeEnum.planeXZ, PlaneModeEnum.planeYZ].includes(this.oDirection)) {
			this.direction = this.oDirection;
		}
		this.$emit('changeDirection', this.direction);
	},
	watch: {
		formData: {
			handler ({ x, y, z, u, v, w }) {
				this.$emit('change', [new THREE.Vector3(u, v, w), new THREE.Vector3(x, y, z)]);
			},
			deep: true,
		},
		direction (value) {
			this.$emit('changeDirection', value);
		},
		oDirection (value) {
			if ([PlaneModeEnum.planeXY, PlaneModeEnum.planeXZ, PlaneModeEnum.planeYZ].includes(value)) {
				this.direction = value;
			}
		},
		position: {
			handler (value) {
				const [startPoint, endPoint] = value;
				this.formData = {
					x: endPoint?.x ?? startPoint?.x ?? 0,
					y: endPoint?.y ?? startPoint?.y ?? 0,
					z: endPoint?.z ?? startPoint?.z ?? 0,
					u: startPoint?.x ?? 0,
					v: startPoint?.y ?? 0,
					w: startPoint?.z ?? 0,
				};
			},
			deep: true,
		}
	}
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/base.less';
@import '~@/assets/styles/mixins/variables.less';

.ant-card {
	width: 416px;

	.input-wrapper {
		display: flex;
		margin-top: 20px;

		.input-list {
			flex: 1;

			&:not(:last-child) {
				margin-right: 30px
			}

		}
	}

	/deep/ .ant-card-body {
		padding-top: 8px;
		padding-bottom: 8px
	}
}

.base-wrapper {
	display:flex;
	justify-content:space-between;
	align-items:center;
	margin-top:10px;
	margin-bottom:10px;
}

.m-input {
	flex: 1;
	margin-left: 8px;
	border-radius: 2px;
	background: #f5f5f5;
}
</style>
