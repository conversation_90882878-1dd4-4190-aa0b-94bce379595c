<template>
	<div class="user-layout-forget">
		<a-form-model ref="ruleForm" :model="form" :rules="rules" v-if="step === StepEnum.ONE">
			<div class="user-layout-forget-title">
				<h2>忘记密码</h2>
			</div>

			<a-form-model-item prop="email" has-feedback>
				<a-input type="text" size="large" v-model.trim="form.email" placeholder="请输入登录邮箱">
					<a-icon slot="prefix" type="mail" :style="{ color: 'rgba(0,0,0,.25)' }" />
				</a-input>
			</a-form-model-item>

			<a-row :gutter="24">
				<a-col :span="15">
					<a-form-model-item prop="captcha">
						<a-input type="text" size="large" v-model.trim="form.captcha" :maxLength="6" placeholder="请输入验证码" @change="inputCodeChange">
							<a-icon slot="prefix" type="smile" :style="{ color: 'rgba(0,0,0,.25)' }" />
						</a-input>
					</a-form-model-item>
				</a-col>
				<a-col :span="8">
					<img style="margin-top: 2px; cursor: pointer;" :src="randCodeImage" alt @click="getCaptcha">
				</a-col>
			</a-row>

			<a-row :gutter="24">
				<a-col :span="24">
					<a-button size="large" type="primary" class="forget-button" @click.stop.prevent="next">下一步</a-button>
					<a class="a-center" v-href @click="toLogin">去登录?</a>
				</a-col>
			</a-row>
		</a-form-model>

		<div v-if="step === StepEnum.TWO">
			<h2>邮件已发送</h2>
			<p>我们已向您注册的邮箱发送了一封找回密码邮件</p>
			<p>请登录您的邮箱完成验证</p>
			<a-button
				size="large"
				type="primary"
				html-type="submit"
				class="forget-button"
				@click.stop.prevent="goEmail"
			>立即登录邮箱完成验证
			</a-button>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 忘记密码
 * Created by cjking on 2020/12/14.
 * Copyright 2020, Inc.
 * =================================== */
import checkCode from '@/assets/img/check-code.png';
import { debounce } from '@/utils/utils';
import { baseValidate, formValidate } from '@/utils/validate';
import { forgotPassword, getSmSCode } from '@/api';

const StepEnum = {
	ONE: 1, // 第一步
	TWO: 2,  // 第二步
};

export default {
	name: 'ForgetPassword',
	data () {
		return {
			StepEnum,
			labelCol: {
				xs: { span: 24 },
				sm: { span: 5 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			form: {
				email: '',      // 登录邮箱
				captcha: '',    // 验证码
			},
			rules: {
				email: formValidate.customAsync(debounce((rule, value) => {
					return new Promise(async (resolve, reject) => {
						if (!value) {
							return reject(`请输入登录邮箱!`);
						}
						if (!baseValidate.checkEmail(this.form.email)) {
							return reject(`请输入正确的邮箱!`);
						}
						// const res = await checkUserExists(this.form.email);
						// if (res?.success && !res.result) {
						// 	return resolve();
						// } else {
						// 	reject(`不能使用此邮箱，未注册!`);
						// }
						return resolve();
					});
				}, 300)),
				captcha: formValidate.noEmpty('请输入验证码!'),
			},
			step: StepEnum.ONE,
			randCodeImage: '',
			currDateTime: null,
		};
	},
	created () {
		this.getCaptcha();
	},
	methods: {
		/**
		 * 更新验证码
		 */
		inputCodeChange (e) {
			this.inputCodeContent = e.target.value;
		},

		/**
		 * 获取验证码
		 */
		getCaptcha () {
			this.currDateTime = Date.now();
			getSmSCode(this.currDateTime).then(res => {
				if (res?.success && res.result) {
					this.randCodeImage = res.result.base64 ? `data:image/jpeg;base64,${ res.result.base64 }` : res.result;
				} else {
					this.randCodeImage = checkCode;
					this.$message.error(res?.message);
				}
			}).catch(() => {
				this.randCodeImage = checkCode;
			});
		},

		/**
		 * 下一步
		 */
		next () {
			this.$refs.ruleForm.validate(async valid => {
				if (!valid) return false;
				const params = {
					email: this.form.email,
					code: this.form.captcha,
					url: window.location.origin + '/user/updatePwd', // eg: http://localhost:8681/user/updatePwd
				};
				const res = await forgotPassword(params).catch(() => this.getCaptcha());
				if (res?.success) {
					this.step = StepEnum.TWO;
				}
			});
		},

		/**
		 * 打开登录邮件地址
		 */
		goEmail () {
			let mailLink = this.form.email.split('@')[1];
			mailLink = mailLink?.toLowerCase();
			if (mailLink === '163.com') {
				this.openNewWindow('http://www.163.com');
			} else if (mailLink === '126.com') {
				this.openNewWindow('http://www.126.com');
			} else if (mailLink === 'qq.com') {
				this.openNewWindow('http://www.qq.com');
			} else if (mailLink === 'sina.com') {
				this.openNewWindow('http://mail.sina.com');
			} else if (mailLink === 'sina.cn') {
				this.openNewWindow('http://mail.sina.cn');
			} else if (mailLink === 'foxmail.com') {
				this.openNewWindow('http://mail.foxmail.com');
			} else if (mailLink === 'gmail.com') {
				this.openNewWindow('https://mail.google.com');
			} else if (mailLink === 'outlook.com') {
				this.openNewWindow('https://login.live.com');
			} else if (mailLink === 'yahoo.com') {
				this.openNewWindow('https://mail.yahoo.com');
			} else if (mailLink === 'sohu.com') {
				this.openNewWindow('https://mail.sohu.com');
			} else if (mailLink === '189.cn') {
				this.openNewWindow('http://mail.189.cn');
			} else {
				this.$message.info('请登录您的邮箱完成修改!');
			}
		},

		/**
		 * 打开新窗口
		 */
		openNewWindow (url) {
			window.open(url, '_blank');
		},

		/**
		 * 返回登录页
		 */
		toLogin () {
			this.$router.push('/user/login');
		},
	},
};
</script>

<style lang="less" scoped>
.user-layout-forget {
	width: 315px;

	&-title {
		text-align: left;
		> h2 {
			color: #6362FF;
		}
	}

	button.forget-button {
		padding: 0 15px;
		font-size: 16px;
		height: 40px;
		width: 100%;
		margin-top: 5px;
	}

	.a-center {
		display: flex;
		justify-content: center;
		margin-top: 5px;
	}
}
</style>
