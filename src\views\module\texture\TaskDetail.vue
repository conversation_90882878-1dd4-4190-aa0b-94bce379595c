<template>
	<a-layout id="layout-basic">
		<GlobalHeader size="sm" :menu="ModuleEnum.texture" justify-content="center">
			<template #leftAction>
				<div style="margin-left: 20px;margin-top: -2px;">
					<a v-href @click="goToView('/texture/project/detail/' + projectId)">
						<img :src="backHomeImg" style="height: 30px; cursor: pointer;" alt="">
					</a>
				</div>
			</template>
			<template #logo>
				<div class="logo">
					<JEllipsis :value="taskName" :length="14" />
				</div>
			</template>
			<template #title>
				<!-- 导航 -->
				<Navigation
					:action="curAction"
					:selectMode="curSelectMode"
					:renderMode="curRenderMode"
					:disabledSelectFace="disabledSelectFace"
					:disabledSelectSolid="disabledSelectSolid"
					:disabled="solveLoading || submitLoading"
					config="重置视图|视图模式|渲染模式|投影模式|平面裁切"
					@resetView="resetView"
					@resetCamera="resetCamera"
					@updateView="updateView"
					@changeCameraMode="changeCameraMode"
					@changeRenderMode="changeRenderMode"
					@selectModeHandle="selectModeHandle"
					@changeClipping="changeClipping"
					@mouseLeftSetting="mouseLeftSetting"
					@mouseRightSetting="mouseRightSetting"
				/>
				<HeaderTip :module-object="moduleObject" :style="{position: 'absolute', top: '50px', left: '0px'}" />

			</template>
		</GlobalHeader>

		<a-layout-sider
			ref="siderRef"
			class="project-sider"
			:width="siderWidth"
		>
			<!-- 模具 panel -->
			<CollapseTree
				:style="{paddingTop: moduleObject.expired || moduleObject.zeroCount ? '40px' : '0'}"
				showLine
				title="模具Part"
				panelKey="part"
				:treeData="[]"
				defaultExpandAll
				disabledRightMenu
			>
				<template #content>
					<TextureSettings
						:dataList="partList"
						@onAllVisible="allVisibleHandler"
						@onAllColor="allColorHandler"
						@onAllOpacity="allOpacityHandler"
						@uploadCallback="uploadCallback"
					/>
				</template>
			</CollapseTree>

			<!-- 参数配置 panel -->
			<CollapseTree
				showLine
				title="参数配置"
				panelKey="model"
				defaultExpandAll
				:treeData="[]"
			>
				<template #content>
					<TextureSettings
						v-if="settings.length"
						:dataList="settings"
						@onColor="colorHandler"
						@onVisible="visibleHandler"
						@onOpacity="opacityHandler"
						@sliderValueChange="sliderValueChangeHandler"
						@switchValueChange="switchValueChangeHandler"
						@carouselValueChange="carouselValueChangeHandler"
					/>
					<a-divider />
					<a-affix :offset-bottom="0">
						<div class="submit">
							<a-button type="primary" :disabled="disabled" @click="submitTask">提交计算</a-button>
						</div>
					</a-affix>
				</template>
			</CollapseTree>
		</a-layout-sider>

		<a-layout-content :style="{ background: DEFAULT_VIEW_BACKGROUND, position: 'relative' }">
			<!-- 蒙层 -->
			<div class="masking" v-if="solveLoading" />

			<!-- 视图渲染区 -->
			<TextureView
				ref="textureView"
				v-if="visibleView"
				:hasCover="hasCover"
				@tip="handleTip"
				@submit="submitTask"
			/>
		</a-layout-content>

		<div class="project-right" ref="rightRef">
			<!-- 结果 -->
			<TextureResult
				:style="{paddingTop: moduleObject.expired || moduleObject.zeroCount ? '40px' : '0'}"
				:dataList="results"
				:view="$refs.textureView"
				@onColor="resultColorHandler"
				@onVisible="resultVisibleHandler"
				@onOpacity="resultOpacityHandler"
			/>
		</div>

		<div class="tip-name">{{ tipName }}</div>
		<div class="network-status">网络状态：{{ online }}</div>

	</a-layout>
</template>

<script>
/* ===================================
 * 纹理-任务详情
 * Created by cjking on 2022/02/20.
 * Copyright 2022, Inc.
 * =================================== */
import { mapActions, mapGetters } from 'vuex';
import { logger } from '@/utils/logger';
import { IMessage } from '@/http/message';
import { mixinWebSocket, mixinModule } from '@/mixins';
import { getProjectTaskDetail, makeDispersed, submitRhino, updateTaskCacheParams } from '@/api';
import { debounce, deepCopy, noop, checkIfLoaded, isEmpty, addEventHandler } from '@/utils/utils';
import {
	SelectModeEnum, DEFAULT_VIEW_BACKGROUND, ProgressStatusEnum,
	RenderModeEnum, WsTypeEnum, TaskStatusEnum, ModuleEnum, TEXTURE_GUIDED_POINT,
} from '@/constants';
import TextureView from '@/components/core/TextureView';
import Navigation from '@/components/core/Navigation';
import GlobalHeader from '@/components/page/GlobalHeader';
import CollapseTree from '@/components/common/CollapseTree';
import TextureResult from '@/views/module/texture/TextureResult';
import FileLoader from '@/components/core/FileLoader';
import ImageDropdown from '@/components/common/ImageDropdown';
import ImageSlider from '@/components/common/ImageSlider';
import SliderInput from '@/components/common/SliderInput';
import SwitchInput from '@/components/common/SwitchInput';
import TextSlider from '@/components/common/TextSlider';
import LabelTitle from '@/components/common/LabelTitle';
import JEllipsis from '@/components/common/JEllipsis.vue';
import PartItem from '@/components/common/PartItem';
import TextureSettings from '@/views/module/texture/TextureSettings';
import HeaderTip from '@/components/common/HeaderTip';

import backHomeImg from '@/assets/img/project/navigation/backHome.png';
import { defaultJsonData, defaultPartData } from '@/views/module/texture/defaultJsonData';

const components = {
	TextureView,
	Navigation,
	GlobalHeader,
	CollapseTree,
	TextureResult,
	FileLoader,
	ImageDropdown,
	ImageSlider,
	SliderInput,
	TextSlider,
	SwitchInput,
	LabelTitle,
	PartItem,
	JEllipsis,
	TextureSettings,
	HeaderTip,
};

const PanelEnum = {
	part: 'part',
	mesh: 'mesh',
	resolve: 'resolve',
};

const ActionTypeEnum = {
	submit: 'submit',
	saveData: 'saveData',
};

const getFirstItem = (list, defaultValue = undefined) => list?.length ? list[0] : defaultValue;

export default {
	name: 'TaskDetail',
	mixins: [
		mixinWebSocket, mixinModule,
	],
	components: {
		...components,
	},
	data () {
		return {
			backHomeImg,
			ModuleEnum,
			PanelEnum,
			DEFAULT_VIEW_BACKGROUND,
			siderWidth: 320,
			curSelectMode: SelectModeEnum.surface,
			curRenderMode: RenderModeEnum.surfaceAndWireframe,
			curAction: '',
			hasCover: false,
			visibleView: true,
			submitLoading: false,
			disabledSelectFace: false,
			disabledSelectSolid: false,
			partList: [{ ...deepCopy(defaultPartData) }],
			settings: [...deepCopy(defaultJsonData)],
			modelList: [],
			taskInfo: {},
			fileName: '',
			online: '正常',
			downLink: '0 M/s',
			tipName: '',
			results: [],
			taskId: '',
			projectId: '',
			solveLoading: false,
		};
	},
	computed: {
		...mapGetters([
			'isLogin', 'userInfo', 'collapsed', 'solidNames', 'moduleList',
		]),

		taskName () {
			return this.taskInfo?.sysTask?.taskName || '';
		},
		disabled () {
			return this.userInfo.id !== this.taskInfo?.sysTask?.createBy;
		},
		path () {
			return `${ this.userInfo.orgCode }/${ this.projectId }/${ this.taskId }`;
		},
	},
	mounted () {
		this.userId = this.userInfo.id || '';
		this.projectId = this.$route.params.id || '';
		this.taskId = this.$route.params.taskId || '';
		this.currentModule = '2'; // 设置当前为纹理模块

		// 监听离散或计算数据
		this.listenDispersedOrCompute(false);

		this.initEvents();

		this.initProjectData();
	},
	methods: {
		...mapActions(['initPermissionList']),
		/**
		 * 初始化缓存数据
		 */
		initCacheData (taskInfo) {
			if (taskInfo?.viewParamSets) {
				const viewParamSets = taskInfo?.viewParamSets;
				if (viewParamSets.partList) this.partList = viewParamSets.partList;
				if (viewParamSets.settings) this.settings = viewParamSets.settings;
				if (viewParamSets.results) this.results = viewParamSets.results;
				if (viewParamSets.hiddenObjectMap) {
					this.$refs.textureView.hiddenObjectMap = viewParamSets.hiddenObjectMap;
				}
			}
			if (this.fileName) {
				const part = this.partList[0];
				part.value = this.fileName ? [this.fileName] : [];
				part.submitLoading = !this.fileName ? false : part.submitLoading;
			}
		},

		/**
		 * 初始化树
		 */
		initTree () {
			if (this.partList?.length) {
				this.allVisibleHandler(this.partList[0]);
				this.allColorHandler(this.partList[0]);
				this.allOpacityHandler(this.partList[0]);
			}
		},

		/**
		 * 将任务缓存保存到服务器
		 */
		async saveTaskCacheToServer () {
			const params = {
				id: this.taskId,
				viewParamSets: { // 页面所有参数设置
					partList: this.partList, // 模具参数
					settings: this.settings, // 参数配置
					results: this.results,   // 结果参数
					hiddenObjectMap: this.$refs.textureView?.hiddenObjectMap,   // 隐藏对象参数
				},
			};
			logger.log('更新任务缓存数据参数 params: ', params);
			const res = await updateTaskCacheParams(params, { loading: false });
			logger.log('更新任务缓存数据结果 res: ', res);
		},

		/**
		 * 提交任务(延迟提交)
		 */
		debounceSubmitTask: debounce(function () {
			this.submitTask();
		}, 1000),

		/**
		 * 提交任务
		 */
		async submitTask () {
			if (this.solveLoading || this.taskInfo?.sysTask?.status === TaskStatusEnum.waiting) {
				return;
			}

			await this.initPermissionList();

			if (!this.moduleObject.canCreate) {
				if (this.moduleObject.expired) {
					this.$message.warning('套餐已过期');
				} else if (this.moduleObject.zeroCount) {
					this.$message.warning('套餐剩余下载次数不足');
				}
				return;
			}

			if (this.disabled) {
				this.$message.warning('只有创建者可以提交');
				return;
			}

			this.solveLoading = true;
			this.openViewLoading('计算中...', true);

			const { status, postParams } = this.submitValidate();
			if (!status) return;

			// 清除加载的纹理结果数据
			this.$refs.textureView?.clearTextureResult();
			this.results = [];

			const params = {
				id: this.taskId,
				module: 'texture',
				paramSets: postParams,          // 计算值参数
				viewParamSets: {    // 页面所有参数设置
					partList: this.partList,    // 模具参数
					settings: this.settings,    // 参数配置
					results: [],                // 结果参数，需要清空服务器保存的缓存来同步页面显示
				},
			};

			const res = await submitRhino(params, { loading: false });
			logger.log('提交任务 res: ', res);
			if (res?.success && res.result) {
				if (res.result.execute === ActionTypeEnum.saveData) {
					this.closeViewLoading();
					this.solveLoading = false;
				}
				this.$message.success(res.message || '提交任务成功');
			}
		},

		/**
		 * 提交验证
		 */
		submitValidate () {
			const result = { status: false, postParams: undefined };

			const commonFormat = () => this.settings.map(s => {
				// 范围限制，防止误输入
				if (typeof s.max === 'number' && Number(s.value) > s.max) {
					s.value = s.max;
				} else if (typeof s.min === 'number' && Number(s.value) < s.min) {
					s.value = s.min;
				}

				return {
					name: s.key,
					text: Number(s.value),
				};
			});

			/**
			 * 获取方向值
			 * @return {*}
			 */
			const getDirectionValues = () => {
				const onewayChangeData = this.getSetting('oneway_change');
				const children = onewayChangeData['children' + onewayChangeData.value];
				return children.map(child => {
					return {
						name: child.key,
						text: Number(child.value),
					};
				});
			};

			// 组装参数
			const postParams = [
				{
					name: 'path',
					text: this.path,
				},
				{
					name: this.partList[0].key,
					text: getFirstItem(this.partList[0].value, ''),
				},
				...commonFormat(),
				...getDirectionValues(),
			].filter(p => p); // 过滤空值

			logger.log('提交任务参数：', postParams);

			const getValue = (key) => postParams.find(p => p.name === key).text;

			// 弹窗提示
			const toast = (msg, duration = 5) => {
				this.closeViewLoading();
				this.solveLoading = false;
				this.$message.config({
					top: `10%`,
					duration: duration,
					maxCount: 3,
				});
				this.$message.warning(msg, duration);
			};

			if (this.disabled) {
				toast('只有创建者可以提交!');
				return result;
			}

			// 验证模型是否存在
			if (!getValue('model_part')) {
				toast('请先上传文件!');
				return result;
			}

			if (this.getSetting('partition').invalid) {
				toast('请选择可用的细分变种!');
				return result;
			}

			result.status = true;
			result.postParams = postParams;

			return result;
		},

		/**
		 * 上传离散文件
		 */
		async uploadCallback ({ file }) {

			this.submitLoading = true;
			const part = this.partList[0];
			part.submitLoading = true;

			const formData = new FormData();
			formData.append('file', file);
			formData.append('taskId', this.taskId);
			formData.append('module', ModuleEnum.texture);
			const res = await makeDispersed(formData).catch(() => part.submitLoading = false);
			logger.log('上传离散文件结果 res: ', res);

			if (res.code !== IMessage.OK.code) {
				this.$modal.error({ content: res.message });
				part.submitLoading = false;
				return;
			}

			// 必须用后端返回的名称，后缀被改了
			if (res?.result) this.fileName = res.result;
			logger.log('上传离散文件结果 fileName: ', this.fileName);

			this.openViewLoading();
		},

		/**
		 * 初始化项目数据
		 */
		async initProjectData () {
			try {
				await this.getTaskInfo();
			} catch (e) {
				logger.error('初始化项目数据 error: ', e);
			}
		},

		/**
		 * 获取任务详情
		 */
		async getTaskInfo () {
			const res = await getProjectTaskDetail({ id: this.taskId });
			logger.log('获取任务详情 res: ', res);

			if (res?.success && res?.result) {
				this.taskInfo = res?.result || {};
				const sysTask = this.taskInfo.sysTask || {};
				logger.log('获取任务详情 fileName: ', this.taskInfo.fileName);

				// 模型名称
				this.fileName = this.taskInfo.fileName || '';

				// 是否有缩略图
				this.hasCover = !!sysTask.cover;

				this.initCacheData(this.taskInfo);

				// 解析离散数据
				if (sysTask.type) {
					logger.log('纹理离散数据 dispersedResult: ', sysTask.dispersedResult);
					const dispersedResult = sysTask.dispersedResult;
					if (!dispersedResult && sysTask.status === ProgressStatusEnum.FAIL) {
						this.$modal.error({
							content: '任务离散失败，请重新创建任务',
							okText: '返回任务列表',
							onOk: () => this.$router.push(`/texture/project/detail/${ this.projectId }`),
						});
						return;
					}
					if (dispersedResult.filePath) {
						await this.$refs.textureView?.loadData(dispersedResult.filePath);
					} else {
						// await this.$refs.textureView?.loadDataV2(dispersedResult);
						await this.$refs.textureView?.loadDataV3(dispersedResult);
					}
				}

				// 解析求解数据
				if (sysTask.type && sysTask.resultJson) {
					const canLoadPoint = this.checkGuidedPoint();
					logger.log('引导点开关是否开启：', canLoadPoint);
					const results = await this.$refs.textureView?.loadTextureComputeData(sysTask, canLoadPoint);
					this.results = results || [];
				}

				// 初始化树
				this.initTree();

				const progressLoading = sysTask.status === ProgressStatusEnum.LOADING;

				// 离散中
				this.submitLoading = (sysTask.type === WsTypeEnum.dispersed && progressLoading) || (sysTask.type === WsTypeEnum.resolve && progressLoading);
				if (this.submitLoading) {
					this.openViewLoading();
				}

				// 计算中
				this.solveLoading = sysTask.type === WsTypeEnum.resolve && progressLoading;
				if (this.solveLoading) {
					this.openViewLoading();
				}

				// 处理右键隐藏的数据
				this.$nextTick(() => {
					const hiddenObjectMap = this.$refs.textureView?.hiddenObjectMap;
					if (hiddenObjectMap) {
						this.$refs.textureView?.updateVisibleByList(Object.keys(hiddenObjectMap), false);
					}
				});
			}
		},

		/**
		 * 检查引导点开关是否开启
		 * @return {boolean}
		 */
		checkGuidedPoint () {
			const onewayChangeData = this.getSetting('oneway_change');
			const children = onewayChangeData['children0'];
			const canLoadPoint = !!children.find(child => child.key === 'guided_point').value;
			logger.log('引导点开关是否开启：', canLoadPoint);
			return canLoadPoint;
		},

		/**
		 * 监听离散或计算数据
		 */
		listenDispersedOrCompute (loading = true) {
			const params = {};
			this.listenWsStatus(params, {
				loading: loading,
				callback: async (res) => {
					logger.log('监听离散或计算数据: ', res);

					this.solveLoading = false;
					this.submitLoading = false;

					const part = this.partList[0];
					part.value = this.fileName ? [this.fileName] : [];
					part.submitLoading = !this.fileName ? false : part.submitLoading;
					this.saveTaskCacheToServer().then();

					// 轮询检测，等待textureView页面加载完毕
					if (!loading) {
						await checkIfLoaded(!!this.$refs.textureView);
					}

					// 监听纹理离散数据
					if (res.type === WsTypeEnum.dispersed) {
						logger.log('监听纹理离散数据（Rhino离散）dispersedResult: ', res.dispersedResult);
						if (res.dispersedResult?.filePath) {
							await this.$refs.textureView?.loadData(res.dispersedResult.filePath);
						} else {
							// await this.$refs.textureView?.loadDataV2(res.dispersedResult);
							await this.$refs.textureView?.loadDataV3(res.dispersedResult);
						}
					}

					// 纹理计算数据
					if (res.type === WsTypeEnum.resolve) {
						const canLoadPoint = this.checkGuidedPoint();
						logger.log('引导点开关是否开启：', canLoadPoint);
						const results = await this.$refs.textureView?.loadTextureComputeData(res, canLoadPoint);
						this.results = results || [];
					}

					// 下载step文件的状态通知
					if (res.type === WsTypeEnum.resultExportStatus) {
						logger.log('下载step文件的状态通知 res:', res);
						if (res.resultJson) {
							this.results.forEach(data => {
								['result_wl_line_3dm', 'result_wl_line_step'].forEach(key => {
									if (key === data.name) {
										data.status = res.resultJson[key + '_status'];
									}
								});
							});
							this.results = [...this.results];
						}
					}
				},
				errCallback: (res) => {
					this.submitLoading = false;
					this.solveLoading = false;
					const action = res.type === WsTypeEnum.resolve ? '求解' : '离散';
					this.$modal.error({ content: res.errors || `${ action }失败，请稍后重试!` });
				},
			});
		},

		/**
		 * 监听websocket状态
		 */
		async listenWsStatus (params, options) {
			const loading = options.loading ?? true;
			const loadingMsg = options.loadingMsg ?? '';
			const callback = options.callback || noop;
			const errCallback = options.errCallback || noop;
			const progressCallback = options.progressCallback || noop;

			// const key = Date.now();
			const key = this.taskId;
			if (loading) this.openViewLoading(loadingMsg);
			const socket = await this.initWebsocket(key);
			// socket.send(params);
			this.$root.$on(`${ key }Callback`, res => {
				// status:状态 0:未开始，1:创建中，8:完成，9:失败
				// type:类型 dispersed:离散数据，resolve:求解器数据
				if (res.status === ProgressStatusEnum.LOADING) {
					progressCallback(res);
				}
				if (res.status === ProgressStatusEnum.SUCCESS) {
					this.closeViewLoading();
					// socket.onClose(res.type);
					callback(res);
				}
				if (res.status === ProgressStatusEnum.FAIL) {
					this.closeViewLoading();
					errCallback(res);
				}
			});
		},

		/**
		 * 切换折叠
		 */
		toggleCollapsed () {
			const collapsed = !this.collapsed;
			this.siderWidth = collapsed ? 0 : 320;
			this.$nextTick(() => {
				this.$store.dispatch('setCollapsed', collapsed);
			});
		},

		/**
		 * 开启加载中
		 */
		openViewLoading (loadingMsg = '', resetCount) {
			this.submitLoading = true;
			this.$refs.textureView?.openLoading(loadingMsg, resetCount);
		},

		/**
		 * 关闭加载中
		 */
		closeViewLoading () {
			this.submitLoading = false;
			this.$refs.textureView?.closeLoading();
		},

		/**
		 * 重置视图
		 */
		resetView () {
			if (this.checkAction()) return;
			this.$refs.textureView.resetView();
		},

		/**
		 * 重置相机
		 */
		resetCamera () {
			this.$refs.textureView?.resetCamera();
		},

		/**
		 * 更改投影模式
		 */
		changeCameraMode (mode) {
			if (this.checkAction()) return;
			this.$refs.textureView?.changeCameraMode(mode);
		},

		/**
		 * 更改渲染模式
		 * @param mode
		 */
		changeRenderMode (mode) {
			if (this.checkAction()) return;
			this.curRenderMode = mode;
			this.$refs.textureView?.changeRenderMode(mode);
		},

		/**
		 * 更新视图
		 */
		updateView (direction) {
			if (this.checkAction()) return;
			this.$refs.textureView?.updateView(direction);
		},

		/**
		 * 选择模型(点、线、面、体)
		 */
		selectModeHandle (selectMode) {
			if (this.checkAction()) return;
			this.curSelectMode = selectMode;
			this.$refs.textureView?.selectModeHandle(selectMode);
		},

		/**
		 * 检查动作
		 */
		checkAction (message = '正在执行拖动等动作，请先释放对象') {
			if (this.$refs.textureView?.selectTarget) {
				this.$message.warning(message);
				return true;
			}
			return false;
		},

		/**
		 * 更新颜色
		 */
		updateAllColor (value) {
			this.$refs.textureView?.updateAllColor(value);
		},

		/**
		 * 更新透明度
		 */
		updateAllOpacity (value) {
			this.$refs.textureView?.updateAllOpacity(value / 100);
		},

		/**
		 * 更新颜色
		 */
		updateAllVisible (value) {
			this.$refs.textureView?.updateAllVisible(!!value);
		},

		/**
		 * 更新切面
		 * @params 对象{切面，反转xyz方向，反转正反方向}
		 */
		changeClipping ({ clip, clippingReverse = {} }) {
			this.$refs.textureView?.updateClipping(clip, clippingReverse);
		},

		/**
		 * 鼠标左键设置
		 * @param value
		 */
		mouseLeftSetting (value) {
			this.$refs.textureView?.mouseLeftSetting(value);
		},

		/**
		 * 鼠标右键设置
		 * @param value
		 */
		mouseRightSetting (value) {
			this.$refs.textureView?.mouseRightSetting(value);
		},

		/**
		 * 滑块值更改处理
		 */
		sliderValueChangeHandler ({ key, value }) {
			logger.log('滑块值更改处理 key, value: ', key, value);
			this.debounceSubmitTask();
		},

		/**
		 * 开关值变化处理
		 */
		switchValueChangeHandler ({ key, value }) {
			logger.log('开关值变化处理 key, value: ', key, value);
			// 引导点
			if (key === 'guided_point') {
				const childArray = this.$refs.textureView.getScene().children.filter(child => child.groupName === TEXTURE_GUIDED_POINT);
				for (const child of childArray) {
					if (child && child.isSphere) {
						child.visible = !!value;
					}
				}
			}
		},

		/**
		 * 轮播值更改处理
		 */
		carouselValueChangeHandler ({ key, value }) {
			logger.log('轮播值更改处理 key, value: ', key, value);
			if (key === 'oneway_change') { // 方向选择 0:单向变化, 1:周期变化
				// 方向选择时，如果是切换到周期变化时，需要禁用引导点
				const onewayChangeData = this.getSetting('oneway_change');
				const children = onewayChangeData['children0'];
				const guidedPointItem = children.find(child => child.key === 'guided_point');
				if (guidedPointItem) {
					guidedPointItem.value = 0;
					guidedPointItem.checked = false;

					// 同步视图
					this.switchValueChangeHandler({ key: 'guided_point', value: 0 });
				}
			}
			this.debounceSubmitTask();
		},

		/**
		 * 获取设置
		 * @param key
		 */
		getSetting (key) {
			return this.settings.find(setting => setting.key === key);
		},

		handleTip (obj) {
			if (obj.nickname) {
				this.tipName = `名称：${ obj.nickname }`;
			} else if (obj.groupName) {
				this.tipName = `组名称：${ obj.groupName }`;
			} else {
				this.tipName = '';
			}
		},

		updateOnline () {
			this.online = navigator.onLine ? '正常' : '断开';
		},

		/**
		 * 所有显隐处理
		 */
		allVisibleHandler ({ key, visible }, isInit = false) {
			logger.log('所有显隐处理 key, visible: ', key, visible);

			if (this.checkAction()) return;

			const children = this.getSceneAllSolidAndAllWireframe();
			const textureView = this.$refs.textureView;
			if (!textureView) return;
			textureView.originVisible = visible;
			[...children].forEach(child => {
				textureView.updateObjAttr(child, { visible });
			});
			textureView.requestRender();

			if (!isInit) this.updatePartListData({ visible });
		},

		/**
		 * 所有颜色处理
		 */
		allColorHandler ({ key, color }, isInit = false) {
			logger.log('所有颜色处理 key, color: ', key, color);

			if (this.checkAction()) return;

			const children = this.getSceneAllSolidAndAllWireframe();
			const textureView = this.$refs.textureView;
			if (!textureView) return;
			textureView.originColor = color;
			children.forEach(child => {
				if (!textureView.isLine(child)) {
					textureView.updateObjAttr(child, { color });
				}
			});
			textureView.requestRender();

			if (!isInit) this.updatePartListData({ color });
		},

		/**
		 * 所有透明度处理
		 */
		allOpacityHandler ({ key, opacity }, isInit = false) {
			logger.log('所有透明度处理 key, opacity: ', key, opacity);

			if (this.checkAction()) return;

			const children = this.getSceneAllSolidAndAllWireframe();
			const textureView = this.$refs.textureView;
			if (!textureView) return;
			textureView.originOpacity = opacity;
			children.forEach(child => {
				textureView.updateObjAttr(child, { opacity });
			});

			if (!isInit) this.updatePartListData({ opacity });
		},

		/**
		 * 显示/隐藏处理
		 */
		visibleHandler ({ key, visible }) {
			logger.log('显示/隐藏处理 key, visible: ', key, visible);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);

			if (setting && setting.value?.length) {
				this.updateAttrByNameList([...setting.value], true, setting.color, visible);
			}
		},

		/**
		 * 结果显示/隐藏处理
		 */
		resultVisibleHandler ({ type, visible }) {
			logger.log('显示/隐藏处理 type, visible: ', type, visible);
			const children = this.getResultWlLines(type);
			const textureView = this.$refs.textureView;
			if (!textureView) return;
			children.forEach(child => {
				textureView.updateObjAttr(child, { visible });
			});
			textureView.requestRender();
		},

		/**
		 * 结果颜色处理
		 */
		resultColorHandler ({ type, color }) {
			logger.log('颜色处理 type, color: ', type, color);
			const children = this.getResultWlLines(type);
			const textureView = this.$refs.textureView;
			if (!textureView) return;
			children.forEach(child => {
				textureView.updateObjAttr(child, { color });
			});
			textureView.requestRender();
		},

		/**
		 * 结果透明度处理
		 */
		resultOpacityHandler ({ type, opacity }) {
			logger.log('透明度处理 type, opacity: ', type, opacity);
			const children = this.getResultWlLines(type);
			const textureView = this.$refs.textureView;
			if (!textureView) return;
			children.forEach(child => {
				textureView.updateObjAttr(child, { opacity });
			});
			textureView.requestRender();
		},

		/**
		 * 颜色处理
		 */
		colorHandler ({ key, color }) {
			logger.log('颜色处理 key, color: ', key, color);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);
			if (setting.value?.length && setting.activate) {
				this.$refs.textureView?.updateAttrByNameList([...setting.value], {
					selected: true,
					color,
				});
			}
		},

		/**
		 * 透明度处理
		 */
		opacityHandler ({ key, opacity }) {
			logger.log('透明度处理 key, opacity: ', key, opacity);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);
			if (setting.value?.length && setting.activate) {
				this.$refs.textureView?.updateAttrByNameList([...setting.value], {
					selected: true,
					color: setting.color,
					opacity,
				});
			}
		},

		/**
		 * 获取场景全部实体和全部线框
		 * @returns {Object3D[]|*[]}
		 */
		getSceneAllSolidAndAllWireframe () {
			return this.$refs.textureView?.getSceneAllSolidAndAllWireframe();
		},

		/**
		 * 更新部件数据
		 */
		updatePartListData ({ visible, color, opacity, value, submitLoading } = {}) {
			const part = this.partList[0];
			if (!isEmpty(visible)) {
				part.visible = visible;
			}
			if (!isEmpty(color)) {
				part.color = color;
			}
			if (!isEmpty(opacity)) {
				part.opacity = opacity;
			}
			if (!isEmpty(value)) {
				part.value = value;
			}
			if (!isEmpty(submitLoading)) {
				part.submitLoading = submitLoading;
			}
		},

		/**
		 * 获取结果纹理线
		 * @returns {Object3D[]|*[]}
		 */
		getResultWlLines (type) {
			return this.$refs.textureView?.getSceneChildrenByCondition('resultWlLineGroup', child => child.textureResultType === type);
		},

		goToView (path) {
			this.saveTaskCacheToServer();
			this.$refs.textureView.reset();
			this.$nextTick(() => {
				this.destroy();
				this.$router.push(path);
			});
		},

		initEvents () {
			window.addEventListener('online', this.updateOnline);
			window.addEventListener('offline', this.updateOnline);

			// 浏览器刷新事件
			const beforeunloadHandler = (event) => {
				this.saveTaskCacheToServer();
				// // 按标准取消事件
				// event.preventDefault();
				// // Chrome 需要设置 returnValue
				// event.returnValue = 'stop';
			};
			addEventHandler(window, 'beforeunload', (e) => beforeunloadHandler(e));

			// 浏览器返回事件
			if (window.history && window.history.pushState) {
				window.onpopstate = () => {
					// 后退按钮触发事件
					this.saveTaskCacheToServer();
				};
			}
		},

		/**
		 * 销毁
		 */
		destroy () {
			this.onClose(); // close ws
			this.visibleView = false;
		},
	},
	beforeDestroy () {
		this.destroy();
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/grid.less";
@import "~@/assets/styles/mixins/variables.less";

#layout-basic {
	position: relative;
	background: @primary-background;
	height: 100vh;

	.logo {
		font-size: 16px;
		font-weight: 500;
		color: #111111;
		margin-left: 20px;
	}

	.header-title {
		display: flex;
		width: 100%;

		span {
			font-size: 16px;
			font-weight: bold;
			text-align: center;
			width: 260px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			margin: 0 auto;
		}
	}

	.ant-layout-sider {
		margin-top: 52px;
	}

	.ant-layout-content {
		margin-top: 52px;
	}

	.project-sider {
		overflow: auto;
		height: 100%;
		max-height: calc(100vh - 50px - 2px);
		overflow-x: hidden;
		background: transparent;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			left: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.project-right {
		overflow: auto;
		height: auto;
		overflow-x: hidden;
		background: linear-gradient(#C7D8E1, #E3EAEE);
		position: absolute;
		right: 0;
		top: 52px;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			right: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.ant-empty-normal {
		margin: 0 0;
	}

	.list {
		img {
			width: 20px;

			&.w18 {
				width: 18px;
				height: 18px;
			}
		}

		.item-meta {
			display: flex;
			align-items: center;
			cursor: pointer;

			&:hover {
				color: @primary;
			}

			.active {
				color: @primary;
			}
		}
	}

	.progress-item {
		.progress-bar {
			max-width: 160px;
			margin-left: 10px;
			min-width: 160px;
		}

		.ant-list-item-action {
			margin-left: 10px;
		}

		.ant-list-item-meta-title {
			display: flex;
			align-items: center;
		}

		.progress-title {
			display: flex;
			align-items: center;
			height: 22px;

			img {
				height: inherit;
				margin-right: 5px;
			}
		}

		.ant-progress {
			line-height: 0;
		}
	}

	.masking {
		position: fixed;
		top: 52px;
		left: 0;
		width: 315px;
		height: calc(100vh - 50px - 2px);
		background: rgba(0, 0, 0, 0.05);
		z-index: 999;
		cursor: not-allowed;
	}

	.tip-name {
		position: fixed;
		right: 200px;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.network-status {
		position: fixed;
		right: 0;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.upload-btn {
		width: 280px;

		/deep/ .ant-btn {
			border-radius: 3px;
			background-color: #6362FF;
		}
	}

	.submit {
		height: 52px;
		width: 296px;
		padding: 10px 4px;
		background-color: white;

		.ant-btn {
			width: 280px;
			height: 32px;
		}
	}
}
</style>
