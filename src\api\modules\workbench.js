/* ===================================
 * 项目相关 API
 * Created by cjking on 2022/02/21.
 * Copyright 2022, Inc.
 * =================================== */
import * as http from '@/http';

/**
 * 创建项目
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const createProject = (params, options) => http.post(`/project/sysProject/add`, params, options);

/**
 * 获取用户项目列表
 * @param params
 * @returns {Promise}
 */
export const getProjectList = (params) => http.get(`/project/sysProject/list`, params);

/**
 * 获取项目详情
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const getProjectInfo = (params, options) => http.get(`/project/sysProject/queryById`, params, options);

/**
 * 编辑项目
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const editProject = (params, options) => http.put(`/project/sysProject/edit`, params, options);

/**
 * 删除项目
 * @param params
 * @returns {Promise}
 */
export const delProject = (params) => http.del(`/project/sysProject/delete`, params);
