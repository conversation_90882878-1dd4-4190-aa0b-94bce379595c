<template>
	<div class="collapse-tree">
		<a-collapse v-model="innerActiveKeys" expandIconPosition="right">
			<a-collapse-panel class="collapse-panel" :key="panelKey" :disabled="disabled">

				<template slot="header">
					<div class="collapse-panel-header">
						<!-- <div class="icon">
							<img :src="headerIcon" v-if="headerIcon">
						</div> -->
						<div class="title">{{ title }}
							<a-tooltip v-if="tip" placement="top">
								<template slot="title">
									{{ tip }}
								</template>
								<a-icon type="question-circle"></a-icon>
							</a-tooltip>
						</div>
						<div class="extra" @click="e => preventDefaults(e)">
							<slot name="extra"></slot>
						</div>
					</div>
				</template>

				<!-- 内容区域 -->
				<slot name="content">
					<template v-for="data in innerDataList">
						<a-tree :key="data.key" :default-expanded-keys="defaultExpandedKeys" :defaultExpandAll="defaultExpandAll">
							<a-icon slot="switcherIcon" type="down" />
							<a-tree-node :key="data.key">
								<template slot="title">
									<div class="tree-node-custom parent">
										<div class="icon">
											<img :style="parentImgStyle" :src="getImgUrl(data.icon)" v-if="data.icon">
											<slot name="extraIcon" v-if="data.extraIcon"></slot>
										</div>
										<div class="title ml8">
											<a-tooltip placement="rightTop">
												<template slot="title">
													<span v-if="data.title.length > 20">{{ data.title }}</span>
												</template>
												<span :class="{ 'selected': data.selected }" @click="onSelectAll($event, data)">{{ data.title }}</span>
											</a-tooltip>
										</div>
										<div class="icon-actions" @click="e => preventDefaults(e)">
											<slot name="parentActions">
												<img v-if="data.showEye" :src="data.visible ? getEyeImg('eyeShow') : getEyeImg('eyeHide')" @click="onEyeClickAll($event, data)">
												<img v-if="data.showAdd && (!data.menus || !data.menus.length)" src="~@/assets/img/project/detail/add.png" @click="onParentAdd($event, data)">
												<a-dropdown v-if="data.showAdd && data.menus && data.menus.length" @click="(e) => preventDefaults(e)">
													<img src="~@/assets/img/project/detail/add.png">
													<a-menu slot="overlay">
														<template v-for="(menu, dataMenuIndex) in data.menus">
															<a-menu-item class="collapse-menu-item" :key="dataMenuIndex" :disabled="menu.disabled" v-if="menu.isAuto" @click="onParentMenuAdd(data, menu)">
																{{ menu.title }}
															</a-menu-item>
															<a-menu-item class="collapse-menu-item" :key="dataMenuIndex" :disabled="menu.disabled" v-else @click="onParentAdd(null, data)">
																{{ menu.title }}
															</a-menu-item>
															<a-menu-divider :key="dataMenuIndex + '_divider'" v-if="dataMenuIndex < data.menus.length - 1" />
														</template>
													</a-menu>
												</a-dropdown>
											</slot>
										</div>
									</div>
								</template>

								<template v-for="(child, index) in data.children">
									<a-tree-node :key="child.key" style="margin-left: 8px;">
										<template slot="title">
											<div class="tree-node-custom child">
												<div class="icon">
													<img :style="childImgStyle" :src="getImgUrl(child.icon)" v-if="child.icon">
													<slot name="extraChildIcon" v-if="child.extraIcon"></slot>
												</div>
												<div class="title">
													<RightMenu
														:data="child"
														:groupName="data.title"
														:disabledRightMenu="disabledRightMenu"
														:disabledRightMenuEdit="child.disabledRightMenuEdit"
														:disabledRightMenuDel="child.disabledRightMenuDel"
														@onContextMenuClick="onContextMenuClick"
													>
														<template #title>
															<a-tooltip placement="rightTop">
																<template slot="title">
																	<span v-if="child.title.length > 20">{{ child.title }}</span>
																</template>
																<span :class="{ 'opacity65': true, 'selected': child.selected }" @click="onSelect($event, child, data.title)">{{ child.title }}</span>
															</a-tooltip>
														</template>
													</RightMenu>
												</div>
												<div class="icon-actions">
													<slot name="childActions">
														<img v-if="child.showDown" src="~@/assets/img/project/detail/down.png" @click="onDownload($event, child, data.title)">
														<img v-if="child.showEye" :src="child.visible ? getEyeImg('eyeShow') : getEyeImg('eyeHide')" @click="onEyeClick($event, child, data.title)">
														<img v-if="child.showAdd && (!child.menus || !child.menus.length)" src="~@/assets/img/project/detail/add.png" @click="onChildAdd($event, child, data.title)">
														<a-dropdown v-if="child.showAdd && child.menus && child.menus.length" @click="(e) => preventDefaults(e)">
															<img src="~@/assets/img/project/detail/add.png">
															<a-menu slot="overlay">
																<template v-for="(childMenu, childMenuIndex) in child.menus">
																	<a-menu-item class="collapse-menu-item" :key="childMenuIndex" :disabled="childMenu.disabled" v-if="childMenu.isAuto" @click="onChildMenuAdd(child, data.title, childMenu)">
																		{{ childMenu.title }}
																	</a-menu-item>
																	<a-menu-item class="collapse-menu-item" :key="childMenuIndex" :disabled="childMenu.disabled" v-else @click="onChildAdd(null, child, data.title)">
																		{{ childMenu.title }}
																	</a-menu-item>
																	<a-menu-divider :key="childMenuIndex + '_divider'" v-if="childMenuIndex < child.menus.length - 1" />
																</template>
															</a-menu>
														</a-dropdown>
													</slot>
												</div>
											</div>
										</template>
										<template slot="switcherIcon">
											<slot name="icon">
												<template v-if="showLine">
													<i :class="{ 'line-vertical': true, 'last': index === data.children.length - 1 }"></i>
													<i class="line-horizontal"></i>
												</template>
											</slot>
										</template>
									</a-tree-node>
								</template>
							</a-tree-node>
						</a-tree>
					</template>
				</slot>

			</a-collapse-panel>
		</a-collapse>
	</div>
</template>

<script>
/* ===================================
 * 自定义折叠面板
 * Created by cjking on 2021/10/26.
 * Copyright 2021, Inc.
 * =================================== */
// import { logger } from '@/utils/logger';
import { deepCopy, flatten, isBase64, noop } from '@/utils/utils';

import RightMenu from '@/components/common/RightMenu';

function defaultValues (initialValues) {
	return {
		showEye: false,
		showAdd: false,
		showDown: false,
		selected: false,
		visible: false,
		icon: undefined,
		extraIcon: undefined,
		disabledRightMenuDel: false,    // 禁用右键删除
		disabledRightMenuEdit: false,   // 禁用右键编辑
		disabledRightMenuDetail: false, // 禁用右键详情
		menus: [
			// { id: '1', title: '1st menu item', isAuto: true },
			// { id: '2', title: '2st menu item' }
		],
		children: [
			// {
			// 	title: '子菜单名称',
			// 	icon: '',
			// 	showEye: false,
			// 	showAdd: false,
			// 	showDown: false,
			// 	selected: false,
			// 	visible: false,
			// 	menus: [
			// 		{ id: '1', title: '1st menu item', isAuto: true },
			// 		{ id: '2', title: '2st menu item' },
			// 	],
			// },
		],
		handler: noop,
		...initialValues,
	};
}

export default {
	name: 'CollapseTree',
	components: {
		RightMenu,
	},
	props: {
		title: {
			type: String,
			required: true,
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
		activeKeys: {
			type: Array,
			required: false,
			default: () => [],
		},
		panelKey: {
			type: String,
			required: true,
		},
		icon: {
			type: [String, undefined],
			required: false,
			default: undefined,
		},
		showLine: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabledRightMenu: {
			type: Boolean,
			required: false,
			default: false,
		},
		defaultExpandAll: {
			type: Boolean,
			required: false,
			default: false,
		},
		defaultExpandedKeys: {
			type: Array,
			required: false,
			default: () => [],
		},
		iconBasePath: {
			type: String,
			required: false,
			default: () => 'assets/img/project/detail',
		},
		parentImgStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
		childImgStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
		treeData: {
			type: Array,
			required: false,
			default: () => [
				// {
				// 	key: '0-0',
				// 	title: 'Mesh',
				// 	icon: 'part',
				// 	showEye: true,
				// 	showAdd: true,
				// 	children: [
				// 		{ key: '0-0-1', title: 'solid_1', icon: 'cube', showEye: true, showAdd: true, showDown: true },
				// 		{ key: '0-0-2', title: 'solid_2', icon: 'cube', showEye: true, showAdd: true, showDown: true },
				// 	],
				// },
				// {
				// 	key: '0-1',
				// 	title: 'Mesh2',
				// 	icon: 'part',
				// 	showEye: true,
				// 	showAdd: true,
				// 	children: [
				// 		{ key: '0-1-1', title: 'solid_1', icon: 'cube', showEye: false, showAdd: true, showDown: true },
				// 		{ key: '0-1-2', title: 'solid_2', icon: 'cube', showEye: true, showAdd: true, showDown: true, handler: (e, data, groupName) => {} },
				// 	],
				// },
			],
		},
	},
	data () {
		return {
			innerDataList: [],
			innerActiveKeys: [],
		};
	},
	watch: {
		activeKeys: {
			immediate: true,
			handler (keys) {
				this.$nextTick(() => {
					if (this.defaultExpandAll) {
						this.innerActiveKeys = [this.panelKey];
					} else {
						this.innerActiveKeys = keys?.length ? keys : [];
					}
				});
			},
		},
		treeData: {
			deep: true,
			immediate: true,
			handler () {
				this.initData(this.treeData);
			},
		},
	},
	computed: {
		headerIcon () {
			return this.getImgUrl(this.icon);
		},
	},
	methods: {
		/**
		 * 初始化数据
		 */
		initData (sourceList) {
			const tmpSourceList = deepCopy(sourceList);
			const objHandler = (item) => Object.assign(item, defaultValues(item));
			const arrayHandler = (list) => {
				for (const item of list) {
					objHandler(item);
					if (item.children) {
						arrayHandler(item.children);
					}
				}
			};
			for (const tmpSource of tmpSourceList) {
				objHandler(tmpSource);
				if (tmpSource.children) {
					arrayHandler(tmpSource.children);
				}
			}
			this.innerDataList = tmpSourceList;
		},

		/**
		 * 根据文件名获取图片完整地址
		 */
		getImgUrl (filePath) {
			if (!filePath) {
				return '';
			}
			if (isBase64(filePath)) {
				return filePath;
			}
			// ../.. 相对于目录 src
			return require(`../../${ this.iconBasePath }/${ filePath }.png`);
		},

		/**
		 * 获取眼睛显示/禁用图像
		 */
		getEyeImg (name) {
			return this.getImgUrl(name);
		},

		/**
		 * 下载事件(子集)
		 */
		onDownload (event, data, groupName) {
			this.preventDefaults(event);
			data = deepCopy(data);
			// logger.log('下载事件(子集) groupName, data: ', groupName, data);
			this.$emit('onDownload', this.packResult(groupName, data));
		},

		/**
		 * 眼睛点击事件(父级)
		 */
		onEyeClickAll (event, data) {
			this.preventDefaults(event);
			data.visible = !data.visible;
			if (data.children?.length) {
				for (const child of data.children) {
					child.visible = data.visible;
				}
			}
			data = deepCopy(data);
			// logger.log('眼睛点击事件(父级) data: ', data);
			this.$emit('onVisibleAll', this.packResult(data.title, data));
		},

		/**
		 * 眼睛点击事件(子集)
		 */
		onEyeClick (event, data, groupName) {
			this.preventDefaults(event);
			data.visible = !data.visible;
			this.commonHandler(data.key, 'visible');
			data = deepCopy(data);
			// logger.log('眼睛点击事件(子集) groupName, data: ', groupName, data);
			this.$emit('onVisible', this.packResult(groupName, data));
		},

		/**
		 * 添加事件(父级)
		 */
		onParentAdd (event, data) {
			this.preventDefaults(event);
			data = deepCopy(data);
			// logger.log('添加事件(父级) data: ', data);
			this.$emit('onParentAdd', this.packResult(data.title, data));
		},

		/**
		 * 添加事件(父级下拉菜单)
		 */
		onParentMenuAdd (data, menu) {
			data = deepCopy(data);
			menu = deepCopy(menu);
			// logger.log('添加事件(父级下拉菜单) data, menu: ', data, menu);
			this.$emit('onParentMenuAdd', this.packResult(data.title, data, null, menu));
		},

		/**
		 * 添加事件(子集)
		 */
		onChildAdd (event, data, groupName) {
			this.preventDefaults(event);
			data = deepCopy(data);
			// logger.log('添加事件(子集) groupName, data: ', groupName, data);
			this.$emit('onChildAdd', this.packResult(groupName, data));
		},

		/**
		 * 添加事件(子集下拉菜单)
		 */
		onChildMenuAdd (data, groupName, menu) {
			data = deepCopy(data);
			menu = deepCopy(menu);
			// logger.log('添加事件(子集下拉菜单) groupName, data, menu: ', groupName, data, menu);
			this.$emit('onChildMenuAdd', this.packResult(groupName, data, null, menu));
		},

		/**
		 * 选择事件(父级)
		 */
		onSelectAll (event, data) {
			this.preventDefaults(event);
			data.selected = !data.selected;
			if (data.children?.length) {
				for (const child of data.children) {
					child.selected = data.selected;
				}
			}
			data = deepCopy(data);
			// logger.log('选择事件(父级) data: ', data);
			this.$emit('onSelectAll', this.packResult(data.title, data));
		},

		/**
		 * 选择事件(子集)
		 */
		onSelect (event, data, groupName) {
			this.preventDefaults(event);
			data.selected = !data.selected;
			this.commonHandler(data.key, 'selected');
			data = deepCopy(data);
			// logger.log('选择事件(子集) groupName, data: ', groupName, data);
			this.$emit('onSelect', this.packResult(groupName, data));
		},

		/**
		 * 右键点击事件
		 */
		onContextMenuClick ({ groupName, data, type }) { // type: edit、delete
			// logger.log('右键点击事件 data, type: ', data, type);
			this.$emit('onContextMenuClick', this.packResult(groupName, data, type));
		},

		/**
		 * 公共处理程序
		 */
		commonHandler (dataKey, key) {
			let parentKey;
			const flattenList = flatten(this.innerDataList);
			for (const item of flattenList) {
				if (item.key === dataKey) {
					const tmpArr = item.key.split('-');
					parentKey = tmpArr.slice(0, tmpArr.length - 1).join('-');
					break;
				}
			}

			for (const item of this.innerDataList) {
				if (item.key === parentKey) {
					item[key] = item.children.every(child => child[key]);
					break;
				}
			}
		},

		/**
		 * 打包结果
		 */
		packResult (groupName, data, type, menu) {
			return { header: this.title, groupName, data, type, menu };
		},

		/**
		 * 防止默认值
		 */
		preventDefaults (e) {
			e?.preventDefault();
			e?.stopPropagation();
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/mixins/variables.less";

.collapse-menu-item {
	padding: 0 12px;
}

.collapse-tree {

	/deep/ .ant-tree li .ant-tree-node-content-wrapper {
		&:hover {
			background: none;
			cursor: default;
		}
		&.ant-tree-node-selected {
			background: none;
			cursor: default;
		}
	}

	/deep/ .ant-collapse > .ant-collapse-item {
		border-bottom: 3px solid #E5E5E5;
		.ant-collapse-header {
			border-left: 2px solid @primary;
			border-radius: 0;
			color: #666666;
			background: #F9F9FF;
			padding: 9px 40px 9px 16px;
		}
	}

	.collapse-panel {

		.disable-selection();

		img {
			width: 20px;
			.disable-selection();

			&.w18 {
				width: 18px;
				height: 18px;
			}
		}

		.collapse-panel-header {
			display: flex;
			align-items: center;
			width: 265px;

			.icon {
				flex: 0 0 20px;
			}
			.title {
				flex: 1;
				margin-left: 2px;
				font-size: 16px;
				font-weight: bold;
				.disable-selection();
			}
			.extra {
				flex: 0 0 70px;
				text-align: end;
				.disable-selection();
			}
		}

		.tree-node-custom {
			display: flex;

			&.parent {
				width: 257px;
			}

			&.child {
				width: 231px;
			}

			.icon {
				flex: 0 0 20px;
			}
			.title {
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				&.ml8 {
					margin-left: 8px;
				}

				span {
					display: block;
					cursor: pointer;
					color: #000000;
					.disable-selection();
				}

				.opacity65 {
					color: rgba(0, 0, 0, 0.65);
				}

				.selected {
					color: @primary;
				}
			}
			.icon-actions {
				flex: 0 0 70px;
				text-align: end;

				img {
					margin-top: -2px;
					margin-right: 5px;
					cursor: pointer;

					&:last-child {
						margin-right: 0;
					}
				}
			}
		}
	}

	.line-vertical {
		position: absolute;
		left: 12px;
		top: -4px;
		width: 1px;
		height: 32px;
		background: rgba(58, 58, 58, 0.5);
		&.last {
			top: -8px;
			height: 20px;
		}
	}
	.line-horizontal {
		position: absolute;
		left: 12px;
		top: 12px;
		width: 14px;
		height: 1px;
		background: rgba(58, 58, 58, 0.5);
	}
}
</style>
