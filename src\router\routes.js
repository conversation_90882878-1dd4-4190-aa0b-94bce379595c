import { UserLayout, TabLayout, RouteView } from '@/components/layouts';

/**
 * 基础路由
 */
const Routes = [
	{
		path: '/',
		desc: '首页',
		component: () => import(/* webpackChunkName: "user" */ '@/views/base/Blank'),
	},
	{
		path: '/user',
		component: UserLayout,
		redirect: '/user/login',
		children: [
			{
				path: 'login',
				desc: '登录',
				component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login'),
			},
			{
				path: 'register',
				desc: '注册',
				component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register'),
			},
			{
				path: 'forgetPwd',
				desc: '忘记密码',
				component: () => import(/* webpackChunkName: "user" */ '@/views/user/ForgetPassword'),
			},
			{
				path: 'updatePwd',
				desc: '通过邮件修改密码',
				component: () => import(/* webpackChunkName: "user" */ '@/views/user/UpdatePassword'),
			},
		],
	},
	{
		path: '/base',
		desc: '企业套餐',
		component: TabLayout,
		redirect: '/base/authorize',
		children: [
			{
				path: 'authorize',
				name: 'base-authorize',
				component: () => import(/* webpackChunkName: "base" */ '@/views/base/Authorize'),
			},
			{
				path: 'blank',
				name: 'base-blank',
				component: () => import(/* webpackChunkName: "base" */ '@/views/base/Blank'),
			},
		],
	},
	{
		path: '/mold',
		desc: '随形冷却',
		component: TabLayout,
		redirect: '/mold/project',
		children: [
			{
				path: 'project',
				component: RouteView,
				children: [
					{
						path: '',
						redirect: '/mold/project/list',
					},
					{
						path: 'list',
						desc: '项目列表',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "mold" */ '@/views/module/mold/ProjectList'),
					},
					{
						path: 'detail/:id',
						desc: '任务列表',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "mold" */ '@/views/module/mold/TaskList'),
					},
					{
						path: 'detail/:id/:taskId',
						desc: '任务详情',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "mold" */ '@/views/module/mold/TaskDetail'),
					},
				],
			},
		],
	},
	{
		path: '/texture',
		desc: '数字纹理',
		component: TabLayout,
		redirect: '/texture/project',
		children: [
			{
				path: 'project',
				component: RouteView,
				children: [
					{
						path: '',
						redirect: '/texture/project/list',
					},
					{
						path: 'list',
						desc: '项目列表',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "texture" */ '@/views/module/texture/ProjectList'),
					},
					{
						path: 'detail/:id',
						desc: '任务列表',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "texture" */ '@/views/module/texture/TaskList'),
					},
					{
						path: 'detail/:id/:taskId',
						desc: '任务详情',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "texture" */ '@/views/module/texture/TaskDetail'),
					},
				],
			},
		],
	},
	{
		path: '/shoe',
		desc: '客制化鞋',
		component: TabLayout,
		redirect: '/shoe/project',
		children: [
			{
				path: 'project',
				component: RouteView,
				children: [
					{
						path: '',
						redirect: '/shoe/project/list',
					},
					{
						path: 'list',
						desc: '项目列表',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "shoe" */ '@/views/module/shoe/ProjectList'),
					},
					{
						path: 'detail/:id',
						desc: '任务列表',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "shoe" */ '@/views/module/shoe/TaskList'),
					},
					{
						path: 'detail/:id/:taskId',
						desc: '任务详情',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "shoe" */ '@/views/module/shoe/TaskDetail'),
					},
				],
			},
		],
	},
	{
		path: '/example',
		desc: '测试案例',
		component: TabLayout,
		redirect: '/example/model',
		children: [
			// {
			// 	path: 'vtp',
			// 	desc: 'vtp视图渲染',
			// 	meta: { type: 2 },
			// 	component: () => import(/* webpackChunkName: "example" */ '@/views/test/VtpView'),
			// },
			{
				path: 'model',
				component: RouteView,
				children: [
					{
						path: '',
						redirect: '/example/model/view',
					},
					{
						path: 'view',
						desc: '模型视图渲染器',
						meta: { type: 2 },
						component: () => import(/* webpackChunkName: "example" */ '@/views/module/model/ModelDetail'),
					},
				],
			},
		],
	},
	{
		path: '*',
		desc: '统一错误拦截页',
		component: () => import(/* webpackChunkName: "404" */ '@/views/exception/404'),
	},
];

// 生产环境不需要示例，需删除相关路由
if (process.env.VUE_APP_MODE === 'production') {
	for (let i = 0; i < Routes.length; i++) {
		const route = Routes[i];
		if (route.path === '/example') {
			Routes.splice(i, 1);
		}
	}
}

export { Routes };
