<template>
	<a-layout class="layout-basic">
		<GlobalHeader :menu="ModuleEnum.texture" />
		<HeaderTip :module-object="moduleObject" :style="{ marginTop: '80px', marginBottom: '-80px'}" />
		<a-layout style="margin-top: 80px;">
			<a-layout-content class="container">
				<div class="main">
					<div class="action">
						<div class="actions">
							<div>
								<a-breadcrumb separator=">">
									<a-breadcrumb-item>
										<router-link to="/texture/project/list">{{ projectInfo.projectName }}</router-link>
									</a-breadcrumb-item>
									<a-breadcrumb-item>任务列表</a-breadcrumb-item>
								</a-breadcrumb>
							</div>
							<div>
								<a-button
									:disabled="!moduleObject.canCreate || userInfo.id !== projectInfo.createBy"
									class="btn"
									icon="plus"
									type="primary"
									size="large"
									@click="openCreateTaskModal()"
								>新建任务
								</a-button>
							</div>
						</div>
					</div>
					<div>
						<a-form layout="inline" @keyup.enter.native="searchQuery">
							<a-row :gutter="24">
								<a-col :md="6" :sm="12">
									<a-form-item label="任务名称">
										<a-input placeholder="请输入任务名称" v-model="queryParam.taskName" allowClear></a-input>
									</a-form-item>
								</a-col>
								<a-col :md="8" :sm="12">
									<a-form-item label="日期范围">
										<a-range-picker
											format="YYYY-MM-DD"
											v-model="queryParam.createTime"
											:show-time="false"
											@change="handleCreateTimeChange"
											allowClear
										/>
									</a-form-item>
								</a-col>
								<a-col :md="4" :sm="8">
									<div
										style="float: left;overflow: hidden;margin-top:4px;"
									>
										<a-button type="primary" @click="searchQuery" icon="search">查询
										</a-button>
										<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
									</div>
								</a-col>
							</a-row>
						</a-form>
					</div>
					<br>
					<a-list
						:grid="{ gutter: 28, sm: 4, md: 4 }"
						:pagination="pagination"
						:data-source="dataSource"
					>
						<a-list-item slot="renderItem" slot-scope="record">
							<a-card :bodyStyle="{padding:0,height:'308px'}" class="task-card">
								<router-link :to="'/texture/project/detail/' + record.projectId + '/' + record.id">
									<img class="card-icon" :src="getPreviewImg(record)" alt="" @error="defaultImgHandle">
								</router-link>
								<div class="card-bottom">
									<div style="display:flex;align-items:center;justify-content:flex-start">
										<router-link
											:to="'/texture/project/detail/' + record.projectId + '/' + record.id"
										>
											<h1>
												<b>
													<j-ellipsis
														:value="record.taskName"
														:length="10"
													>
													</j-ellipsis>
												</b>
											</h1>
										</router-link>
										<div v-if="userInfo.id === record.createBy" @click="openEditTaskModal(record)">
											<svg-icon
												type="edit"
												class-name="edit"
												:vStyle="{width:'20px',height:'20px'}"
												title="编辑"
											></svg-icon>
										</div>
									</div>
									<div class="secondary">
										{{ record.createByName }} {{ record.createTime }}
									</div>
								</div>
								<img v-if="userInfo.id === record.createBy" :src="failCircleImg" class="fail-circle" alt="" @click="delProjectTask(record)">
							</a-card>
						</a-list-item>
					</a-list>
				</div>
				<!-- 新建项目弹窗 -->
				<create-task
					v-if="visible"
					ref="createTaskModal"
					@close="closeCallback"
					@ok="okCallback"
				/>
			</a-layout-content>
		</a-layout>
	</a-layout>
</template>

<script>
/* ===================================
 * 我的项目
 * Created by cjking on 2021/06/21.
 * Copyright 2021, Inc.
 * =================================== */
import { mapGetters } from 'vuex';
import { logger } from '@/utils/logger';
import { storage } from '@/utils/storage';
import { ModuleEnum, ProjectTypeEnum } from '@/constants';
import { delProjectTask, getProjectTaskList, getProjectInfo } from '@/api';

import Config from '@/config/Config';
import JEllipsis from '@/components/common/JEllipsis';
import CreateTask from '@/views/module/texture/CreateTask';
import GlobalHeader from '@/components/page/GlobalHeader';
import HeaderTip from '@/components/common/HeaderTip';

import msgImg from '@/assets/img/msg.png';
import starImg from '@/assets/img/star.png';
import likeImg from '@/assets/img/like.png';
import notifyImg from '@/assets/img/notify.png';
import failCircleImg from '@/assets/img/fail_circle.png';
import defaultImg from '@/assets/img/project/list/default-task-icon.png';
import { mixinModule } from '@/mixins';

export default {
	name: 'ProjectList',
	components: {
		GlobalHeader,
		CreateTask,
		JEllipsis,
		HeaderTip,
	},
	mixins: [
		mixinModule
	],
	data () {
		return {
			msgImg,
			starImg,
			likeImg,
			notifyImg,
			failCircleImg,
			ModuleEnum,
			ProjectTypeEnum,
			pathname: window?.location?.pathname, // 当前路由
			queryParam: {
				taskName: '',
				createTime: [],
				createTime_begin: '',
				createTime_end: '',
			},
			/* 分页参数 */
			pagination: {
				current: 1,
				pageSize: 8,
				pageSizeOptions: ['8', '16', '24'],
				showTotal: (total, range) => {
					return range[0] + '-' + range[1] + ' 共' + total + '条';
				},
				onChange: this.handlePageChange,
				onShowSizeChange: this.handlePageChange,
				showQuickJumper: false,
				showSizeChanger: true,
				total: 0,
			},
			visible: false,
			dataSource: [],
			projectInfo: {},
			currentType: ProjectTypeEnum.private, // 0:公开项目, 1:我的项目(私有项目)
			currentModule: '2',
		};
	},
	computed: {
		...mapGetters(['userInfo']),

		basePath () {
			return `${ Config.staticDomainURL }/${ this.userInfo.orgCode }`;
		},
	},
	created () {
		this.projectId = this.$route.params.id;
		this.loadData();
	},
	methods: {
		/**
		 * 获取用户项目列表
		 */
		async loadData (arg) {
			if (arg === 1) {
				this.pagination.current = 1;
			}
			const res0 = await getProjectInfo({
				id: this.projectId,
			});
			this.projectInfo = res0.result;
			const res = await getProjectTaskList({
				pageNo: this.pagination.current,
				pageSize: this.pagination.pageSize,
				createTime_begin: this.queryParam.createTime_begin,
				createTime_end: this.queryParam.createTime_end,
				taskName: this.queryParam.taskName ? ('*' + this.queryParam.taskName + '*') : '',
				order: 'desc',
				column: 'createTime',
				projectId: this.projectId,
			});
			logger.log('获取用户项目列表 res: ', res);
			if (res?.success && res.result) {
				this.dataSource = res.result.records || [];
				this.pagination.total = res.result.total || 0;
			}
		},

		/**
		 * 打开新建项目弹窗
		 */
		openCreateTaskModal () {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.createTaskModal.showModal(this.projectId, this.moduleObject.id);
			});
		},

		/**
		 * 打开编辑项目弹窗
		 */
		openEditTaskModal (record) {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.createTaskModal.editModal(record, this.projectId, this.moduleObject.id);
			});
		},

		/**
		 * 取消创建项目回调函数
		 */
		closeCallback () {
			this.visible = false;
		},

		/**
		 * 创建项目后回调函数
		 */
		okCallback (isEdit) {
			this.visible = false;
			this.loadData(isEdit ? undefined : 1);
		},

		/**
		 * 查看项目详情
		 */
		lookDetail (item) {
			this.$router.push('/texture/project/detail/' + item.id);
		},

		/**
		 * 删除任务
		 */
		delProjectTask (item) {
			logger.log('删除任务 item: ', item);
			this.$confirm({
				title: '提示',
				content: `真的要删除任务【${ item.taskName }】吗 ?`,
				okText: '确认',
				cancelText: '取消',
				onOk: async () => {
					storage.remove(item.id);
					const res = await delProjectTask({ id: item.id });
					if (res?.success) {
						this.$message.success('删除任务成功');
						await this.loadData(1);
					}
				},
			});
		},

		/**
		 * 获取预览图
		 */
		getPreviewImg (item) {
			if (item.cover) {
				return `${ Config.staticDomainURL }/${ item.cover }?_` + Date.now();
			} else {
				return defaultImg;
			}
		},

		/**
		 * 默认图
		 * @param {Event} event
		 */
		defaultImgHandle (event) {
			const img = event.target;
			img.src = defaultImg;
			// img.style.padding = '55px 100px';
			img.onerror = null; // 防止闪图
		},

		handlePageChange (page, pageSize) {
			this.pagination.current = page;
			this.pagination.pageSize = pageSize;
			this.loadData();
		},

		handleCreateTimeChange (value, dataString) {
			this.queryParam.createTime_begin = dataString[0];
			this.queryParam.createTime_end = dataString[1];
		},

		searchQuery () {
			this.loadData(1);
		},

		searchReset () {
			this.queryParam = {
				taskName: '',
				createTime: [],
				createTime_begin: '',
				createTime_end: '',
			};
			this.loadData(1);
		}
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.layout-basic {
	height: 100vh;
	background: #EDEDED;

	.ant-layout-sider {
		background: @white;
		color: #000000;
		box-shadow: 4px 0 10px 0 rgba(0, 0, 0, 0.1);
	}

	.ant-layout-content {
		color: #000000;
	}

	.container {
		background-color: @white;

		.main {
			max-width: 1600px;
			margin: 0 auto;
			padding: 10px 20px;

			.actions {
				width: 100%;
				height: 60px;
				background: @white;
				display: flex;
				justify-content: space-between;
				align-items: center;
				position: relative;

				.label {
					font-size: 18px;
					font-weight: bold;
				}

				.btn:not(:disabled) {
					background: @primary;
				}
			}
		}
	}
}

.task-card {
	position: relative;

	.card-icon {
		width: 100%;
		height: 208px;
		background-color: #EFEFEF;

		&:hover {
			cursor: pointer;
		}
	}

	.fail-circle {
		visibility: hidden;
		position: absolute;
		right: -14px;
		top: -14px;
		cursor: pointer;
	}

	&:hover {
		.fail-circle {
			visibility: visible;
		}
	}
}

.card-bottom {
	height: 100px;
	padding: 10px 20px 10px 20px;

	h1 {
		font-size: 20px;

		&:hover {
			color: #1890FF;
			cursor: pointer;
		}
	}

	.secondary {
		margin-top: 0px;
		color: #999999;
	}

	position: relative;
}

@media screen and (max-width: 1200px) {
	.layout-basic {
		.project {
			.box {
				width: calc((100vw - 4.5vw - 200px - 60px - 8px) / 3);
			}
		}
	}
}

/deep/ .ant-list .ant-spin-container {
	min-height: calc(100vh - 240px);
}

.svg-icon {
	color: @primary;
	margin-left: 10px;
	cursor: pointer;
}

</style>
