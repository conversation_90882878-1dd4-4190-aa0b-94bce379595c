/* ===================================
 * 指令：转换为字符串
 * Created by cjking on 2021/10/20.
 * Copyright 2021, Inc.
 * =================================== */
import { noop } from '@/utils/utils';

export const ToStringDirective = Vue => Vue.directive('toString', {
	bind (el) {
		const blurHandler = function (e) {
			if (e.target && e.target.value) {
				const stringValue = String(e.target.value);
				if (e.target.value !== stringValue) {
					e.target.value = stringValue;
					el.dispatchEvent(new Event('input'));
				}
			}
		};
		el.removeEventListener('blur', noop);
		el.addEventListener('blur', blurHandler);
	},
});
