import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import VueLazyLoad from 'vue-lazyload';
import initDirectives from './directives';
import * as filters from '@/filters';

import '@/components/svgIcon'; // 全局注册svg组件
import '@/permission';  // permission control
import '@/assets/styles/main.less';

import Config from '@/config/Config';
import { initAntDesignComponents } from '@/antdv';

import homeDefault from '@/assets/img/home_default.jpg';

Object.keys(filters).forEach(key => Vue.filter(key, filters[key]));

Vue.use(VueLazyLoad, {
	preLoad: 1.3,
	error: homeDefault,
	loading: homeDefault,
	attempt: 1,
});

initDirectives();
initAntDesignComponents();

Vue.config.devtools = !Config.production;   // 显示浏览器 vue devtools 面板
Vue.config.productionTip = false;           // 消息提示的环境配置，设置为开发环境或者生产环境

new Vue({
	store,
	router,
	render: h => h(App),
}).$mount('#app');
