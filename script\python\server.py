import utils
import logger

logger = logger.Logger()


async def getServer(env):
	# 服务器地址
	serversDict = {}
	servers = serversDict[env]
	selectIndex = await utils.getSelectValue(servers, '请选择服务器地址？')
	server = servers[selectIndex - 1]
	logger.choose("您选择的服务器地址是: %s" % server, "blue")
	return server


# 获取内网IP
def getInsideIP(public_ip):
	# 服务器地址
	servers = {
		"*************": "*************"
	}

	if public_ip not in servers.keys():
		return public_ip

	insideIP = servers[public_ip]
	return insideIP
