// 导入我们需要执行操作的脚本集
import { expose } from 'http://192.168.87.19:8901/npm/comlink@4.3.1/dist/esm/comlink.mjs';
import openCascade from './CascadeWorker/opencascade.js/dist/opencascade.wasm.js';
import { loadFiles } from './CascadeWorker/CascadeFileUtils.js';

async function initOCC (params = {}) {
	console.log('来自主线程的参数 params: ', params);

	self.oc = await getOCC();
	console.log('occ: ', self.oc);

	return true;
}

async function initData ({ fileName, fileText }) {
	console.log('fileName: ', fileName);
	// console.log('fileText: ', fileText);
	const response = loadFiles(fileName, fileText);
	console.log('response: ', response);

	return response;
}

async function getOCC () {
	const OpenCascade = openCascade;
	return new OpenCascade({
		locateFile (path) {
			if (path.endsWith('.wasm')) {
				return './CascadeWorker/opencascade.js/dist/opencascade.wasm.wasm';
			}
			return path;
		},
	}).then((occ) => {
		console.log('OpenCascade loaded!');
		return occ;
	});
}

// expose the Analyzer "API" with Comlink
expose({
	initOCC,
	initData,
});
