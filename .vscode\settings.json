{
	"editor.detectIndentation": false,
	"editor.tabSize": 4,
	// "editor.fontFamily": "Fira Code",
	"editor.fontLigatures": true,
	"window.zoomLevel": 0,
	"files.associations": {
		"*.cjson": "jsonc",
		"*.wxss": "css",
		"*.wxs": "javascript",
		"*.json": "jsonc"
	},
	"emmet.includeLanguages": {
		"wxml": "html"
	},
	"minapp-vscode.disableAutoConfig": true,
	"workbench.editor.enablePreview": false,
	"workbench.startupEditor": "newUntitledFile",
	"[typescript]": {
		"editor.defaultFormatter": "vscode.typescript-language-features"
	},
	"[json]": {
		"editor.defaultFormatter": "vscode.json-language-features"
	},
	// 使能每一种语言默认格式化规则
	"[html]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[css]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[less]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[javascript]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	// "[typescript]": {
	//     "editor.defaultFormatter": "esbenp.prettier-vscode"
	// },
	/* prettier的配置 */
	"prettier.printWidth": 100, // 超过最大值换行
	"prettier.tabWidth": 4, // 缩进字节数
	"prettier.useTabs": true, // 缩进不使用tab，使用空格
	"prettier.semi": true, // 句尾添加分号
	"prettier.singleQuote": true, // 使用单引号代替双引号
	"prettier.proseWrap": "preserve", // 默认值。因为使用了一些折行敏感型的渲染器（如GitHub comment）而按照markdown文本样式进行折行
	"prettier.arrowParens": "avoid", //  (x) => {} 箭头函数参数只有一个时是否要有小括号。avoid：省略括号
	"prettier.bracketSpacing": true, // 在对象，数组括号与文字之间加空格 "{ foo: bar }"
	"prettier.disableLanguages": ["vue"], // 不格式化vue文件，vue文件的格式化单独设置
	"prettier.endOfLine": "auto", // 结尾是 \n \r \n\r auto
	"prettier.htmlWhitespaceSensitivity": "ignore",
	"prettier.ignorePath": ".prettierignore", // 不使用prettier格式化的文件填写在项目的.prettierignore文件中
	"prettier.jsxBracketSameLine": false, // 在jsx中把'>' 是否单独放一行
	"prettier.jsxSingleQuote": false, // 在jsx中使用单引号代替双引号
	"prettier.requireConfig": false, // Require a 'prettierconfig' to format prettier
	"prettier.trailingComma": "es5", // 在对象或数组最后一个元素后面是否加逗号（在ES5中加尾逗号）
	"vetur.format.defaultFormatter.js": "vscode-typescript",
	"vetur.format.enable": true, // vetur相关设置
	"vetur.validation.template": true, // vetur相关设置
	"vetur.format.defaultFormatter.html": "js-beautify-html", // vetur相关设置
	"vetur.format.defaultFormatterOptions": {
		"js-beautify-html": {
			"wrap_line_length": 100,
			"wrap_attributes": "auto",
			"end_with_newline": false
		},
		"prettier": {
			"singleQuote": true
		}
    },
    "vetur.format.options.tabSize": 4,
    "vetur.format.options.useTabs": true,
	"javascript.format.insertSpaceAfterConstructor": true,
	"typescript.format.insertSpaceAfterConstructor": true,
	"javascript.format.insertSpaceBeforeFunctionParenthesis": true, //  #让函数(名)和后面的括号之间加个空格
	"typescript.format.insertSpaceBeforeFunctionParenthesis": true,
	// "editor.formatOnType": true, //开启自动格式化
	// "javascript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": true,
	"[jsonc]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[vue]": {
		"editor.defaultFormatter": "octref.vetur"
	},
	"open-php-html-js-in-browser.selectedBrowser": "Chrome",
	"liveServer.settings.donotShowInfoMsg": true,
	// "terminal.integrated.shell.osx": "/bin/bash"
	"terminal.integrated.shell.osx": "/bin/zsh",
	"terminal.external.linuxExec": "item2",
	"terminal.external.osxExec": "item2.app",
	"editor.codeActionsOnSave": ["source.fixAll.eslint"]
}
