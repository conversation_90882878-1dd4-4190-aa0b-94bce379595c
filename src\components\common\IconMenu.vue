<template>
	<div class="icon-menu">
		<img :src="imgSrc" :style="vStyle" v-if="imgSrc" alt="">
		<a-icon :type="type" :style="vStyle" v-if="!imgSrc" />
		<span class="text" v-if="title">{{ title }}</span>
	</div>
</template>

<script>
/* ===================================
 * 图标菜单
 * Created by cjking on 2021/04/09.
 * Copyright 2021, Inc.
 * =================================== */

export default {
	name: 'IconMenu',
	props: {
		title: {
			type: String,
			required: false,
			default: '',
		},
		type: {
			type: String,
			required: false,
			default: '',
		},
		vStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
		imgSrc: {
			type: String,
			required: false,
			default: '',
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/mixins/variables.less";

.icon-menu {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 50px;
	line-height: 50px;

	i {
		font-size: 30px;
		margin-right: 0;
		color: @blue-primary;
	}

	img {
		float: left;
		border: none;
		width: 22px;
		height: 22px;
	}

	.text {
		line-height: 16px;
		color: #333333;
		margin-top: 5px;
	}
}
</style>
