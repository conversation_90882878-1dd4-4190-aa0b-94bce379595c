import Vue from 'vue';
import VueRouter from 'vue-router';
import { Routes } from '@/router/routes';

Vue.use(VueRouter);

const router = new VueRouter({
	mode: 'history',
	routes: Routes,
	base: process.env.BASE_URL,
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
	// logger.log('路由守卫 to: ', to);
	next();
});

// 获取原型对象上的push函数
const originalPush = VueRouter.prototype.push;
// 修改原型对象中的push方法
VueRouter.prototype.push = function push (location) {
	return originalPush.call(this, location).catch(err => err);
};

export default router;
