<template>
	<div class="user-layout-login">

		<a-form-model ref="ruleForm" :model="form" :rules="rules" @keyup.enter.native="handleSubmit" :hideRequiredMark="true">
			<div class="user-layout-login-title"><img :src="loginLogo" style="height: 70px"></div>
			<br>
			<br>
			<a-form-model-item prop="username" has-feedback :colon="false">
				<template #label>
					<label><b>账号</b></label>
				</template>
				<a-input type="text" size="large" v-model.trim="form.username" placeholder="邮箱/手机号/帐号">
					<a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
				</a-input>
			</a-form-model-item>

			<a-form-model-item prop="password" has-feedback :colon="false">
				<template #label>
					<label><b>密码</b></label>
				</template>
				<a-input type="password" size="large" v-model.trim="form.password" placeholder="请输入密码">
					<a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }" />
				</a-input>
			</a-form-model-item>

			<!--<a-row :gutter="24">
				<a-col :span="14">
					<a-form-model-item prop="captcha" label="验证码">
						<a-input type="text" size="large" v-model.trim="form.captcha" placeholder="请输入验证码">
							<a-icon slot="prefix" type="message" :style="{ color: 'rgba(0,0,0,.25)' }" />
						</a-input>
					</a-form-model-item>
				</a-col>
				<a-col :span="9" style="margin-top: 36px;">
					<img style="margin-top: 2px; cursor: pointer;" :src="randCodeImage" alt @click="getCaptcha">
				</a-col>
			</a-row>-->
		</a-form-model>

		<div style="display: flex; justify-content: space-between; align-items: center;">
			<a v-href @click="forgetPassword">忘记密码?</a>
		</div>
		<a-button size="large" type="primary" class="login-button" :loading="btnLoading" @click.stop.prevent="handleSubmit">立即登录</a-button>
		<br>
		<br>
		<div style="display: flex; justify-content: center; align-items: center;">
			登录即表示您同意并遵守<a v-href @click="handleUserAgreementModal">《用户协议》</a>和<a v-href @click="handlePrivacyAgreementModal">《隐私协议》</a>
		</div>
		<br>
		<PrivacyAgreementModal ref="privacyAgreementModal" />
		<UserAgreementModal ref="userAgreementModal" />
	</div>
</template>

<script>
/* ===================================
 * 登录页
 * Created by cjking on 2021/04/07.
 * Copyright 2021, Inc.
 * =================================== */
import { mapActions } from 'vuex';
import { getSmSCode } from '@/api';
import { logger } from '@/utils/logger';
import { formValidate } from '@/utils/validate';
import { clearUserData, deleteNullAttr } from '@/utils/utils';
import checkCode from '@/assets/img/check-code.png';
import loginLogo from '@/assets/img/login-logo.png';
import PrivacyAgreementModal from './modules/PrivacyAgreementModal.vue';
import UserAgreementModal from './modules/UserAgreementModal.vue';

export default {
	components: {
		PrivacyAgreementModal,
		UserAgreementModal,
	},
	data () {
		return {
			btnLoading: false,
			labelCol: {
				xs: { span: 24 },
				sm: { span: 5 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			form: {
				username: '',   // 账号
				password: '',   // 密码
				captcha: '',    // 验证码
			},
			rules: {
				username: formValidate.noEmpty('请输入帐号'),
				password: formValidate.noEmpty('请输入密码'),
				// captcha: formValidate.noEmpty('请输入验证码'),
			},
			inputCodeContent: '',
			randCodeImage: checkCode,
			loginLogo,
			currDateTime: null,
		};
	},
	created () {
		// clearUserData();
		// this.getCaptcha();
	},
	methods: {
		...mapActions(['Login', 'initPermissionList']),

		/**
		 * 获取验证码
		 */
		getCaptcha () {
			this.currDateTime = Date.now();
			getSmSCode(this.currDateTime).then(res => {
				if (res?.success && res.result) {
					this.randCodeImage = res.result.base64 ? `data:image/jpeg;base64,${ res.result.base64 }` : res.result;
				} else {
					this.randCodeImage = checkCode;
					this.$message.error(res?.message);
				}
			}).catch(() => {
				this.randCodeImage = checkCode;
			});
		},

		/**
		 * 下一步
		 */
		handleSubmit () {
			this.$refs.ruleForm.validate(async valid => {
				if (!valid) return false;
				// const params = {
				// 	username: this.form.username,   // 账号
				// 	password: this.form.password,   // 密码
				// 	captcha: this.form.captcha,     // 验证码
				// 	checkKey: this.currDateTime     // 验证码key
				// };
				const params = {
					username: this.form.username,   // 账号
					password: this.form.password,   // 密码
					// code: this.form.captcha,        // 验证码
				};
				try {
					this.btnLoading = true;
					const res = await this.Login(deleteNullAttr(params));
					logger.log('登录成功 res: ', res);
					if (!res) {
						this.btnLoading = false;
						// this.getCaptcha();
						this.form.captcha = '';
						return;
					}
					const res0 = await this.initPermissionList();
					if (!res0) {
						this.btnLoading = false;
						this.form.captcha = '';
					}
					return this.$router.push('/');
				} catch (e) {
					this.btnLoading = false;
					// this.getCaptcha();
					this.form.captcha = '';
				}
			});
		},

		/**
		 * 忘记密码
		 */
		forgetPassword () {
			this.$message.info('请联系管理员重置密码！');
			// this.$router.push('/user/forgetPwd');
		},
		handlePrivacyAgreementModal () {
			this.$refs.privacyAgreementModal.open();
		},
		handleUserAgreementModal () {
			this.$refs.userAgreementModal.open();
		}
	},
};
</script>

<style lang="less" scoped>
.user-layout-login {
	width: 405px;
	position: relative;
	border-radius: 4px;

	&-title {
		text-align: center;
		> h1 {
			color: #6362FF;
		}
	}

	&-tip {
		text-align: center;
	}

	button.login-button {
		padding: 0 15px;
		font-size: 16px;
		height: 40px;
		width: 100%;
		margin-top: 15px;
	}

	.a-center {
		display: flex;
		justify-content: center;
		margin-top: 5px;
	}
}
</style>
