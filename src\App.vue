<template>
	<a-config-provider :locale="locale">
		<a-spin :spinning="spinning">
			<div id="app">
				<router-view />
			</div>
		</a-spin>
	</a-config-provider>
</template>

<script>
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';

moment.locale('en');

export default {
	data () {
		return {
			locale: zhCN,
			loading: false,
		};
	},
	computed: {
		spinning () {
			return this.$store.state?.spin?.spinning; // 需要监听的数据
		},
	},
	watch: {
		'spinning' (spinning) {
			this.loading = spinning;
		},
	},
};
</script>

<style lang="less">
#app {
	height: 100vh;
}
</style>
