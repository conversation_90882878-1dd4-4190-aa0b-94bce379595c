#!/usr/bin/python3
# -*- coding: UTF-8 -*-
###################################
# 增量版本号脚本
# 命令: increment_version([-l], [-t], <version>, [<position>], [<leftmost>])
# Created by cjking on 2020/07/06.
###################################
import sys
import utils
import logger

logger = logger.Logger()


# EXAMPLE   ------------->   # RESULT
# ret = increment_version()  # None，show help
# ret = increment_version('00.001')  # 00.002
# ret = increment_version('1')  # 2
# ret = increment_version(1)                      # 2
# ret = increment_version('1', 2)                 # 1.1
# ret = increment_version('1', 3)                 # 1.0.1
# ret = increment_version('1.0.0')                # 1.0.1
# ret = increment_version('1.2.3.9')              # 1.2.3.10
# ret = increment_version('00.00.001')            # 00.00.002
# ret = increment_version('-l', '00.001')         # 0.2
# ret = increment_version('1.1.1.1', '2')         # 1.2.0.0
# ret = increment_version('1.1.1.1', 2)           # 1.2.0.0
# ret = increment_version('-t', '1.1.1', 2)       # 1.2
# ret = increment_version('v1.1.3')               # v1.1.4
# ret = increment_version('1.2.9', 2, 4)          # 1.3.0.0
# ret = increment_version('-t', '1.2.9', 2, 4)    # 1.3
# ret = increment_version('1.2.9', 'last', 4)     # 1.2.9.1
# ret = increment_version('1.2.9.1', 'last', 4)   # 1.2.9.2
# print('ret: ', ret)
def increment_version(*kwargs):
	# 获取被调用函数名称
	funcName = sys._getframe().f_code.co_name

	usage = ('USAGE: %s([-l], [-t], <version>, [<position>], [<leftmost>]) \n\
			-l : remove leading zeros \n\
			-t : drop trailing zeros \n\
	<version> : The version string. \n\
	<position> : Optional. The position (starting with one) of the number \n\
				within <version> to increment.  If the position does not \n\
				exist, it will be created.  Defaults to last position. \n\
	<leftmost> : The leftmost position that can be incremented.  If does not \n\
				exist, position will be created.  This right-padding will \n\
				occur even to right of <position>, unless passed the -t flag.\n' % funcName)

	if utils.isEmpty(kwargs) or utils.isEmpty(kwargs[0]):
		logger.choose(usage)
		return

	# Get flags.
	flag_remove_leading_zeros = 0
	flag_drop_trailing_zeros = 0

	# str[0:1]  # 截取第一位的字符
	while utils.notEmpty(kwargs[0]) and kwargs[0][0:1] == "-":
		if kwargs[0] == "--":
			kwargs = utils.removeTupleItem(kwargs, kwargs[0])
			break
		elif kwargs[0] == "-l":
			flag_remove_leading_zeros = 1
		elif kwargs[0] == "-t":
			flag_drop_trailing_zeros = 1
		else:
			logger.error("Invalid flag: %s\n" % (kwargs[0]), show_prefix=False)
			logger.choose(usage)
			# raise Exception("\nInvalid flag: %s\n" % (kwargs[0]))  # 抛出异常
			exit(1)
		kwargs = utils.removeTupleItem(kwargs, kwargs[0])

	# Get arguments.
	if len(kwargs) < 1:
		logger.choose(usage)
		exit(1)

	v = kwargs[0]

	targetPos = kwargs[-1]
	minPos = kwargs[-1]

	# target position
	# exp1 if condition else exp2
	# targetPos = kwargs[1] if len(kwargs) > 1 else "last"
	if len(kwargs) < 2 or utils.isEmpty(kwargs[1]):
		targetPos = "last"
	else:
		targetPos = 'last' if kwargs[1] == 'last' else utils.toNumber(kwargs[1])

	if len(kwargs) < 3 or utils.isEmpty(kwargs[2]):
		minPos = targetPos
	else:
		minPos = 'last' if kwargs[2] == 'last' else utils.toNumber(kwargs[2])

	# Split version string into array using its periods.
	v = str(v).split('.')

	# Determine target position.
	if targetPos == "last":
		if minPos == "last":
			minPos = 0
		targetPos = len(v) if len(v) > minPos else minPos

	if targetPos <= 0:
		logger.error("Invalid position: %s\n" % targetPos, show_prefix=False)
		logger.choose(usage)
		exit(1)

	# offset to match array index
	targetPos -= 1

	# Make sure minPosition exists.
	while len(v) < minPos:
		v.append("0")

	# Increment target position.
	placeholder = "%0" + str(len(v[targetPos])) + "d"
	v[targetPos] = placeholder % (int(v[targetPos]) + 1)

	# Remove leading zeros, if -l flag passed.
	if flag_remove_leading_zeros == 1:
		for pos in range(len(v)):
			v[pos] = str(int(v[pos] * 1))

	# If targetPosition was not at end of array, reset following positions to
	#   zero (or remove them if -t flag was passed).
	def updateVal(p, act):
		if p > targetPos:
			if act == 'remove':
				v.pop()  # 删除并返回指定位置元素，如果未指定位置则默认操作列表最后一个元素。
			if act == 'edit':
				v[p] = 0

	if flag_drop_trailing_zeros == 1:
		# 列表解析
		[updateVal(p, 'remove') for p in range(len(v) - 1, 0, -1)]
	else:
		# 列表解析
		[updateVal(p, 'edit') for p in range(len(v) - 1, 0, -1)]

	return ".".join(str(p) for p in v)


# 选择版本号
async def chooseVersion(repository, env):
	tag_list = utils.getTagList(repository, env)
	if utils.isEmpty(tag_list):
		return None
	index = await utils.getSelectValue(tag_list, '请选择tag版本号？')
	tag_version = tag_list[index - 1]
	logger.choose("您选择的tag版本号是: %s" % tag_version, "blue")
	return tag_version
