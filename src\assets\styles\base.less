// 自定义文字图标
@font-face {
	font-family: "iconfont";
	src: url("~@/assets/fonts/iconfont.eot");
	src: url("~@/assets/fonts/iconfont.eot?#iefix") format("embedded-opentype"),
	url("~@/assets/fonts/iconfont.woff2") format("woff2"),
	url("~@/assets/fonts/iconfont.woff") format("woff"),
	url("~@/assets/fonts/iconfont.ttf") format("truetype"),
	url("~@/assets/fonts/iconfont.svg#iconfont") format("svg");
}

.iconfont {
	font-family: "iconfont", serif !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	-moz-osx-font-smoothing: grayscale;
}

/* size */
@header-top-height: 0;
@header-height: 56px;
@footer-height: 450px;
@carousel-height: 560px;

/* text color */
@heading-color: #000;
@text-color: #404040;
@text-color-secondary: #999;
@input-placeholder-color: #B2B2B2;
@dark-theme-text-color: #FFFFFF;

/* color */
@primary-color: #6362FF; // 全局主色
@info-color: #86E2C7;
@error-color: #E5252F;
@warning-color: #DD9524;
@success-color: #17AE45;
@link-color: @primary-color;
@home-bg-color: #FFFFFF;

//@link-color: #1890ff; // 链接色
//@success-color: #52c41a; // 成功色
//@warning-color: #faad14; // 警告色
//@error-color: #f5222d; // 错误色
//@font-size-base: 14px; // 主字号
//@heading-color: rgba(0, 0, 0, 0.85); // 标题色
//@text-color: rgba(0, 0, 0, 0.65); // 主文本色
//@text-color-secondary: rgba(0, 0, 0, 0.45); // 次文本色
//@disabled-color: rgba(0, 0, 0, 0.25); // 失效色
//@border-radius-base: 4px; // 组件/浮层圆角
//@border-color-base: #d9d9d9; // 边框色
//@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15); // 浮层阴影

/* font-size */
@font-size-base: 14px;
@font-size-sm: @font-size-base - 2px;
@font-size-lg: @font-size-base + 2px;
@font-size-xl: @font-size-base + 4px;
@font-size-xxl: @font-size-base + 6px;
@font-size-xxxl: @font-size-base + 10px;
@font-size-super: @font-size-base + 18px;

/* 默认字体的1.5倍 */
@line-height-base: 1.5;

/* 描边 */
@border-color-base: #E0E0E0;

/* 背景色 */
@background-color-light: #F5F6F7;
@btn-disable-bg: #34AFF5;

/* k-form-design */
@layout-color: rgb(152, 103, 247);

@primary-background-color: fade(@primary-color, 6%);
@primary-hover-bg-color: fade(@primary-color, 20%);
@layout-background-color: fade(@layout-color, 12%);
@layout-hover-bg-color: fade(@layout-color, 24%);

@title-text-color: #FFF;
@border-color: #CCC;

@left-right-width: 270px;
@operating-area-height: 45px;

// 主按钮
.ant {
	&-btn {
		&-primary {
			background-color: @primary-color;

			&:hover {
				background-color: @primary-color;
				opacity: 0.6;
			}
		}

		&-success {
			background-color: @success-color;

			&:hover {
				background-color: @success-color;
				opacity: 0.6;
			}
		}
	}
}

// 全局标题/文字
h1,
.h1 {
	font-size: @font-size-xxxl;
}

h2,
.h2 {
	font-size: @font-size-xxl;
}

h3,
.h3 {
	font-size: @font-size-xl;
}

h4,
.h4 {
	font-size: @font-size-lg;
}

h5,
.h5 {
	font-size: @font-size-base;
	font-weight: normal;
}

h6,
.h6 {
	font-size: @font-size-sm;
	font-weight: normal;
}

.primary-color {
	color: @primary-color;
}

// 状态颜色
.status {
    &-default:before {
		font-family: "iconfont", serif;
		content: "\e60d";
		color: @info-color;
		padding-right: 4px;
	}
    
	&-success:before {
		font-family: "iconfont", serif;
		content: "\e60d";
		color: @success-color;
		padding-right: 4px;
	}

	&-error:before {
		font-family: "iconfont", serif;
		content: "\e60d";
		color: @error-color;
		padding-right: 4px;
	}

	&-warning:before {
		font-family: "iconfont", serif;
		content: "\e60d";
		color: @warning-color;
		padding-right: 4px;
	}

	&-primary:before {
		font-family: "iconfont", serif;
		content: "\e60d";
		color: @primary-color;
		padding-right: 4px;
	}

}

// 通用表格
.ant-table table {
	border-collapse: separate;
	border-spacing: 0 3px;
	.ant-table-thead > .ant-table-row {
		& > th {
			background-color: #F4F5FC;
		}
	}
	.ant-table-tbody > .ant-table-row {
		& > td {
			background-color: #FFF;
			a {
				margin-right: 40px;
			}
		}
		.ant-table-row-selected > td {
			background-color: rgba(245, 252, 255, 1);
		}
	}
}

// 解决混合排序表头文字不齐的问题
.ant-table-header-column .ant-table-column-title {
	display: table-cell;
	vertical-align: middle;
}

// 自定义轮播图<>翻页和---——翻页
.ant-carousel {
	.slick-slide {
		text-align: center;
		background: white;
		.carousel-bg {
			max-width: 100%;
			height: @carousel-height;
			overflow: hidden;
			object-fit: cover;
		}
	}

	.slick-slide h3 {
		color: #FFF;
	}

	.slick-dots li button {
		background-color: gray;
		opacity: 0.7;
	}
	.slick-dots .slick-active li button {
		opacity: 0.7;
		background-color: #FFF;
	}
}

.slide-left-enter,
.slide-right-leave-active {
	opacity: 0;
	transform: translate(30px, 0);
}

.slide-left-leave-active,
.slide-right-enter {
	opacity: 0;
	transform: translate(-30px, 0);
}

// 未加载完vue不显示
[v-cloak] {
	display: none;
}

.markdown-body {
	padding: 30px 60px;
	overflow: hidden;
	p {
		img {
			max-width: 100%;
		}
	}
	.table {
		tr {
			td,
			th {
				border: gray solid 1px;
				padding: 10px;
			}
			th {
				background-color: #F4F5FC;
			}
		}
	}
}

.disable-selection() {
	/* 阻止选中 */
	-ms-user-select: none !important;
	-webkit-user-select: none !important;
	-moz-user-select: none !important;
	user-select: none !important;
}

.span-input-fun() {
	margin: 5px 0 0;
	list-style: none;
	position: relative;
	display: inline-block;
	width: 100%;
	height: 32px;
	line-height: 1.5;
	padding: 4px 11px;
	color: #333333;
	font-size: 14px;
	background-color: #FFF;
	background-image: none;
	border: 1px solid #D9D9D9;
	border-radius: 4px;
	cursor: pointer;

	&.disabled {
		color: rgba(0, 0, 0, 0.25);
		background-color: #F5F5F5;
		cursor: not-allowed;
		opacity: 1;
	}
	&.disabled-cursor {
		cursor: not-allowed;
		opacity: 1;
	}
	&.has-error {
		border: 1px solid #F5222D;
	}

	.form-color-picker {
		display: inline-block;
		width: 20px;
		height: 12px;
		margin: 4px 0 0 0;

		&.border {
			border: 1px solid rgb(0, 0, 0);
		}
	}
	.color-value {
		display: inline-block;
		position: absolute;
		top: 4px;
		left: 35px;
		cursor: pointer;

		&.gray {
			color: rgba(0, 0, 0, 0.25);
		}

		&.disabled {
			cursor: not-allowed;
		}
	}
}

.override-modal {
	.ant-form-item-label {
		line-height: 36px;
	}

	.ant-modal-wrap {
		width: 100%;
		height: 100%;
		overflow: hidden;
		margin-left: 1px;
		pointer-events: none; // none:穿透该层 auto:恢复点击处理
		z-index: 1000;
	}

	.ant-modal-close-x {
		width: 46px;
		height: 46px;
		line-height: 46px;
	}

	.ant-modal {
		margin-left: 321px;
		margin-top: 3px;
		height: 100%;

		.ant-modal-header {
			padding: 11px 24px;
			font-size: 16px;
			font-weight: bold;
		}

		.ant-form-item {
			margin-bottom: 0;
		}

		.mb5 {
			margin-bottom: 5px;
		}

		.mb10 {
			margin-bottom: 10px;
		}
	}

	.has-error {
		.ant-form-explain {
			font-size: 12px;
			min-height: 18px;
		}
	}

	.title {
		height: 24px;
		font-size: 16px;
		font-weight: bold;
		color: #111111;
		display: flex;
		justify-content: flex-start;
		align-items: center;

		img {
			width: 24px;
		}
	}

	.impose {
		height: 20px;
		font-size: 16px;
		font-weight: 500;
		color: #111111;
		line-height: 20px;

		&.mb10 {
			display: block;
			margin-bottom: 10px;
		}
	}

	.list-container {
		border: 1px solid #E8E8E8;
		border-radius: 4px;
		overflow: auto;
		padding: 8px 14px;
		max-height: 150px;

		.ant-list-empty-text {
			padding: 6px;
			color: rgba(0, 0, 0, 0.25);
			font-size: 14px;
			text-align: center;
		}

		.ant-list-item {
			padding: 6px 0;
		}
	}
}
