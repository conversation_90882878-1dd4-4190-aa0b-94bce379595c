<template>
	<div class="grid">
		<!--<div class="grid-cell fixed-width"></div>-->
		<div class="grid-cell controller">
			<ViewController
				:action="action"
				:selectDraw="selectDraw"
				:selectMode="selectMode"
				:renderMode="renderMode"
				:planeMode="planeMode"
				:viewMode="viewMode"
				:disabledSelectFace="disabledSelectFace"
				:disabledSelectSolid="disabledSelectSolid"
				:disabled="disabled"
				:config="config"
				@onDraw="onDraw"
				@onAction="onAction"
				@resetView="resetView"
				@clearDrawn="clearDrawn"
				@resetCamera="resetCamera"
				@changeCameraMode="changeCameraMode"
				@changeRenderMode="changeRenderMode"
				@selectModeHandle="selectModeHandle"
				@updateView="updateView"
				@changeClipping="changeClipping"
				@updateRotate="updateRotate"
				@mouseLeftSetting="mouseLeftSetting"
				@mouseRightSetting="mouseRightSetting"
				@onPlaneModeSelect="onPlaneModeSelect"
				@otherSelect="otherSelect"
			/>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 导航控制器
 * Update by cjking on 2021/10/21.
 * Copyright 2021, Inc.
 * =================================== */
import { SelectModeEnum, RenderModeEnum, PlaneModeEnum } from '@/constants';
import ViewController from '@/components/core/ViewController';

export default {
	name: 'Navigation',
	components: {
		ViewController,
	},
	props: {
		selectMode: { // 点选模式
			type: String,
			required: false,
			default: SelectModeEnum.solid,
		},
		action: { // 动作: 框选等
			type: String,
			required: false,
			default: '',
		},
		selectDraw: { // 绘图模式
			type: String,
			required: false,
			default: '',
		},
		renderMode: { // 渲染模式
			type: String,
			required: false,
			default: RenderModeEnum.surfaceAndWireframe,
		},
		planeMode: { // 参考平面模式
			type: String,
			required: false,
			default: '',
		},
		viewMode: {
			type: String,
			required: false,
			default: '',
		},
		disabledSelectFace: { // 禁用选择面
			type: Boolean,
			required: false,
			default: false,
		},
		disabledSelectSolid: { // 禁用选择体
			type: Boolean,
			required: false,
			default: false,
		},
		disabled: { // 整体禁用
			type: Boolean,
			required: false,
			default: false,
		},
		config: {
			type: String,
			required: true,
		},
	},
	methods: {
		/**
		 * 更新视图
		 */
		updateView (direction) {
			this.$emit('updateView', direction);
		},

		/**
		 * 选择模型(点、线、面、体)
		 */
		selectModeHandle (choose) {
			this.$emit('selectModeHandle', choose);
		},

		/**
		 * 重置相机
		 */
		resetCamera () {
			this.$emit('resetCamera');
		},

		/**
		 * 重置视图
		 */
		resetView () {
			this.$emit('resetView');
		},

		/**
		 * 绘图
		 * type: 画点、画线、画圆
		 */
		onDraw (type) {
			this.$emit('onDraw', type);
		},

		/**
		 * 控制操作
		 * type: 添加控制点、复制、移动
		 */
		onAction (type) {
			this.$emit('onAction', type);
		},

		/**
		 * 清除绘制
		 */
		clearDrawn () {
			this.$emit('clearDrawn');
		},

		/**
		 * 参考平面
		 */
		onPlaneModeSelect (action) {
			this.$emit('onPlaneModeSelect', action);
		},

		/**
		 * 更改视图模式
		 */
		changeCameraMode (mode) {
			this.$emit('changeCameraMode', mode);
		},

		/**
		 * 更改渲染模式
		 * @param mode
		 */
		changeRenderMode (mode) {
			this.$emit('changeRenderMode', mode);
		},

		/**
		 * 更改渲染模式
		 * @param value
		 */
		changeClipping (value) {
			this.$emit('changeClipping', value);
		},

		/**
		 * 旋转视图
		 * @param obj
		 */
		updateRotate (obj) {
			this.$emit('updateRotate', obj);
		},

		/**
		 * 鼠标左键设置
		 * @param value
		 */
		mouseLeftSetting (value) {
			this.$emit('mouseLeftSetting', value);
		},

		/**
		 * 鼠标右键设置
		 * @param value
		 */
		mouseRightSetting (value) {
			this.$emit('mouseRightSetting', value);
		},

		/**
		 * 其他选择更改
		 */
		otherSelect (value) {
			this.$emit('otherSelect', value);
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.grid {
	margin-top: 1px;
	background: #FFFFFF;

	&.offset {
		margin-left: 20px;
	}

	.grid-cell {
		flex: 1;
		margin-top: -1px;
		text-align: center;

		&.fixed-width {
			width: 500px;
			position: absolute;
			top: 51px;
			left: 0;
		}

		&.controller {
			display: flex;
			width: 100%;
		}

		&.disabled {
			img {
				cursor: not-allowed;
			}

			.menu {
				cursor: not-allowed;

				.text {
					color: @disabled-text;
				}
			}
		}

		.menu {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 5px;
			cursor: pointer;

			img {
				width: 24px;
				height: 24px;
			}

			.text {
				height: 12px;
				line-height: 12px;
				font-size: 12px;
				font-weight: 500;
				margin-top: 3px;
			}

			&:hover {
				.text {
					color: @primary;
				}
			}
		}
	}
}

.ant-divider-vertical {
	margin: 0;
	top: 12px;
	height: 24px;
	left: 0;
}
</style>
