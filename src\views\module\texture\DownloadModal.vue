<template>
	<a-modal title="下载结果" :visible="visible" :mask-closable="false" @cancel="close" :footer="false">
		<HeaderTip :moduleObject="moduleObject" :vStyle="{ marginTop: '-24px', marginBottom: '24px' }" />
		<div class="box">
			<div class="item">
				<a-checkbox :checked="checkAll" :disabled="!checkAllDisabled" @change="onCheckAllChange">
					全选
				</a-checkbox>
				<a-button type="primary" icon="download" class="btn" :disabled="!moduleObject.canDownload" @click="handleSubmit">确认下载</a-button>
			</div>
			<template v-for="data in dataSource">
				<div :key="data.name" class="item rewrite">
					<a-checkbox :checked="data.checked" :disabled="data.status !== TaskStatusEnum.success" @change="e => onChange(e, data)">
						{{ data.label }}
						<a-tooltip title="重新生成" v-if="data.status === TaskStatusEnum.error">
							<a-icon type="redo" style="color: #6362FF; margin-left: 5px;" @click="e => regenerate(e, data)" />
						</a-tooltip>
					</a-checkbox>
					<span v-if="!data.status" class="sc-status-default"><a-icon type="question-circle" /> 未生成</span>
					<span v-if="data.status === TaskStatusEnum.waiting" class="sc-status-primary"><a-icon type="loading" /> 生成中</span>
					<span v-if="data.status === TaskStatusEnum.success" class="sc-status-success"><a-icon type="check-circle" /> 已生成</span>
					<span v-if="data.status === TaskStatusEnum.error" class="sc-status-error"><a-icon type="close-circle" /> 生成失败</span>
				</div>
			</template>
		</div>
	</a-modal>
</template>

<script>
/* ===================================
* 纹理结果下载弹窗
* Created by cjking on 2022/08/04.
* Copyright 2022, Inc.
* =================================== */
import { mapActions } from 'vuex';
import { mixinModule } from '@/mixins';
import { TaskStatusEnum } from '@/constants';
import { downloadWlFile, downloadZip } from '@/api';
import { arrayBufferToJson, formatDate, saveArrayBuffer } from '@/utils/utils';
import HeaderTip from '@/components/common/HeaderTip';

export default {
	name: 'DownloadModal',
	components: {
		HeaderTip,
	},
	mixins: [mixinModule],
	props: {
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	data () {
		return {
			TaskStatusEnum,
			visible: false,
			loading: false,
			dataSource: [],
			taskId: '',
			checkAll: false,
			indeterminate: true,
			checkedList: [],
		};
	},
	mounted () {
		this.currentModule = '2'; // 纹理
		this.taskId = this.$route.params.taskId || '';
	},
	computed: {
		checkAllDisabled () {
			return this.dataSource.some(data => data.status === TaskStatusEnum.success);
		},
	},
	watch: {
		dataList: {
			deep: true,
			immediate: true,
			handler (list) {
				const dataList = list.filter(data => ['result_wl_line_3dm', 'result_wl_line_step'].includes(data.name));
				this.dataSource = dataList;
			},
		},
	},
	methods: {
		...mapActions(['initPermissionList']),
		/**
		 * 显示弹窗
		 */
		async open () {
			this.visible = true;
			this.loading = false;

			if (this.moduleObject.canDownload) {
				const list = this.dataSource.filter(data => !data.status);
				if (list.length) {
					const typeName = list.map(data => data.name).join(',');
					await this.downloadFileByName(typeName);
				}
			}
		},

		/**
		 * 按名称下载文件
		 */
		async downloadFileByName (typeName) {
			const params = {
				id: this.taskId,
				typeName: typeName,
			};
			const res = await downloadWlFile(params);
			if (res?.success) {
				// this.$message.success('下载请求成功');

				this.dataSource.forEach(data => {
					data.status = TaskStatusEnum.waiting;
				});
				this.$forceUpdate();
			}
		},

		/**
		 * 关闭弹窗
		 */
		close () {
			this.visible = false;
		},

		/**
		 * 确定
		 */
		async handleSubmit () {

			// 过滤空值
			this.checkedList = this.checkedList.filter(key => key);

			if (!this.checkedList.length) {
				this.$message.warn('请选择文件');
				return;
			}

			const fileNames = [];
			this.checkedList.forEach(key => {
				fileNames.push(key);
			});
			const params = {
				id: this.taskId,
				typeName: fileNames.join(','),
			};
			const res = await downloadZip(params, {
				interceptRes: false,
				responseType: 'arraybuffer',
			});
			if (res?.data && res.data.byteLength > 200) { // 错误消息时，返回数据长度约123
				saveArrayBuffer(res.data, this.taskId + '_' + formatDate(new Date(), 'YYYYMMDDHHmmss') + '.zip');
				this.initPermissionList();
			} else {
				const data = arrayBufferToJson(res.data);
				if (data) {
					this.$notification.error({ message: '温馨提示', description: data.message });
				}
			}
		},

		/**
		 * 全选操作
		 */
		onCheckAllChange (e) {
			this.dataSource.forEach(data => data.checked = e.target.checked);
			this.checkedList = e.target.checked ? this.dataSource.filter(data => data.checked).map(data => this.nameHandler(data.name)) : [];
			this.checkAll = e.target.checked;
		},

		/**
		 * 单选操作
		 */
		onChange (e, data) {
			this.dataSource.forEach(d => {
				if (d.name === data.name) {
					data.checked = e.target.checked;
				}
			});
			this.checkedList = this.dataSource.filter(data => data.checked).map(data => this.nameHandler(data.name));
			this.checkAll = this.checkedList.length === this.dataSource.length;
			this.$forceUpdate();
		},

		/**
		 * 重新生成
		 */
		async regenerate (e) {
			e?.preventDefault?.();
			e?.stopPropagation?.();
			const list = this.dataSource.filter(data => !data.status || data.status === TaskStatusEnum.error);
			if (list.length) {
				const typeName = list.map(data => this.nameHandler(data.name)).join(',');
				await this.downloadFileByName(typeName);
			}
		},

		nameHandler (name) {
			if (name && name.endsWith('_3dm')) {
				return name.replace('_3dm', '') + '.3dm';
			}
			if (name && name.endsWith('_step')) {
				return name.replace('_step', '') + '.stp';
			}
			return '';
		},
	},
};
</script>

<style lang="less" scoped>
.box {
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px;

		&.rewrite {
			padding: 13px 10px;
			background: #F8F8F8;
			border-radius: 2px;
			margin: 4px 0;

			/deep/ span {
				font-size: 14px;
				font-weight: 500;
			}
		}

		.btn {
			width: 110px;
			height: 32px;
			margin-right: -10px;
		}

		.sc-status {
			&-default {
				color: #404040;

				i {
					color: #DD9524;
				}
			}

			&-primary {
				color: #6362FF;

				i {
					color: #6362FF;
				}
			}

			&-success {
				color: #17AE45;

				i {
					color: #17AE45;
				}
			}

			&-error {
				color: #E5252F;

				i {
					color: #E5252F;
				}
			}
		}
	}

	/deep/ .ant-checkbox-wrapper-disabled {
		:last-child {
			color: rgba(0, 0, 0, 0.7);
			cursor: not-allowed;
		}

		span {
			font-size: 14px;
			font-weight: 500;
		}
	}
}
</style>
