<template>
	<div class="header-tip" v-if="tip" :style="vStyle">
		<a-icon type="info-circle" />&nbsp;{{ tip }}
	</div>
</template>

<script>
export default {
	props: {
		moduleObject: {
			type: Object,
			required: true,
			default: () => {},
		},
		vStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
	},
	computed: {
		tip () {
			if (!this.moduleObject.hasPackage) {
				return '您的企业暂未开通任何套餐，请联系业务人员开通';
			} else if (this.moduleObject.expired && this.moduleObject.zeroCount) {
				return '您的套餐已超过有效期，下载次数已用完，请联系业务人员';
			} else if (this.moduleObject.expired) {
				return '您的套餐已超过有效期，请联系业务人员';
			} else if (this.moduleObject.zeroCount) {
				return '您的套餐下载次数已用完，请联系业务人员';
			}
			return '';
		},
	},
};
</script>
<style lang="less" scoped>
.header-tip {
	width: 100%;
	height: 40px;
	background: #6362FF;
	color: white;
	font-weight: 400;
	font-size: 14px;
	text-align: center;
	line-height: 40px;
    pointer-events: none;
}
</style>
