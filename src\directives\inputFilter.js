/* ===================================
 * 指令：input限制只能输入xxx
 * Created by cjking on 2021/10/29.
 * Copyright 2021, Inc.
 * =================================== */
const addListener = function (el, type, fn) {
	el.addEventListener(type, fn, false);
};

// 去掉空格
const spaceFilter = function (el) {
	addListener(el, 'input', () => {
		el.value = el.value.replace(/\s+/, '');
	});
};

// 限制只能输入整数和小数（适用于价格类、最多两位小数）
const priceFilter = function (el) {
	addListener(el, 'input', () => {
		el.value = el.value.match(/\d+\.?\d{0,2}/) || null;
		if (isNaN(el.value)) {
			el.value = '';
		}
	});
};

// 限制只能输入整数和小数（最多20位小数）
const floatFilter = function (el) {
	addListener(el, 'input', () => {
		el.value = el.value.match(/\d+\.?\d{0,20}/) || null;
		if (isNaN(el.value)) {
			el.value = '';
		}
	});
};

// 限制只能输入数字(整数)
const integerFilter = function (el) {
	addListener(el, 'input', () => {
		el.value = (el.value.match(/^(\d)*/g)[0]) || null;
		if (isNaN(el.value)) {
			el.value = '';
		}
	});
};

// 限制只能输入字母数字（适用于运单号）
const integerLetterFilter = function (el) {
	addListener(el, 'input', () => {
		el.value = el.value.replace(/[\W]/g, '');
		el.dispatchEvent(new Event('input'));
	});
};

export const InputFilterDirective = Vue => Vue.directive('input-filter', {
	bind (el, binding) {
		if (el.tagName.toLowerCase() !== 'input') {
			el = el.getElementsByTagName('input')[0];
		}
		spaceFilter(el);
		switch (binding.arg) {
			case 'price':
				priceFilter(el);
				break;
			case 'integer':
				integerFilter(el);
				break;
			case 'integerLetter':
				integerLetterFilter(el);
				break;
			case 'float':
				floatFilter(el);
				break;
			default:
				// eslint-disable-next-line no-console
				console.warn('未知指令类型', binding.arg);
				break;
		}
	},
});
