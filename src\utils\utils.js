import Vue from 'vue';
import store from '@/store';
import Config from '@/config/Config';
import { loadFile } from '@/api';
import { logger } from '@/utils/logger';
import { message } from 'ant-design-vue';
import { animate } from '@/utils/animate';
import { ExponentReg, SCIENTIFIC_COUNTING_METHOD } from '@/utils/validate';

/**
 * 空函数
 */
export const noop = () => {};

/**
 * 判断对象是否包含特定的自身（非继承）属性
 * @param item
 * @param key
 * @return {boolean}
 */
export const hasOwnProperty = (item, key) => Object.prototype.hasOwnProperty.call(item, key);

/**
 * 睡眠函数
 * @param ms 等待时间，毫秒
 * @param {Function=} callback
 * @return {Number|Promise}
 */
export const sleep = (ms = 0, callback = undefined) => {
	if (isFunction(callback)) {
		return setTimeout(callback, ms);
	}
	return new Promise(resolve => {
		setTimeout(resolve, ms);
	});
};

/**
 * 删除前导斜杠
 * @param str
 * @returns {string|*}
 */
export function removeLeadingSlash (str) {
	return str[0] === '/' ? str.substr(1) : str;
}

/**
 * 获取url参数
 * @param key 可选
 * @param url 可选
 */
export const getUrlParams = (key, url) => {
	url = url || window.location.href;
	const params = {};
	url.replace(/([^?&]+)=([^?&]+)/g, (s, v, k) => {
		params[decodeURIComponent(v)] = decodeURIComponent(k);
		return k + '=' + v;
	});
	return (key ? params[key] : params) || '';
};

/**
 * 判断是否微信平台
 * @returns {boolean}
 */
export const isWeiXin = () => /micromessenger/i.test(navigator.userAgent);

/**
 * 判断是否对象
 * @returns {boolean}
 */
export const isObject = (args) => {
	return Object.prototype.toString.call(args) === '[object Object]';
};

/**
 * 判断是否函数
 * @returns {boolean}
 */
export function isFunction (value) {
	return Object.prototype.toString.call(value) === '[object Function]';
}

/**
 * 判断是否字符串
 * @returns {boolean}
 */
export const isString = (value) => typeof value === 'string';

/**
 * 判断是否数字
 * @returns {boolean}
 */
export const isNumber = (value) => typeof value === 'number' && !isNaN(value);

/**
 * 判断是否数组
 * @returns {boolean}
 */
export const isArray = Array.isArray || function (value) {
	return Object.prototype.toString.call(value) === '[object Array]';
};

/**
 * 空对象验证
 * @param obj
 * @param {Array<*>} whiteList  不包含属性(白名单)
 * @returns {boolean}
 */
export const isEmptyObject = (obj, whiteList = []) => {
	if (!obj) return true;
	if (typeof obj !== 'object') return true;
	for (const key in obj) {
		if (Object.prototype.hasOwnProperty.call(obj, key) && !whiteList.includes(key)) {
			return false;
		}
	}
	return true;
};

/**
 * 空对象数组验证
 * @param list
 * @param {Array<*>} whiteList  不包含属性(白名单)
 * @returns {boolean}
 */
export const isEmptyObjectArray = (list, whiteList = []) => {
	if (!list) return true;
	if (typeof list !== 'object') return true;
	if (!list.length) return true;
	const temList = [];
	const handleList = (innerList) => {
		for (const item of innerList) {
			if (Array.isArray(item)) {
				handleList(item);
			} else {
				if (!isEmptyObject(deleteNullAttr(item), whiteList)) {
					temList.push(item);
				}
			}
		}
	};
	if (Array.isArray(list)) {
		handleList(list);
	}
	return !temList.length;
};

/**
 * 完整对象验证（是否属性都有值）
 * @param {Object} obj
 * @returns {boolean}
 */
export const isFullObject = (obj = {}) => {
	for (const key in obj) {
		if (Object.prototype.hasOwnProperty.call(obj, key) && isEmpty(obj[key])) {
			return false;
		}
	}
	return true;
};

/**
 * 字符串转换成对象
 * @returns {Object}
 */
export function makeMap (str) {
	const arr = str.split(',');
	const obj = {};
	arr.forEach(key => obj[trim(key)] = true);
	return function () {
		return obj[arguments[0]];
	};
}

/**
 * 去除左右空格
 * @param {String} str
 * @return {String}
 */
export const trim = (str) => {
	if (!str || typeof str !== 'string') return str;
	return str.replace(/^\s+|\s+$/g, '');
};

/**
 * 去除所有空格
 * @param {String} str
 * @return {String}
 */
export const trimAll = (str) => {
	return str.replace(/\s+/g, '');
};

/**
 * 空判断
 * @param value
 * @return {boolean}
 */
export const isEmpty = (value) => value === null || value === undefined || value === '';

/**
 * 非空判断
 * @param value
 * @return {boolean}
 */
export const isNoEmpty = (value) => !isEmpty(value);

/**
 * 判断是否字符串数字(纯数字)
 */
export const isNumberStr = (value) => /^[0-9]*$/.test(trim(value));

/**
 * 判断是否有重复
 */
export const checkIsMultiple = (str, target, matchCase = true) => {
	const list = String(str).split('');
	return list.filter(s => s === target || (!matchCase ? s === target.toUpperCase() : s === target)).length > 1;
};

/**
 * 判断是否为数字类型
 */
export const checkNumber = (args) => {
	args = String(args);
	if (/^([-+])?\d+(\.\d+)?$/.test(args)) {
		return true;
	}
	if (SCIENTIFIC_COUNTING_METHOD.test(args)) {
		return true;
	}
	if (args.startsWith('e') || args.startsWith('E') || args.endsWith('e') || args.endsWith('E') ||
		args.includes('.e') || args.includes('.E') || args.includes('e.') || args.includes('E.')) {
		return false;
	}
	if (/[a-zA-Z]/.test(args)) {
		const str = args.replace(/[e|E]/g, ''); // 去除科学符号位
		if (/[a-zA-Z]/.test(str)) { // 若还有字母，表示非数字结构
			return false;
		}
	}
	const tmpStr = args.slice(1, args.length); // 去除首位符号位
	return !(tmpStr.includes('-') || tmpStr.includes('+') || checkIsMultiple(tmpStr, 'e', false));
};

/**
 * 转换为number
 */
export const toNumber = (num) => {
	if (typeof num === 'number') return num;
	if (num && /^[0-9].*$/.test(num)) {
		return Number(num);
	}
	return null;
};

/**
 * 首字母大写
 * @param {String} str
 * @return {String}
 */
export const firstUpperCase = (str) => str.replace(/^\S/, s => s.toUpperCase());

/**
 * 清除用户登录数据
 */
export const clearUserData = () => {
	store.dispatch('ClearLoginData').catch(err => err);
};

/**
 * dataURL转换为Blob对象
 * @param dataUrl
 */
export const dataURLtoBlob = (dataUrl) => {
	const arr = dataUrl.split(',');
	const mime = arr[0].match(/:(.*?);/)[1];
	const bStr = atob(arr[1]);
	let n = bStr.length;
	const u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bStr.charCodeAt(n);
	}
	return new Blob([u8arr], { type: mime });
};

/**
 * Blob转换为File对象
 * @param blob
 * @param fileName
 * @returns {File}
 */
export const blobToFile = (blob, fileName) => {
	blob.lastModifiedDate = new Date();
	blob.name = fileName;
	return blob;
};

/**
 * File/Blob对象转换为dataURL
 * @param {File|Blob} file
 * @returns {Promise<string>}
 */
function readBlobAsDataURL (file) {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => resolve(reader.result);
		reader.onerror = error => reject(error);
	});
}

/**
 * 文件转base64
 * @param {File|Blob} file
 * @returns {Promise<string>}
 */
export const getBase64 = (file) => readBlobAsDataURL(file);

/**
 * 文件转text
 * @param {File|Blob} file
 * @returns {Promise<string>}
 */
export function getText (file) {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsText(file, 'utf8');
		reader.onload = () => resolve(reader.result);
		reader.onerror = error => reject(error);
	});
}

/**
 * 文件十六进制转换
 * @param {File|Blob} file
 * @returns {Promise<Array<string>>}
 */
export const getHex = (file) => {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsArrayBuffer(file);
		reader.onload = () => {
			let u = new Uint8Array(reader.result);
			const a = new Array(u.length);
			let i = u.length;
			while (i--) { // map to hex
				a[i] = (u[i] < 16 ? '0' : '') + u[i].toString(16);
			}
			u = null; // free memory
			resolve(a);
		};
		reader.onerror = error => reject(error);
	});
};

/**
 * 判断是否rar压缩文件
 * 判断文件头 rar的文件头前4个字节为52617221
 * @param {File|Blob} file
 * @returns {Promise<boolean>}
 */
export const checkRar = (file) => {
	return new Promise(async resolve => {
		const hexArray = await getHex(file);
		const res = hexArray?.slice(0, 4);
		resolve(res.join('') === '52617221');
	});
};

/**
 * 将base64转换为文件
 * @param base64     base64字符串
 * @param type       mime类型
 */
export const base64ToBlob = (base64, type) => {
	const arr = base64.split(',');
	const mime = arr[0].match(/:(.*?);/)[1] || type;
	// 去掉url的头，并转化为byte
	const bytes = window.atob(arr[1]);
	// 处理异常,将ascii码小于0的转换为大于0
	const ab = new ArrayBuffer(bytes.length);
	// 生成视图（直接针对内存）：8位无符号整数，长度1个字节
	const ia = new Uint8Array(ab);
	for (let i = 0; i < bytes.length; i++) {
		ia[i] = bytes.charCodeAt(i);
	}
	return new Blob([ab], { type: mime });
};

/**
 * 获取自定义id
 * @param count
 * @returns {string}
 */
export const getCustomId = (count = 6) => {
	count = count || 6;
	let id = '';
	for (let i = 0; i < count; i++) {
		id += (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
	}
	return id;
};

/**
 * 防抖函数
 * 返回一个函数，只要它继续被调用，就不会被触发。该函数将在停止调用 "wait" 毫秒后被调用。
 * 如果传递了`immediate`，则在前沿而不是后沿触发该函数。
 * @param func 传入函数
 * @param wait 表示时间窗口的间隔
 * @param immediate 是否立即执行
 * @return {Function}
 */
export const debounce = (func, wait, immediate) => {
	let timeout;
	return function () {
		const self = this;
		// 参数转为数组
		const args = Array.prototype.slice.call(arguments);
		return new Promise(function (resolve) { // 返回一个promise对象
			if (timeout) {
				clearTimeout(timeout);
			}
			if (immediate) {
				const callNow = !timeout;
				timeout = setTimeout(function () {
					timeout = null;
				}, wait);
				if (callNow) {
					resolve(func.apply(self, args)); // 值操作
				}
			} else {
				timeout = setTimeout(function () {
					resolve(func.apply(self, args));
				}, wait);
			}
		});
	};
};

/**
 * 节流函数
 * 创建一个受限制的函数，每“wait”毫秒最多只调用一次“func”。
 * @param func 回调函数
 * @param wait 表示时间窗口的间隔
 * @param immediate 是否立即执行
 * @return {Function}
 */
export function throttle (func, wait, immediate) {
	const callback = func;
	let timerId = null;

	function throttled () {
		const context = this;
		const args = arguments;

		// 直接执行
		if (immediate) {
			callback.apply(context, args);
			immediate = false;
			return;
		}

		// 如果定时器已存在，直接返回。
		if (timerId) {
			return;
		}

		timerId = setTimeout(function () {
			// 注意这里 将 clearTimeout 放到 内部来执行了
			clearTimeout(timerId);
			timerId = null;

			callback.apply(context, args);
		}, wait);
	}

	// 返回一个闭包
	return throttled;
}

/**
 * 对象合并
 * @param {Object} source
 * @param {Object} target
 * @param {Array<string>} [exclude=] exclude
 * @returns {Object}
 */
export const extend = (source, target, exclude = []) => {
	for (const prop in source) {
		if (Object.prototype.hasOwnProperty.call(source, prop) && Object.prototype.hasOwnProperty.call(source, prop) && !exclude.includes(prop)) {
			source[prop] = target[prop];
		}
	}
	return source;
};

/**
 * 将当前时间换成时间格式字符串
 * @param time      时间戳
 * @param format    格式
 * @param options   可选项
 * @param options.firstDayOfMonth   获取当月第一天
 * @param options.firstDayOfSeason  获取当季第一天
 * @param options.firstDayOfYear    获取当年第一天
 * @returns {string}
 *
 * 将 Date 转化为指定格式的String * 月(M)、日(d)、12小时(h)、24小时(H)、分(m)、秒(s)、周(E)、季度(q)
 * 可以用 1-2 个占位符 * 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
 * eg:
 *    (Common.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss.S") ==> 2018-07-02 08:09:04.423
 *    (Common.formatDate(new Date(), "yyyy-MM-dd E HH:mm:ss") ==> 2018-03-10 二 20:09:04
 *    (Common.formatDate(new Date(), "yyyy-MM-dd EE hh:mm:ss") ==> 2018-03-10 周二 08:09:04
 *    (Common.formatDate(new Date(), "yyyy-MM-dd EEE hh:mm:ss") ==> 2018-03-10 星期二 08:09:04
 *    (Common.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss") ==> 2018-03-10 08:09:04
 *    (Common.formatDate(new Date(), "yyyy-MM-dd_hh-mm-ss") ==> 2018-03-10_08-09-04
 *    (Common.formatDate(new Date(), "yyyy-M-d h:m:s.S") ==> 2018-7-2 8:9:4.18
 */
export const formatDate = (time, format = 'yyyy-MM-dd', options) => {
	if (!time) return '';
	time = time || new Date();
	let date;
	if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(time)) {
		date = new Date(time);
	} else if (/^[0-9]*$/.test(time)) {
		date = new Date(Number(time));
	} else if (typeof time === 'string' && time.includes(' ') && time.includes('-')) {
		date = new Date(time.replace(/-/g, '/'));
	} else if (typeof time === 'string' && (time.includes('年') || time.includes('月') || time.includes('日'))) {
		date = new Date(time.replace(/[年月日]/g, '/'));
	} else {
		date = new Date(time);
	}
	if (options) {
		// 获取当月第一天
		if (options.firstDayOfMonth) {
			return date.setDate(1) && formatDate(date, format);
		}
		// 获取当季第一天
		if (options.firstDayOfSeason) {
			const month = date.getMonth();
			if (month < 3) {
				date.setMonth(0);
			} else if (month > 2 && month < 6) {
				date.setMonth(3);
			} else if (month > 5 && month < 9) {
				date.setMonth(6);
			} else if (month > 8 && month < 11) {
				date.setMonth(9);
			}
			date.setDate(1);
			return formatDate(date, format);
		}
		// 获取当年第一天
		if (options.firstDayOfYear) {
			return date.setDate(1) && date.setMonth(0) && formatDate(date, format);
		}
		// 获取本周第一天
		if (options.firstWeekOfDay) {
			const weekday = date.getDay() || 7; // 获取星期几,getDay()返回值是 0（周日） 到 6（周六） 之间的一个整数。0||7为7，即weekday的值为1-7
			date.setDate(date.getDate() - weekday + 1);// 往前算（weekday-1）天，年份、月份会自动变化
			return formatDate(date, format);
		}
	}
	const dateObj = {
		'M+': date.getMonth() + 1, // 月份
		'd+': date.getDate(), 		// 日
		'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
		'H+': date.getHours(), 	// 小时
		'm+': date.getMinutes(), 	// 分
		's+': date.getSeconds(), 	// 秒
		'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
		'S+': date.getMilliseconds(), // 毫秒
	};
	const week = {
		0: '\u65e5',
		1: '\u4e00',
		2: '\u4e8c',
		3: '\u4e09',
		4: '\u56db',
		5: '\u4e94',
		6: '\u516d',
	};
	if (/(y+)/i.test(format)) {
		format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
	}
	if (/(E+)/.test(format)) {
		format = format.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? '\u661f\u671f' : '\u5468') : '') + week[date.getDay() + '']);
	}
	for (const k in dateObj) {
		if (Object.prototype.hasOwnProperty.call(dateObj, k) && new RegExp('(' + k + ')').test(format)) {
			format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? dateObj[k] : ('00' + dateObj[k]).substr(('' + dateObj[k]).length));
		}
	}
	return format;
};

/**
 * 时间转化
 * @param value      时间戳
 * @param full    格式    '00:00:00' ， '0秒'
 * @param isArray  是否字符串
 * @param level    级别  m  ，  s
 * @returns  {string}
 */
export const formatDateDuring = (value, full, isArray, level = 's') => {
	if ((!value || value === 0) && !isArray) {
		return full ? '00:00:00' : '0秒';
	}
	let secondTime = parseInt(value);
	let minuteTime = 0;
	let hourTime = 0;
	let dayTime = 0;
	let text = [];
	if (secondTime > 60) { // 如果秒数大于60，将秒数转换成整数
		// 获取分钟，除以60取整数，得到整数分钟
		minuteTime = parseInt(String(secondTime / 60));
		// 获取秒数，秒数取佘，得到整数秒数
		secondTime = parseInt(String(secondTime % 60));
		// 如果分钟大于60，将分钟转换成小时
		if (minuteTime > 60) {
			// 获取小时，获取分钟除以60，得到整数小时
			hourTime = parseInt(String(minuteTime / 60));
			// 获取小时后取佘的分，获取分钟除以60取佘的分
			minuteTime = parseInt(String(minuteTime % 60));
		}
		// 如果小时大于24，将小时转换成天
		if (isArray !== 'simple' && hourTime > 24) {
			// 获取天，获取天除以24，得到整数天
			dayTime = parseInt(String(hourTime / 24));
			// 获取天后取佘的小时，获取小时除以24取佘的小时
			hourTime = parseInt(String(hourTime % 24));
		}
	}
	if ((secondTime > 0 || full) && level === 's') {
		text.push(secondTime + (isArray ? '' : '秒'));
	}
	if (minuteTime > 0 || full) {
		text.unshift(minuteTime + (secondTime > 0 && level === 'm' ? 1 : 0) + '分');
	}
	if (hourTime > 0 || full) {
		text.unshift(hourTime + '小时');
	}
	if (dayTime > 0 || full) {
		text.unshift(dayTime + '天');
	}
	text = text.join(full ? ':' : '');
	text = full ? text.replace(/[^\d:]/g, '')
		.replace(/(^|:)(\d)(:|$)/g, '$10$2$3')
		.replace(/(^|:)(\d)(:|$)/g, '$10$2$3') : text;

	return isArray ? text.split(/\D+/) : text;
};

/**
 * 对象深拷贝
 * @param {Object} obj
 * @returns {null|*}
 */
export const deepCopy = (obj) => {
	if (!obj) return null;
	let result;
	// 判断是否是简单数据类型
	if (typeof obj === 'object') {
		// 复杂数据类型
		result = obj.constructor === Array ? [] : {};
		for (const key in obj) {
			if (Object.prototype.hasOwnProperty.call(obj, key)) {
				result[key] = typeof obj[key] === 'object' ? deepCopy(obj[key]) : obj[key];
			}
		}
	} else {
		// 简单数据类型,直接赋值
		result = obj;
	}
	return result;
};

/**
 * 优雅的处理 async/await
 * @param asyncFunc
 * @return {Promise<*[]>}
 */
export const errorCaptured = async (asyncFunc) => {
	try {
		const res = asyncFunc instanceof Promise ? await asyncFunc : await asyncFunc();
		return [null, res];
	} catch (e) {
		return [e, null];
	}
};

/**
 * 判断是否为base64字符串
 * @param {String} str
 */
export const isBase64 = (str) => {
	if (!isString(str)) return false;
	return str.startsWith('data:') && str.indexOf('base64') !== -1;
};

/**
 * 中横线(下划线)转换驼峰
 * @param {String} str
 * @returns {*}
 */
export const camelCase = (str) => {
	return str.replace(/([:\-_]+(.))/g, (_, separator, letter, offset) => offset ? letter.toUpperCase() : letter)
		.replace(/^moz([A-Z])/, 'Moz$1');
};

/**
 * 驼峰转换下划线
 * @param {String} str
 * @returns {string}
 */
export const camelcaseToHyphen = (str) => str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();

/**
 * 驼峰转换中横线
 * @param {String} str
 * @returns {string}
 */
export const camelcaseToUnderline = (str) => str.replace(/([a_z])([A_Z])/g, '$1_$2').toLowerCase();

/**
 * 获取浏览器类型
 */
export const getBrowser = () => {
	// 取得浏览器的userAgent字符串
	const userAgent = navigator.userAgent;
	logger.log('userAgent: ', userAgent);

	// 判断是否Opera浏览器
	const isOpera = userAgent.indexOf('Opera') > -1;
	if (isOpera) {
		return 'Opera';
	}

	// 判断是否Firefox浏览器
	if (userAgent.indexOf('Firefox') > -1) {
		return 'Firefox';
	}

	// 判断是否Chrome浏览器
	if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Edge') === -1) {
		return 'Chrome';
	}

	// 判断是否Safari浏览器
	if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Edge') === -1) {
		return 'Safari';
	}

	// 判断是否Edge浏览器
	if (userAgent.indexOf('Mozilla') > -1 && userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Edge') > -1) {
		return 'Edge';
	}

	// 判断是否IE浏览器
	if ((userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1 && !isOpera) || (!!window.ActiveXObject || 'ActiveXObject' in window)) {
		return 'IE';
	}
};

/**
 * 判断系统类型
 */
export const getSystem = () => {
	const agent = navigator.userAgent.toLowerCase();
	const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
	if (agent.indexOf('win32') >= 0 || agent.indexOf('wow32') >= 0) {
		return 'Windows32';
	}
	if (agent.indexOf('win64') >= 0 || agent.indexOf('wow64') >= 0) {
		return 'Windows64';
	}
	if (isMac) {
		return 'Mac';
	}
};

/**
 * 获取字符串的长度ascii长度为1 中文长度为2
 * @param str
 * @returns {number}
 */
export const getStrFullLength = (str = '') =>
	str.split('').reduce((pre, cur) => {
		const charCode = cur.charCodeAt(0);
		if (charCode >= 0 && charCode <= 128) {
			return pre + 1;
		}
		return pre + 2;
	}, 0);

/**
 * 给定一个字符串和一个长度,将此字符串按指定长度截取
 * @param str
 * @param maxLength
 * @returns {string}
 */
export const cutStrByFullLength = (str = '', maxLength) => {
	let showLength = 0;
	return str.split('').reduce((pre, cur) => {
		const charCode = cur.charCodeAt(0);
		if (charCode >= 0 && charCode <= 128) {
			showLength += 1;
		} else {
			showLength += 2;
		}
		if (showLength <= maxLength) {
			return pre + cur;
		}
		return pre;
	}, '');
};

/**
 * 触发 window.resize
 */
export const triggerWindowResizeEvent = () => {
	const event = document.createEvent('HTMLEvents');
	event.initEvent('resize', true, true);
	event.eventType = 'message';
	window.dispatchEvent(event);
};

/**
 * 随机生成数字
 *
 * 示例：生成长度为 12 的随机数：randomNumber(12)
 * 示例：生成 3~23 之间的随机数：randomNumber(3, 23)
 *
 * @param1 最小值 | 长度
 * @param2 最大值
 * @return int 生成后的数字
 */
export function randomNumber (...args) {
	// 生成 最小值 到 最大值 区间的随机数
	const random = (min, max) => {
		return Math.floor(Math.random() * (max - min + 1) + min);
	};
	if (args.length === 1) {
		const [length] = args;
		// 生成指定长度的随机数字，首位一定不是 0
		const nums = [...Array(length).keys()].map((i) => (i > 0 ? random(0, 9) : random(1, 9)));
		return parseInt(nums.join(''));
	} else if (args.length >= 2) {
		const [min, max] = args;
		return random(min, max);
	} else {
		return Number.NaN;
	}
}

/**
 * 随机生成字符串
 * @param {Number} length 字符串的长度
 * @param {String} [chats] chats 可选字符串区间（只会生成传入的字符串中的字符）
 * @return string 生成的字符串
 */
export function randomString (length, chats) {
	if (!length) length = 1;
	if (!chats) chats = '0123456789qwertyuioplkjhgfdsazxcvbnm';
	let str = '';
	for (let i = 0; i < length; i++) {
		const num = randomNumber(0, chats.length - 1);
		str += chats[num];
	}
	return str;
}

/**
 * 随机生成uuid
 * @return string 生成的uuid
 */
export function randomUUID () {
	const chats = '0123456789abcdef';
	return randomString(32, chats);
}

/**
 * 生成 UUID
 * @return {string}
 */
export function generateUUID () {
	const chars = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.split('');
	for (let i = 0, len = chars.length; i < len; i++) {
		switch (chars[i]) {
			case 'x':
				chars[i] = Math.floor(Math.random() * 16).toString(16);
				break;
			case 'y':
				chars[i] = (Math.floor(Math.random() * 4) + 8).toString(16);
				break;
		}
	}
	return chars.join('');
}

/**
 * 如果值不存在就 push 进数组，反之不处理
 * @param array 要操作的数据
 * @param value 要添加的值
 * @param key 可空，如果比较的是对象，可能存在地址不一样但值实际上是一样的情况，可以传此字段判断对象中唯一的字段，例如 id。不传则直接比较实际值
 * @returns {boolean} 成功 push 返回 true，不处理返回 false
 */
export function pushIfNotExist (array, value, key) {
	for (const item of array) {
		if (key && (item[key] === value[key])) {
			return false;
		} else if (item === value) {
			return false;
		}
	}
	array.push(value);
	return true;
}

/**
 * 创建一个从 object 中选中的属性的对象。
 * @param obj
 * @param keys
 */
export const pick = (obj, ...keys) => {
	if (keys.length === 1 && keys[0] instanceof Array) {
		keys = keys[0];
	}
	const result = {};
	for (const key of keys) {
		if (Object.prototype.hasOwnProperty.call(obj, key)) {
			result[key] = deepCopy(obj[key]);
		}
	}
	return result;
};

/**
 * 删除对象中值为空的属性（null、undefined、''）
 * @param {Object} obj          参数对象
 * @param {Array<*>} delValues  需要一并删除的值(value)
 * @param {Array<*>} delKeys    需要一并删除的属性(key)
 * @param {Boolean} removeZero  删除值为0的属性
 * @returns {object}
 */
export const deleteNullAttr = (obj, delKeys = [], delValues = [], removeZero = false) => {
	const newObj = deepCopy(obj);
	for (const key in newObj) {
		if (Object.prototype.hasOwnProperty.call(newObj, key)) {
			if (
				delKeys.includes(key) ||
				delValues.includes(newObj[key]) ||
				isEmpty(newObj[key])
			) {
				delete newObj[key];
			}
			if (removeZero && newObj[key] === 0) {
				delete newObj[key];
			}
		}
	}
	return newObj;
};

/**
 * 判断是否同一天
 * @param {String|number} timeStampA
 * @param {String|number} timeStampB
 * @returns {boolean}
 */
export function isSameDay (timeStampA, timeStampB) {
	const dateA = new Date(timeStampA);
	const dateB = new Date(timeStampB);
	return dateA.setHours(0, 0, 0, 0) === dateB.setHours(0, 0, 0, 0);
}

/**
 * 是否modal右上角的x取消
 * @param element
 * @returns {boolean|boolean}
 */
export const isCloseBtn = (element) => {
	return (element.target.tagName === 'svg' && element.target.dataset === 'close') ||
		element.target.classList.contains('ant-modal-close-x') ||
		((element.path?.length > 3 && element.path[2].classList.contains('ant-modal-close-icon')) || element.path[2].classList.contains('ant-modal-close-x'));
};

/**
 * 对象转换为url参数
 * @param {Object} data
 * @returns {string}
 */
export const toUrlString = (data) => {
	const result = [];
	for (const key in data) {
		const value = data[key];
		if (value.constructor === Array) {
			value.forEach(val => result.push(encodeURIComponent(key) + '=' + encodeURIComponent(val)));
		} else {
			result.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));
		}
	}
	return result.join('&');
};

/**
 * 获取文件目录
 * @param {String} str
 * @returns {string}
 */
export const dirname = (str = '') => {
	if (!str) return '';
	const list = str.split('/');
	return list.slice(0, list.length - 1).join('/');
};

/**
 * 获取文件名
 * @param {String} str
 * @returns {string}
 */
export const basename = (str = '') => {
	if (!str) return '';
	if (str.includes('?')) {
		str = str.split('?')[0];
	}
	let idx = str.lastIndexOf('/');
	idx = idx > -1 ? idx : str.lastIndexOf('\\');
	if (idx < 0) {
		return str;
	}
	return str.substring(idx + 1);
};

/**
 * 获取文件扩展名
 * @param {String} filename
 * @param {Boolean} deleteDecimalPoint 删除小数点
 * @returns {string}
 */
export const getFileExtensionName = (filename, deleteDecimalPoint = false) => {
	if (!filename) return '';
	// 文件扩展名匹配正则
	const reg = /\.[^.]+$/;
	const matches = reg.exec(filename);
	if (matches) {
		if (matches.length && deleteDecimalPoint) {
			return (matches[0] || '').replace('.', '');
		}
		return matches[0] || '';
	}
	return '';
};

/**
 * 判断是否图片
 * @param {String} filename
 * @param {String} extensions - default: gif|png|jpeg|jpg
 * @returns {string}
 */
export const isImage = (filename, extensions = 'gif|png|jpeg|jpg') => {
	const extension = getFileExtensionName(filename).replace('.', '');
	return extensions.includes(extension);
};

/**
 * 字符串转boolean
 * @param boolStr
 * @returns {boolean}
 */
export const toBoolean = boolStr => {
	if (boolStr === 'true' || boolStr === 1 || boolStr === '1') {
		return true;
	}
	if (boolStr === 'false' || boolStr === 0 || boolStr === '0') {
		return false;
	}
	return boolStr;
};

/**
 * 扁平化数组
 * @param {Array} list
 * @param {Boolean} needCopy
 * @returns {[]}
 */
export const flatten = (list, needCopy = true) => {
	const result = [];
	const flattenHandler = (innerList) => {
		innerList.forEach(item => {
			const tempObj = needCopy ? deepCopy(item) : item;
			delete tempObj.children;
			result.push(tempObj);
			if (item.children) {
				flattenHandler(item.children);
			}
		});
	};
	flattenHandler(list);
	return result;
};

/**
 * 深度递归遍历数组，并将所有元素与遍历到的子数组中的元素合并为一个一维数组返回
 * @param {Array} arr
 * @returns {*}
 */
export const flat = (arr) => {
	if (Object.prototype.toString.call(arr) !== '[object Array]') return false;
	const list = [];
	arr.forEach(item => {
		if (item instanceof Array) {
			list.push(...flat(item));
		} else {
			list.push(item);
		}
	});
	return list;
};

/**
 * 获取静态url地址
 * @param item
 * @returns {*}
 */
export const getStaticUrl = (item) => {
	let filePath = (item.url || item.filePath);
	if (/^https?/.test(filePath)) {
		return filePath;
	}
	if (filePath && isBase64(filePath)) { // 判断当前字符串是否base64地址
		return filePath;
	}
	if (filePath && filePath.startsWith(Config.staticDomainURL)) { // 判断当前字符串是否以 ${ Config.staticDomainURL } 作为开头
		return filePath;
	}
	if (filePath && !filePath.startsWith('/')) { // 判断当前字符串是否以 "/" 作为开头
		filePath = '/' + filePath;
	}
	if (filePath && !filePath.startsWith(Config.staticDomainURL)) {
		filePath = Config.staticDomainURL + filePath;
	} else if (!filePath && item.response && item.response.message) {
		filePath = Config.staticDomainURL + '/' + item.response.message;
	}
	return filePath;
};

/**
 * 下载文件
 * @param {string} fileName  文件名
 * @param {string|null} type      文件类型 类型为null时，表示自动识别文件类型
 * @param {File|string} content   文件数据
 */
export const downloadFile = async (fileName, type, content) => {
	let dataUrl = '';
	if (isBase64(content)) {
		dataUrl = content;
	} else {
		if (/https?/.test(content) || content.startsWith(Config.staticDomainURL)) {
			content = await loadFile(content);
		}
		// Blob转化为链接
		let blobData = null;
		if (type) {
			blobData = new Blob([content], { type: type || 'application/json;charset=utf-8' });
		} else {
			blobData = new Blob([content]);
		}
		logger.log('getBrowser: ', getBrowser());
		dataUrl = window.URL.createObjectURL(blobData);
		if (window?.navigator.msSaveOrOpenBlob) { // 是Edge浏览器
			window.navigator.msSaveOrOpenBlob(blobData, fileName);
			return;
		}
	}

	if (getBrowser() === 'IE') { // 是IE浏览器
		// 判断是否是base64
		if (isBase64(content) && window.navigator && window.navigator.msSaveOrOpenBlob) { // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片/文件
			const bStr = atob(content.split(',')[1]);
			let n = bStr.length;
			const u8arr = new Uint8Array(n);
			while (n--) {
				u8arr[n] = bStr.charCodeAt(n);
			}
			const blob = new Blob([u8arr]);
			window.navigator.msSaveOrOpenBlob(blob, fileName);
		} else {
			// 调用创建iframe的函数
			createIframe(content);
		}
	} else {
		downloadFileByNewWindow(dataUrl, fileName);
	}
};

/**
 * 下载文件(通过新开窗口下载)
 * @param {string} fileUrl  文件地址
 * @param {string} fileName 文件名
 */
export const downloadFileByNewWindow = (fileUrl, fileName) => {
	// 新版浏览器支持download,添加属性
	const aDom = document.createElement('a');
	aDom.setAttribute('href', fileUrl);
	aDom.setAttribute('download', fileName);
	aDom.setAttribute('target', '_blank');
	aDom.setAttribute('display', 'none');
	document.body.appendChild(aDom);
	aDom.click();
	document.body.removeChild(aDom); 		// 下载完成移除元素
	window.URL.revokeObjectURL(fileUrl);	// 释放掉blob对象
};

/**
 * 下载图片/文件的函数
 */
export const openDownload = () => {
	// iframe的src属性不为空,调用execCommand(),保存图片/文件
	const iframeEle = window.document.body.querySelector('#iframeEle');
	if (iframeEle.getAttribute('src') !== 'about:blank') {
		window.frames.iframeEle.document.execCommand('SaveAs'); // 浏览器是不允许JS跨域操作。在两个页面中加上 document.domain="xxx.com"; 把它指向同一域，就可以操作。
	}
};

/**
 * 创建iframe并赋值的函数,传入参数为图片/文件的src属性值
 * @param data
 */
export const createIframe = (data) => {
	// 如果隐藏的iframe不存在则创建
	let iframeEle = window.document.body.querySelector('#iframeEle');
	if (!iframeEle) {
		iframeEle = window.document.createElement('iframe');
		iframeEle.setAttribute('id', 'iframeEle');
		iframeEle.setAttribute('name', 'iframeEle');
		iframeEle.setAttribute('width', '0');
		iframeEle.setAttribute('height', '0');
		iframeEle.setAttribute('src', 'about:blank');
		iframeEle.addEventListener('load', () => openDownload(), false);
		window.document.body.appendChild(iframeEle);
	}
	// iframe的src属性如不指向图片/文件地址,则手动修改,加载图片/文件
	if (iframeEle.getAttribute('src') !== data) {
		iframeEle.setAttribute('src', data);
	} else {
		// 如指向图片/文件地址,直接调用下载方法
		openDownload();
	}
};

export const save = (blob, fileName) => {
	const href = URL.createObjectURL(blob);
	// 新版浏览器支持download,添加属性
	const link = document.createElement('a');
	link.setAttribute('href', href);
	link.setAttribute('download', fileName);
	link.setAttribute('target', '_blank');
	link.setAttribute('display', 'none');
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);    // 下载完成移除元素
	window.URL.revokeObjectURL(href);	// 释放掉blob对象
};

export const saveString = (text, filename) => {
	save(new Blob([text], { type: 'text/plain' }), filename);
};

export const saveArrayBuffer = (buffer, filename) => {
	save(new Blob([buffer], { type: 'application/octet-stream' }), filename);
};

/**
 * 文件大小显示(单位：M)
 * @param size  文件大小
 * @param {string} unit 单位：K、M、AUTO
 * @returns {string}
 */
export const toFileSize = (size, unit = 'AUTO') => {
	const oneK = 1024;
	const oneM = 1024 * 1024;
	unit = unit.toUpperCase();

	const resMap = {};
	resMap.K = () => (size / oneK).toFixed(2) + 'K';
	resMap.M = () => (size / oneM).toFixed(2) + 'M';
	resMap.AUTO = () => {
		if (size > oneM) return resMap.M();
		if (size > oneK && size < oneM) return resMap.K();
		return size + 'B'; // Byte
	};
	return resMap[unit]();
};

/**
 * 复制内容到剪贴板
 * @param {string} content  复制的内容
 * @returns {boolean}
 */
export const copyToClipBoard = (content) => {
	// 动态创建 textarea 标签
	const textarea = document.createElement('textarea');
	// 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
	textarea.readOnly = 'readonly';
	textarea.style.position = 'absolute';
	textarea.style.left = '-9999px';
	// 将要 copy 的值赋给 textarea 标签的 value 属性
	textarea.value = content || '';
	// 将 textarea 插入到 body 中
	document.body.appendChild(textarea);
	// 选中值并复制
	textarea.select();
	textarea.setSelectionRange(0, textarea.value.length);
	const result = document.execCommand('Copy');
	if (result) {
		message.success('复制成功');
	}
	document.body.removeChild(textarea);
};

/**
 * 数组去重
 * @param {array} list  数据列表
 * @returns {array} list
 */
export const unique = (list) => {
	const result = [];
	for (const item of list) {
		if (isObject(item) || Array.isArray(item)) {
			const exist = result.some(l => l && JSON.stringify(l) === JSON.stringify(item));
			if (!exist) {
				result.push(item);
			}
		} else {
			if (!result.includes(item)) {
				result.push(item);
			}
		}
	}
	return result;
};

/**
 * 数字转科学计数法
 * @param num
 * @param fractionDigits 小数点后保留的位数
 * @return string 将数字按科学计数法格式返回
 */
export const toExponential = (num, fractionDigits) => {
	if (Number(num) === 0) return '0';
	return Number(num).toExponential(fractionDigits);
};

/**
 * 文本替换为科学记数法
 * @param {string} text 文本
 * @param {number=} fractionDigits 小数点后保留的位数
 * @return {string} html字符串
 */
export const toScienceHtml = (text, fractionDigits) => {
	return text.replace(/[1-9]\d{6,}(\.\d+)?|0\.000000\d+/g, function (val) {
		const str = Number(val).toExponential(fractionDigits);
		if (str.indexOf('e') !== -1) {
			let [n, m] = str.split('e');
			m = m.replace(/\+/g, '');
			return `${ n }*10<sup>${ m }</sup>`;
		}
	}).replace(/>=/g, '&ge;').replace(/<=/g, '&le;');
};

/**
 * 科学计数法转数字字符串(即：转换为非科学计数法)
 * @param num
 * @param fractionDigits 有效位数
 * @return string 将数字按科学计数法格式返回
 */
export const toNonExponential = (num, fractionDigits) => {
	const m = Number(num).toExponential().match(/^[+-]?\d(?:\.(\d*))?[E|e]([+-]\d+)/);
	if (!m) return num;
	fractionDigits = Number(fractionDigits ?? Math.max(0, (m[1] || '').length - m[2]));
	if (fractionDigits > 20) return num;
	return Number(num).toFixed(fractionDigits);
};

/**
 * 返回顶部
 */
export const goTop = (nativeElement) => {
	const doc = nativeElement || document.documentElement;
	if (doc) {
		const scrollToTop = (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0); // 获取指定元素滚动条的滚动高度
		if (scrollToTop > 0) {
			animate(scrollToTop, 0, 300, 'Linear', value => {
				doc.scrollTop = value;
			});
		}
	}
};

/**
 * 验证是否存在错误
 * 只要有一个错误，即不通过
 */
export const checkValueError = (list) => {
	for (const item of list) {
		if (item.forms && item.forms.length) {
			for (const childItem of item.forms) {
				if (Object.prototype.hasOwnProperty.call(childItem, 'pass') && !childItem.pass) {
					logger.log('验证是否存在错误 childItem: ', childItem);
					return false;
				}
			}
		} else {
			if (Object.prototype.hasOwnProperty.call(item, 'pass') && !item.pass) {
				logger.log('验证是否存在错误 item: ', item);
				return false;
			}
		}
	}
	return true;
};

/**
 * 拼接时间
 * @param count
 * @param isContainsToday 是否包含当天
 * @return {{startTime: string, endTime: string}}
 */
export const timeFormat = (count, isContainsToday = false) => {
	const formatT = (day = 1) => {
		const time = new Date();
		const oneDay = 24 * 60 * 60 * 1000;
		time.setTime(time.getTime() - oneDay * (day - (isContainsToday ? 1 : 0)));
		const Y = time.getFullYear();
		const M = ((time.getMonth() + 1) >= 10 ? (time.getMonth() + 1) : '0' + (time.getMonth() + 1));
		const D = (time.getDate() >= 10 ? time.getDate() : '0' + time.getDate());
		return Y + '-' + M + '-' + D;
	};
	const startTime = formatT(count);
	const endTime = formatT(1);
	return {
		startTime,
		endTime,
	};
};

/**
 * 获取最近7天
 */
export const sevenDays = () => timeFormat(7);

/**
 * 获取最近30天
 */
export const thirtyDays = () => timeFormat(30);

/**
 * 计算开始和结束的时间差
 * @param sDate1
 * @param sDate2
 * @return {number}
 */
export const mistiming = (sDate1, sDate2) => {
	let aDate;
	aDate = sDate1.split('-');
	const oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
	aDate = sDate2.split('-');
	const oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]);
	const oneDay = 24 * 60 * 60 * 1000;
	const iDays = parseInt(String(Math.abs(oDate1 - oDate2) / oneDay), 10);
	return iDays + 1;
};

/**
 * 判断开始和结束之间的时间差是否在目标内(eg:是否在90天内)
 * @param start
 * @param end
 * @param target
 * @return {{state: boolean, day: number}}
 */
export const countDate = (start, end, target = 1) => {
	const days = mistiming(start, end);
	const state = days > target ? Boolean(0) : Boolean(1);
	return {
		state,
		day: days,
	};
};

/**
 * 获取指数串
 * */
export const getPowStr = (sourceStr) => {
	sourceStr = sourceStr || '';
	let str = '';
	const powStrList = ['⁺', '⁻', '⁰', '¹', '²', '³', '⁴', '⁵', '⁶', '⁷', '⁸', '⁹'];
	const numList = ['+', '-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
	const list = sourceStr.split('').filter(s => s);
	for (let i = 0; i < list.length; i++) {
		const index = numList.findIndex(numStr => numStr === list[i]);
		if (powStrList[index] && powStrList[index] !== powStrList[0]) {
			str += powStrList[index];
		}
	}
	return str;
};

/**
 * 数字转换为指数形式字符串
 * @param fractionDigits    需转换值
 * @param precision         有效数字位数
 */
export const toPowStr = (fractionDigits, precision = 6) => {
	let value = isEmpty(fractionDigits) ? '' : String(fractionDigits).toLowerCase();
	value = Number(value);
	if (isNaN(value)) {
		return fractionDigits;
	}
	value = value.toExponential();
	let [base, pow] = value.split('e');
	base = base || '1';
	pow = getPowStr(pow || '¹');
	return base + 'x10' + pow;
};

/**
 * 获取标准指数串
 * */
export const getPowToNumStr = (sourceStr) => {
	let str = '';
	const powStrList = ['⁺', '⁻', '⁰', '¹', '²', '³', '⁴', '⁵', '⁶', '⁷', '⁸', '⁹'];
	const numList = ['+', '-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
	const list = sourceStr.split('').filter(s => s);
	for (let i = 0; i < list.length; i++) {
		const index = powStrList.findIndex(singleStr => singleStr === list[i]);
		if (numList[index]) {
			str += numList[index];
		}
	}
	return str;
};

/**
 * 指数形式字符串转换数字
 */
export const powToNumber = (fractionDigits) => {
	let value = String(fractionDigits).toLowerCase();
	if (value.startsWith('.')) {
		value = value.substring(1);
	}
	if (!ExponentReg.test(value)) { // 非科学计数法形式的字符串
		return fractionDigits;
	}

	// 如果是含有x
	if (value.includes('x')) {
		let [base, pow] = value.split('x');
		base = Number(base || '1');
		pow = pow.replace('10', '');
		pow = Number(getPowToNumStr(pow) || '1');
		return base * Math.pow(10, pow);
	}
	let pow = value.replace('10', '');
	pow = Number(getPowToNumStr(pow) || '1');
	return Math.pow(10, pow);
};

/**
 * 对比两个对象是否完全相等(浅比对)
 */
export const eq = (a, b, aStack, bStack) => {
	// === 结果为 true 的区别出 +0 和 -0
	if (a === b) return a !== 0 || 1 / a === 1 / b;

	// typeof null 的结果为 object ，这里做判断，是为了让有 null 的情况尽早退出函数
	if (a == null || b == null) return false;

	// 判断 NaN
	// eslint-disable-next-line no-self-compare
	if (a !== a) return b !== b;

	// 判断参数 a 类型，如果是基本类型，在这里可以直接返回 false
	const type = typeof a;
	if (type !== 'function' && type !== 'object' && typeof b !== 'object') return false;

	// 更复杂的对象使用 deepEq 函数进行深度比较
	return deepEq(a, b, aStack, bStack);
};

/**
 * 深度对比两个对象是否完全相等
 */
export const deepEq = (a, b, aStack, bStack) => {
	// a 和 b 的内部属性 [[class]] 相同时 返回 true
	const className = Object.prototype.toString.call(a);
	if (className !== Object.prototype.toString.call(b)) return false;

	switch (className) {
		case '[object RegExp]':
		case '[object String]':
			return '' + a === '' + b;
		case '[object Number]':
			// eslint-disable-next-line no-self-compare
			if (+a !== +a) return +b !== +b;
			return +a === 0 ? 1 / +a === 1 / b : +a === +b;
		case '[object Date]':
		case '[object Boolean]':
			return +a === +b;
	}

	const areArrays = className === '[object Array]';
	// 不是数组
	if (!areArrays) {
		// 过滤掉两个函数的情况
		if (typeof a !== 'object' || typeof b !== 'object') return false;

		const aCtor = a.constructor;
		const bCtor = b.constructor;
		// aCtor 和 bCtor 必须都存在并且都不是 Object 构造函数的情况下，aCtor 不等于 bCtor， 那这两个对象就真的不相等啦
		if (aCtor !== bCtor && !(isFunction(aCtor) && aCtor instanceof aCtor && isFunction(bCtor) && bCtor instanceof bCtor) && ('constructor' in a && 'constructor' in b)) {
			return false;
		}
	}

	aStack = aStack || [];
	bStack = bStack || [];
	let length = aStack.length;

	// 检查是否有循环引用的部分
	while (length--) {
		if (aStack[length] === a) {
			return bStack[length] === b;
		}
	}

	aStack.push(a);
	bStack.push(b);

	// 数组判断
	if (areArrays) {
		length = a.length;
		if (length !== b.length) return false;

		while (length--) {
			if (!eq(a[length], b[length], aStack, bStack)) return false;
		}
	} else { // 对象判断
		const keys = Object.keys(a);
		let key;
		length = keys.length;

		if (Object.keys(b).length !== length) return false;
		while (length--) {
			key = keys[length];
			if (!(Object.prototype.hasOwnProperty.call(b, key) && eq(a[key], b[key], aStack, bStack))) return false;
		}
	}
	aStack.pop();
	bStack.pop();
	return true;
};

/**
 * 转换为数字或科学计数法
 * @param value
 * @return {string}
 */
export const toNumberOrExponential = (value) => (value || '').replace(/[^\dEe\\.+-]/g, '');

/**
 * 验证重复元素，有重复返回true；否则返回false
 * @param list
 * @return {boolean}
 */
export const isRepeat = (list) => {
	const hash = {};
	for (const key of list) {
		if (hash[key]) {
			return true;
		}
		hash[key] = true;
	}
	return false;
};

/**
 * 获取随机十六进制颜色
 * @returns {string}
 */
export const getRandomColor = () => {
	const r = Math.floor(Math.random() * 256);
	const g = Math.floor(Math.random() * 256);
	const b = Math.floor(Math.random() * 256);
	return '#' + (Array(6).join('0') + (r.toString(16) + g.toString(16) + b.toString(16))).slice(-6);
};

/**
 * RGB 转 16进制
 * eg:
 *  rgbHex(65, 131, 196);
 *    //=> '4183c4'
 *
 *    rgbHex('rgb(40, 42, 54)');
 *    //=> '282a36'
 *
 *    rgbHex(65, 131, 196, 0.2);
 *    //=> '4183c433'
 *
 *    rgbHex(40, 42, 54, '75%');
 *    //=> '282a36bf'
 *
 *    rgbHex('rgba(40, 42, 54, 75%)');
 *    //=> '282a36bf'
 * @returns {string}
 */
export const rgbToHex = (red, green, blue, alpha) => {
	const isPercent = (red + (alpha || '')).toString().includes('%');

	if (typeof red === 'string') {
		[red, green, blue, alpha] = red.match(/(0?\.?\d{1,3})%?\b/g).map(component => Number(component));
	} else if (alpha !== undefined) {
		alpha = Number.parseFloat(alpha);
	}

	if (typeof red !== 'number' ||
		typeof green !== 'number' ||
		typeof blue !== 'number' ||
		red > 255 ||
		green > 255 ||
		blue > 255
	) {
		throw new TypeError('Expected three numbers below 256');
	}

	if (typeof alpha === 'number') {
		if (!isPercent && alpha >= 0 && alpha <= 1) {
			alpha = Math.round(255 * alpha);
		} else if (isPercent && alpha >= 0 && alpha <= 100) {
			alpha = Math.round(255 * alpha / 100);
		} else {
			throw new TypeError(`Expected alpha value (${ alpha }) as a fraction or percentage`);
		}

		alpha = (alpha | 1 << 8).toString(16).slice(1);
	} else {
		alpha = '';
	}

	return ((blue | green << 8 | red << 16) | 1 << 24).toString(16).slice(1) + alpha;
};

/**
 * 16进制 转 RGB
 * @param hex
 * @returns {string}
 */
export const hexToRgb = (hex) => {
	const rgb = [];

	hex = hex.substr(1); // 去除前缀 # 号

	if (hex.length === 3) { // 处理 "#abc" 成 "#aabbcc"
		hex = hex.replace(/(.)/g, '$1$1');
	}

	hex.replace(/../g, function (color) {
		rgb.push(parseInt(color, 0x10)); // 按16进制将字符串转换为数字
	});

	return 'rgb(' + rgb.join(',') + ')';
};

/**
 * 检查并更新到 Rgba
 * @param rgba
 * @return {String}
 */
const checkAndUpdateToRgba = (rgba) => {
	rgba = trimAll(rgba);
	if (rgba.startsWith('rgb')) {
		return rgba;
	}
	if (rgba.startsWith('#')) {
		return hexToRgb(rgba);
	}
	return rgba;
};

/**
 * RGBA 转 Number
 * eg: rgba(237, 237, 237, 1) => [237 / 255, 237 / 255, 237 / 255, 1] => [0.929411764705882, 0.929411764705882, 0.929411764705882, 1]
 * @param {String} rgba
 * @returns {number[]}
 */
export const rgbaToNumber = (rgba) => {
	rgba = checkAndUpdateToRgba(rgba);
	const [r, g, b, a] = rgba
		.replace('rgba', '')
		.replace('rgb', '')
		.replace('(', '')
		.replace(')', '')
		.split(',');
	return [Number(r) / 255, Number(g) / 255, Number(b) / 255, Number(a)];
};

/**
 * 清空节点
 * @returns {string}
 */
export const clearEmptyElement = (elem) => {
	while (elem?.firstChild) elem.removeChild(elem.firstChild);
};

export function makeSubManager () {
	let currentSub = null;

	const api = {
		sub (subscription) {
			api.unsub();
			currentSub = subscription;
		},
		unsub () {
			if (currentSub !== null) {
				currentSub.unsubscribe();
				currentSub = null;
			}
		},
	};

	return api;
}

/**
 * Retrieves the volume cropping filter, if any, of a source proxy.
 */
export function getCropFilter (pxm, proxy) {
	// find 3d view
	const view3d = pxm.getViews().find((v) => v.getProxyName() === 'View3D');

	if (!view3d) {
		throw new Error('Cannot find 3D view!');
	}

	// find volume rep
	const volRep = pxm.getRepresentation(proxy, view3d);

	if (!volRep || !volRep.getCropFilter) {
		throw new Error('Cannot find the volume rep with a crop filter!');
	}

	return volRep.getCropFilter();
}

/**
 * A wrapper function for pxm.createRepresentationInAllViews that
 * correctly manages which representation is bound to 2D manipulators.
 */
export function createRepresentationInAllViews (pxm, source) {
	const views2D = pxm.getViews().filter((v) => v.isA('vtkView2DProxy'));
	// reach in to get sliceRepresentation, since it's not default exposed
	const origReps = views2D.map((v) =>
		v.getReferenceByName('sliceRepresentation'),
	);

	pxm.createRepresentationInAllViews(source);

	// do not focus labelmaps
	if (source.getProxyName() === 'LabelMap') {
		views2D.forEach((view, i) =>
			view.bindRepresentationToManipulator(origReps[i]),
		);
	}
}

/**
 * Wrap a mutation as a vuex action.
 */
export function wrapMutationAsAction (mutation) {
	return ({ commit }, value) => commit(mutation, value);
}

/**
 * Renames keys according to a mapping from old to new key.
 */
export function remapIdKeys (obj, mapping) {
	const newObj = {};
	Object.keys(obj).forEach((id) => {
		let newId = id;
		if (id in mapping) {
			newId = mapping[id];
		}
		newObj[newId] = obj[id];
	});
	return newObj;
}

/**
 * Replaces elements in a list according to a mapping.
 */
export function remapIdList (list, mapping) {
	return list.map((id) => {
		if (id in mapping) {
			return mapping[id];
		}
		return id;
	});
}

/**
 * 阻止冒泡方法
 * @param {Event} event
 */
export const stopPropagation = (event) => {
	event = event || window.event;
	if (event.stopPropagation) { // W3C阻止冒泡方法
		event.stopPropagation();
	} else {
		event.cancelBubble = true; // IE阻止冒泡方法
	}
};

/**
 * 将html格式字符串转化为dom的函数
 * @param template
 * @param data
 * @param methods
 * @return {Element}
 */
export const htmlStrToDom = (template, data = {}, methods = {}) => {
	data = Object.assign({}, data);
	const Component = Vue.extend({
		template: template,
		data () {
			return data;
		},
		methods: {
			...methods,
		},
	});
	const instance = new Component().$mount();
	return instance.$el;
};

/**
 * 获得最后一位
 * @param {String|Array} args
 * @return {*}
 */
export const getLast = (args) => {
	if (isString(args)) {
		return args.slice(args.length - 1);
	}
	if (isArray(args)) {
		return args[args.length - 1];
	}
	return args;
};

/**
 * 数组排序(支持多条件)
 * example
 *    list.sort(
 *        firstBy(function (v1, v2) { return v1.name.length - v2.name.length; })
 *        .thenBy(function (v1, v2) { return v1.population - v2.population; })
 *        .thenBy(function (v1, v2) { return v1.id - v2.id; })
 *    );
 *
 *    list.sort(
 *        firstBy(function (v) { return v.name.length; })
 *        .thenBy("population") // 按ASCII字符代码从小到大排序
 *        .thenBy("id") // 按ASCII字符代码从小到大排序
 *    );
 */
export const firstBy = (() => {
	const identity = v => v;
	const ignoreCase = v => typeof (v) === 'string' ? v.toLowerCase() : v;
	const makeCompareFunction = (f, opt) => {
		opt = typeof (opt) === 'number' ? { direction: opt } : opt || {};
		if (typeof (f) !== 'function') {
			const prop = f;
			f = (v1) => v1[prop] ? v1[prop] : '';
		}
		if (f.length === 1) {
			const uf = f;
			const preProcess = opt.ignoreCase ? ignoreCase : identity;
			f = (v1, v2) => preProcess(uf(v1)) < preProcess(uf(v2)) ? -1 : preProcess(uf(v1)) > preProcess(uf(v2)) ? 1 : 0;
		}
		if (opt.direction === -1) return (v1, v2) => -f(v1, v2);
		return f;
	};

	function tb (func, opt = undefined) {
		const x = typeof (this) === 'function' ? this : false;
		const y = makeCompareFunction(func, opt);
		const f = x ? (a, b) => (x(a, b) || y(a, b)) : y;
		f.thenBy = tb;
		return f;
	}

	return tb;
})();

/**
 * 二分查找
 * @param arr
 * @param key
 * @return {number}
 */
export const binarySearch = (arr, key) => {
	let left = 0; // 初始左边界
	let right = arr.length - 1; // 初始右边界
	// 如果left > right 证明整个数组结束了仍没有找到目标值
	while (left <= right) {
		const mid = left + Math.floor((right - left) / 2); // 防止溢出
		if (key === arr[mid]) {
			return mid;
		} else if (key > arr[mid]) {
			// 目标值大于中值，则中间左侧可以抛弃了
			left = mid + 1;
		} else {
			// 目标值小于中值，则中间右侧可以抛弃了
			right = mid - 1;
		}
	}
	return -1;
};

/**
 * 获取邻接键
 * 查找最后一个小等于target/第一个大等于target的元素
 * @param arr
 * @param target
 * @return {number[]}
 */
export const getAdjoinKey = (arr, target) => {
	const option = binarySearch(arr, target);
	if (option < 0) return [-1, -1];
	else {
		let left = option - 1;
		let right = option + 1;
		while (left >= 0 && arr[left] === target) left--;
		while (right < arr.length && arr[right] === target) right++;
		if (right >= arr.length) right = -1;
		return [left + 1, right - 1];
	}
};

/**
 * 绑定监听事件
 * @param target
 * @param eventName
 * @param fn
 * @param removeEvent
 */
export const addEventHandler = (target, eventName, fn, removeEvent = false) => {
	if (removeEvent) {
		removeEventHandler(target, eventName, fn);
	}
	if (target?.addEventListener) {
		target.addEventListener(eventName, fn);
	} else {
		target?.attachEvent('on' + eventName, fn);
	}
};

/**
 * 移除监听事件
 * @param target
 * @param eventName
 * @param fn
 */
export const removeEventHandler = (target, eventName, fn) => {
	if (target?.removeEventListener) {
		target.removeEventListener(eventName, fn);
	} else {
		target?.detachEvent('on' + eventName, fn);
	}
};

/**
 * 检查某条件是否加载完毕
 * @param condition
 * @return {Promise<Boolean>}
 */
export const checkIfLoaded = (condition) => {
	return new Promise(async (resolve) => {
		if (!condition) {
			await sleep(50);
			return await checkIfLoaded(condition);
		} else {
			return resolve(true);
		}
	});
};

/**
 * 并集
 * @param arr1
 * @param arr2
 * @returns {*}
 *
 * eg:
 *  两数组 arr1 = [1, 2, 3]，arr2 = [2, 4, 5], 求arr1，arr2数组的并集
 *  => [1,2,3,4,5]
 */
export const union = (arr1, arr2) => arr1.concat(arr2.filter(v => !arr1.includes(v)));

/**
 * 交集
 * @param arr1
 * @param arr2
 * @returns {*}
 *
 * eg:
 *  两数组 arr1 = [1, 2, 3]，arr2 = [2, 4, 5], 求arr1，arr2数组的交集
 *  => [2]
 */
export const intersection = (arr1, arr2) => arr1.filter(v => arr2.includes(v));

/**
 * 差集
 * @param arr1
 * @param arr2
 * @returns {*}
 *
 * eg:
 *  两数组 arr1 = [1, 2, 3]，arr2 = [2, 4, 5], 求arr1，arr2数组的差集
 *  => [1,3,4,5]
 */
export const difference = (arr1, arr2) => arr1.concat(arr2).filter(v => !arr1.includes(v) || !arr2.includes(v));

/**
 * ArrayBuffer转json
 * @param arrayBuffer
 * @return {any}
 */
export const arrayBufferToJson = (arrayBuffer) => {
	// 转换为json对象
	try {
		const enc = new TextDecoder('utf-8');
		const uint8Msg = new Uint8Array(arrayBuffer);
		const data = JSON.parse(enc.decode(uint8Msg));
		return data;
	} catch (e) {
		logger.error('arrayBufferToJson error: ', e);
		return null;
	}
};
