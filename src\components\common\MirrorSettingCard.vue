<template>
	<a-card :title="title" :bodyStyle="{width: '260px'}" @keydown.8.stop>
		<div
			class="base-wrapper"
		>
			<label for=""><b>平面</b></label>
			<a-radio-group
				v-model="mirrorPlane.type"
				button-style="solid"
				size="small"
				@change="handleChange"
			>
				<a-radio-button value="planeXY">
					XY
				</a-radio-button>
				<a-radio-button value="planeYZ">
					YZ
				</a-radio-button>
				<a-radio-button value="planeXZ">
					XZ
				</a-radio-button>
			</a-radio-group>
		</div>
	</a-card>
</template>

<script>
import {
	PlaneModeEnum
} from '@/constants';
export default {
	name: 'CollapseTree',
	props: {
		oDirection: {
			type: String,
			default: 'planeXY'
		},
		title: {
			type: String,
			required: true,
		},
	},
	data () {
		return {
			mirrorPlane: {
				type: 'planeXY',
			}
		};
	},
	mounted () {
		if ([PlaneModeEnum.planeXY, PlaneModeEnum.planeXZ, PlaneModeEnum.planeYZ].includes(this.oDirection)) {
			this.mirrorPlane.type = this.oDirection;
		}
		this.$emit('change', this.mirrorPlane);
	},
	methods: {
		handleChange () {
			this.$emit('change', this.mirrorPlane);
		},

	},
	watch: {
		oDirection (value) {
			if ([PlaneModeEnum.planeXY, PlaneModeEnum.planeXZ, PlaneModeEnum.planeYZ].includes(value)) {
				this.mirrorPlane.type = value;
				this.$emit('change', this.mirrorPlane);
			}
		},
	}
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/base.less';
@import '~@/assets/styles/mixins/variables.less';
.ant-card {
	/deep/ .ant-card-head {
		padding: 0 20px;
	}
	/deep/ .ant-card-body {
		padding: 10px 20px
	}

	/deep/ .ant-card-head-title {
		padding-top: 12px;
		padding-bottom: 12px;
	}
}
.base-wrapper {
	display:flex;
	justify-content:space-between;
	align-items:center;
	margin-top:10px;
	margin-bottom:10px;
}

.action-btn:not(:last-child) {
	margin-right: 8px;
}
</style>
