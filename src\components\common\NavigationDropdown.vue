<template>
	<div :class="['grid-cell']">
		<a-dropdown placement="bottomCenter">
			<a-menu class="menu-dropdown" slot="overlay">
				<a-menu-item key="-1" style="text-align: center;" v-if="title">
					{{ title }}
				</a-menu-item>
				<a-divider />
				<template v-for="(data, index) in dataList">
					<template v-if="index >= startIndex">
						<a-menu-item :key="index">
							<div :class="['grid-cell', value === data.key ? 'active' : '', data.disabled ? 'disabled' : '']" @click="onSelect($event, data)">
								<img class="render" :src="data.icon">{{ data.desc }}
							</div>
						</a-menu-item>
						<a-divider :key="'divider_' + index" v-if="data.showDivider" />
					</template>
				</template>
			</a-menu>
			<div style="position: relative;" :title="title">
				<slot name="selectIcon">
					<img class="render" :src="selectIcon">
					<!--<img class="render sub fix1" :src="subDownImg">-->
					<img class="render sub fix1" :src="downTriangleImg">
				</slot>
			</div>
		</a-dropdown>
	</div>
</template>

<script>
/* ===================================
 * 导航下拉菜单
 * Created by cjking on 2022/06/30.
 * Copyright 2022, Inc.
 * =================================== */
import subDownImg from '@/assets/img/project/navigation/subDown.png';
import downTriangleImg from '@/assets/img/project/navigation/down-triangle.png';

const icons = {
	subDownImg,
	downTriangleImg,
};

export default {
	name: 'NavigationDropdown',
	props: {
		value: {
			type: String,
			required: false,
			default: undefined,
		},
		title: {
			type: String,
			required: false,
			default: '',
		},
		dataList: {
			type: Array,
			required: true,
		},
		startIndex: { // 开始下标
			type: Number,
			required: false,
			default: 0,
		},
	},
	model: {
		prop: 'value',
		event: 'change.value',
	},
	data () {
		return {
			...icons,
		};
	},
	computed: {
		selectIcon () {
			if (!this.value) {
				return this.dataList[0]?.icon;
			}
			return this.dataList.find(data => data.key === this.value)?.icon;
		},
	},
	methods: {
		onSelect (e, data) {
			this.$emit('value', e);
			this.$emit('change.value', data.key);
			this.$emit('onSelect', data);
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/mixins/variables.less";

.grid {
	display: flex;
	height: 46px;
	line-height: 46px;
	justify-content: flex-start;

	&.wrapper {
		width: auto;
		margin: 1px auto 0;
	}

	/deep/ .ant-divider-vertical {
		margin: 0 0 0 0;
		top: 15px;
		height: 20px;
		left: 10px;
	}
}

.grid-cell {
	flex: 1;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-items: center;

	img, .text {
		margin-left: 5px;
		margin-right: 5px;
		width: 20px;
		height: 20px;
		cursor: pointer;
	}

	.p3 {
		padding: 3px;
	}

	&:before {
		opacity: 0;
		content: "\2714";
		color: @primary;
	}

	&.active {
		img, .svg-icon {
			background: @primary-assist;
			border-radius: 4px;
			border: 1px solid @primary;
		}

		&:before {
			content: "\2714";
			color: @primary;
			opacity: 1;
		}
	}

	.active-draw {
		img, .svg-icon {
			border-radius: 3px;
			border: 1px solid @primary;
			background: @primary-assist;
		}
	}

	.svg-icon-overwrite {
		display: block;
		margin-left: 8px;
		margin-top: 6px;
	}

	.action-active {
		/deep/ svg {
			border-radius: 3px;
			border: 1px solid @primary;
			background: @primary-assist;
		}
	}

	&.disabled {
		img, .svg-icon {
			background: @disabled-bg;
			cursor: not-allowed;
		}
	}

	.svg-icon {
		font-size: 20px;
		padding: 4px;
		cursor: pointer;
	}

	.mt {
		margin-top: 12px;
	}

	.sub {
		//width: 5px;
		//height: 5px;
		width: 8px;
		height: 4px;
		position: absolute;
		right: 5px;
		bottom: 12px;

		&.fix1 {
			//right: 0;
			//bottom: 10px;
			right: -10px;
			bottom: 19px;
		}

		&.fix2 {
			right: 0;
			bottom: 0;
		}
	}

	.nav-clear {
		font-size: 24px;
		margin-left: 5px;
		color: @primary;
	}
}

.ant-dropdown-menu {
	padding: 0 4px;

	.ant-divider-horizontal {
		margin: 0;
		left: 0;
	}

	.ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title {
		padding: 5px 2px;
	}

	.ant-dropdown-menu-item:hover {
		background: none;

		img, .svg-icon {
			background: @primary-assist;
			border-radius: 4px;
			border: 1px solid @primary;
		}
	}
}
</style>
