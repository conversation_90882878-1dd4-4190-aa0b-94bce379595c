import Vue from 'vue';
import {
	Icon,
	Layout,
	Menu,
	Button,
	Collapse,
	Spin,
	Carousel,
	ConfigProvider,
	Select,
	DatePicker,
	TimePicker,
	Calendar,
	Radio,
	Pagination,
	Popconfirm,
	Table,
	Modal,
	Transfer,
	Card,
	Dropdown,
	InputNumber,
	Tooltip,
	Tabs,
	Col,
	Row,
	Form,
	FormModel,
	Avatar,
	Steps,
	Tree,
	TreeSelect,
	Checkbox,
	Input,
	Popover,
	Divider,
	Badge,
	// BackTop,
	Breadcrumb,
	Drawer,
	Tag,
	List,
	Switch,
	Alert,
	Upload,
	message,
	notification,
	Empty,
	Descriptions,
	Cascader,
	Slider,
	Result,
	Progress,
	Affix,
} from 'ant-design-vue';

const components = [
	Icon,
	Layout,
	Menu,
	Button,
	Collapse,
	Spin,
	Carousel,
	ConfigProvider,
	Select,
	DatePicker,
	TimePicker,
	Calendar,
	Radio,
	Pagination,
	Popconfirm,
	Table,
	Modal,
	Transfer,
	Card,
	Dropdown,
	InputNumber,
	Tooltip,
	Tabs,
	Col,
	Row,
	Form,
	FormModel,
	Avatar,
	Steps,
	Tree,
	TreeSelect,
	Checkbox,
	Input,
	Popover,
	Divider,
	Badge,
	Breadcrumb,
	Drawer,
	Tag,
	List,
	Switch,
	Alert,
	Upload,
	message,
	notification,
	Empty,
	Descriptions,
	Cascader,
	Slider,
	Result,
	Progress,
	Affix,
];

export const initAntDesignComponents = () => {
	Vue.prototype.$modal = Modal;
	Vue.prototype.$confirm = Modal.confirm;
	Vue.prototype.$error = Modal.error;
	Vue.prototype.$info = Modal.info;
	Vue.prototype.$warning = Modal.warning;
	Vue.prototype.$success = Modal.success;
	message.config({ // 全局配置
		top: `10%`,
		duration: 5,
		maxCount: 3,
	});
	Vue.prototype.$message = message;
	Vue.prototype.$notification = notification;
	components.forEach(component => Vue.use(component));
};
