<template>
	<div>
		<template v-if="tags && tags.length > 0">
			<template v-for="tag in tags">
				<span :key="tag" @click="handleClick(tag)">
					<a-tag class="tag" :color="selectTag === tag ? 'blue' : ''" :closable="closable" @close="handleClose($event, tag)">
						<template v-if="fullCharNum === 0">
							{{ tag }}
						</template>
						<JEllipsis
							v-else
							:length="fullCharNum"
							:value="tag"
						/>
					</a-tag>
				</span>
			</template>
		</template>
	</div>
</template>

<script>
/* ===================================
 * 参数tag标签公共组件
 * Created by zhangzheng on 2022/04/08.
 * Copyright 2023, Inc.
 * =================================== */
import { deepCopy } from '@/utils/utils';
import { logger } from '@/utils/logger';
import JEllipsis from './JEllipsis.vue';

export default {
	name: 'Tags',
	components: {
		JEllipsis
	},
	props: {
		dataList: {
			type: Array,
			required: true,
			default: () => []
		},
		closable: {
			type: Boolean,
			required: false,
			default: true,
		},
		activeTag: {
			type: String,
			required: false,
			default: ''
		},
		fullCharNum: {
			type: Number,
			required: false,
			default: 0,
		}
	},
	data () {
		return {
			tags: [],
			inputVisible: false,
			inputValue: '',
			selectTag: '',
		};
	},
	watch: {
		dataList: {
			immediate: true,
			handler (list) {
				this.tags = list || [];
			},
		},
		activeTag: {
			immediate: true,
			handler () {
				this.selectTag = this.activeTag;
			}
		}
	},
	methods: {
		handleClick (tag) {
			if (this.selectTag === tag) {
				this.selectTag = '';
			} else {
				this.selectTag = tag;
			}
			this.$emit('click', this.selectTag);
		},
		/**
		 * 处理输入关闭值
		 */
		handleClose (event, removedTag) {
			this.tags = this.tags.filter(tag => tag !== removedTag);
			this.$emit('remove', removedTag);
		},
	},
};
</script>

<style lang="less" scoped>
.tag {
	height: 24px;
	line-height: 24px;
}
.ant-tag-blue {
    color: #6263ff;
    background: #e6e7ff;
    border-color: #6263ff;
}
.ant-tag {
	border-radius: 2px;
	margin-top: 2px;
	margin-bottom: 2px;
	margin-right: 4px;
}
</style>
