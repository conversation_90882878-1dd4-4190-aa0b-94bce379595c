# 项目名称
repository_name = client-frontend

# docker私服地址
# 内网
api_in_network = https://docker-registry.fmock.cn
# 外网
# api_out_network = https://registry.fangzhenxiu.com
api_out_network = https://docker-registry.fmock.cn

# 本地认证文件
config_in_network = .netrc
# config_out_network = .netrc_prod
config_out_network = .netrc

# 登录认证信息
username = admin
password = 123456
filePath = /usr/local/sbin

# 服务器ssh端口
ssh_port = 22
ssh_port_prod = 27859
# 项目端口
server_port = 8102
