/* ===================================
 * 自定义指令
 * Created by cjking on 2020/05/02.
 * Copyright 2020, Inc.
 * =================================== */
import Vue from 'vue';
import { CopyDirective } from '@/directives/copy';
import { AHrefDirective } from '@/directives/href';
import { LimitDirective } from '@/directives/limit';
import { ResizeDirective } from '@/directives/resize';
import { ImgLazyDirective } from '@/directives/imgLazy';
import { ToStringDirective } from '@/directives/toString';
import { DownloadDirective } from '@/directives/download';
import { DebounceDirective } from '@/directives/debounce';
import { InputFilterDirective } from '@/directives/inputFilter';
import { ClickOutsideDirective } from '@/directives/clickOutside';
import { ExponentialToNumberDirective } from '@/directives/exponential';
import { DialogDragDirective } from '@/directives/dialogDrag';

const initDirectives = () => {
	Vue.use(CopyDirective);
	Vue.use(LimitDirective);
	Vue.use(AHrefDirective);
	Vue.use(ResizeDirective);
	Vue.use(ImgLazyDirective);
	Vue.use(ToStringDirective);
	Vue.use(DownloadDirective);
	Vue.use(DebounceDirective);
	Vue.use(InputFilterDirective);
	Vue.use(ClickOutsideDirective);
	Vue.use(ExponentialToNumberDirective);
	Vue.use(DialogDragDirective);
};

export default initDirectives;
