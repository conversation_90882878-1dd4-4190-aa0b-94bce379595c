<template>
	<a-dropdown
		:style="vStyle"
		:visible="innerVisible"
		:trigger="['contextmenu']"
		:getPopupContainer="getPopupContainer"
	>
		<slot name="title">
			<span></span>
		</slot>
		<template #overlay>
			<slot name="menu">
				<!--<a-menu refs="rightMenuDropdown" @click="({ key: type }) => onContextMenuClick(type)">
					<a-menu-item key="detail">详情</a-menu-item>
					<a-menu-item key="edit">编辑</a-menu-item>
					<a-menu-item key="delete">删除</a-menu-item>
				</a-menu>-->
			</slot>
		</template>
	</a-dropdown>
</template>

<script>
/* ===================================
 * 右键菜单
 * Updated by cjking on 2021/10/26.
 * Copyright 2021, Inc.
 * =================================== */
export default {
	name: 'RightMenu',
	props: {
		visible: {
			type: Boolean,
			required: false,
			default: false,
		},
		vStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
		getPopupContainer: {
			type: Function,
			required: false,
			default: () => document.body,
		},
	},
	data () {
		return {
			innerVisible: false,
		};
	},
	watch: {
		visible: {
			immediate: true,
			handler () {
				this.innerVisible = this.visible;
			},
		},
	},
	methods: {
		// /**
		//  * 右键菜单点击事件
		//  */
		// onContextMenuClick (type) {
		// 	this.$emit('onContextMenuClick', { type });
		// },
	},
};
</script>
