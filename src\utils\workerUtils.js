// Argument validation
const isValidObjectWith = fields => obj =>
	// eslint-disable-next-line no-prototype-builtins
	!!obj && !Array.isArray(obj) && fields.every(field => obj.hasOwnProperty(field));

const isValidAction = obj => {
	return isValidObjectWith(['message', 'func'])(obj) &&
		typeof obj.func === 'function' && typeof obj.message === 'string';
};

const isValidActionsArray = arr => arr.every(isValidAction);

const isValidPostParams = obj => {
	return isValidObjectWith(['message', 'args'])(obj) &&
		Array.isArray(obj.args) && typeof obj.message === 'string';
};

const isValidPostParamsArray = arr => arr.every(isValidPostParams);

const isValidObjectsArray = arr => (fields = []) =>
	arr.every(isValidObjectWith(fields));

const testArray = {
	'actionsArray': arr => isValidActionsArray(arr),
	'arraysArray': arr => arr.every(item => Array.isArray(item)),
	'objectsArray': arr => isValidObjectsArray(arr)(),
	'postParamsArray': arr => isValidPostParamsArray(arr),
	'stringsArray': arr => arr.every(item => typeof item === 'string'),
};

const isValidArg = arg => type => {
	if (type === 'null') return arg === null;
	if (type === 'undefined') return arg === undefined;
	if (type === 'action') return isValidAction(arg);
	if (Array.isArray(arg)) {
		if (type !== 'array' && !testArray[type]) return false;
		if (type === 'array') return true;
		return testArray[type](arg);
	}
	if (arg) return typeof arg === type.toString(); // eslint-disable-line
	return false;
};

const isValid = argument => (types = null) => {
	if (Array.isArray(types)) return types.some(type => isValidArg(argument)(type));
	if (isValidArg(argument)(types)) return true;
	return false;
};

// Argument error builder
const argumentError = ({ expected = '', received, extraInfo = '' }) => {
	try {
		return new TypeError(`${ 'You should provide ' + expected }${ '\n' + extraInfo }${ '\nReceived: ' + JSON.stringify(received) }`);
	} catch (err) {
		if (err.message === 'Converting circular structure to JSON') {
			return new TypeError(`${ 'You should provide ' + expected }${ '\n' + extraInfo }${ '\nReceived a circular structure: ' + received }`);
		}
		throw err;
	}
};

// Response builder
const makeResponse = (work) => `\
	self.onmessage = function(event) {
		const args = event.data.message.args
		if (args) {
			self.postMessage((${ work }).apply(null, args))
			return close()
		}
		self.postMessage((${ work })())
		return close()
	}
`;

const makeResponseByTxt = (scriptUrl) => {
	const content = loadFile(scriptUrl, 'text');
	const workerContent = `${ content }`; // 全部用反引号包起来变成纯文本
	return workerContent;
};

// async Response builder
const makeResponseAsync = (work) => `\
	self.onmessage = async function(event) {
		const args = event.data.message.args;
		if (args) {
			const result = await (${ work }).apply(null, args);
			self.postMessage(result);
			return close();
		}
		const result = await (${ work })();
		self.postMessage(result);
		return close();
	};
`;

/**
 * 加载文件
 * @param url
 * @param responseType arrayBuffer、blob、json、text
 * @return {Promise<any>}
 */
async function loadFile (url, responseType = 'json') {
	try {
		const response = await fetch(url);
		return await response[responseType]();
	} catch (error) {
		// eslint-disable-next-line no-console
		console.log('Request Failed', error);
	}
}

/**
 * 创建 worker
 * @param response
 * @param workerOptions
 * @return {Worker}
 */
const createDisposableWorker = (response, workerOptions = undefined) => {
	const URL = window.URL || window.webkitURL;
	const blob = new Blob([response], { type: 'application/javascript' });
	const objectURL = URL.createObjectURL(blob);
	const worker = new Worker(objectURL, workerOptions);
	worker.post = (message) => new Promise((resolve, reject) => {
		worker.onmessage = event => {
			if (objectURL) URL.revokeObjectURL(objectURL);
			resolve(event.data);
		};
		worker.onerror = e => {
			// eslint-disable-next-line no-console
			console.error(`Error: Line ${ e.lineno } in ${ e.filename }: ${ e.message }`);
			reject(e);
		};
		worker.postMessage({ message });
	});
	return worker;
};

const createDisposableWorker2 = (response, workerOptions = undefined) => {
	const URL = window.URL || window.webkitURL;
	const blob = new Blob([response], { type: 'application/javascript' });
	const objectURL = URL.createObjectURL(blob);
	const worker = new Worker(objectURL, workerOptions);
	worker.post = (message) => new Promise((resolve, reject) => {
		worker.onmessage = event => {
			if (objectURL) URL.revokeObjectURL(objectURL);
			resolve(event.data);
		};
		worker.onerror = e => {
			// eslint-disable-next-line no-console
			console.error(`Error: Line ${ e.lineno } in ${ e.filename }: ${ e.message }`);
			reject(e);
		};
		worker.postMessage({ message });
	});
	return worker;
};

/**
 * 执行 worker
 *
 * eg:
 *  const fun = (arg1) => `${arg1}`;
 *  run(fun, ['Another'], { type: 'module' });
 *
 * @param work
 * @param args
 * @param workerOptions
 * @return {null|Promise<*>}
 */
const run = (work = null, args, workerOptions = undefined) => {
	const validWork = isValid(work)('function');
	const validArgs = isValid(args)(['array', 'undefined']);
	if (validWork && validArgs) {
		const worker = createDisposableWorker(makeResponse(work), workerOptions);
		return worker.post({ args });
	}
	// eslint-disable-next-line no-console
	if (!validWork) console.error(argumentError({ expected: 'a function', received: work }));
	// eslint-disable-next-line no-console
	if (!validArgs) console.error(argumentError({ expected: 'an array', received: args }));
	return null;
};

/**
 * 执行 worker
 *
 * eg:
 *  const fun = async (arg1) => `${arg1}`;
 *  run(fun, ['Another'], { type: 'module' });
 *
 * @param work
 * @param args
 * @param workerOptions
 * @return {null|Promise<*>}
 */
const runAsync = (work = null, args, workerOptions = undefined) => {
	const validWork = isValid(work)('function');
	const validArgs = isValid(args)(['array', 'undefined']);
	if (validWork && validArgs) {
		const worker = createDisposableWorker(makeResponseAsync(work), workerOptions);
		return worker.post({ args });
	}
	// eslint-disable-next-line no-console
	if (!validWork) console.error(argumentError({ expected: 'a function', received: work }));
	// eslint-disable-next-line no-console
	if (!validArgs) console.error(argumentError({ expected: 'an array', received: args }));
	return null;
};

const runAsync2 = (scriptUrl = null, args, workerOptions = undefined) => {
	const validArgs = isValid(args)(['array', 'undefined']);
	if (validArgs) {
		const worker = createDisposableWorker2(makeResponseByTxt(scriptUrl), workerOptions);
		return worker.post({ args });
	}
	// eslint-disable-next-line no-console
	if (!validArgs) console.error(argumentError({ expected: 'an array', received: args }));
	return null;
};

export { run, runAsync, runAsync2, makeResponseByTxt };
