/**
 * Minified by jsDelivr using Terser v5.9.0.
 * Original file: /npm/rhino3dm@7.11.1/rhino3dm.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
var rhino3dm=function(){var e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return"undefined"!=typeof __filename&&(e=e||__filename),function(r){var t,n,o=void 0!==(r=r||{})?r:{};o.ready=new Promise((function(e,r){t=e,n=r}));var i,a={};for(i in o)o.hasOwnProperty(i)&&(a[i]=o[i]);var s=[],u="./this.program",c=!1,f=!1,l=!1,d=!1;c="object"==typeof window,f="function"==typeof importScripts,l="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,d=!c&&!l&&!f;var p,h,m,v,g="";function y(e){return o.locateFile?o.locateFile(e,g):g+e}l?(g=f?require("path").dirname(g)+"/":__dirname+"/",p=function(e,r){return m||(m=require("fs")),v||(v=require("path")),e=v.normalize(e),m.readFileSync(e,r?null:"utf8")},h=function(e){var r=p(e,!0);return r.buffer||(r=new Uint8Array(r)),D(r.buffer),r},process.argv.length>1&&(u=process.argv[1].replace(/\\/g,"/")),s=process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof no))throw e})),process.on("unhandledRejection",ge),function(e){process.exit(e)},o.inspect=function(){return"[Emscripten Module object]"}):d?("undefined"!=typeof read&&(p=function(e){return read(e)}),h=function(e){var r;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(D("object"==typeof(r=read(e,"binary"))),r)},"undefined"!=typeof scriptArgs?s=scriptArgs:void 0!==arguments&&(s=arguments),"function"==typeof quit&&function(e){quit(e)},"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(c||f)&&(f?g=self.location.href:"undefined"!=typeof document&&document.currentScript&&(g=document.currentScript.src),e&&(g=e),g=0!==g.indexOf("blob:")?g.substr(0,g.lastIndexOf("/")+1):"",p=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},f&&(h=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),function(e,r,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):t()},n.onerror=t,n.send(null)});var w=o.print||console.log.bind(console),_=o.printErr||console.warn.bind(console);for(i in a)a.hasOwnProperty(i)&&(o[i]=a[i]);a=null,o.arguments&&(s=o.arguments),o.thisProgram&&(u=o.thisProgram),o.quit&&o.quit;var E=16;function b(e,r){return r||(r=E),Math.ceil(e/r)*r}var k,T,P=function(e){e};o.wasmBinary&&(k=o.wasmBinary),o.noExitRuntime&&o.noExitRuntime,"object"!=typeof WebAssembly&&ge("no native wasm support detected");var F=!1;function D(e,r){e||ge("Assertion failed: "+r)}var C="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function S(e,r,t){for(var n=r+t,o=r;e[o]&&!(o>=n);)++o;if(o-r>16&&e.subarray&&C)return C.decode(e.subarray(r,o));for(var i="";r<o;){var a=e[r++];if(128&a){var s=63&e[r++];if(192!=(224&a)){var u=63&e[r++];if((a=224==(240&a)?(15&a)<<12|s<<6|u:(7&a)<<18|s<<12|u<<6|63&e[r++])<65536)i+=String.fromCharCode(a);else{var c=a-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i}function A(e,r){return e?S(j,e,r):""}function $(e,r,t,n){if(!(n>0))return 0;for(var o=t,i=t+n-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a);if(s<=127){if(t>=i)break;r[t++]=s}else if(s<=2047){if(t+1>=i)break;r[t++]=192|s>>6,r[t++]=128|63&s}else if(s<=65535){if(t+2>=i)break;r[t++]=224|s>>12,r[t++]=128|s>>6&63,r[t++]=128|63&s}else{if(t+3>=i)break;r[t++]=240|s>>18,r[t++]=128|s>>12&63,r[t++]=128|s>>6&63,r[t++]=128|63&s}}return r[t]=0,t-o}function O(e,r,t){return $(e,j,r,t)}function R(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++t)),n<=127?++r:r+=n<=2047?2:n<=65535?3:4}return r}var x,M,j,W,N,B,U,z,I,L="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function H(e,r){for(var t=e,n=t>>1,o=n+r/2;!(n>=o)&&N[n];)++n;if((t=n<<1)-e>32&&L)return L.decode(j.subarray(e,t));for(var i="",a=0;!(a>=r/2);++a){var s=W[e+2*a>>1];if(0==s)break;i+=String.fromCharCode(s)}return i}function q(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,i=0;i<o;++i){var a=e.charCodeAt(i);W[r>>1]=a,r+=2}return W[r>>1]=0,r-n}function V(e){return 2*e.length}function G(e,r){for(var t=0,n="";!(t>=r/4);){var o=B[e+4*t>>2];if(0==o)break;if(++t,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n}function X(e,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var n=r,o=n+t-4,i=0;i<e.length;++i){var a=e.charCodeAt(i);if(a>=55296&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++i);if(B[r>>2]=a,(r+=4)+4>o)break}return B[r>>2]=0,r-n}function Y(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r}function K(e){var r=R(e)+1,t=Qn(r);return t&&$(e,M,t,r),t}function Z(e,r){M.set(e,r)}function J(e,r,t){for(var n=0;n<e.length;++n)M[r++>>0]=e.charCodeAt(n);t||(M[r>>0]=0)}function Q(e,r){return e%r>0&&(e+=r-e%r),e}function ee(e){x=e,o.HEAP8=M=new Int8Array(e),o.HEAP16=W=new Int16Array(e),o.HEAP32=B=new Int32Array(e),o.HEAPU8=j=new Uint8Array(e),o.HEAPU16=N=new Uint16Array(e),o.HEAPU32=U=new Uint32Array(e),o.HEAPF32=z=new Float32Array(e),o.HEAPF64=I=new Float64Array(e)}o.INITIAL_MEMORY;var re,te=[],ne=[],oe=[],ie=[];function ae(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)fe(o.preRun.shift());Se(te)}function se(){!0,o.noFSInit||He.init.initialized||He.init(),ze.init(),Se(ne)}function ue(){He.ignorePermissions=!1,Se(oe)}function ce(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)le(o.postRun.shift());Se(ie)}function fe(e){te.unshift(e)}function le(e){ie.unshift(e)}var de=0,pe=null,he=null;function me(e){de++,o.monitorRunDependencies&&o.monitorRunDependencies(de)}function ve(e){if(de--,o.monitorRunDependencies&&o.monitorRunDependencies(de),0==de&&(null!==pe&&(clearInterval(pe),pe=null),he)){var r=he;he=null,r()}}function ge(e){o.onAbort&&o.onAbort(e),_(e+=""),F=!0,1,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(e);throw n(r),r}function ye(e,r){return String.prototype.startsWith?e.startsWith(r):0===e.indexOf(r)}o.preloadedImages={},o.preloadedAudios={};var we="data:application/octet-stream;base64,";function _e(e){return ye(e,we)}var Ee="file://";function be(e){return ye(e,Ee)}var ke,Te,Pe="rhino3dm.wasm";function Fe(){try{if(k)return new Uint8Array(k);if(h)return h(Pe);throw"both async and sync fetching of the wasm failed"}catch(e){ge(e)}}function De(){return k||!c&&!f||"function"!=typeof fetch||be(Pe)?Promise.resolve().then(Fe):fetch(Pe,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+Pe+"'";return e.arrayBuffer()})).catch((function(){return Fe()}))}function Ce(){var e={env:Zn,wasi_snapshot_preview1:Zn};function r(e,r){var t=e.exports;o.asm=t,ee((T=o.asm.memory).buffer),re=o.asm.__indirect_function_table,ve()}function t(e){r(e.instance)}function i(r){return De().then((function(r){return WebAssembly.instantiate(r,e)})).then(r,(function(e){_("failed to asynchronously prepare wasm: "+e),ge(e)}))}if(me(),o.instantiateWasm)try{return o.instantiateWasm(e,r)}catch(e){return _("Module.instantiateWasm callback failed with error: "+e),!1}return(k||"function"!=typeof WebAssembly.instantiateStreaming||_e(Pe)||be(Pe)||"function"!=typeof fetch?i(t):fetch(Pe,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,e).then(t,(function(e){return _("wasm streaming compile failed: "+e),_("falling back to ArrayBuffer instantiation"),i(t)}))}))).catch(n),{}}function Se(e){for(;e.length>0;){var r=e.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?re.get(t)():re.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(o)}}_e(Pe)||(Pe=y(Pe));var Ae={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function $e(e){return Qn(e+Ae.SIZE)+Ae.SIZE}function Oe(e,r){}function Re(e){this.excPtr=e,this.ptr=e-Ae.SIZE,this.set_type=function(e){B[this.ptr+Ae.TYPE_OFFSET>>2]=e},this.get_type=function(){return B[this.ptr+Ae.TYPE_OFFSET>>2]},this.set_destructor=function(e){B[this.ptr+Ae.DESTRUCTOR_OFFSET>>2]=e},this.get_destructor=function(){return B[this.ptr+Ae.DESTRUCTOR_OFFSET>>2]},this.set_refcount=function(e){B[this.ptr+Ae.REFCOUNT_OFFSET>>2]=e},this.set_caught=function(e){e=e?1:0,M[this.ptr+Ae.CAUGHT_OFFSET>>0]=e},this.get_caught=function(){return 0!=M[this.ptr+Ae.CAUGHT_OFFSET>>0]},this.set_rethrown=function(e){e=e?1:0,M[this.ptr+Ae.RETHROWN_OFFSET>>0]=e},this.get_rethrown=function(){return 0!=M[this.ptr+Ae.RETHROWN_OFFSET>>0]},this.init=function(e,r){this.set_type(e),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=B[this.ptr+Ae.REFCOUNT_OFFSET>>2];B[this.ptr+Ae.REFCOUNT_OFFSET>>2]=e+1},this.release_ref=function(){var e=B[this.ptr+Ae.REFCOUNT_OFFSET>>2];return B[this.ptr+Ae.REFCOUNT_OFFSET>>2]=e-1,1===e}}function xe(e,r,t){throw new Re(e).init(r,t),e,e}function Me(e,r){var t=new Date(1e3*B[e>>2]);B[r>>2]=t.getUTCSeconds(),B[r+4>>2]=t.getUTCMinutes(),B[r+8>>2]=t.getUTCHours(),B[r+12>>2]=t.getUTCDate(),B[r+16>>2]=t.getUTCMonth(),B[r+20>>2]=t.getUTCFullYear()-1900,B[r+24>>2]=t.getUTCDay(),B[r+36>>2]=0,B[r+32>>2]=0;var n=Date.UTC(t.getUTCFullYear(),0,1,0,0,0,0),o=(t.getTime()-n)/864e5|0;return B[r+28>>2]=o,Me.GMTString||(Me.GMTString=K("GMT")),B[r+40>>2]=Me.GMTString,r}function je(e,r){return Me(e,r)}function We(e){return B[to()>>2]=e,e}var Ne={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,r){for(var t=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),t++):t&&(e.splice(n,1),t--)}if(r)for(;t;t--)e.unshift("..");return e},normalize:function(e){var r="/"===e.charAt(0),t="/"===e.substr(-1);return(e=Ne.normalizeArray(e.split("/").filter((function(e){return!!e})),!r).join("/"))||r||(e="."),e&&t&&(e+="/"),(r?"/":"")+e},dirname:function(e){var r=Ne.splitPath(e),t=r[0],n=r[1];return t||n?(n&&(n=n.substr(0,n.length-1)),t+n):"."},basename:function(e){if("/"===e)return"/";var r=(e=(e=Ne.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===r?e:e.substr(r+1)},extname:function(e){return Ne.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return Ne.normalize(e.join("/"))},join2:function(e,r){return Ne.normalize(e+"/"+r)}};function Be(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return function(){return crypto.getRandomValues(e),e[0]}}if(l)try{var r=require("crypto");return function(){return r.randomBytes(1)[0]}}catch(e){}return function(){ge("randomDevice")}}var Ue={resolve:function(){for(var e="",r=!1,t=arguments.length-1;t>=-1&&!r;t--){var n=t>=0?arguments[t]:He.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,r="/"===n.charAt(0)}return(r?"/":"")+(e=Ne.normalizeArray(e.split("/").filter((function(e){return!!e})),!r).join("/"))||"."},relative:function(e,r){function t(e){for(var r=0;r<e.length&&""===e[r];r++);for(var t=e.length-1;t>=0&&""===e[t];t--);return r>t?[]:e.slice(r,t-r+1)}e=Ue.resolve(e).substr(1),r=Ue.resolve(r).substr(1);for(var n=t(e.split("/")),o=t(r.split("/")),i=Math.min(n.length,o.length),a=i,s=0;s<i;s++)if(n[s]!==o[s]){a=s;break}var u=[];for(s=a;s<n.length;s++)u.push("..");return(u=u.concat(o.slice(a))).join("/")}},ze={ttys:[],init:function(){},shutdown:function(){},register:function(e,r){ze.ttys[e]={input:[],output:[],ops:r},He.registerDevice(e,ze.stream_ops)},stream_ops:{open:function(e){var r=ze.ttys[e.node.rdev];if(!r)throw new He.ErrnoError(43);e.tty=r,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.get_char)throw new He.ErrnoError(60);for(var i=0,a=0;a<n;a++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new He.ErrnoError(29)}if(void 0===s&&0===i)throw new He.ErrnoError(6);if(null==s)break;i++,r[t+a]=s}return i&&(e.node.timestamp=Date.now()),i},write:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.put_char)throw new He.ErrnoError(60);try{for(var i=0;i<n;i++)e.tty.ops.put_char(e.tty,r[t+i])}catch(e){throw new He.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){if(!e.input.length){var r=null;if(l){var t=Buffer.alloc?Buffer.alloc(256):new Buffer(256),n=0;try{n=m.readSync(process.stdin.fd,t,0,256,null)}catch(e){if(-1==e.toString().indexOf("EOF"))throw e;n=0}r=n>0?t.slice(0,n).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(r=window.prompt("Input: "))&&(r+="\n"):"function"==typeof readline&&null!==(r=readline())&&(r+="\n");if(!r)return null;e.input=Yn(r,!0)}return e.input.shift()},put_char:function(e,r){null===r||10===r?(w(S(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&e.output.length>0&&(w(S(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,r){null===r||10===r?(_(S(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&e.output.length>0&&(_(S(e.output,0)),e.output=[])}}};function Ie(e){for(var r=b(e,16384),t=Qn(r);e<r;)M[t+e++]=0;return t}var Le={ops_table:null,mount:function(e){return Le.createNode(null,"/",16895,0)},createNode:function(e,r,t,n){if(He.isBlkdev(t)||He.isFIFO(t))throw new He.ErrnoError(63);Le.ops_table||(Le.ops_table={dir:{node:{getattr:Le.node_ops.getattr,setattr:Le.node_ops.setattr,lookup:Le.node_ops.lookup,mknod:Le.node_ops.mknod,rename:Le.node_ops.rename,unlink:Le.node_ops.unlink,rmdir:Le.node_ops.rmdir,readdir:Le.node_ops.readdir,symlink:Le.node_ops.symlink},stream:{llseek:Le.stream_ops.llseek}},file:{node:{getattr:Le.node_ops.getattr,setattr:Le.node_ops.setattr},stream:{llseek:Le.stream_ops.llseek,read:Le.stream_ops.read,write:Le.stream_ops.write,allocate:Le.stream_ops.allocate,mmap:Le.stream_ops.mmap,msync:Le.stream_ops.msync}},link:{node:{getattr:Le.node_ops.getattr,setattr:Le.node_ops.setattr,readlink:Le.node_ops.readlink},stream:{}},chrdev:{node:{getattr:Le.node_ops.getattr,setattr:Le.node_ops.setattr},stream:He.chrdev_stream_ops}});var o=He.createNode(e,r,t,n);return He.isDir(o.mode)?(o.node_ops=Le.ops_table.dir.node,o.stream_ops=Le.ops_table.dir.stream,o.contents={}):He.isFile(o.mode)?(o.node_ops=Le.ops_table.file.node,o.stream_ops=Le.ops_table.file.stream,o.usedBytes=0,o.contents=null):He.isLink(o.mode)?(o.node_ops=Le.ops_table.link.node,o.stream_ops=Le.ops_table.link.stream):He.isChrdev(o.mode)&&(o.node_ops=Le.ops_table.chrdev.node,o.stream_ops=Le.ops_table.chrdev.stream),o.timestamp=Date.now(),e&&(e.contents[r]=o),o},getFileDataAsRegularArray:function(e){if(e.contents&&e.contents.subarray){for(var r=[],t=0;t<e.usedBytes;++t)r.push(e.contents[t]);return r}return e.contents},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,r){var t=e.contents?e.contents.length:0;if(!(t>=r)){r=Math.max(r,t*(t<1048576?2:1.125)>>>0),0!=t&&(r=Math.max(r,256));var n=e.contents;e.contents=new Uint8Array(r),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,r){if(e.usedBytes!=r){if(0==r)return e.contents=null,void(e.usedBytes=0);if(!e.contents||e.contents.subarray){var t=e.contents;return e.contents=new Uint8Array(r),t&&e.contents.set(t.subarray(0,Math.min(r,e.usedBytes))),void(e.usedBytes=r)}if(e.contents||(e.contents=[]),e.contents.length>r)e.contents.length=r;else for(;e.contents.length<r;)e.contents.push(0);e.usedBytes=r}},node_ops:{getattr:function(e){var r={};return r.dev=He.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,He.isDir(e.mode)?r.size=4096:He.isFile(e.mode)?r.size=e.usedBytes:He.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.timestamp),r.mtime=new Date(e.timestamp),r.ctime=new Date(e.timestamp),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp),void 0!==r.size&&Le.resizeFileStorage(e,r.size)},lookup:function(e,r){throw He.genericErrors[44]},mknod:function(e,r,t,n){return Le.createNode(e,r,t,n)},rename:function(e,r,t){if(He.isDir(e.mode)){var n;try{n=He.lookupNode(r,t)}catch(e){}if(n)for(var o in n.contents)throw new He.ErrnoError(55)}delete e.parent.contents[e.name],e.name=t,r.contents[t]=e,e.parent=r},unlink:function(e,r){delete e.contents[r]},rmdir:function(e,r){var t=He.lookupNode(e,r);for(var n in t.contents)throw new He.ErrnoError(55);delete e.contents[r]},readdir:function(e){var r=[".",".."];for(var t in e.contents)e.contents.hasOwnProperty(t)&&r.push(t);return r},symlink:function(e,r,t){var n=Le.createNode(e,r,41471,0);return n.link=t,n},readlink:function(e){if(!He.isLink(e.mode))throw new He.ErrnoError(28);return e.link}},stream_ops:{read:function(e,r,t,n,o){var i=e.node.contents;if(o>=e.node.usedBytes)return 0;var a=Math.min(e.node.usedBytes-o,n);if(a>8&&i.subarray)r.set(i.subarray(o,o+a),t);else for(var s=0;s<a;s++)r[t+s]=i[o+s];return a},write:function(e,r,t,n,o,i){if(r.buffer===M.buffer&&(i=!1),!n)return 0;var a=e.node;if(a.timestamp=Date.now(),r.subarray&&(!a.contents||a.contents.subarray)){if(i)return a.contents=r.subarray(t,t+n),a.usedBytes=n,n;if(0===a.usedBytes&&0===o)return a.contents=r.slice(t,t+n),a.usedBytes=n,n;if(o+n<=a.usedBytes)return a.contents.set(r.subarray(t,t+n),o),n}if(Le.expandFileStorage(a,o+n),a.contents.subarray&&r.subarray)a.contents.set(r.subarray(t,t+n),o);else for(var s=0;s<n;s++)a.contents[o+s]=r[t+s];return a.usedBytes=Math.max(a.usedBytes,o+n),n},llseek:function(e,r,t){var n=r;if(1===t?n+=e.position:2===t&&He.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new He.ErrnoError(28);return n},allocate:function(e,r,t){Le.expandFileStorage(e.node,r+t),e.node.usedBytes=Math.max(e.node.usedBytes,r+t)},mmap:function(e,r,t,n,o,i){if(D(0===r),!He.isFile(e.node.mode))throw new He.ErrnoError(43);var a,s,u=e.node.contents;if(2&i||u.buffer!==x){if((n>0||n+t<u.length)&&(u=u.subarray?u.subarray(n,n+t):Array.prototype.slice.call(u,n,n+t)),s=!0,!(a=Ie(t)))throw new He.ErrnoError(48);M.set(u,a)}else s=!1,a=u.byteOffset;return{ptr:a,allocated:s}},msync:function(e,r,t,n,o){if(!He.isFile(e.node.mode))throw new He.ErrnoError(43);if(2&o)return 0;Le.stream_ops.write(e,r,0,n,t,!1);return 0}}},He={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e,r){if(r=r||{},!(e=Ue.resolve(He.cwd(),e)))return{path:"",node:null};var t={follow_mount:!0,recurse_count:0};for(var n in t)void 0===r[n]&&(r[n]=t[n]);if(r.recurse_count>8)throw new He.ErrnoError(32);for(var o=Ne.normalizeArray(e.split("/").filter((function(e){return!!e})),!1),i=He.root,a="/",s=0;s<o.length;s++){var u=s===o.length-1;if(u&&r.parent)break;if(i=He.lookupNode(i,o[s]),a=Ne.join2(a,o[s]),He.isMountpoint(i)&&(!u||u&&r.follow_mount)&&(i=i.mounted.root),!u||r.follow)for(var c=0;He.isLink(i.mode);){var f=He.readlink(a);if(a=Ue.resolve(Ne.dirname(a),f),i=He.lookupPath(a,{recurse_count:r.recurse_count}).node,c++>40)throw new He.ErrnoError(32)}}return{path:a,node:i}},getPath:function(e){for(var r;;){if(He.isRoot(e)){var t=e.mount.mountpoint;return r?"/"!==t[t.length-1]?t+"/"+r:t+r:t}r=r?e.name+"/"+r:e.name,e=e.parent}},hashName:function(e,r){for(var t=0,n=0;n<r.length;n++)t=(t<<5)-t+r.charCodeAt(n)|0;return(e+t>>>0)%He.nameTable.length},hashAddNode:function(e){var r=He.hashName(e.parent.id,e.name);e.name_next=He.nameTable[r],He.nameTable[r]=e},hashRemoveNode:function(e){var r=He.hashName(e.parent.id,e.name);if(He.nameTable[r]===e)He.nameTable[r]=e.name_next;else for(var t=He.nameTable[r];t;){if(t.name_next===e){t.name_next=e.name_next;break}t=t.name_next}},lookupNode:function(e,r){var t=He.mayLookup(e);if(t)throw new He.ErrnoError(t,e);for(var n=He.hashName(e.id,r),o=He.nameTable[n];o;o=o.name_next){var i=o.name;if(o.parent.id===e.id&&i===r)return o}return He.lookup(e,r)},createNode:function(e,r,t,n){var o=new He.FSNode(e,r,t,n);return He.hashAddNode(o),o},destroyNode:function(e){He.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return 49152==(49152&e)},flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:function(e){var r=He.flagModes[e];if(void 0===r)throw new Error("Unknown file open mode: "+e);return r},flagsToPermissionString:function(e){var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:function(e,r){return He.ignorePermissions||(-1===r.indexOf("r")||292&e.mode)&&(-1===r.indexOf("w")||146&e.mode)&&(-1===r.indexOf("x")||73&e.mode)?0:2},mayLookup:function(e){var r=He.nodePermissions(e,"x");return r||(e.node_ops.lookup?0:2)},mayCreate:function(e,r){try{He.lookupNode(e,r);return 20}catch(e){}return He.nodePermissions(e,"wx")},mayDelete:function(e,r,t){var n;try{n=He.lookupNode(e,r)}catch(e){return e.errno}var o=He.nodePermissions(e,"wx");if(o)return o;if(t){if(!He.isDir(n.mode))return 54;if(He.isRoot(n)||He.getPath(n)===He.cwd())return 10}else if(He.isDir(n.mode))return 31;return 0},mayOpen:function(e,r){return e?He.isLink(e.mode)?32:He.isDir(e.mode)&&("r"!==He.flagsToPermissionString(r)||512&r)?31:He.nodePermissions(e,He.flagsToPermissionString(r)):44},MAX_OPEN_FDS:4096,nextfd:function(e,r){e=e||0,r=r||He.MAX_OPEN_FDS;for(var t=e;t<=r;t++)if(!He.streams[t])return t;throw new He.ErrnoError(33)},getStream:function(e){return He.streams[e]},createStream:function(e,r,t){He.FSStream||(He.FSStream=function(){},He.FSStream.prototype={object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}});var n=new He.FSStream;for(var o in e)n[o]=e[o];e=n;var i=He.nextfd(r,t);return e.fd=i,He.streams[i]=e,e},closeStream:function(e){He.streams[e]=null},chrdev_stream_ops:{open:function(e){var r=He.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new He.ErrnoError(70)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,r){return e<<8|r},registerDevice:function(e,r){He.devices[e]={stream_ops:r}},getDevice:function(e){return He.devices[e]},getMounts:function(e){for(var r=[],t=[e];t.length;){var n=t.pop();r.push(n),t.push.apply(t,n.mounts)}return r},syncfs:function(e,r){"function"==typeof e&&(r=e,e=!1),He.syncFSRequests++,He.syncFSRequests>1&&_("warning: "+He.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var t=He.getMounts(He.root.mount),n=0;function o(e){return He.syncFSRequests--,r(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,o(e));++n>=t.length&&o(null)}t.forEach((function(r){if(!r.type.syncfs)return i(null);r.type.syncfs(r,e,i)}))},mount:function(e,r,t){var n,o="/"===t,i=!t;if(o&&He.root)throw new He.ErrnoError(10);if(!o&&!i){var a=He.lookupPath(t,{follow_mount:!1});if(t=a.path,n=a.node,He.isMountpoint(n))throw new He.ErrnoError(10);if(!He.isDir(n.mode))throw new He.ErrnoError(54)}var s={type:e,opts:r,mountpoint:t,mounts:[]},u=e.mount(s);return u.mount=s,s.root=u,o?He.root=u:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),u},unmount:function(e){var r=He.lookupPath(e,{follow_mount:!1});if(!He.isMountpoint(r.node))throw new He.ErrnoError(28);var t=r.node,n=t.mounted,o=He.getMounts(n);Object.keys(He.nameTable).forEach((function(e){for(var r=He.nameTable[e];r;){var t=r.name_next;-1!==o.indexOf(r.mount)&&He.destroyNode(r),r=t}})),t.mounted=null;var i=t.mount.mounts.indexOf(n);t.mount.mounts.splice(i,1)},lookup:function(e,r){return e.node_ops.lookup(e,r)},mknod:function(e,r,t){var n=He.lookupPath(e,{parent:!0}).node,o=Ne.basename(e);if(!o||"."===o||".."===o)throw new He.ErrnoError(28);var i=He.mayCreate(n,o);if(i)throw new He.ErrnoError(i);if(!n.node_ops.mknod)throw new He.ErrnoError(63);return n.node_ops.mknod(n,o,r,t)},create:function(e,r){return r=void 0!==r?r:438,r&=4095,r|=32768,He.mknod(e,r,0)},mkdir:function(e,r){return r=void 0!==r?r:511,r&=1023,r|=16384,He.mknod(e,r,0)},mkdirTree:function(e,r){for(var t=e.split("/"),n="",o=0;o<t.length;++o)if(t[o]){n+="/"+t[o];try{He.mkdir(n,r)}catch(e){if(20!=e.errno)throw e}}},mkdev:function(e,r,t){return void 0===t&&(t=r,r=438),r|=8192,He.mknod(e,r,t)},symlink:function(e,r){if(!Ue.resolve(e))throw new He.ErrnoError(44);var t=He.lookupPath(r,{parent:!0}).node;if(!t)throw new He.ErrnoError(44);var n=Ne.basename(r),o=He.mayCreate(t,n);if(o)throw new He.ErrnoError(o);if(!t.node_ops.symlink)throw new He.ErrnoError(63);return t.node_ops.symlink(t,n,e)},rename:function(e,r){var t,n,o=Ne.dirname(e),i=Ne.dirname(r),a=Ne.basename(e),s=Ne.basename(r);if(t=He.lookupPath(e,{parent:!0}).node,n=He.lookupPath(r,{parent:!0}).node,!t||!n)throw new He.ErrnoError(44);if(t.mount!==n.mount)throw new He.ErrnoError(75);var u,c=He.lookupNode(t,a),f=Ue.relative(e,i);if("."!==f.charAt(0))throw new He.ErrnoError(28);if("."!==(f=Ue.relative(r,o)).charAt(0))throw new He.ErrnoError(55);try{u=He.lookupNode(n,s)}catch(e){}if(c!==u){var l=He.isDir(c.mode),d=He.mayDelete(t,a,l);if(d)throw new He.ErrnoError(d);if(d=u?He.mayDelete(n,s,l):He.mayCreate(n,s))throw new He.ErrnoError(d);if(!t.node_ops.rename)throw new He.ErrnoError(63);if(He.isMountpoint(c)||u&&He.isMountpoint(u))throw new He.ErrnoError(10);if(n!==t&&(d=He.nodePermissions(t,"w")))throw new He.ErrnoError(d);try{He.trackingDelegate.willMovePath&&He.trackingDelegate.willMovePath(e,r)}catch(t){_("FS.trackingDelegate['willMovePath']('"+e+"', '"+r+"') threw an exception: "+t.message)}He.hashRemoveNode(c);try{t.node_ops.rename(c,n,s)}catch(e){throw e}finally{He.hashAddNode(c)}try{He.trackingDelegate.onMovePath&&He.trackingDelegate.onMovePath(e,r)}catch(t){_("FS.trackingDelegate['onMovePath']('"+e+"', '"+r+"') threw an exception: "+t.message)}}},rmdir:function(e){var r=He.lookupPath(e,{parent:!0}).node,t=Ne.basename(e),n=He.lookupNode(r,t),o=He.mayDelete(r,t,!0);if(o)throw new He.ErrnoError(o);if(!r.node_ops.rmdir)throw new He.ErrnoError(63);if(He.isMountpoint(n))throw new He.ErrnoError(10);try{He.trackingDelegate.willDeletePath&&He.trackingDelegate.willDeletePath(e)}catch(r){_("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+r.message)}r.node_ops.rmdir(r,t),He.destroyNode(n);try{He.trackingDelegate.onDeletePath&&He.trackingDelegate.onDeletePath(e)}catch(r){_("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+r.message)}},readdir:function(e){var r=He.lookupPath(e,{follow:!0}).node;if(!r.node_ops.readdir)throw new He.ErrnoError(54);return r.node_ops.readdir(r)},unlink:function(e){var r=He.lookupPath(e,{parent:!0}).node,t=Ne.basename(e),n=He.lookupNode(r,t),o=He.mayDelete(r,t,!1);if(o)throw new He.ErrnoError(o);if(!r.node_ops.unlink)throw new He.ErrnoError(63);if(He.isMountpoint(n))throw new He.ErrnoError(10);try{He.trackingDelegate.willDeletePath&&He.trackingDelegate.willDeletePath(e)}catch(r){_("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+r.message)}r.node_ops.unlink(r,t),He.destroyNode(n);try{He.trackingDelegate.onDeletePath&&He.trackingDelegate.onDeletePath(e)}catch(r){_("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+r.message)}},readlink:function(e){var r=He.lookupPath(e).node;if(!r)throw new He.ErrnoError(44);if(!r.node_ops.readlink)throw new He.ErrnoError(28);return Ue.resolve(He.getPath(r.parent),r.node_ops.readlink(r))},stat:function(e,r){var t=He.lookupPath(e,{follow:!r}).node;if(!t)throw new He.ErrnoError(44);if(!t.node_ops.getattr)throw new He.ErrnoError(63);return t.node_ops.getattr(t)},lstat:function(e){return He.stat(e,!0)},chmod:function(e,r,t){var n;"string"==typeof e?n=He.lookupPath(e,{follow:!t}).node:n=e;if(!n.node_ops.setattr)throw new He.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&r|-4096&n.mode,timestamp:Date.now()})},lchmod:function(e,r){He.chmod(e,r,!0)},fchmod:function(e,r){var t=He.getStream(e);if(!t)throw new He.ErrnoError(8);He.chmod(t.node,r)},chown:function(e,r,t,n){var o;"string"==typeof e?o=He.lookupPath(e,{follow:!n}).node:o=e;if(!o.node_ops.setattr)throw new He.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:function(e,r,t){He.chown(e,r,t,!0)},fchown:function(e,r,t){var n=He.getStream(e);if(!n)throw new He.ErrnoError(8);He.chown(n.node,r,t)},truncate:function(e,r){if(r<0)throw new He.ErrnoError(28);var t;"string"==typeof e?t=He.lookupPath(e,{follow:!0}).node:t=e;if(!t.node_ops.setattr)throw new He.ErrnoError(63);if(He.isDir(t.mode))throw new He.ErrnoError(31);if(!He.isFile(t.mode))throw new He.ErrnoError(28);var n=He.nodePermissions(t,"w");if(n)throw new He.ErrnoError(n);t.node_ops.setattr(t,{size:r,timestamp:Date.now()})},ftruncate:function(e,r){var t=He.getStream(e);if(!t)throw new He.ErrnoError(8);if(0==(2097155&t.flags))throw new He.ErrnoError(28);He.truncate(t.node,r)},utime:function(e,r,t){var n=He.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(r,t)})},open:function(e,r,t,n,i){if(""===e)throw new He.ErrnoError(44);var a;if(t=void 0===t?438:t,t=64&(r="string"==typeof r?He.modeStringToFlags(r):r)?4095&t|32768:0,"object"==typeof e)a=e;else{e=Ne.normalize(e);try{a=He.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var s=!1;if(64&r)if(a){if(128&r)throw new He.ErrnoError(20)}else a=He.mknod(e,t,0),s=!0;if(!a)throw new He.ErrnoError(44);if(He.isChrdev(a.mode)&&(r&=-513),65536&r&&!He.isDir(a.mode))throw new He.ErrnoError(54);if(!s){var u=He.mayOpen(a,r);if(u)throw new He.ErrnoError(u)}512&r&&He.truncate(a,0),r&=-131713;var c=He.createStream({node:a,path:He.getPath(a),flags:r,seekable:!0,position:0,stream_ops:a.stream_ops,ungotten:[],error:!1},n,i);c.stream_ops.open&&c.stream_ops.open(c),!o.logReadFiles||1&r||(He.readFiles||(He.readFiles={}),e in He.readFiles||(He.readFiles[e]=1,_("FS.trackingDelegate error on read file: "+e)));try{if(He.trackingDelegate.onOpenFile){var f=0;1!=(2097155&r)&&(f|=He.tracking.openFlags.READ),0!=(2097155&r)&&(f|=He.tracking.openFlags.WRITE),He.trackingDelegate.onOpenFile(e,f)}}catch(r){_("FS.trackingDelegate['onOpenFile']('"+e+"', flags) threw an exception: "+r.message)}return c},close:function(e){if(He.isClosed(e))throw new He.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{He.closeStream(e.fd)}e.fd=null},isClosed:function(e){return null===e.fd},llseek:function(e,r,t){if(He.isClosed(e))throw new He.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new He.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new He.ErrnoError(28);return e.position=e.stream_ops.llseek(e,r,t),e.ungotten=[],e.position},read:function(e,r,t,n,o){if(n<0||o<0)throw new He.ErrnoError(28);if(He.isClosed(e))throw new He.ErrnoError(8);if(1==(2097155&e.flags))throw new He.ErrnoError(8);if(He.isDir(e.node.mode))throw new He.ErrnoError(31);if(!e.stream_ops.read)throw new He.ErrnoError(28);var i=void 0!==o;if(i){if(!e.seekable)throw new He.ErrnoError(70)}else o=e.position;var a=e.stream_ops.read(e,r,t,n,o);return i||(e.position+=a),a},write:function(e,r,t,n,o,i){if(n<0||o<0)throw new He.ErrnoError(28);if(He.isClosed(e))throw new He.ErrnoError(8);if(0==(2097155&e.flags))throw new He.ErrnoError(8);if(He.isDir(e.node.mode))throw new He.ErrnoError(31);if(!e.stream_ops.write)throw new He.ErrnoError(28);e.seekable&&1024&e.flags&&He.llseek(e,0,2);var a=void 0!==o;if(a){if(!e.seekable)throw new He.ErrnoError(70)}else o=e.position;var s=e.stream_ops.write(e,r,t,n,o,i);a||(e.position+=s);try{e.path&&He.trackingDelegate.onWriteToFile&&He.trackingDelegate.onWriteToFile(e.path)}catch(r){_("FS.trackingDelegate['onWriteToFile']('"+e.path+"') threw an exception: "+r.message)}return s},allocate:function(e,r,t){if(He.isClosed(e))throw new He.ErrnoError(8);if(r<0||t<=0)throw new He.ErrnoError(28);if(0==(2097155&e.flags))throw new He.ErrnoError(8);if(!He.isFile(e.node.mode)&&!He.isDir(e.node.mode))throw new He.ErrnoError(43);if(!e.stream_ops.allocate)throw new He.ErrnoError(138);e.stream_ops.allocate(e,r,t)},mmap:function(e,r,t,n,o,i){if(0!=(2&o)&&0==(2&i)&&2!=(2097155&e.flags))throw new He.ErrnoError(2);if(1==(2097155&e.flags))throw new He.ErrnoError(2);if(!e.stream_ops.mmap)throw new He.ErrnoError(43);return e.stream_ops.mmap(e,r,t,n,o,i)},msync:function(e,r,t,n,o){return e&&e.stream_ops.msync?e.stream_ops.msync(e,r,t,n,o):0},munmap:function(e){return 0},ioctl:function(e,r,t){if(!e.stream_ops.ioctl)throw new He.ErrnoError(59);return e.stream_ops.ioctl(e,r,t)},readFile:function(e,r){if((r=r||{}).flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var t,n=He.open(e,r.flags),o=He.stat(e).size,i=new Uint8Array(o);return He.read(n,i,0,o,0),"utf8"===r.encoding?t=S(i,0):"binary"===r.encoding&&(t=i),He.close(n),t},writeFile:function(e,r,t){(t=t||{}).flags=t.flags||577;var n=He.open(e,t.flags,t.mode);if("string"==typeof r){var o=new Uint8Array(R(r)+1),i=$(r,o,0,o.length);He.write(n,o,0,i,void 0,t.canOwn)}else{if(!ArrayBuffer.isView(r))throw new Error("Unsupported data type");He.write(n,r,0,r.byteLength,void 0,t.canOwn)}He.close(n)},cwd:function(){return He.currentPath},chdir:function(e){var r=He.lookupPath(e,{follow:!0});if(null===r.node)throw new He.ErrnoError(44);if(!He.isDir(r.node.mode))throw new He.ErrnoError(54);var t=He.nodePermissions(r.node,"x");if(t)throw new He.ErrnoError(t);He.currentPath=r.path},createDefaultDirectories:function(){He.mkdir("/tmp"),He.mkdir("/home"),He.mkdir("/home/<USER>")},createDefaultDevices:function(){He.mkdir("/dev"),He.registerDevice(He.makedev(1,3),{read:function(){return 0},write:function(e,r,t,n,o){return n}}),He.mkdev("/dev/null",He.makedev(1,3)),ze.register(He.makedev(5,0),ze.default_tty_ops),ze.register(He.makedev(6,0),ze.default_tty1_ops),He.mkdev("/dev/tty",He.makedev(5,0)),He.mkdev("/dev/tty1",He.makedev(6,0));var e=Be();He.createDevice("/dev","random",e),He.createDevice("/dev","urandom",e),He.mkdir("/dev/shm"),He.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){He.mkdir("/proc"),He.mkdir("/proc/self"),He.mkdir("/proc/self/fd"),He.mount({mount:function(){var e=He.createNode("/proc/self","fd",16895,73);return e.node_ops={lookup:function(e,r){var t=+r,n=He.getStream(t);if(!n)throw new He.ErrnoError(8);var o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return o.parent=o,o}},e}},{},"/proc/self/fd")},createStandardStreams:function(){o.stdin?He.createDevice("/dev","stdin",o.stdin):He.symlink("/dev/tty","/dev/stdin"),o.stdout?He.createDevice("/dev","stdout",null,o.stdout):He.symlink("/dev/tty","/dev/stdout"),o.stderr?He.createDevice("/dev","stderr",null,o.stderr):He.symlink("/dev/tty1","/dev/stderr");He.open("/dev/stdin",0),He.open("/dev/stdout",1),He.open("/dev/stderr",1)},ensureErrnoError:function(){He.ErrnoError||(He.ErrnoError=function(e,r){this.node=r,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},He.ErrnoError.prototype=new Error,He.ErrnoError.prototype.constructor=He.ErrnoError,[44].forEach((function(e){He.genericErrors[e]=new He.ErrnoError(e),He.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:function(){He.ensureErrnoError(),He.nameTable=new Array(4096),He.mount(Le,{},"/"),He.createDefaultDirectories(),He.createDefaultDevices(),He.createSpecialDirectories(),He.filesystems={MEMFS:Le}},init:function(e,r,t){He.init.initialized=!0,He.ensureErrnoError(),o.stdin=e||o.stdin,o.stdout=r||o.stdout,o.stderr=t||o.stderr,He.createStandardStreams()},quit:function(){He.init.initialized=!1;var e=o._fflush;e&&e(0);for(var r=0;r<He.streams.length;r++){var t=He.streams[r];t&&He.close(t)}},getMode:function(e,r){var t=0;return e&&(t|=365),r&&(t|=146),t},findObject:function(e,r){var t=He.analyzePath(e,r);return t.exists?t.object:null},analyzePath:function(e,r){try{e=(n=He.lookupPath(e,{follow:!r})).path}catch(e){}var t={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=He.lookupPath(e,{parent:!0});t.parentExists=!0,t.parentPath=n.path,t.parentObject=n.node,t.name=Ne.basename(e),n=He.lookupPath(e,{follow:!r}),t.exists=!0,t.path=n.path,t.object=n.node,t.name=n.node.name,t.isRoot="/"===n.path}catch(e){t.error=e.errno}return t},createPath:function(e,r,t,n){e="string"==typeof e?e:He.getPath(e);for(var o=r.split("/").reverse();o.length;){var i=o.pop();if(i){var a=Ne.join2(e,i);try{He.mkdir(a)}catch(e){}e=a}}return a},createFile:function(e,r,t,n,o){var i=Ne.join2("string"==typeof e?e:He.getPath(e),r),a=He.getMode(n,o);return He.create(i,a)},createDataFile:function(e,r,t,n,o,i){var a=r?Ne.join2("string"==typeof e?e:He.getPath(e),r):e,s=He.getMode(n,o),u=He.create(a,s);if(t){if("string"==typeof t){for(var c=new Array(t.length),f=0,l=t.length;f<l;++f)c[f]=t.charCodeAt(f);t=c}He.chmod(u,146|s);var d=He.open(u,577);He.write(d,t,0,t.length,0,i),He.close(d),He.chmod(u,s)}return u},createDevice:function(e,r,t,n){var o=Ne.join2("string"==typeof e?e:He.getPath(e),r),i=He.getMode(!!t,!!n);He.createDevice.major||(He.createDevice.major=64);var a=He.makedev(He.createDevice.major++,0);return He.registerDevice(a,{open:function(e){e.seekable=!1},close:function(e){n&&n.buffer&&n.buffer.length&&n(10)},read:function(e,r,n,o,i){for(var a=0,s=0;s<o;s++){var u;try{u=t()}catch(e){throw new He.ErrnoError(29)}if(void 0===u&&0===a)throw new He.ErrnoError(6);if(null==u)break;a++,r[n+s]=u}return a&&(e.node.timestamp=Date.now()),a},write:function(e,r,t,o,i){for(var a=0;a<o;a++)try{n(r[t+a])}catch(e){throw new He.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),a}}),He.mkdev(o,i,a)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!p)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=Yn(p(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new He.ErrnoError(29)}},createLazyFile:function(e,r,t,n,o){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var r=e%this.chunkSize,t=e/this.chunkSize|0;return this.getter(t)[r]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",t,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+t+". Status: "+e.status);var r,n=Number(e.getResponseHeader("Content-length")),o=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,i=(r=e.getResponseHeader("Content-Encoding"))&&"gzip"===r,a=1048576;o||(a=n);var s=this;s.setDataGetter((function(e){var r=e*a,o=(e+1)*a-1;if(o=Math.min(o,n-1),void 0===s.chunks[e]&&(s.chunks[e]=function(e,r){if(e>r)throw new Error("invalid range ("+e+", "+r+") or no bytes requested!");if(r>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",t,!1),n!==a&&o.setRequestHeader("Range","bytes="+e+"-"+r),"undefined"!=typeof Uint8Array&&(o.responseType="arraybuffer"),o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+t+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):Yn(o.responseText||"",!0)}(r,o)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!i&&n||(a=n=1,n=this.getter(0).length,a=n,w("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=a,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!f)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new i;Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:a}}else s={isDevice:!1,url:t};var u=He.createFile(e,r,s,n,o);s.contents?u.contents=s.contents:s.url&&(u.contents=null,u.url=s.url),Object.defineProperties(u,{usedBytes:{get:function(){return this.contents.length}}});var c={};return Object.keys(u.stream_ops).forEach((function(e){var r=u.stream_ops[e];c[e]=function(){return He.forceLoadFile(u),r.apply(null,arguments)}})),c.read=function(e,r,t,n,o){He.forceLoadFile(u);var i=e.node.contents;if(o>=i.length)return 0;var a=Math.min(i.length-o,n);if(i.slice)for(var s=0;s<a;s++)r[t+s]=i[o+s];else for(s=0;s<a;s++)r[t+s]=i.get(o+s);return a},u.stream_ops=c,u},createPreloadedFile:function(e,r,t,n,i,a,s,u,c,f){Browser.init();var l=r?Ue.resolve(Ne.join2(e,r)):e;function d(t){function d(t){f&&f(),u||He.createDataFile(e,r,t,n,i,c),a&&a(),ve()}var p=!1;o.preloadPlugins.forEach((function(e){p||e.canHandle(l)&&(e.handle(t,l,d,(function(){s&&s(),ve()})),p=!0)})),p||d(t)}me(),"string"==typeof t?Browser.asyncLoad(t,(function(e){d(e)}),s):d(t)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(e,r,t){r=r||function(){},t=t||function(){};var n=He.indexedDB();try{var o=n.open(He.DB_NAME(),He.DB_VERSION)}catch(e){return t(e)}o.onupgradeneeded=function(){w("creating db"),o.result.createObjectStore(He.DB_STORE_NAME)},o.onsuccess=function(){var n=o.result.transaction([He.DB_STORE_NAME],"readwrite"),i=n.objectStore(He.DB_STORE_NAME),a=0,s=0,u=e.length;function c(){0==s?r():t()}e.forEach((function(e){var r=i.put(He.analyzePath(e).object.contents,e);r.onsuccess=function(){++a+s==u&&c()},r.onerror=function(){s++,a+s==u&&c()}})),n.onerror=t},o.onerror=t},loadFilesFromDB:function(e,r,t){r=r||function(){},t=t||function(){};var n=He.indexedDB();try{var o=n.open(He.DB_NAME(),He.DB_VERSION)}catch(e){return t(e)}o.onupgradeneeded=t,o.onsuccess=function(){var n=o.result;try{var i=n.transaction([He.DB_STORE_NAME],"readonly")}catch(e){return void t(e)}var a=i.objectStore(He.DB_STORE_NAME),s=0,u=0,c=e.length;function f(){0==u?r():t()}e.forEach((function(e){var r=a.get(e);r.onsuccess=function(){He.analyzePath(e).exists&&He.unlink(e),He.createDataFile(Ne.dirname(e),Ne.basename(e),r.result,!0,!0,!0),++s+u==c&&f()},r.onerror=function(){u++,s+u==c&&f()}})),i.onerror=t},o.onerror=t}},qe={mappings:{},DEFAULT_POLLMASK:5,umask:511,calculateAt:function(e,r){if("/"!==r[0]){var t;if(-100===e)t=He.cwd();else{var n=He.getStream(e);if(!n)throw new He.ErrnoError(8);t=n.path}r=Ne.join2(t,r)}return r},doStat:function(e,r,t){try{var n=e(r)}catch(e){if(e&&e.node&&Ne.normalize(r)!==Ne.normalize(He.getPath(e.node)))return-54;throw e}return B[t>>2]=n.dev,B[t+4>>2]=0,B[t+8>>2]=n.ino,B[t+12>>2]=n.mode,B[t+16>>2]=n.nlink,B[t+20>>2]=n.uid,B[t+24>>2]=n.gid,B[t+28>>2]=n.rdev,B[t+32>>2]=0,Te=[n.size>>>0,(ke=n.size,+Math.abs(ke)>=1?ke>0?(0|Math.min(+Math.floor(ke/4294967296),4294967295))>>>0:~~+Math.ceil((ke-+(~~ke>>>0))/4294967296)>>>0:0)],B[t+40>>2]=Te[0],B[t+44>>2]=Te[1],B[t+48>>2]=4096,B[t+52>>2]=n.blocks,B[t+56>>2]=n.atime.getTime()/1e3|0,B[t+60>>2]=0,B[t+64>>2]=n.mtime.getTime()/1e3|0,B[t+68>>2]=0,B[t+72>>2]=n.ctime.getTime()/1e3|0,B[t+76>>2]=0,Te=[n.ino>>>0,(ke=n.ino,+Math.abs(ke)>=1?ke>0?(0|Math.min(+Math.floor(ke/4294967296),4294967295))>>>0:~~+Math.ceil((ke-+(~~ke>>>0))/4294967296)>>>0:0)],B[t+80>>2]=Te[0],B[t+84>>2]=Te[1],0},doMsync:function(e,r,t,n,o){var i=j.slice(e,e+t);He.msync(r,i,o,t,n)},doMkdir:function(e,r){return"/"===(e=Ne.normalize(e))[e.length-1]&&(e=e.substr(0,e.length-1)),He.mkdir(e,r,0),0},doMknod:function(e,r,t){switch(61440&r){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}return He.mknod(e,r,t),0},doReadlink:function(e,r,t){if(t<=0)return-28;var n=He.readlink(e),o=Math.min(t,R(n)),i=M[r+o];return O(n,r,t+1),M[r+o]=i,o},doAccess:function(e,r){if(-8&r)return-28;var t;if(!(t=He.lookupPath(e,{follow:!0}).node))return-44;var n="";return 4&r&&(n+="r"),2&r&&(n+="w"),1&r&&(n+="x"),n&&He.nodePermissions(t,n)?-2:0},doDup:function(e,r,t){var n=He.getStream(t);return n&&He.close(n),He.open(e,r,0,t,t).fd},doReadv:function(e,r,t,n){for(var o=0,i=0;i<t;i++){var a=B[r+8*i>>2],s=B[r+(8*i+4)>>2],u=He.read(e,M,a,s,n);if(u<0)return-1;if(o+=u,u<s)break}return o},doWritev:function(e,r,t,n){for(var o=0,i=0;i<t;i++){var a=B[r+8*i>>2],s=B[r+(8*i+4)>>2],u=He.write(e,M,a,s,n);if(u<0)return-1;o+=u}return o},varargs:void 0,get:function(){return qe.varargs+=4,B[qe.varargs-4>>2]},getStr:function(e){return A(e)},getStreamFromFD:function(e){var r=He.getStream(e);if(!r)throw new He.ErrnoError(8);return r},get64:function(e,r){return e}};function Ve(e,r,t){qe.varargs=t;try{var n=qe.getStreamFromFD(e);switch(r){case 0:return(o=qe.get())<0?-28:He.open(n.path,n.flags,0,o).fd;case 1:case 2:case 13:case 14:return 0;case 3:return n.flags;case 4:var o=qe.get();return n.flags|=o,0;case 12:o=qe.get();return W[o+0>>1]=2,0;default:return-28;case 9:return We(28),-1}}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),-e.errno}}function Ge(e,r){try{var t=qe.getStreamFromFD(e);return qe.doStat(He.stat,t.path,r)}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),-e.errno}}function Xe(e,r,t){qe.varargs=t;try{var n=qe.getStreamFromFD(e);switch(r){case 21509:case 21505:case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:case 21523:case 21524:return n.tty?0:-59;case 21519:if(!n.tty)return-59;var o=qe.get();return B[o>>2]=0,0;case 21520:return n.tty?-28:-59;case 21531:o=qe.get();return He.ioctl(n,r,o);default:ge("bad ioctl syscall "+r)}}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),-e.errno}}function Ye(e,r,t){qe.varargs=t;try{var n=qe.getStr(e),o=qe.get();return He.open(n,r,o).fd}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),-e.errno}}function Ke(e,r){try{return e=qe.getStr(e),qe.doStat(He.stat,e,r)}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),-e.errno}}var Ze={};function Je(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function Qe(e){return this.fromWireType(U[e>>2])}var er={},rr={},tr={},nr=48,or=57;function ir(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=nr&&r<=or?"_"+e:e}function ar(e,r){return e=ir(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function sr(e,r){var t=ar(r,(function(e){this.name=r,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var ur=void 0;function cr(e){throw new ur(e)}function fr(e,r,t){function n(r){var n=t(r);n.length!==e.length&&cr("Mismatched type converter count");for(var o=0;o<e.length;++o)_r(e[o],n[o])}e.forEach((function(e){tr[e]=r}));var o=new Array(r.length),i=[],a=0;r.forEach((function(e,r){rr.hasOwnProperty(e)?o[r]=rr[e]:(i.push(e),er.hasOwnProperty(e)||(er[e]=[]),er[e].push((function(){o[r]=rr[e],++a===i.length&&n(o)})))})),0===i.length&&n(o)}function lr(e){var r=Ze[e];delete Ze[e];var t=r.elements,n=t.length,o=t.map((function(e){return e.getterReturnType})).concat(t.map((function(e){return e.setterArgumentType}))),i=r.rawConstructor,a=r.rawDestructor;fr([e],o,(function(e){return t.forEach((function(r,t){var o=e[t],i=r.getter,a=r.getterContext,s=e[t+n],u=r.setter,c=r.setterContext;r.read=function(e){return o.fromWireType(i(a,e))},r.write=function(e,r){var t=[];u(c,e,s.toWireType(t,r)),Je(t)}})),[{name:r.name,fromWireType:function(e){for(var r=new Array(n),o=0;o<n;++o)r[o]=t[o].read(e);return a(e),r},toWireType:function(e,o){if(n!==o.length)throw new TypeError("Incorrect number of tuple elements for "+r.name+": expected="+n+", actual="+o.length);for(var s=i(),u=0;u<n;++u)t[u].write(s,o[u]);return null!==e&&e.push(a,s),s},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:a}]}))}var dr={};function pr(e){var r=dr[e];delete dr[e];var t=r.rawConstructor,n=r.rawDestructor,o=r.fields;fr([e],o.map((function(e){return e.getterReturnType})).concat(o.map((function(e){return e.setterArgumentType}))),(function(e){var i={};return o.forEach((function(r,t){var n=r.fieldName,a=e[t],s=r.getter,u=r.getterContext,c=e[t+o.length],f=r.setter,l=r.setterContext;i[n]={read:function(e){return a.fromWireType(s(u,e))},write:function(e,r){var t=[];f(l,e,c.toWireType(t,r)),Je(t)}}})),[{name:r.name,fromWireType:function(e){var r={};for(var t in i)r[t]=i[t].read(e);return n(e),r},toWireType:function(e,r){for(var o in i)if(!(o in r))throw new TypeError('Missing field:  "'+o+'"');var a=t();for(o in i)i[o].write(a,r[o]);return null!==e&&e.push(n,a),a},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:n}]}))}function hr(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}function mr(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);vr=e}var vr=void 0;function gr(e){for(var r="",t=e;j[t];)r+=vr[j[t++]];return r}var yr=void 0;function wr(e){throw new yr(e)}function _r(e,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=r.name;if(e||wr('type "'+n+'" must have a positive integer typeid pointer'),rr.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;wr("Cannot register type '"+n+"' twice")}if(rr[e]=r,delete tr[e],er.hasOwnProperty(e)){var o=er[e];delete er[e],o.forEach((function(e){e()}))}}function Er(e,r,t,n,o){var i=hr(t);_r(e,{name:r=gr(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===t)n=M;else if(2===t)n=W;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);n=B}return this.fromWireType(n[e>>i])},destructorFunction:null})}function br(e){if(!(this instanceof Nr))return!1;if(!(e instanceof Nr))return!1;for(var r=this.$$.ptrType.registeredClass,t=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o}function kr(e){return{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}function Tr(e){wr(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Pr=!1;function Fr(e){}function Dr(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}function Cr(e){e.count.value-=1,0===e.count.value&&Dr(e)}function Sr(e){return"undefined"==typeof FinalizationGroup?(Sr=function(e){return e},e):(Pr=new FinalizationGroup((function(e){for(var r=e.next();!r.done;r=e.next()){var t=r.value;t.ptr?Cr(t):console.warn("object already deleted: "+t.ptr)}})),Sr=function(e){return Pr.register(e,e.$$,e.$$),e},Fr=function(e){Pr.unregister(e.$$)},Sr(e))}function Ar(){if(this.$$.ptr||Tr(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=Sr(Object.create(Object.getPrototypeOf(this),{$$:{value:kr(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function $r(){this.$$.ptr||Tr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&wr("Object already scheduled for deletion"),Fr(this),Cr(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Or(){return!this.$$.ptr}var Rr=void 0,xr=[];function Mr(){for(;xr.length;){var e=xr.pop();e.$$.deleteScheduled=!1,e.delete()}}function jr(){return this.$$.ptr||Tr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&wr("Object already scheduled for deletion"),xr.push(this),1===xr.length&&Rr&&Rr(Mr),this.$$.deleteScheduled=!0,this}function Wr(){Nr.prototype.isAliasOf=br,Nr.prototype.clone=Ar,Nr.prototype.delete=$r,Nr.prototype.isDeleted=Or,Nr.prototype.deleteLater=jr}function Nr(){}var Br={};function Ur(e,r,t){if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||wr("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}}function zr(e,r,t){o.hasOwnProperty(e)?((void 0===t||void 0!==o[e].overloadTable&&void 0!==o[e].overloadTable[t])&&wr("Cannot register public name '"+e+"' twice"),Ur(o,e,e),o.hasOwnProperty(t)&&wr("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),o[e].overloadTable[t]=r):(o[e]=r,void 0!==t&&(o[e].numArguments=t))}function Ir(e,r,t,n,o,i,a,s){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function Lr(e,r,t){for(;r!==t;)r.upcast||wr("Expected null or instance of "+t.name+", got an instance of "+r.name),e=r.upcast(e),r=r.baseClass;return e}function Hr(e,r){if(null===r)return this.isReference&&wr("null is not a valid "+this.name),0;r.$$||wr('Cannot pass "'+Wt(r)+'" as a '+this.name),r.$$.ptr||wr("Cannot pass deleted object as a pointer of type "+this.name);var t=r.$$.ptrType.registeredClass;return Lr(r.$$.ptr,t,this.registeredClass)}function qr(e,r){var t;if(null===r)return this.isReference&&wr("null is not a valid "+this.name),this.isSmartPointer?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r.$$||wr('Cannot pass "'+Wt(r)+'" as a '+this.name),r.$$.ptr||wr("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&r.$$.ptrType.isConst&&wr("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);var n=r.$$.ptrType.registeredClass;if(t=Lr(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&wr("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:wr("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var o=r.clone();t=this.rawShare(t,$t((function(){o.delete()}))),null!==e&&e.push(this.rawDestructor,t)}break;default:wr("Unsupporting sharing policy")}return t}function Vr(e,r){if(null===r)return this.isReference&&wr("null is not a valid "+this.name),0;r.$$||wr('Cannot pass "'+Wt(r)+'" as a '+this.name),r.$$.ptr||wr("Cannot pass deleted object as a pointer of type "+this.name),r.$$.ptrType.isConst&&wr("Cannot convert argument of type "+r.$$.ptrType.name+" to parameter type "+this.name);var t=r.$$.ptrType.registeredClass;return Lr(r.$$.ptr,t,this.registeredClass)}function Gr(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function Xr(e){this.rawDestructor&&this.rawDestructor(e)}function Yr(e){null!==e&&e.delete()}function Kr(e,r,t){if(r===t)return e;if(void 0===t.baseClass)return null;var n=Kr(e,r,t.baseClass);return null===n?null:t.downcast(n)}function Zr(){return Object.keys(rt).length}function Jr(){var e=[];for(var r in rt)rt.hasOwnProperty(r)&&e.push(rt[r]);return e}function Qr(e){Rr=e,xr.length&&Rr&&Rr(Mr)}function et(){o.getInheritedInstanceCount=Zr,o.getLiveInheritedInstances=Jr,o.flushPendingDeletes=Mr,o.setDelayFunction=Qr}var rt={};function tt(e,r){for(void 0===r&&wr("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r}function nt(e,r){return r=tt(e,r),rt[r]}function ot(e,r){return r.ptrType&&r.ptr||cr("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!==!!r.smartPtr&&cr("Both smartPtrType and smartPtr must be specified"),r.count={value:1},Sr(Object.create(e,{$$:{value:r}}))}function it(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;var t=nt(this.registeredClass,r);if(void 0!==t){if(0===t.$$.count.value)return t.$$.ptr=r,t.$$.smartPtr=e,t.clone();var n=t.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?ot(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):ot(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,a=this.registeredClass.getActualType(r),s=Br[a];if(!s)return o.call(this);i=this.isConst?s.constPointerType:s.pointerType;var u=Kr(r,this.registeredClass,i.registeredClass);return null===u?o.call(this):this.isSmartPointer?ot(i.registeredClass.instancePrototype,{ptrType:i,ptr:u,smartPtrType:this,smartPtr:e}):ot(i.registeredClass.instancePrototype,{ptrType:i,ptr:u})}function at(){st.prototype.getPointee=Gr,st.prototype.destructor=Xr,st.prototype.argPackAdvance=8,st.prototype.readValueFromPointer=Qe,st.prototype.deleteObject=Yr,st.prototype.fromWireType=it}function st(e,r,t,n,o,i,a,s,u,c,f){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=f,o||void 0!==r.baseClass?this.toWireType=qr:n?(this.toWireType=Hr,this.destructorFunction=null):(this.toWireType=Vr,this.destructorFunction=null)}function ut(e,r,t){o.hasOwnProperty(e)||cr("Replacing nonexistant public symbol"),void 0!==o[e].overloadTable&&void 0!==t?o[e].overloadTable[t]=r:(o[e]=r,o[e].argCount=t)}function ct(e,r,t){return t&&t.length?o["dynCall_"+e].apply(null,[r].concat(t)):o["dynCall_"+e].call(null,r)}function ft(e,r,t){return-1!=e.indexOf("j")?ct(e,r,t):re.get(r).apply(null,t)}function lt(e,r){D(e.indexOf("j")>=0,"getDynCaller should only be called with i64 sigs");var t=[];return function(){t.length=arguments.length;for(var n=0;n<arguments.length;n++)t[n]=arguments[n];return ft(e,r,t)}}function dt(e,r){var t=-1!=(e=gr(e)).indexOf("j")?lt(e,r):re.get(r);return"function"!=typeof t&&wr("unknown function pointer with signature "+e+": "+r),t}var pt=void 0;function ht(e){var r=ro(e),t=gr(r);return eo(r),t}function mt(e,r){var t=[],n={};throw r.forEach((function e(r){n[r]||rr[r]||(tr[r]?tr[r].forEach(e):(t.push(r),n[r]=!0))})),new pt(e+": "+t.map(ht).join([", "]))}function vt(e,r,t,n,o,i,a,s,u,c,f,l,d){f=gr(f),i=dt(o,i),s&&(s=dt(a,s)),c&&(c=dt(u,c)),d=dt(l,d);var p=ir(f);zr(p,(function(){mt("Cannot construct "+f+" due to unbound types",[n])})),fr([e,r,t],n?[n]:[],(function(r){var t,o;r=r[0],o=n?(t=r.registeredClass).instancePrototype:Nr.prototype;var a=ar(p,(function(){if(Object.getPrototypeOf(this)!==u)throw new yr("Use 'new' to construct "+f);if(void 0===l.constructor_body)throw new yr(f+" has no accessible constructor");var e=l.constructor_body[arguments.length];if(void 0===e)throw new yr("Tried to invoke ctor of "+f+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(l.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),u=Object.create(o,{constructor:{value:a}});a.prototype=u;var l=new Ir(f,a,u,d,t,i,s,c),h=new st(f,l,!0,!1,!1),m=new st(f+"*",l,!1,!1,!1),v=new st(f+" const*",l,!1,!0,!1);return Br[e]={pointerType:m,constPointerType:v},ut(p,a),[h,m,v]}))}function gt(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=ar(e.name||"unknownFunctionName",(function(){}));t.prototype=e.prototype;var n=new t,o=e.apply(n,r);return o instanceof Object?o:n}function yt(e,r,t,n,o){var i=r.length;i<2&&wr("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==r[1]&&null!==t,s=!1,u=1;u<r.length;++u)if(null!==r[u]&&void 0===r[u].destructorFunction){s=!0;break}var c="void"!==r[0].name,f="",l="";for(u=0;u<i-2;++u)f+=(0!==u?", ":"")+"arg"+u,l+=(0!==u?", ":"")+"arg"+u+"Wired";var d="return function "+ir(e)+"("+f+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";s&&(d+="var destructors = [];\n");var p=s?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[wr,n,o,Je,r[0],r[1]];a&&(d+="var thisWired = classParam.toWireType("+p+", this);\n");for(u=0;u<i-2;++u)d+="var arg"+u+"Wired = argType"+u+".toWireType("+p+", arg"+u+"); // "+r[u+2].name+"\n",h.push("argType"+u),m.push(r[u+2]);if(a&&(l="thisWired"+(l.length>0?", ":"")+l),d+=(c?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",s)d+="runDestructors(destructors);\n";else for(u=a?1:2;u<r.length;++u){var v=1===u?"thisWired":"arg"+(u-2)+"Wired";null!==r[u].destructorFunction&&(d+=v+"_dtor("+v+"); // "+r[u].name+"\n",h.push(v+"_dtor"),m.push(r[u].destructorFunction))}return c&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",h.push(d),gt(Function,h).apply(null,m)}function wt(e,r){for(var t=[],n=0;n<e;n++)t.push(B[(r>>2)+n]);return t}function _t(e,r,t,n,o,i,a){var s=wt(t,n);r=gr(r),i=dt(o,i),fr([],[e],(function(e){var n=(e=e[0]).name+"."+r;function o(){mt("Cannot call "+n+" due to unbound types",s)}var u=e.registeredClass.constructor;return void 0===u[r]?(o.argCount=t-1,u[r]=o):(Ur(u,r,n),u[r].overloadTable[t-1]=o),fr([],s,(function(e){var o=[e[0],null].concat(e.slice(1)),s=yt(n,o,null,i,a);return void 0===u[r].overloadTable?(s.argCount=t-1,u[r]=s):u[r].overloadTable[t-1]=s,[]})),[]}))}function Et(e,r,t,n,o,i){D(r>0);var a=wt(r,t);o=dt(n,o);var s=[i],u=[];fr([],[e],(function(e){var t="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[r-1])throw new yr("Cannot register multiple constructors with identical number of parameters ("+(r-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[r-1]=function(){mt("Cannot construct "+e.name+" due to unbound types",a)},fr([],a,(function(n){return e.registeredClass.constructor_body[r-1]=function(){arguments.length!==r-1&&wr(t+" called with "+arguments.length+" arguments, expected "+(r-1)),u.length=0,s.length=r;for(var e=1;e<r;++e)s[e]=n[e].toWireType(u,arguments[e-1]);var i=o.apply(null,s);return Je(u),n[0].fromWireType(i)},[]})),[]}))}function bt(e,r,t,n,o,i,a,s){var u=wt(t,n);r=gr(r),i=dt(o,i),fr([],[e],(function(e){var n=(e=e[0]).name+"."+r;function o(){mt("Cannot call "+n+" due to unbound types",u)}s&&e.registeredClass.pureVirtualFunctions.push(r);var c=e.registeredClass.instancePrototype,f=c[r];return void 0===f||void 0===f.overloadTable&&f.className!==e.name&&f.argCount===t-2?(o.argCount=t-2,o.className=e.name,c[r]=o):(Ur(c,r,n),c[r].overloadTable[t-2]=o),fr([],u,(function(o){var s=yt(n,o,e,i,a);return void 0===c[r].overloadTable?(s.argCount=t-2,c[r]=s):c[r].overloadTable[t-2]=s,[]})),[]}))}function kt(e,r,t){return e instanceof Object||wr(t+' with invalid "this": '+e),e instanceof r.registeredClass.constructor||wr(t+' incompatible with "this" of type '+e.constructor.name),e.$$.ptr||wr("cannot call emscripten binding method "+t+" on deleted object"),Lr(e.$$.ptr,e.$$.ptrType.registeredClass,r.registeredClass)}function Tt(e,r,t,n,o,i,a,s,u,c){r=gr(r),o=dt(n,o),fr([],[e],(function(e){var n=(e=e[0]).name+"."+r,f={get:function(){mt("Cannot access "+n+" due to unbound types",[t,a])},enumerable:!0,configurable:!0};return f.set=u?function(){mt("Cannot access "+n+" due to unbound types",[t,a])}:function(e){wr(n+" is a read-only property")},Object.defineProperty(e.registeredClass.instancePrototype,r,f),fr([],u?[t,a]:[t],(function(t){var a=t[0],f={get:function(){var r=kt(this,e,n+" getter");return a.fromWireType(o(i,r))},enumerable:!0};if(u){u=dt(s,u);var l=t[1];f.set=function(r){var t=kt(this,e,n+" setter"),o=[];u(c,t,l.toWireType(o,r)),Je(o)}}return Object.defineProperty(e.registeredClass.instancePrototype,r,f),[]})),[]}))}var Pt=[],Ft=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Dt(e){e>4&&0==--Ft[e].refcount&&(Ft[e]=void 0,Pt.push(e))}function Ct(){for(var e=0,r=5;r<Ft.length;++r)void 0!==Ft[r]&&++e;return e}function St(){for(var e=5;e<Ft.length;++e)if(void 0!==Ft[e])return Ft[e];return null}function At(){o.count_emval_handles=Ct,o.get_first_emval=St}function $t(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=Pt.length?Pt.pop():Ft.length;return Ft[r]={refcount:1,value:e},r}}function Ot(e,r){_r(e,{name:r=gr(r),fromWireType:function(e){var r=Ft[e].value;return Dt(e),r},toWireType:function(e,r){return $t(r)},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:null})}function Rt(e,r,t){switch(r){case 0:return function(e){var r=t?M:j;return this.fromWireType(r[e])};case 1:return function(e){var r=t?W:N;return this.fromWireType(r[e>>1])};case 2:return function(e){var r=t?B:U;return this.fromWireType(r[e>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function xt(e,r,t,n){var o=hr(t);function i(){}r=gr(r),i.values={},_r(e,{name:r,constructor:i,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,r){return r.value},argPackAdvance:8,readValueFromPointer:Rt(r,o,n),destructorFunction:null}),zr(r,i)}function Mt(e,r){var t=rr[e];return void 0===t&&wr(r+" has unknown type "+ht(e)),t}function jt(e,r,t){var n=Mt(e,"enum");r=gr(r);var o=n.constructor,i=Object.create(n.constructor.prototype,{value:{value:t},constructor:{value:ar(n.name+"_"+r,(function(){}))}});o.values[t]=i,o[r]=i}function Wt(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function Nt(e,r){switch(r){case 2:return function(e){return this.fromWireType(z[e>>2])};case 3:return function(e){return this.fromWireType(I[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Bt(e,r,t){var n=hr(t);_r(e,{name:r=gr(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Wt(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Nt(r,n),destructorFunction:null})}function Ut(e,r,t){switch(r){case 0:return t?function(e){return M[e]}:function(e){return j[e]};case 1:return t?function(e){return W[e>>1]}:function(e){return N[e>>1]};case 2:return t?function(e){return B[e>>2]}:function(e){return U[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function zt(e,r,t,n,o){r=gr(r),-1===o&&(o=4294967295);var i=hr(t),a=function(e){return e};if(0===n){var s=32-8*t;a=function(e){return e<<s>>>s}}var u=-1!=r.indexOf("unsigned");_r(e,{name:r,fromWireType:a,toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Wt(t)+'" to '+this.name);if(t<n||t>o)throw new TypeError('Passing a number "'+Wt(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+n+", "+o+"]!");return u?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:Ut(r,i,0!==n),destructorFunction:null})}function It(e,r,t){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(e){var r=U,t=r[e>>=2],o=r[e+1];return new n(x,o,t)}_r(e,{name:t=gr(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})}function Lt(e,r){var t="std::string"===(r=gr(r));_r(e,{name:r,fromWireType:function(e){var r,n=U[e>>2];if(t)for(var o=e+4,i=0;i<=n;++i){var a=e+4+i;if(i==n||0==j[a]){var s=A(o,a-o);void 0===r?r=s:(r+=String.fromCharCode(0),r+=s),o=a+1}}else{var u=new Array(n);for(i=0;i<n;++i)u[i]=String.fromCharCode(j[e+4+i]);r=u.join("")}return eo(e),r},toWireType:function(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var n="string"==typeof r;n||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||wr("Cannot pass non-string to std::string");var o=(t&&n?function(){return R(r)}:function(){return r.length})(),i=Qn(4+o+1);if(U[i>>2]=o,t&&n)O(r,i+4,o+1);else if(n)for(var a=0;a<o;++a){var s=r.charCodeAt(a);s>255&&(eo(i),wr("String has UTF-16 code units that do not fit in 8 bits")),j[i+4+a]=s}else for(a=0;a<o;++a)j[i+4+a]=r[a];return null!==e&&e.push(eo,i),i},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){eo(e)}})}function Ht(e,r,t){var n,o,i,a,s;t=gr(t),2===r?(n=H,o=q,a=V,i=function(){return N},s=1):4===r&&(n=G,o=X,a=Y,i=function(){return U},s=2),_r(e,{name:t,fromWireType:function(e){for(var t,o=U[e>>2],a=i(),u=e+4,c=0;c<=o;++c){var f=e+4+c*r;if(c==o||0==a[f>>s]){var l=n(u,f-u);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),u=f+r}}return eo(e),t},toWireType:function(e,n){"string"!=typeof n&&wr("Cannot pass non-string to C++ string type "+t);var i=a(n),u=Qn(4+i+r);return U[u>>2]=i>>s,o(n,u+4,i+r),null!==e&&e.push(eo,u),u},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){eo(e)}})}function qt(e,r,t,n,o,i){Ze[e]={name:gr(r),rawConstructor:dt(t,n),rawDestructor:dt(o,i),elements:[]}}function Vt(e,r,t,n,o,i,a,s,u){Ze[e].elements.push({getterReturnType:r,getter:dt(t,n),getterContext:o,setterArgumentType:i,setter:dt(a,s),setterContext:u})}function Gt(e,r,t,n,o,i){dr[e]={name:gr(r),rawConstructor:dt(t,n),rawDestructor:dt(o,i),fields:[]}}function Xt(e,r,t,n,o,i,a,s,u,c){dr[e].fields.push({fieldName:gr(r),getterReturnType:t,getter:dt(n,o),getterContext:i,setterArgumentType:a,setter:dt(s,u),setterContext:c})}function Yt(e,r){_r(e,{isVoid:!0,name:r=gr(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})}function Kt(e){return e||wr("Cannot use deleted val. handle = "+e),Ft[e].value}function Zt(e,r,t){e=Kt(e),r=Mt(r,"emval::as");var n=[],o=$t(n);return B[t>>2]=o,r.toWireType(n,e)}function Jt(e){var r=[];return B[e>>2]=$t(r),r}var Qt={};function en(e){var r=Qt[e];return void 0===r?gr(e):r}var rn=[];function tn(e,r,t,n,o){return(e=rn[e])(r=Kt(r),t=en(t),Jt(n),o)}function nn(e,r,t,n){(e=rn[e])(r=Kt(r),t=en(t),null,n)}function on(e,r){return(e=Kt(e))==(r=Kt(r))}function an(){return"object"==typeof globalThis?globalThis:Function("return this")()}function sn(e){return 0===e?$t(an()):(e=en(e),$t(an()[e]))}function un(e){var r=rn.length;return rn.push(e),r}function cn(e,r){for(var t=new Array(e),n=0;n<e;++n)t[n]=Mt(B[(r>>2)+n],"parameter "+n);return t}function fn(e,r){for(var t=cn(e,r),n=t[0],o=n.name+"_$"+t.slice(1).map((function(e){return e.name})).join("_")+"$",i=["retType"],a=[n],s="",u=0;u<e-1;++u)s+=(0!==u?", ":"")+"arg"+u,i.push("argType"+u),a.push(t[1+u]);var c="return function "+ir("methodCaller_"+o)+"(handle, name, destructors, args) {\n",f=0;for(u=0;u<e-1;++u)c+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(f?"+"+f:"")+");\n",f+=t[u+1].argPackAdvance;c+="    var rv = handle[name]("+s+");\n";for(u=0;u<e-1;++u)t[u+1].deleteObject&&(c+="    argType"+u+".deleteObject(arg"+u+");\n");return n.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",i.push(c),un(gt(Function,i).apply(null,a))}function ln(e){return e=en(e),$t(o[e])}function dn(e,r){return $t((e=Kt(e))[r=Kt(r)])}function pn(e){e>4&&(Ft[e].refcount+=1)}function hn(e,r){return(e=Kt(e))instanceof(r=Kt(r))}function mn(e){return"number"==typeof(e=Kt(e))}function vn(e){return"string"==typeof(e=Kt(e))}function gn(e){for(var r="",t=0;t<e;++t)r+=(0!==t?", ":"")+"arg"+t;var n="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n";for(t=0;t<e;++t)n+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return n+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",n)(Mt,o,$t)}var yn,wn={};function _n(e,r,t,n){e=Kt(e);var o=wn[r];return o||(o=gn(r),wn[r]=o),o(e,t,n)}function En(){return $t([])}function bn(e){return $t(en(e))}function kn(){return $t({})}function Tn(e){Je(Ft[e].value),Dt(e)}function Pn(e,r,t){e=Kt(e),r=Kt(r),t=Kt(t),e[r]=t}function Fn(e,r){return $t((e=Mt(e,"_emval_take_value")).readValueFromPointer(r))}function Dn(){ge()}function Cn(e,r,t){j.copyWithin(e,r,r+t)}function Sn(){return j.length}function An(e){try{return T.grow(e-x.byteLength+65535>>>16),ee(T.buffer),1}catch(e){}}function $n(e){e>>>=0;var r=Sn(),t=2147483648;if(e>t)return!1;for(var n=1;n<=4;n*=2){var o=r*(1+.2/n);if(o=Math.min(o,e+100663296),An(Math.min(t,Q(Math.max(16777216,e,o),65536))))return!0}return!1}function On(e){for(var r=yn();yn()-r<e;);}yn=l?function(){var e=process.hrtime();return 1e3*e[0]+e[1]/1e6}:"undefined"!=typeof dateNow?dateNow:function(){return performance.now()};var Rn={};function xn(){return u||"./this.program"}function Mn(){if(!Mn.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:xn()};for(var r in Rn)e[r]=Rn[r];var t=[];for(var r in e)t.push(r+"="+e[r]);Mn.strings=t}return Mn.strings}function jn(e,r){try{var t=0;return Mn().forEach((function(n,o){var i=r+t;B[e+4*o>>2]=i,J(n,i),t+=n.length+1})),0}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),e.errno}}function Wn(e,r){try{var t=Mn();B[e>>2]=t.length;var n=0;return t.forEach((function(e){n+=e.length+1})),B[r>>2]=n,0}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),e.errno}}function Nn(e){try{var r=qe.getStreamFromFD(e);return He.close(r),0}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),e.errno}}function Bn(e,r){try{var t=qe.getStreamFromFD(e),n=t.tty?2:He.isDir(t.mode)?3:He.isLink(t.mode)?7:4;return M[r>>0]=n,0}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),e.errno}}function Un(e,r,t,n){try{var o=qe.getStreamFromFD(e),i=qe.doReadv(o,r,t);return B[n>>2]=i,0}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),e.errno}}function zn(e,r,t,n,o){try{var i=qe.getStreamFromFD(e),a=4294967296*t+(r>>>0),s=9007199254740992;return a<=-s||a>=s?-61:(He.llseek(i,a,n),Te=[i.position>>>0,(ke=i.position,+Math.abs(ke)>=1?ke>0?(0|Math.min(+Math.floor(ke/4294967296),4294967295))>>>0:~~+Math.ceil((ke-+(~~ke>>>0))/4294967296)>>>0:0)],B[o>>2]=Te[0],B[o+4>>2]=Te[1],i.getdents&&0===a&&0===n&&(i.getdents=null),0)}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),e.errno}}function In(e,r,t,n){try{var o=qe.getStreamFromFD(e),i=qe.doWritev(o,r,t);return B[n>>2]=i,0}catch(e){return void 0!==He&&e instanceof He.ErrnoError||ge(e),e.errno}}function Ln(e){P(0|e)}function Hn(e){var r=Date.now()/1e3|0;return e&&(B[e>>2]=r),r}function qn(e){var r=null;if(l)try{r=(0,require("crypto").randomBytes)(16)}catch(e){}else c&&void 0!==window.crypto&&void 0!==window.crypto.getRandomValues&&(r=new Uint8Array(16),window.crypto.getRandomValues(r));if(!r){r=new Array(16);for(var t=(new Date).getTime(),n=0;n<16;n++){var o=(t+256*Math.random())%256|0;t=t/256|0,r[n]=o}}r[6]=15&r[6]|64,r[8]=127&r[8]|128,Z(r,e)}var Vn=function(e,r,t,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=He.nextInode++,this.name=r,this.mode=t,this.node_ops={},this.stream_ops={},this.rdev=n},Gn=365,Xn=146;Object.defineProperties(Vn.prototype,{read:{get:function(){return(this.mode&Gn)===Gn},set:function(e){e?this.mode|=Gn:this.mode&=~Gn}},write:{get:function(){return(this.mode&Xn)===Xn},set:function(e){e?this.mode|=Xn:this.mode&=~Xn}},isFolder:{get:function(){return He.isDir(this.mode)}},isDevice:{get:function(){return He.isChrdev(this.mode)}}}),He.FSNode=Vn,He.staticInit(),ur=o.InternalError=sr(Error,"InternalError"),mr(),yr=o.BindingError=sr(Error,"BindingError"),Wr(),at(),et(),pt=o.UnboundTypeError=sr(Error,"UnboundTypeError"),At();function Yn(e,r,t){var n=t>0?t:R(e)+1,o=new Array(n),i=$(e,o,0,o.length);return r&&(o.length=i),o}ne.push({func:function(){Jn()}});var Kn,Zn={__cxa_allocate_exception:$e,__cxa_atexit:Oe,__cxa_throw:xe,__gmtime_r:je,__sys_fcntl64:Ve,__sys_fstat64:Ge,__sys_ioctl:Xe,__sys_open:Ye,__sys_stat64:Ke,_embind_finalize_value_array:lr,_embind_finalize_value_object:pr,_embind_register_bool:Er,_embind_register_class:vt,_embind_register_class_class_function:_t,_embind_register_class_constructor:Et,_embind_register_class_function:bt,_embind_register_class_property:Tt,_embind_register_emval:Ot,_embind_register_enum:xt,_embind_register_enum_value:jt,_embind_register_float:Bt,_embind_register_integer:zt,_embind_register_memory_view:It,_embind_register_std_string:Lt,_embind_register_std_wstring:Ht,_embind_register_value_array:qt,_embind_register_value_array_element:Vt,_embind_register_value_object:Gt,_embind_register_value_object_field:Xt,_embind_register_void:Yt,_emval_as:Zt,_emval_call_method:tn,_emval_call_void_method:nn,_emval_decref:Dt,_emval_equals:on,_emval_get_global:sn,_emval_get_method_caller:fn,_emval_get_module_property:ln,_emval_get_property:dn,_emval_incref:pn,_emval_instanceof:hn,_emval_is_number:mn,_emval_is_string:vn,_emval_new:_n,_emval_new_array:En,_emval_new_cstring:bn,_emval_new_object:kn,_emval_run_destructors:Tn,_emval_set_property:Pn,_emval_take_value:Fn,abort:Dn,emscripten_memcpy_big:Cn,emscripten_resize_heap:$n,emscripten_thread_sleep:On,environ_get:jn,environ_sizes_get:Wn,fd_close:Nn,fd_fdstat_get:Bn,fd_read:Un,fd_seek:zn,fd_write:In,setTempRet0:Ln,time:Hn,uuid_generate:qn},Jn=(Ce(),o.___wasm_call_ctors=function(){return(Jn=o.___wasm_call_ctors=o.asm.__wasm_call_ctors).apply(null,arguments)}),Qn=o._malloc=function(){return(Qn=o._malloc=o.asm.malloc).apply(null,arguments)},eo=o._free=function(){return(eo=o._free=o.asm.free).apply(null,arguments)},ro=o.___getTypeName=function(){return(ro=o.___getTypeName=o.asm.__getTypeName).apply(null,arguments)},to=(o.___embind_register_native_and_builtin_types=function(){return(o.___embind_register_native_and_builtin_types=o.asm.__embind_register_native_and_builtin_types).apply(null,arguments)},o.___errno_location=function(){return(to=o.___errno_location=o.asm.__errno_location).apply(null,arguments)});o.stackSave=function(){return(o.stackSave=o.asm.stackSave).apply(null,arguments)},o.stackRestore=function(){return(o.stackRestore=o.asm.stackRestore).apply(null,arguments)},o.stackAlloc=function(){return(o.stackAlloc=o.asm.stackAlloc).apply(null,arguments)},o._setThrew=function(){return(o._setThrew=o.asm.setThrew).apply(null,arguments)},o.dynCall_ji=function(){return(o.dynCall_ji=o.asm.dynCall_ji).apply(null,arguments)},o.dynCall_jiji=function(){return(o.dynCall_jiji=o.asm.dynCall_jiji).apply(null,arguments)};function no(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function oo(e){function r(){Kn||(Kn=!0,o.calledRun=!0,F||(se(),ue(),t(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),ce()))}e=e||s,de>0||(ae(),de>0||(o.setStatus?(o.setStatus("Running..."),setTimeout((function(){setTimeout((function(){o.setStatus("")}),1),r()}),1)):r()))}if(he=function e(){Kn||oo(),Kn||(he=e)},o.run=oo,o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return!0,oo(),r.ready}}();"object"==typeof exports&&"object"==typeof module?module.exports=rhino3dm:"function"==typeof define&&define.amd?define([],(function(){return rhino3dm})):"object"==typeof exports&&(exports.rhino3dm=rhino3dm);
//# sourceMappingURL=/sm/1d31374848d50c9134c2ee031f5485d0d38f1b3e7c09092a2c43a1768f7ebe1c.map
