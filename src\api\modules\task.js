/* ===================================
 * 任务相关API
 * Created by cjking on 2022/02/23.
 * Copyright 2022, Inc.
 * =================================== */
import * as http from '@/http';

/**
 * 离散文件
 * @param {Object} params
 * @return {Promise}
 */
export const makeDispersed = (params) => http.post(`/task/sysTask/makeDispersed`, params, {
	headers: { 'Content-Type': 'multipart/form-data' },
	loading: true,
});

/**
 * 按面名离散线
 * @param {Object} params
 * @return {Promise}
 */
export const dispersedLinesByFaceName = (params) => http.post(`/task/sysTask/makeDispersedByFace`, params);

/**
 * 创建项目任务
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const createProjectTask = (params, options) => http.post(`/task/sysTask/add`, params, options);

/**
 * 编辑项目任务
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const editProjectTask = (params, options) => http.put(`/task/sysTask/edit`, params, options);

/**
 * 删除项目任务
 * @param params
 * @returns {Promise}
 */
export const delProjectTask = (params) => http.del(`/task/sysTask/delete`, params);

/**
 * 获取项目任务列表
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const getProjectTaskList = (params, options) => http.get(`/task/sysTask/list`, params, options);

/**
 * 获取项目任务详情
 * @param params
 * @returns {Promise}
 */
export const getProjectTaskDetail = (params) => http.get(`/task/sysTask/queryById`, params);

/**
 * 提交任务
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const submitRhino = (params, options) => http.put(`/task/sysTask/submitRhino`, params, options);

/**
 * 更新任务缓存数据
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const updateTaskCacheParams = (params, options) => http.put(`/task/sysTask/saveParamSets`, params, options);

/**
 * 更新缩略图
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const updateThumbnail = (params, options) => http.post(`/task/sysTask/saveCover`, params, options);

/**
 * 下载step文件
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const downloadStep = (params, options) => http.put(`/task/sysTask/exportResultFile`, params, options);

/**
 * 下载zip文件
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const downloadZip = (params, options) => http.put(`/task/sysTask/downZip`, params, options);

/**
 * 下载纹理文件
 * @param params
 * @param {DefaultOptionsType} [options=] options 参数 [可选]
 * @returns {Promise}
 */
export const downloadWlFile = (params, options) => http.put(`/task/sysTask/exportWLResultFile`, params, options);
