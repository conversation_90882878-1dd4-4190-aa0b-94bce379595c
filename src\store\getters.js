import { storage } from '@/utils/storage';
import { IS_LOGIN, USER_INFO, MODULE_LIST } from '@/constants';

const getters = {
	token: state => state.user.token,
	userInfo: state => {
		state.user.userInfo = storage.get(USER_INFO) || {};
		return state.user.userInfo;
	},
	isLogin: state => {
		state.user.isLogin = storage.get(IS_LOGIN);
		return state.user.isLogin;
	},
	moduleList: state => {
		state.user.moduleList = storage.get(MODULE_LIST) || [];
		return state.user.moduleList;
	},
	spin: state => state.spin,
	lastTaskId: state => {
		state.lastTaskId = storage.get('lastTaskId');
		return state.lastTaskId;
	},
	resultsCache: state => {
		state.results = storage.get('results');
		return state.results;
	},
	partListCache: state => {
		state.partList = storage.get('partList') || [];
		return state.partList;
	},
	settingsCache: state => {
		state.settings = storage.get('settings') || [];
		return state.settings;
	},
	hiddenObjectMapCache: state => {
		state.hiddenObjectMap = storage.get('hiddenObjectMap');
		return state.hiddenObjectMap;
	},
};

export default getters;
