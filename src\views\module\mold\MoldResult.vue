<template>
	<div class="collapse-result">
		<a-collapse>
			<a-collapse-panel class="collapse-panel" key="1">

				<template slot="header">
					<div class="collapse-panel-header">
						<div class="title" style="margin-left:16px">结果</div>
						<div class="extra">
							<slot name="extra">
								<a-button type="default" size="small" icon="download" class="btn-down" @click="e => handleDownloadModal(e)">
									下载结果
								</a-button>
							</slot>
						</div>
					</div>
				</template>

				<!-- 内容区域 -->
				<slot name="content">

					<template v-for="(data, index) in dataList">
						<div class="box" :key="index">
							<div class="column">
								<div class="content">
									<div class="item">
										<span class="label">{{ data.label }}</span>
									</div>

									<div class="item right" v-if="data.value">
										{{ data.value }}
									</div>
									<div class="item right" v-else>
										<!-- <div class="icon"> -->
										<!--<img v-if="data.url" class="down" :src="downIcon" @click="onDownloadFile(data.url)">
											<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />-->
										<!-- <img v-if="data.url" class="down" :src="downIcon" @click="onDownloadFile(data)"> -->
										<!-- </div> -->

										<div class="mr-10" @click="visibleHandler(data)">
											<svg-icon :type="data.visible ? 'param-visible' : 'param-hidden'" />
										</div>

										<div class="mr-10">
											<input type="color" v-model="data.color" @input="colorHandler(data)">
										</div>

										<a-popover :title="null" placement="topRight">
											<template #content>
												<div class="display-flex-center">
													<a-slider
														:min="0"
														:max="100"
														:step="1"
														v-model="data.opacity"
														:style="{ width: '100px', margin: '10px 0 0 10px' }"
														@change="opacityHandler(data)"
													/>
													<div class="opacity">{{ data.opacity }}%</div>
												</div>
											</template>
											<svg-icon type="param-opacity" />
										</a-popover>
									</div>
								</div>
							</div>
						</div>
					</template>

				</slot>

			</a-collapse-panel>
		</a-collapse>
		<DownloadModal v-if="showDownloadModal" :dataList="dataList" ref="downloadModal" @ok="showDownloadModal = false" />
	</div>
</template>

<script>
/* ===================================
 * 随形冷却-求解结果页
 * Created by cjking on 2022/05/16.
 * Copyright 2022, Inc.
 * =================================== */
import { preventDefaults } from '@/components/core/ViewUtils';
import { basename, downloadFileByNewWindow, deepCopy } from '@/utils/utils';

import downIcon from '@/assets/img/texture/down.png';
import { downloadStep, getProjectTaskDetail } from '@/api';
import Config from '@/config/Config';
import DownloadModal from './DownloadModal.vue';
import { TaskStatusEnum } from '@/constants';

const icons = { downIcon };

export default {
	name: 'MoldResult',
	components: {
		DownloadModal,
	},
	props: {
		activeKey: {
			type: Array,
			required: false,
			default: () => ['1'],
		},
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	data () {
		return {
			showDownloadModal: false,
			...icons,
		};
	},
	methods: {
		preventDefaults,
		/**
		 * 显示/隐藏处理
		 */
		visibleHandler (data) {
			data.visible = !data.visible;
			this.$emit('onVisible', data);
		},

		/**
		 * 颜色处理
		 */
		colorHandler (data) {
			this.$emit('onColor', data);
		},

		/**
		 * 透明度处理
		 */
		opacityHandler (data) {
			this.$emit('onOpacity', data);
		},

		/**
		 * 文件下载
		 */
		// async onDownloadFile (data) {
		// 	const params = {
		// 		id: this.$route.params.taskId || '',
		// 		typeName: data.key || '',
		// 	};
		// 	const res = await downloadStep(params);
		// 	if (res?.success) {
		// 		this.$message.success('提交下载请求成功');
		// 	}
		// },

		/**
		 * 文件加载中
		 */
		fileLoading () {
			this.$message.error('文件正在生成中，请稍后再试');
		},

		/**
		 * 下载弹窗
		 */
		async handleDownloadModal (e) {
			this.preventDefaults(e);
			const taskId = this.$route.params.taskId || '';
			let res = await getProjectTaskDetail({ id: taskId });
			this.dataList.forEach(i => {
				if (res.result?.sysTask?.resultJson[i.key]) {
					i.status = res.result?.sysTask?.resultJson[i.key + '_status'];
				} else {
					i.status = 2; // 未计算
				}
			});
			if (res.result?.sysTask.status !== TaskStatusEnum.success) {
				// 任务状态不成功时
				this.$notification.warning({ message: '温馨提示', description: '结果未生成' });
				return;
			}
			this.showDownloadModal = true;
			this.$nextTick(() => {
				this.$refs.downloadModal.open();
			});
			const typeName = this.dataList
				.filter(i => ['result_pipe_line', 'result_make_pipe', 'result_include_water_model'].includes(i.key) &&
					!i.status)
				.map(i => i.key).join(',');
			if (typeName) {
				const params = {
					id: taskId,
					typeName: typeName,
				};
				const res0 = await downloadStep(params);
				if (res0?.success) {
					this.$message.success('下载结果生成中');
				} else {
					return;
				}
				res = await getProjectTaskDetail({ id: taskId });
				this.dataList.forEach(i => {
					if (res.result?.sysTask?.resultJson[i.key]) {
						i.status = res.result?.sysTask?.resultJson[i.key + '_status'];
					} else {
						i.status = 2; // 未计算
					}
				});
			}

		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.collapse-result {
	width: 340px;
	position: relative;

	.btn-down {
		position: absolute;
		top: 8px;
		right: 18px;
		height: 24px;
		border-radius: 2px;
		color: #8E8AFF;
		background-color: #FFF;
		border-color: #8E8AFF;

		&:hover {
			color: rgba(142, 138, 255, 0.5);
			background-color: rgba(255, 255, 255, 0.5);
			border-color: rgba(142, 138, 255, 0.5);
		}
	}

	/deep/ .ant-collapse > .ant-collapse-item {
		border-bottom: 3px solid #E5E5E5;

		.ant-collapse-header {
			border-left: 2px solid @primary;
			border-radius: 0;
			background: #F9F9FF;
			padding: 9px 16px;
			padding-right: 40px;
		}
	}

	.collapse-panel {

		.disable-selection();

		.collapse-panel-header {
			display: flex;

			.icon {
				flex: 0 0 20px;
			}

			.title {
				flex: 1;
				margin-left: 8px;
				font-size: 16px;
				font-weight: bold;
				.disable-selection();
			}

			.extra {
				flex: 0 0 70px;
				text-align: end;
				.disable-selection();
			}
		}

		.label {
			height: 20px;
			line-height: 20px;
			font-size: 14px;
			font-weight: bold;
			color: #111111;
		}

		input[type="color"] {
			width: 20px;
			height: 22px;
			border: 0;
			background-color: transparent;
		}

		.box {
			&.mtb-16 {
				margin: 16px 0;
			}

			.content {
				display: flex;
				width: 100%;

				.item {
					flex: 0 0 50%;

					svg {
						width: 20px;
						height: 20px;
						color: @primary;
					}

					.icon {
						padding: 0 12px;
						color: @primary;
						cursor: pointer;

						&.right {
							text-align: right;
						}
					}

					&.right {
						display: flex;
						justify-content: flex-end;
						align-items: center;

						svg, input[type="color"] {
							cursor: pointer;
						}

						.mr-10 {
							margin-right: 10px;
						}
					}
				}
			}

			.column {
				padding: 5px 3px;
			}
		}
	}

	.down {
		width: 20px;
		margin-top: -3px;
	}

	.sync {
		width: 20px;
		height: 20px;
		vertical-align: -5px;
	}
}
</style>
