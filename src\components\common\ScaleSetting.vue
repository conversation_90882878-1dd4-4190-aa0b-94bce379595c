<template>
	<a-card :title="title" :bodyStyle="{width: '240px'}" @keydown.8.stop>
		<div>起始点坐标</div>
		<div class="base-wrapper">
			<label for=""><b>X</b></label>
			<a-input-number v-model="formData.x" placeholder="请输入" class="m-input"></a-input-number>
		</div>
		<div class="base-wrapper">
			<label for=""><b>Y</b></label>
			<a-input-number v-model="formData.y" placeholder="请输入" class="m-input"></a-input-number>
		</div>
		<div class="base-wrapper">
			<label for=""><b>Z</b></label>
			<a-input-number v-model="formData.z" placeholder="请输入" class="m-input"></a-input-number>
		</div>
		<div>缩放比例</div>
		<div class="base-wrapper">
			<a-input-number
				v-model="formData.scale"
				placeholder="请输入"
				class="m-input ml-0"
				:min="0"
				:parser="value => value.replace('%', '')"
			/>
		</div>
	</a-card>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			required: true,
		},
		position: {
			type: Object,
			default: () => ({})
		},
	},
	data () {
		return {
			formData: {
				scale: 1,
				x: 0,
				y: 0,
				z: 0,
			}
		};
	},
	mounted () {
		this.formData = Object.assign(this.formData, {
			x: this.position.x,
			y: this.position.y,
			z: this.position.z,
		});
		this.$emit('change', this.formData);
	},
	watch: {
		formData: {
			handler ({ x, y, z }) {
				this.$emit('change', this.formData);
			},
			deep: true,
		},
	}
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/base.less';
@import '~@/assets/styles/mixins/variables.less';

.ant-card {
	/deep/ .ant-card-head {
		padding: 0 20px;
	}
	/deep/ .ant-card-body {
		padding: 10px 20px
	}

	/deep/ .ant-card-head-title {
		padding-top: 12px;
		padding-bottom: 12px;
	}
}

.base-wrapper {
	display:flex;
	justify-content:space-between;
	align-items:center;
	margin-top:10px;
	margin-bottom:10px;
}

.m-input {
	flex: 1;
	margin-left: 8px;
}

.ml-0 {
	margin-left: 0
}
</style>
