<template>
	<RightMenu :visible="rightMenuVisible">
		<template #title>
			<div class="view-container" ref="viewContainer"
				@mouseleave="removeBoxSelectListen(true)">
				<a-spin class="view-loading" size="large" :tip="tip" :spinning="loading" />
				<div class="js-view" ref="vtkContainer"
					:style="{ background: background, width: jsViewWidth }">
					<!--<canvas id="mainCanvas" @contextmenu="onContextmenu" @click="onMouseClick"></canvas>-->
					<canvas id="mainCanvas" @contextmenu="onContextmenu"></canvas>
					<canvas id="arrowCanvas"></canvas>
					<div id="label"></div>
				</div>

				<div class="scale-plate">
					<CanvasRuler ref="canvasRulerRef" :parentRef="$refs.viewContainer"
						v-if="rulerLoaded" />
				</div>
				<PointModal ref="pointModal" @ok="pointModalCallback" />
				<MirrorSetting v-if="mirrorCurve.showMirrorSetting" :oDirection="curPlaneMode"
					@change="setMirror" class="mirrorSetting" title="镜像平面" />
				<AngleSetting v-if="rotation.showSetting === 1" @change="setRotateFunc(1, $event)"
					class="mirrorSetting" title="旋转角度" />
				<CirclePatterSetting v-if="rotation.showSetting === 2"
					@change="({ count, angle }) => setRotateFunc(count, angle)"
					class="mirrorSetting" title="圆周阵列" />
				<AxesSetting v-if="translate.showSetting" @change="setLineAxes"
					:oDirection="curPlaneMode" @changeDirection="setLinePlane"
					:position="translate.points" :canEdit="modalEdit" class="mirrorSetting"
					title="平移参数" />
				<ScaleSetting v-if="scaleCurve.showSetting" @change="setScaleLineAxes"
					:position="scaleCurve.point" class="mirrorSetting" title="缩放参数" />
				<HelixSetting v-if="helixSetting.showSetting" @change="handleHelixSetting"
					:turnsNum="helixSetting.turnsNum" :radius="helixSetting.radius"
					class="mirrorSetting" title="螺旋线参数" />
				<CircleSetting v-if="circleSetting.showSetting" @change="handleCircleSetting"
					:radius="circleSetting.radius" class="mirrorSetting" title="圆参数" />
			</div>
		</template>
		<template #menu>
			<!--<a-menu @click="({ key: type }) => onContextMenuClick(type)">
				<a-menu-item key="show">全部显示</a-menu-item>
				<a-menu-item key="copy" :disabled="!copyByName">复制</a-menu-item>
				&lt;!&ndash;<a-menu-item key="move" :disabled="!copyByName">移动</a-menu-item>
				<a-menu-item key="rotate" :disabled="!copyByName">翻转</a-menu-item>&ndash;&gt;
				<a-menu-item key="hidden" :disabled="!contextMenuObject">隐藏</a-menu-item>
				<a-menu-item key="hiddenAllFace">隐藏所有面</a-menu-item>
				<a-menu-item key="hiddenWireframe">隐藏线框</a-menu-item>
				<a-menu-item key="hiddenAllYsLine">隐藏所有自由线</a-menu-item>
				<a-menu-item key="screenshot">屏幕截图</a-menu-item>
			</a-menu>-->
		</template>
	</RightMenu>
</template>

<script>
/* ===================================
 * 随形冷却视图渲染器
 * Updated by cjking on 2022/02/18.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { mapActions, mapGetters } from 'vuex';
import {
	SelectModeEnum, DrawEnum, ViewEnum, RenderModeEnum, CameraModeEnum, PlaneModeEnum,
	ColorEnum, RenderOrderEnum, YS_LINE_GROUP, WIREFRAME, FACE_GROUP,
	waterCoolingResultKeyMap, BJ_POINT_GROUP, DISPERSED_LINE_GROUP, SOLID_GROUP, ActionEnum,
} from '@/constants';
import {
	isEmpty, pick, sleep, toExponential, randomNumber, isString,
	throttle as utilsThrottle, isArray, addEventHandler, removeEventHandler, getSystem,
} from '@/utils/utils';

import * as THREE from 'three';
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { SelectionBox } from '@/assets/jsm/interactive/SelectionBox.js';
import { SelectionHelper } from '@/assets/jsm/interactive/SelectionHelper.js';
import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';
// import { EdgeSplitModifier } from 'three/examples/jsm/modifiers/EdgeSplitModifier.js';
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils.js';

import {
	baseDecode, floatDecode,
	StatisticsUtils, getMaxVal,
	preventDefaults, throttle, toHexColor,
	getMinVal, getWireframeByFace, getLineMaterial,
} from '@/components/core/ViewUtils.js';
import { loadFile } from '@/api';
import Config from '@/config/Config';
import RightMenu from '@/components/common/RightMenu';
import PointModal from '@/components/common/PointModal';
import CanvasRuler from '@/components/core/CanvasRuler';
import { mixinRhino } from '@/mixins/modules/rhino';
import { mixinViewAxes } from '@/mixins/modules/viewAxes';
import { mixinPlane } from '@/mixins/modules/plane';
import { ControlTypeEnum, defaultData, mixinViewCommonFun } from '@/mixins/modules/viewCommonFun';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { TrackballControls } from 'three/examples/jsm/controls/TrackballControls';
import MirrorSetting from '@/components/common/MirrorSettingCard.vue';
import AngleSetting from '@/components/common/AngleSetting.vue';
import CirclePatterSetting from '@/components/common/CirclePatterSetting.vue';
import AxesSetting from '@/components/common/AxesSetting.vue';
import ScaleSetting from '@/components/common/ScaleSetting.vue';
import HelixSetting from '@/components/common/HelixSetting.vue';
import CircleSetting from '@/components/common/CircleSetting.vue';
import EventBus from '@/utils/eventBus';

import bezier from './bezier';

// 标记开关枚举
const TagSwitchEnum = {
	ON: 'ON',
	OFF: 'OFF',
};

const selectSwitch = TagSwitchEnum.OFF;

let rebackList = [];

const mitt = new EventBus();

// -------------------------------------
// 此类属性不能放vue data 里，
// 不然严重影响性能问题，卡顿掉帧严重
let animationId;
let raycasterObjs = []; // 鼠标悬停/点击高亮显示的 obj 列表
const mouse = new THREE.Vector2();
// -------------------------------------

let selectionBox, helper, boxAllSelected;
let delCallback, enterCallback, escCallback,
	ctrlSCallback, ctrlCallback, ctrlZCallback,
	dragCallback, ctrlCCallback, ctrlVCallback, shiftCallback;

// 是否独立 Ctrl 键激活
let independentCtrlKeyActivate = false;

const onKeydown = (event) => {
	const oEvent = event || window.event;
	// 获取键盘的keyCode值
	const keyCode = oEvent.keyCode || oEvent.which || oEvent.charCode;
	// 获取ctrl键对应的事件属性
	const ctrlKeyCode = oEvent.ctrlKey || oEvent.metaKey; // mac metaKey
	// 获取shift键对应的事件属性
	const shiftKey = oEvent.shiftKey;

	independentCtrlKeyActivate = ctrlKeyCode && keyCode === 17;

	logger.log('ctrlKeyCode, shiftKey, keyCode: ', ctrlKeyCode, shiftKey, keyCode);

	if (keyCode === 8 || keyCode === 46) { // 删除键(回退键)
		delCallback && delCallback(event);
	}
	if (keyCode === 13) { // Enter
		enterCallback && enterCallback(event);
	}
	if (keyCode === 16) { // Shift
		shiftCallback && shiftCallback(event);
	}
	if (keyCode === 27) { // Esc
		escCallback && (() => {
			rebackList = [];
			escCallback(event);
		})();
	}
	if (ctrlKeyCode && keyCode === 83) { // ctrl+s
		ctrlSCallback && ctrlSCallback(event);
	}
	if (ctrlKeyCode && keyCode === 90) { // ctrl+z
		ctrlZCallback && ctrlZCallback(event);
	}
	// if (ctrlKeyCode && keyCode === 67) { // ctrl+c
	// 	ctrlCCallback && ctrlCCallback(event);
	// }
	if (ctrlKeyCode && keyCode === 86) { // ctrl+v
		ctrlVCallback && ctrlVCallback(event);
	}
	if (ctrlKeyCode && ctrlCallback) {
		ctrlCallback(event, independentCtrlKeyActivate);
	}
};

const onKeyUp = (event) => {
	const oEvent = event || window.event;
	// 获取键盘的keyCode值
	const keyCode = oEvent.keyCode || oEvent.which || oEvent.charCode;
	// 获取ctrl键对应的事件属性
	const ctrlKeyCode = oEvent.ctrlKey || oEvent.metaKey; // mac metaKey
	// 获取shift键对应的事件属性
	const shiftKey = oEvent.shiftKey;
	logger.log('ctrlKeyCode, shiftKey, keyCode: ', ctrlKeyCode, shiftKey, keyCode);

	if (!shiftKey && keyCode === 16) { // Shift
		shiftCallback && shiftCallback(event);
	}
};

const POINT_NUMBER = 48;

export default {
	name: 'MoldView',
	mixins: [mixinViewCommonFun, mixinViewAxes, mixinRhino, mixinPlane],
	components: {
		RightMenu,
		CanvasRuler,
		PointModal,
		MirrorSetting,
		AngleSetting,
		CirclePatterSetting,
		AxesSetting,
		ScaleSetting,
		HelixSetting,
		CircleSetting,
	},
	props: {
		hasCover: { // 是否有封面图(缩略图)
			type: Boolean,
			required: false,
			default: true,
		},
		copyByName: {
			type: String,
			required: true,
			default: '',
		},
		taskInfoLoading: {
			type: Boolean,
			required: true,
		},
	},
	data () {
		return {
			...defaultData,
			intersectObject: null,      // 拾取对象, object: Object3D
			transformControls: null,    // 平移控制器
			controlling: false,
			dragControls: null,         // 拖动控制器
			chooseLine: false,
			currentFunc: null,
			modalEdit: false,
			selectMode: '',
			distanceLong: 0,			// 初始相机位置到初始目标位置距离
			centerPoint: {
				object: null,
				xyz: null,
				normal: null,
			},
			mirrorCurve: {
				points: [],
				showMirrorSetting: false,
				closeFunc: null,
				tempObj: null,
				mirrorItem: null,
				curve: null,
			},
			rotation: {
				showSetting: 0,
				point: null,
				currentItems: [],
				curve: null,
			},
			translate: {
				points: [],
				showSetting: false,
				closeFunc: null,
				tempObj: null,
				currentItem: null,
				curve: '',
			},
			scaleCurve: {
				showSetting: false,
				curve: null,
				currentItem: null,
				point: null,
				tempObj: null,
				clickPoints: [],
				curveName: '',
			},
			helixSetting: {
				_point: null,
				drawEnd: false,
				showSetting: false,
				turnsNum: 2,
				radius: 10,
				point: null,
				newPoint: null,
				drawResult: null,
			},
			circleSetting: {
				showSetting: false,
				point: null,
				newPoint: null,
				drawResult: null,
			},
		};
	},
	computed: {
		...mapGetters(['userInfo']),

		curCamera: {
			cache: false,
			get () {
				return this.getCurrCamera();
			},
		},
		curControl: {
			cache: false,
			get () {
				return this.getCurrControl();
			},
		},
		defaultColor () {
			return ColorEnum.origin;
		},
		selectColor () {
			return this.curActiveObj ? this.curActiveObj.color : ColorEnum.white;
		},
		basePath () {
			if (this.userInfo.orgCode) {
				return `${Config.staticDomainURL}/${this.userInfo.orgCode}/${this.projectId}/${this.taskId}`;
			}
			return `${Config.staticDomainURL}/${this.projectId}/${this.taskId}`;
		},
		dispersePath () {
			return `${this.basePath}/dispersed`;
		},
		disperseStepPath () {
			return `${this.basePath}/dispersed/step`;
		},
		canTransform () {
			return !this.controlling;
		},
	},
	watch: {
		collapsed () {
			this.resizeCurrentView();
		},
	},
	mounted () {
		this.initIds();
		this.initView();
	},
	methods: {
		preventDefaults,
		...mapActions(['saveSolidNames']),

		initIds () {
			this.projectId = this.$route.params.id;
			this.taskId = this.$route.params.taskId;
		},

		/**
		 * 初始化视图
		 */
		async initView () {
			// this.initStats();
			this.initScene();
			this.initRenderer();
			this.initLight();
			this.initCamera();
			this.initAxes();
			this.initControls();
			this.updateRuler(true);
		},

		initDistance () {
			if (this.distanceLong) return;
			const OA = this.curCamera.position;
			const OC = this.curControl.target;
			this.distanceLong = OA.distanceTo(OC);
		},

		/**
		 * 初始化加载场景数据
		 */
		async loadData () {
			return new Promise(async (resolve) => {
				this.initIds();
				const result = {
					relative_path: '/text',
					meshes: 'meshs.json',
				};
				const meshes = await this.loadRhinoFacesJson('meshes', result, true);
				if (meshes?.length) {
					meshes.forEach(obj => {
						if (obj.isMesh) {
							obj.visible = false;
						}
					});
					raycasterObjs.push(...meshes);
				}
				await this.initSceneByBoundingBox('/text/boundingBox.txt', {
					loadServerConfig: false,
					openBoxHelper: false,
					drawCenterPoint: false,
					updateRuler: false
				});
				this.updateView(ViewEnum.default);

				setTimeout(async () => {
					const solidGroup = this.getGroupByName(SOLID_GROUP);
					solidGroup && solidGroup.clear();
					const wireframeGroup = this.getGroupByName(WIREFRAME);
					wireframeGroup && wireframeGroup.clear();
					this.getScene().remove(solidGroup, wireframeGroup);
					raycasterObjs.length = 0;
					resolve();
					this.requestRender();
				}, 1);
			});

		},

		/**
		 * 加载场景数据
		 */
		async loadDataV3 (result) {
			if (result?.error) {
				this.$modal.error({ content: result.error });
			}
			this.initIds();
			this.openLoading();

			let meshes, faces, crk_curves, other_curves;
			if (result['meshes']) {
				meshes = await this.loadRhinoFacesJson('meshes', result);
				if (meshes?.length) {
					raycasterObjs.push(...meshes);
				}
			}
			if (result['faces']) {
				faces = await this.loadRhinoFacesJson('faces', result);
				if (faces?.length) {
					raycasterObjs.push(...faces);
				}
			}
			if (result['crk_curves']) {
				const filePath = `${this.dispersePath}/${result['crk_curves']}`;
				crk_curves = await this.loadRhinoLinesJson(filePath, { decompress: false, customLineAttributes: true });
				if (crk_curves?.length) {
					let curveIndex = 0;
					for (const crkCurve of crk_curves) {
						curveIndex++;
						crkCurve.canDelete = true;
						crkCurve.isWireframe = true; // 是否边框线
						crkCurve.visible = true;
						crkCurve.linewidth = 1;
						crkCurve.groupName = WIREFRAME;
						crkCurve.renderOrder = RenderOrderEnum.line;
						crkCurve.nickname = 'crk_' + curveIndex;
						raycasterObjs.push(crkCurve);
					}
				}
			}
			if (result['other_curves']) {
				const filePath = `${this.dispersePath}/${result['other_curves']}`;
				other_curves = await this.loadRhinoLinesJson(filePath, { nicknamePrefix: 'zyx_', decompress: false });
				if (other_curves?.length) {
					raycasterObjs.push(...other_curves);
				}
			}

			logger.log('raycasterObjs.length: ', raycasterObjs.length);
			if (raycasterObjs.length && result.boundingBox) {
				await this.initSceneByBoundingBox(result.relative_path + '/' + result.boundingBox);
			}

			this.updateView(ViewEnum.default);

			// 初始化缩略图
			this.initThumbnail();

			// 释放，回收内存
			this.$nextTick(() => {
				meshes = null;
				faces = null;
				crk_curves = null;
				other_curves = null;
				this.closeLoading();
			});
		},

		/**
		 * 开启平移控件
		 */
		initTransFormControls (currentItem, { mode = 'translate' } = {}) {
			if (this.transformControls?.object.children.includes(currentItem) && this.transformControls.type === currentItem.type) return;
			let meshs;
			const type = this.isLine(currentItem) ? 'line' : 'point';

			if (type === 'point') {
				if (currentItem.parent.type === 'Object3D') {
					const nickname = currentItem.groupName;
					const index = this.curvesMap[nickname].spheres.findIndex(item => item.uuid === currentItem.uuid);
					enterCallback();
					currentItem = this.curvesMap[nickname].spheres.find((_, idx) => idx === index);
					this.currentActiveItem = currentItem;
				}
				meshs = currentItem;
				meshs.nickname = currentItem.groupName;
			} else {
				meshs = new THREE.Object3D();
				const { x, y, z } = this.getCenterPoint(currentItem);
				meshs.nickname = currentItem.nickname;
				meshs.tempData = new THREE.Vector3(x, y, z);
				this.getScene().add(meshs);
				meshs.position.set(x, y, z);
				meshs.updateMatrixWorld(true);
				const spheres = this.curvesMap[currentItem.nickname]?.spheres ?? [];
				spheres.length && spheres.forEach(item => meshs.attach(item));
				meshs.attach(currentItem);
				this.removeSceneObj(currentItem);
			}

			const { dragTransformCallback, enterTransformCallback } = this.getTransformCallback(type);
			const draggingEvent = (event) => {
				dragTransformCallback(event);
				this.curControl.enabled = !event.value;
			};

			if (!this.transformControls) {
				this.transformControls = new TransformControls(this.curCamera, this.getRenderer().domElement);
				this.getScene().add(this.transformControls);
			}
			this.transformControls.removeEventListener('dragging-changed', this.transformControls.ev);
			this.transformControls.ev = draggingEvent;
			this.transformControls.addEventListener('dragging-changed', draggingEvent);

			this.transformControls.attach(meshs);
			this.transformControls.setMode(mode);
			this.transformControls.type = currentItem.type;
			this.curControl.enabled = false;
			enterCallback = () => {
				this.resetColorHandle();
				this.curControl.enabled = true;
				enterTransformCallback(meshs);
				this.emitCurveData('ys_line');
			};
		},

		getTransformCallback (type) {
			let dragTransformCallback, enterTransformCallback;
			if (type === 'line') {
				dragTransformCallback = () => { };
				enterTransformCallback = (meshs) => {
					this.transformControls?.detach();
					this.removeSceneObj(this.transformControls);
					this.transformControls = null;
					meshs.clear();
					this.removeSceneObj(meshs);

					raycasterObjs = raycasterObjs.filter(obj => meshs.nickname !== obj.nickname && meshs.nickname !== obj.groupName);
					this.drawCurve(meshs.nickname, this.curvesMap[meshs.nickname].clickPoints.map(item => item.add(meshs.position).sub(meshs.tempData)));
					this.requestRender();
					this.currentActiveItem = null;
				};
			} else {
				dragTransformCallback = (event) => {
					if (!event.value) {
						const selectSphere = event.target?.children[1]?.object;
						this.updateDrawCurve(selectSphere.position);
					}
				};
				enterTransformCallback = (mesh) => {
					this.transformControls?.detach();
					raycasterObjs.push(...this.curvesMap[mesh.nickname].spheres);
					this.removeSceneObj(this.transformControls);
					this.transformControls = null;
					this.currentActiveItem = null;
				};
			}

			return { dragTransformCallback, enterTransformCallback };
		},

		/**
		 * 初始化缩略图
		 */
		initThumbnail () {
			if (raycasterObjs.length > 0 && !this.hasCover) {
				sleep(500, () => this.screenshot(this.taskId, true));
			}
		},

		/**
		 * 加载离散线数据
		 */
		async loadLinesData () {
			const filePath = `${this.dispersePath}/dispersed_lines.json`;
			const objs = await this.loadRhinoLinesJson(filePath, { lineWidth: 2 });
			logger.log('加载离散线数据: ', objs);
			if (objs?.length) {
				const group = this.getScene()?.getObjectByName(DISPERSED_LINE_GROUP);
				if (group) {
					this.$nextTick(() => {
						group.children.push(...objs);
						raycasterObjs.push(...objs);
						this.requestRender();
					});
				}
			}
		},

		/**
		 * 获取数据
		 */
		async getData (filePath) {
			if (!filePath.startsWith('/')) {
				filePath = '/' + filePath;
			}

			// const data = (await import('../../../public/models/json/data.json')).default;
			const path = Config.staticDomainURL + filePath + '?_=' + Date.now();
			const data = await loadFile(path, 'json', false).catch(() => this.tipErrMsg(path, '离散'));
			if (!data) return;

			data.compressed = 'true';
			data.base = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!#$%&()*+-:;/=>?@[]^_,.{|}~`';
			data.baseFloat = ',.-0123456789';
			data.camera = {
				'type': 'Orthographic',
				'position_x': 0,
				'position_y': 0,
				'position_z': 200,
			};

			const { xMin, xMax, yMin, yMax, zMin, zMax } = data; // 获取显示的边界
			// 求对角线
			// 公式：l = a^2 + b^2 + c^2
			const a = Math.pow(Math.abs(xMax - xMin), 2);
			const b = Math.pow(Math.abs(yMax - yMin), 2);
			const c = Math.pow(Math.abs(zMax - zMin), 2);
			const modelLong = Math.sqrt(a + b + c);
			this.modelLong = modelLong;

			this.jsonData = data;

			return data;
		},

		/**
		 * 初始化THREE视图方向
		 */
		initTHREE (up = [0, 0, 1]) {
			// Z is up for FreeCAD
			// THREE.Object3D.DefaultUp = new THREE.Vector3(...up);

			this.curCamera.up.set(...up);
			this.curCamera.lookAt(0, 0, 0);
		},

		/**
		 * 初始化统计
		 */
		initStats () {
			this.stats = new Stats();
			document.body.appendChild(this.stats.dom);
		},

		/**
		 * 初始化场景
		 */
		initScene () {
			const scene = new THREE.Scene();
			const canvas = document.querySelector('#mainCanvas');
			this.setScene(scene);
			this.setCanvas(canvas);
			this.getScene().add(
				this.tempSpheresGroup,
				this.tempCurvesGroup,
				this.spheresGroup,
				this.curvesGroup,
			);
		},

		/**
		 * 初始化渲染场景
		 */
		initRenderer () {
			const renderer = new THREE.WebGLRenderer({
				alpha: true,
				antialias: true,
				canvas: this.getCanvas(),
				sortObjects: true,
			});
			// Clear bg so we can set it with css
			renderer.setClearColor(ColorEnum.black, 0);
			this.setRenderer(renderer);
		},

		/**
		 * 初始化相机
		 */
		initCamera (data) {
			data = data || this.jsonData;
			// Get bounds for global clipping
			const globalMaxMin = [
				{ min: null, max: null },
				{ min: null, max: null },
				{ min: null, max: null },
			];
			if (data?.objects?.length) {
				for (const obj of data.objects) {
					for (let v = 0; v < obj.verts.length; v++) {
						if (isEmpty(globalMaxMin[v % 3]) || obj.verts[v] < globalMaxMin[v % 3].min) {
							globalMaxMin[v % 3].min = obj.verts[v];
						}
						if (isEmpty(globalMaxMin[v % 3]) || obj.verts[v] > globalMaxMin[v % 3].max) {
							globalMaxMin[v % 3].max = obj.verts[v];
						}
					}
				}
			}
			let bigRange = 0;
			// add a little extra
			for (const i of globalMaxMin) {
				const range = i.max - i.min;
				if (range > bigRange) {
					bigRange = range;
				}
				i.min -= range * 0.01;
				i.max += range * 0.01;
			}
			const camCenter = new THREE.Vector3(
				0.5 * (globalMaxMin[0].max - globalMaxMin[0].min) + globalMaxMin[0].min,
				0.5 * (globalMaxMin[1].max - globalMaxMin[1].min) + globalMaxMin[1].min,
				0.5 * (globalMaxMin[2].max - globalMaxMin[2].min) + globalMaxMin[2].min,
			);

			const viewSize = 1.5 * bigRange; // make the view area a little bigger than the object
			this.viewSize = viewSize;
			const canvas = this.getCanvas();
			const aspectRatio = canvas.clientWidth / canvas.clientHeight;
			this.originalAspect = aspectRatio;

			this.globalMaxMin = globalMaxMin;

			let cameraType = data?.camera?.type ?? 'Orthographic';
			if (this.zoomSpeed === 0) {
				cameraType = 'Perspective';
			}
			this.setCameraType(cameraType);

			const initCameraPosition = (camera) => {
				camera.position.set(0, 0, 200);
				camera.lookAt(camCenter);
				camera.updateMatrixWorld();
			};

			const perspectiveCamera = new THREE.PerspectiveCamera(50, aspectRatio, 0.1, this.far);
			initCameraPosition(perspectiveCamera);
			this.setPerspectiveCamera(perspectiveCamera);

			const orthographicCamera = new THREE.OrthographicCamera(-aspectRatio * viewSize / 2, aspectRatio * viewSize / 2, viewSize / 2, -viewSize / 2, 0.1, this.far);
			initCameraPosition(orthographicCamera);
			this.setOrthographicCamera(orthographicCamera);
		},

		/**
		 * 通过边界框初始化场景
		 * @param boundingBoxPath
		 * @param loadServerConfig
		 * @param openBoxHelper
		 * @param drawCenterPoint
		 * @returns {Promise<void>}
		 */
		async initSceneByBoundingBox (boundingBoxPath, { loadServerConfig = true, openBoxHelper = false, drawCenterPoint = false, updateRuler = true } = {}) {
			const vertices = [];
			if (loadServerConfig && !boundingBoxPath.startsWith(Config.staticDomainURL)) {
				boundingBoxPath = Config.staticDomainURL + '/' + boundingBoxPath;
			}
			const textContent = await loadFile(boundingBoxPath, 'text', false);
			if (isString(textContent)) {
				const verticesArray = textContent.split('\n').filter(item => item).map(str => str.replace(/[{|}\s]/g, '').split(','));
				verticesArray.forEach(arr => {
					arr = arr.map(Number);
					vertices.push(arr[0], arr[1], arr[2]);
				});
			}

			const newData = { objects: [{ verts: vertices }] };

			// 求对角线
			// 公式：l = a^2 + b^2 + c^2
			const globalMaxMin = [
				{ min: null, max: null },
				{ min: null, max: null },
				{ min: null, max: null },
			];
			for (let v = 0; v < vertices.length; v++) {

				if (isEmpty(globalMaxMin[v % 3]) || vertices[v] < globalMaxMin[v % 3].min) {
					globalMaxMin[v % 3].min = vertices[v];
				}
				if (isEmpty(globalMaxMin[v % 3]) || vertices[v] > globalMaxMin[v % 3].max) {
					globalMaxMin[v % 3].max = vertices[v];
				}
			}
			const xMin = globalMaxMin[0].min;
			const xMax = globalMaxMin[0].max;
			const yMin = globalMaxMin[1].min;
			const yMax = globalMaxMin[1].max;
			const zMin = globalMaxMin[2].min;
			const zMax = globalMaxMin[2].max;
			const a = Math.pow(Math.abs(xMax - xMin), 2);
			const b = Math.pow(Math.abs(yMax - yMin), 2);
			const c = Math.pow(Math.abs(zMax - zMin), 2);
			const modelLong = Math.sqrt(a + b + c);
			this.modelLong = modelLong;

			for (let i = 2; i <= vertices.length; i += 3) {
				this.zList.push(vertices[i]);
			}

			this.getCenter(openBoxHelper, drawCenterPoint);

			this.minZ = getMinVal(this.zList);
			this.maxZ = getMaxVal(this.zList);
			this.lenZ = Math.abs(this.maxZ - this.minZ);
			this.zList.length = 0;
			logger.log('this.maxZ: ', this.maxZ);
			logger.log('this.lenZ: ', this.lenZ);

			this.initCamera(newData);
			this.reInitControls();
			this.initEvents();
			this.initStatistics();
			if (updateRuler) this.updateRuler();
			this.closeLoading();
			this.animate();

			this.saveSolidNames(this.solidNames);
		},

		/**
		 * 获取中心
		 */
		getCenter (openBoxHelper = false, drawCenterPoint = false) {
			// 在3D空间中表示一个盒子或立方体。其主要用于表示物体在世界坐标中的边界框
			const box = new THREE.Box3();

			// 体加载
			const solidGroup = this.getGroupByName(SOLID_GROUP) || new THREE.Group();
			solidGroup.name = SOLID_GROUP;
			solidGroup.isSolid = true; // 表示体
			solidGroup.canDelete = true;
			raycasterObjs.forEach(solid => solid.isSolid && solidGroup.add(solid));
			this.getScene().add(solidGroup);
			if (solidGroup.children.length) {
				box.expandByObject(solidGroup);
			}

			// 面加载
			const faceGroup = this.getGroupByName(FACE_GROUP) || new THREE.Group();
			faceGroup.name = FACE_GROUP;
			faceGroup.isFace = true; // 表示面
			faceGroup.canDelete = true;
			raycasterObjs.forEach(mesh => mesh.isFace && faceGroup.add(mesh));
			this.getScene().add(faceGroup);
			if (faceGroup.children.length) {
				box.expandByObject(faceGroup);
			}

			// 线框加载
			const wireframeGroup = this.getGroupByName(WIREFRAME) || new THREE.Group();
			wireframeGroup.name = WIREFRAME;
			wireframeGroup.isWireframe = true; // 表示线框
			wireframeGroup.canDelete = true;
			raycasterObjs.forEach(wire => wire.isWireframe && wireframeGroup.add(wire));
			this.getScene().add(wireframeGroup);

			// 离散线加载
			const lineGroup = this.getGroupByName(DISPERSED_LINE_GROUP) || new THREE.Group();
			lineGroup.name = DISPERSED_LINE_GROUP;
			lineGroup.isDispersed = true; // 表示离散线
			lineGroup.canDelete = true;
			raycasterObjs.forEach(line => line.isDispersed && lineGroup.add(line));
			this.getScene().add(lineGroup);

			// 返回包围盒的宽度，高度，和深度
			const boxSize = box.getSize(new THREE.Vector3());

			let group;
			if (solidGroup.children.length) {
				group = solidGroup;
			}
			if (faceGroup.children.length) {
				group = faceGroup;
			}
			// box辅助框，测试完毕可删除
			if (openBoxHelper) {
				if (this.boxHelper) {
					this.getScene().remove(this.boxHelper);
				}
				this.boxHelper = new THREE.BoxHelper(group, 0xffff00);
				this.getScene().add(this.boxHelper);
				this.boxHelper.geometry.computeBoundingBox(); // 绑定盒子模型
			}

			// 返回包围盒的中心点
			const center = box.getCenter(new THREE.Vector3());
			this.camCenter = center.clone();
			this.setCameraCenter(center.clone());
			if (drawCenterPoint) {
				const sphere = this.drawSphere2(this.camCenter, { radius: 2 });
				this.getScene().add(sphere);
				this.requestRender();
			}

			return { center, boxSize, group };
		},

		/**
		 * 分配网格
		 */
		assignMesh (positions, color, opacity, faces, name, faceName = undefined, isLackFace = false) {

			const hasNaN = positions.some(x => isNaN(x));
			if (hasNaN) {
				this.$error({ content: `模型面【${faceName}】存在异常，请修复后重新上传！` });
			}

			color = isLackFace ? ColorEnum.red : color;
			const material = this.getMeshMaterial(color, opacity);
			const mesh = this.createMeshByPosition(positions, material);
			mesh.name = mesh.uuid;
			mesh.solidGroup = name;
			mesh.nickname = faceName;
			mesh.isFace = true;
			mesh.canDelete = true; // 是否可以删除
			mesh.isLackFace = isLackFace; // 是否坏面(缺失)
			mesh.visible = this.originVisible;
			mesh.renderOrder = RenderOrderEnum.face;
			faces.push(mesh.uuid);

			if (!isLackFace) raycasterObjs.push(mesh);
			else {
				this.getScene().add(mesh);
			}

			const list = mesh.geometry.attributes.position.array;
			for (let i = 2; i <= list.length; i += 3) {
				this.zList.push(list[i]);
			}

			if (!this.solidNames.includes(name)) {
				this.solidNames.push(name);
			}
		},

		/**
		 * 获取网格材质
		 */
		getMeshMaterial (color, opacity) {
			let depthWrite = false;
			let transparent = false;
			if (opacity !== 1.0) {
				transparent = true;
			} else {
				depthWrite = true;
			}
			const material = new THREE.MeshLambertMaterial({
				color: new THREE.Color(color), // 默认：#cccccc
				emissive: new THREE.Color(0x333333),
				side: THREE.DoubleSide,
				vertexColors: false,
				flatShading: false,
				opacity: opacity,
				depthWrite: depthWrite,
				transparent: transparent,
				fog: false,
			});
			return material;
		},

		/**
		 * 按 positions 创建网格
		 */
		createMeshByPosition (positions, material) {
			let baseGeometry = new THREE.BufferGeometry();
			baseGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
			baseGeometry = BufferGeometryUtils.mergeVertices(baseGeometry);
			baseGeometry.computeVertexNormals();
			baseGeometry.computeBoundingSphere();

			[].join();

			// // ==================================
			// // 修复表面光滑度，有缺陷，中心点有偏移!!!
			// // EdgeSplitModifier is used to combine verts so that smoothing normals can be generated WITHOUT removing the hard edges of the design
			// // REF: https://threejs.org/examples/?q=edge#webgl_modifier_edgesplit - https://github.com/mrdoob/three.js/pull/20535
			// const edgeSplit = new EdgeSplitModifier();
			// const cutOffAngle = 180;
			// baseGeometry = edgeSplit.modify(baseGeometry, cutOffAngle * Math.PI / 180);
			// baseGeometry.computeVertexNormals();
			// baseGeometry.computeBoundingSphere();
			// // ==================================

			const mesh = new THREE.Mesh(baseGeometry, material);
			return mesh;
		},

		/**
		 * 初始化数据
		 */
		async initData (data) {
			return new Promise(async (resolve) => {
				this.solidNames = [];
				if (data?.objects?.length) {
					for (const obj of data.objects) {
						// Each face gets its own material because they each can
						// have different colors
						const faces = [];
						if (obj.facesToFacets.length > 0) {
							for (let f = 0; f < obj.facesToFacets.length; f++) {
								const faceColor = obj.faceColors.length > 0 ? obj.faceColors[f] : obj.color;
								const positions = new Float32Array(obj.facesToFacets[f].length * 9);
								for (let a = 0; a < obj.facesToFacets[f].length; a++) {
									for (let b = 0; b < 3; b++) {
										for (let c = 0; c < 3; c++) {
											positions[9 * a + 3 * b + c] = obj.verts[3 * obj.facets[3 * obj.facesToFacets[f][a] + b] + c];
										}
									}
								}
								this.assignMesh(positions, faceColor, obj.opacity, faces, obj.name, obj.faceNames[f], obj.lackFaceNames?.includes(obj.faceNames[f]));
							}
						} else {
							// No facesToFacets means that there was a tessellate()
							// mismatch inside FreeCAD. Use all facets in object to
							// create this mesh
							if (obj.facets.length) {
								const positions = new Float32Array(obj.facets.length * 3);
								for (let a = 0; a < obj.facets.length; a++) {
									for (let b = 0; b < 3; b++) {
										positions[3 * a + b] = obj.verts[3 * obj.facets[a] + b];
									}
								}
								this.assignMesh(positions, obj.color, obj.opacity, faces, obj.name);
							}
						}
					}

					this.saveSolidNames(this.solidNames);
				}

				// 加载线框
				if (this.solidNames.length) {
					logger.log('----- 加载线框(前端绘制) ------');
					const group = new THREE.Group();
					group.isWireframe = true;
					group.name = WIREFRAME;
					group.visible = this.originVisible;
					raycasterObjs.forEach((mesh, index) => {
						if (mesh.isFace) {
							const linePavement = getWireframeByFace(mesh, this.getCanvas(), 1);
							linePavement.name = linePavement.uuid;
							linePavement.canDelete = true;
							linePavement.isWireframe = true; // 是否线框
							linePavement.nickname = 'e_' + (index + 1);
							linePavement.groupName = WIREFRAME;
							linePavement.renderOrder = RenderOrderEnum.line;
							group.add(linePavement);
						}
					});
					this.getScene().add(group);
					this.requestRender();
				} else {
					logger.log('----- 加载线框(后端返回) ------');
					const filePath = `${this.disperseStepPath}/wire.3dm`;
					await this.load3DMLines(filePath, {
						message: '加载线框',
						groupAttr: 'isWireframe',
						groupName: WIREFRAME,
						allowSelect: false,
						lineHandler: (line, index) => {
							line.nickname = 'e_' + (index++);
						},
					});
				}

				this.getCenter(false, false);

				this.initMinMaxZ();

				this.dataLoaded = true;

				// // Rhino 离散示例加载
				// setTimeout(async () => {
				// 	this.updateAllVisible(false);
				// 	this.updateAllResultVisible(false);
				// 	await sleep(100);
				// 	await this.loadRhinoBase64('faces');
				// 	await this.loadRhinoBase64('lines');
				// }, 3000);

				// 初始化缩略图
				this.initThumbnail();

				resolve();
			});
		},

		/**
		 * 初始化最小最大 Z
		 */
		initMinMaxZ () {
			this.minZ = getMinVal(this.zList);
			this.maxZ = getMaxVal(this.zList);
			this.lenZ = Math.abs(this.maxZ - this.minZ);
			this.zList.length = 0;
			logger.log('this.maxZ: ', this.maxZ);
			logger.log('this.lenZ: ', this.lenZ);
		},

		/**
		 * 制作管道
		 */
		makeTube (points) {
			const geometry = new THREE.TubeGeometry(new THREE.CatmullRomCurve3(points), 100, 1, 20, false);
			const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
			const mesh = new THREE.Mesh(geometry, material);
			this.getScene().add(mesh);
		},

		/**
		 * 初始化光
		 * HemisphereLight gives different colors of light from the top
		 * and bottom simulating reflected light from the 'ground' and
		 * 'sky'
		 */
		initLight () {
			const scene = this.getScene();

			// 从顶部和底部发出不同颜色的光，模拟来自“地面”和“天空”的反射光
			scene.add(new THREE.HemisphereLight(0xC7E8FF, 0xFFE3B3, 0.4));

			const light1 = new THREE.DirectionalLight(ColorEnum.white, 0.4);
			light1.position.set(5, -2, 3);
			scene.add(light1);

			const light2 = new THREE.DirectionalLight(ColorEnum.white, 0.4);
			light2.position.set(-5, 2, 3);
			scene.add(light2);
		},

		/**
		 * 导航变化(视图切换)
		 */
		navChange (v, flag) {
			if (!this.initPosition) {
				this.initPosition = new THREE.Vector3();
				new THREE.Box3().setFromObject(this.getScene()).getSize(this.initPosition);
			}
			const t = this.initPosition;

			const value = Math.max(t.x, t.y, t.z);

			const vector = flag ? (new THREE.Vector3(
				v[0] * t.x * 2 + (this.camCenter?.x || 0),
				v[1] * t.y * 2 + (this.camCenter?.y || 0),
				v[2] * t.z * 2 + (this.camCenter?.z || 0),
			)) : (new THREE.Vector3(
				v[0] * value * 2.5 + (this.camCenter?.x || 0),
				v[1] * value * 2.5 + (this.camCenter?.y || 0),
				v[2] * value * 2.5 + (this.camCenter?.z || 0),
			));

			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			if (perspectiveControls) {
				perspectiveControls.object?.position.copy(vector);
				if (this.camCenter) {
					perspectiveControls.target = this.camCenter.clone();
				}
				perspectiveControls.update();
			}

			if (orthographicControls) {
				orthographicControls.object.position.copy(vector);
				if (this.camCenter) {
					orthographicControls.target = this.camCenter.clone();
				}
				orthographicControls.update();
			}
		},

		/**
		 * 更改渲染模式
		 * @param mode
		 */
		changeRenderMode (mode) {
			this.curRenderMode = mode;
			// 面和体的材质变化
			if (this.curRenderMode === RenderModeEnum.surfaceAndWireframe) {
				// 体 + 线框模式
				this.setFaceAndEdgeVisible(true);
			} else if (this.curRenderMode === RenderModeEnum.surface) {
				// 体
				this.setFaceVisible(true);
				this.setEdgeVisible(false);
			} else if (this.curRenderMode === RenderModeEnum.wireframe) {
				// 线框模式
				this.setFaceVisible(false);
				this.setEdgeVisible(true);
			}
		},

		/**
		 * 更新剪裁
		 * @params clip
		 * @params clippingReverse 反转方向
		 */
		updateClipping (clip, clippingReverse) {
			const renderer = this.getRenderer();
			if (!renderer) {
				return;
			}
			if (clip.clippingX < 100 || clip.clippingY < 100 || clip.clippingZ < 100) {
				if (!renderer.clippingPlanes) renderer.clippingPlanes = [];
				if (renderer.clippingPlanes.length === 0) {
					this.clipPlaneX = new THREE.Plane(new THREE.Vector3(+clip.xx, +clip.xy, +clip.xz), 0);
					this.clipPlaneY = new THREE.Plane(new THREE.Vector3(+clip.yx, +clip.yy, +clip.yz), 0);
					this.clipPlaneZ = new THREE.Plane(new THREE.Vector3(+clip.zx, +clip.zy, +clip.zz), 0);
					renderer.clippingPlanes.push(this.clipPlaneX, this.clipPlaneY, this.clipPlaneZ);
				} else {
					this.clipPlaneX.setComponents(+clip.xx, +clip.xy, +clip.xz, 0);
					this.clipPlaneY.setComponents(+clip.yx, +clip.yy, +clip.yz, 0);
					this.clipPlaneZ.setComponents(+clip.zx, +clip.zy, +clip.zz, 0);
				}

				if (clippingReverse.x) {
					this.clipPlaneX.negate();
				} else {
					this.clipPlaneX.normalize();
				}

				if (clippingReverse.y) {
					this.clipPlaneY.negate();
				} else {
					this.clipPlaneY.normalize();
				}

				if (clippingReverse.z) {
					this.clipPlaneZ.negate();
				} else {
					this.clipPlaneZ.normalize();
				}

				this.clipPlaneX.constant = (this.globalMaxMin[0].max - this.globalMaxMin[0].min) * clip.clippingX / 100.0 + this.globalMaxMin[0].min;
				this.clipPlaneY.constant = (this.globalMaxMin[1].max - this.globalMaxMin[1].min) * clip.clippingY / 100.0 + this.globalMaxMin[1].min;
				this.clipPlaneZ.constant = (this.globalMaxMin[2].max - this.globalMaxMin[2].min) * clip.clippingZ / 100.0 + this.globalMaxMin[2].min;

			} else {
				renderer.clippingPlanes = [];
			}

			this.requestRender();
		},

		/**
		 * 旋转视图
		 */
		updateRotate ({ axis, angle }) {
			// logger.log('旋转视图 axis, angle: ', axis, angle);
			if (axis === 'x') {
				this.rotate('X', angle);
			}
			if (axis === 'y') {
				this.rotate('Y', angle);
			}
		},

		/**
		 * 相机切换
		 */
		cameraChange (v, bool) {
			this.setControlType(v);
			this.requestRender();
			setTimeout(() => {
				!bool && this.resetView();
			}, 1);
		},

		/**
		 * 鼠标左键设置
		 * @param value
		 */
		mouseLeftSetting (value) {
			// this.mouse.LEFT = value;
			// this.mouse.RIGHT = value === THREE.MOUSE.PAN ? THREE.MOUSE.ROTATE : THREE.MOUSE.PAN;
			// this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 鼠标右键设置
		 * @param value
		 */
		mouseRightSetting (value) {
			// this.mouse.RIGHT = value;
			// this.mouse.LEFT = value === THREE.MOUSE.PAN ? THREE.MOUSE.ROTATE : THREE.MOUSE.PAN;
			// this.reInitControls();
		},

		/**
		 * 其他选择更改
		 */
		otherSelect (type) {
			this.onContextMenuClick(type);
		},

		/**
		 * 初始化控件
		 */
		initControls (controlType = ControlTypeEnum.Trackball) {
			this.setControlType(controlType);

			// if (this.getCameraType() === 'Perspective') {
			// 初始化透视相机控制器
			const perspectiveControls = this.createControl(this.getPerspectiveCamera(), this.camCenter);
			this.setPerspectiveControls(perspectiveControls);

			if (this.camCenter) {
				this.camToSave['Perspective'].controlCenter = this.camCenter.clone();
			}
			// } else {
			// 初始化正交相机控制器
			const orthographicControls = this.createControl(this.getOrthographicCamera(), this.camCenter);
			this.setOrthographicControls(orthographicControls);

			if (this.camCenter) {
				this.camToSave['Orthographic'].controlCenter = this.camCenter.clone();
			}
			// }
		},

		/**
		 * 重新初始化控件
		 */
		reInitControls (controlType) {
			this.getOrthographicControls()?.dispose();
			this.setOrthographicControls(null);

			this.getPerspectiveControls()?.dispose();
			this.setPerspectiveControls(null);

			this.initControls(controlType);
			if (this.openAnimate) {
				this.animate();
			} else {
				this.renderRequested = false;
				this.requestRender();
			}
		},

		/**
		 * 关于控制器变化
		 */
		onControlChange (callRender = true) {
			this.updateRuler();
			// this.getArrowCamera().up = this.curCamera.up;
			// this.updateGridPlane();
			callRender && this.requestRender();
		},

		/**
		 * 更新标尺
		 */
		updateRuler (isInit = false, percent) {
			if (isInit) {
				this.rulerLoaded = true;
				sleep(1, () => this.$refs.canvasRulerRef?.init());
				return;
			}

			const zoom = percent || this.curCamera.zoom;
			this.long = Number(toExponential(this.modelLong / zoom, 2));
			this.lastZoom = zoom;
			sleep(1, () => this.$refs.canvasRulerRef?.init(this.long));
			this.rulerLoaded = true;
		},

		/**
		 * 初始化事件
		 */
		initEvents () {
			const domElement = this.getRenderer()?.domElement;
			ctrlZCallback = () => {
				if (!rebackList.length) return;
				const func = rebackList.pop();
				func();
			};
			delCallback = () => {
				this.curControl.enabled = true;
				if (!this.currentActiveItem) return;
				const object = this.currentActiveItem;
				if (object) {
					this.nickname = object.groupName;
					if (this.isSphere(object)) {
						const item = this.curvesMap[object.groupName];
						const index = item?.spheres.findIndex(it => it === object);
						if (index !== -1 && item.spheres.length <= 2) {
							this.$message.warn('无法删除该点，请删除预设线！');
							return;
						}
						item.clickPoints.splice(index, 1);
						item.spheres.splice(index, 1);
						this.transformControls.detach();
						this.removeSceneObj(this.transformControls);
						this.transformControls = null;
						this.updateDrawCurve();
					} else if (this.isLine(object)) {
						this.removeSceneYsLineByNameList([object.nickname]);
						if (this.transformControls) {
							this.removeSceneObj(this.transformControls.object);
							this.transformControls.detach();
							this.removeSceneObj(this.transformControls);
							this.transformControls = null;
						}
					}
				}
				this.currentActiveItem = null;
				if (this.isYsLine(object, true)) {
					this.emitCurveData('ys_line');
				} else if (this.isCircle(object)) {
					this.emitCircleData();
				}
			};

			ctrlSCallback = (event) => {
				preventDefaults(event);
				this.onSubmitHandler();
			};
			shiftCallback = (event) => {
				// if (event.shiftKey) {
				// 	logger.log('初始化框选事件.');
				// 	this.initBoxSelectEvent();
				// } else {
				// 	logger.log('取消框选事件.');
				// 	this.removeBoxSelectListen();
				// }
			};
			ctrlVCallback = () => {
				this.onCopy(this.copyByName);
			};

			addEventHandler(window, 'keydown', onKeydown, true);
			addEventHandler(window, 'keyup', onKeyUp, true);
			addEventHandler(window, 'resize', this.onMainCanvasResize, true);

			// 禁用右键默认行为
			document.oncontextmenu = (evt) => {
				evt.preventDefault();
				return false;
			};
			// init renderer Event
			// addEventHandler(document, 'pointerdown', this.onMouseDown, true);
			addEventHandler(domElement, 'click', this.onMouseClick, true);
			addEventHandler(domElement, 'wheel', this.onMouseWheel, true);
			addEventHandler(domElement, 'mousemove', this.onMouseMove, true);

			this.onMainCanvasResize();
			this.requestRender();
		},

		/**
		 * 提交处理程序
		 */
		onSubmitHandler () {
			this.$emit('submit');
		},

		/**
		 * 初始化框选事件
		 */
		initBoxSelectEvent (e) {
			if (selectionBox) return;
			// 锁定模型，防止拖动时视图跟着转动
			this.curControl.enabled = false;
			this.clearBoxSelectHelper();
			selectionBox = new SelectionBox(this.curCamera, this.getScene());
			helper = new SelectionHelper(selectionBox, this.getRenderer(), 'selectBox');
			// this.pointerdownFun(e);
			addEventHandler(document, 'pointerdown', this.pointerdownFun, true);
			addEventHandler(document, 'pointermove', this.pointermoveFun, true);
			addEventHandler(document, 'pointerup', this.pointerupFun, true);
		},

		clearBoxSelectHelper () {
			if (helper) {
				helper.element.classList.remove('selectBox');
				helper.element.remove();
			}
			if (selectionBox) {
				selectionBox.collection = [];
			}
			selectionBox = null;
			helper = null;
		},

		pointerdownFun (event) {
			if (selectionBox) {
				selectionBox.collection = [];
				document.getElementById('mainCanvas').style.cursor = 'crosshair';
				this.updateColor(selectionBox.collection, this.defaultColor, ColorEnum.black);
				this.setPoint(event, selectionBox.startPoint);
			}
		},

		pointermoveFun (event) {
			if (helper && helper.isDown) {
				this.updateColor(selectionBox.collection, this.defaultColor, ColorEnum.black);
				this.setPoint(event, selectionBox.endPoint);
				const allSelected = selectionBox.select();
				this.updateColor(allSelected, this.selectColor);
				// logger.log('pointermoveFun allSelected: ', this.selectColor);
				this.requestRender();
			}
		},

		pointerupFun (event) {
			if (selectionBox && !(selectionBox.endPoint.x === 0 && selectionBox.endPoint.y === 0 && selectionBox.endPoint.z === 0)) {
				this.setPoint(event, selectionBox.endPoint);
				const allSelected = selectionBox.select();
				let list = this.filterResults(allSelected);
				list = this.updateColor(list, this.selectColor);
				boxAllSelected = this.formatNicknames(list);
				// logger.log('boxAllSelected: ', boxAllSelected);

				this.allSelected = list;

				if (this.curActiveObj) {
					this.$emit('boxSelectedHandler', boxAllSelected);
				}

				this.$message.destroy();
				this.removeBoxSelectListen();
			}
		},

		updateColor (list, hexColor, lineColor = null) {
			for (const item of list) {
				if (this.curActiveObj) {
					if (this.selectMode !== SelectModeEnum.line && this.selectMode !== SelectModeEnum.point && this.isModelFace(item)) {
						item.material?.color.set(new THREE.Color(hexColor));
						item.material?.emissive.set(new THREE.Color(0x333333));
					}
					if (this.selectMode === SelectModeEnum.line && this.isModelEdge(item)) {
						item.material?.color.set(new THREE.Color(lineColor ?? hexColor));
					}
				} else {
					if (this.isModelFace(item)) {
						item.material?.color.set(new THREE.Color(hexColor));
						item.material?.emissive.set(new THREE.Color(0x333333));
					}
					if (this.isModelEdge(item)) {
						item.material?.color.set(new THREE.Color(lineColor ?? hexColor));
					}
				}
				item.material.needsUpdate = true;
			}

			return list;
		},

		/**
		 * 删除框选监听
		 */
		removeBoxSelectListen (isFromView = false) {

			document.getElementById('mainCanvas').style.cursor = 'default';
			const boxSelectIconEl = document.querySelector('#boxSelectIcon');
			if (boxSelectIconEl && boxSelectIconEl.classList.contains('ant-tooltip-open')) {
				return;
			}

			if (this.curControl && !this.currentActiveItem) {
				this.curControl.enabled = true; // 释放模型锁定
			}

			if (!selectionBox) return;

			// // 独立Ctrl键激活 或者 来自导航icon 触发的事件
			// if (independentCtrlKeyActivate || isFromView) {
			// 	this.$message.info(`${ isFromView ? '鼠标超出边界' : '操作已完成' }，框选事件已释放.`);
			// }

			if (!this.curActiveObj) {
				this.updateColor(selectionBox.collection, this.defaultColor, ColorEnum.black);
			}
			this.clearBoxSelectHelper();

			removeEventHandler(document, 'pointerdown', this.pointerdownFun);
			removeEventHandler(document, 'pointermove', this.pointermoveFun);
			removeEventHandler(document, 'pointerup', this.pointerupFun);

			this.$emit('closeBoxSelect');
		},

		/**
		 * 设置鼠标点击点坐标
		 */
		setPoint (event, point) {
			const sliderWidth = 320;
			const headerHeight = 52;
			const canvas = this.getCanvas();
			const x = ((event.clientX - (this.collapsed ? 0 : sliderWidth)) / canvas.clientWidth) * 2 - 1;
			const y = -((event.clientY - headerHeight) / canvas.clientHeight) * 2 + 1;
			point.set(x, y, 0.5);
		},

		/**
		 * 过滤结果
		 * @param {Array} result
		 */
		filterResults (result) {
			// 过滤出面
			if (this.selectMode === SelectModeEnum.surface || this.checkSelectedModeIsSurface()) {
				return result.filter(child => this.isModelFace(child));
			}

			// 过滤出线
			if (this.selectMode === SelectModeEnum.line || this.checkSelectedModeIsLine()) {
				// 过滤出预设线
				if (this.curActiveObj?.key === 'ys_line') {
					return result.filter(child => this.isYsLine(child, true));
				}
				// 过滤出离散线(出入口可选择线)
				return result.filter(child => this.isModelEdge(child));
			}

			// 过滤出点
			if (this.selectMode === SelectModeEnum.point || this.checkSelectedModeIsPoint()) {
				return result.filter(child => child.isSphere);
			}

			// 过滤空值及线框值
			result = result.filter(c => c && !c.nickname?.startsWith('w_'));

			return result;
		},

		/**
		 * 检查所选类型是面
		 */
		checkSelectedModeIsSurface () {
			return this.curActiveObj?.activate && this.curActiveObj.mode === SelectModeEnum.surface;
		},

		/**
		 * 检查所选类型是线
		 */
		checkSelectedModeIsLine () {
			return this.curActiveObj?.activate && this.curActiveObj.mode === SelectModeEnum.line;
		},

		/**
		 * 检查所选类型是点
		 */
		checkSelectedModeIsPoint () {
			return this.curActiveObj?.activate && this.curActiveObj.mode === SelectModeEnum.point;
		},

		/**
		 * 格式化昵称
		 * @param {Array} result
		 */
		formatNicknames (result) {
			return [...result].map(c => c.nickname);
		},

		/**
		 * 在主画布上调整大小
		 */
		onMainCanvasResize () {
			const canvas = this.getCanvas();
			const renderer = this.getRenderer();
			const orthographicCamera = this.getOrthographicCamera();
			const perspectiveCamera = this.getPerspectiveCamera();
			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			const pixelRatio = window.devicePixelRatio;
			const width = canvas.clientWidth * pixelRatio | 0;
			const height = canvas.clientHeight * pixelRatio | 0;
			const needResize = canvas.width !== width || canvas.height !== height;
			const aspect = canvas.clientWidth / canvas.clientHeight;

			if (needResize) {
				renderer.setSize(width, height, false);
				const change = this.originalAspect / aspect;
				const newSize = this.viewSize * change;
				if (orthographicCamera) {
					orthographicCamera.left = -aspect * newSize / 2;
					orthographicCamera.right = aspect * newSize / 2;
					orthographicCamera.top = newSize / 2;
					orthographicCamera.bottom = -newSize / 2;
					orthographicCamera.updateProjectionMatrix();
				}

				if (perspectiveCamera) {
					perspectiveCamera.aspect = canvas.clientWidth / canvas.clientHeight;
					perspectiveCamera.updateProjectionMatrix();
				}

				if (this.openAnimate) {
					if (perspectiveControls) {
						perspectiveControls.handleResize();
					}
					if (orthographicControls) {
						orthographicControls.handleResize();
					}
				}
			}
			this.requestRender();
		},

		/**
		 * 初始化统计场景的组件和索引
		 */
		initStatistics () {
			const statisticsUtils = new StatisticsUtils();
			const info = statisticsUtils.statistic(this.getScene());
			logger.log('info: ', info);
		},

		/**
		 * 鼠标按下处理
		 */
		onMouseDown (e) {
			preventDefaults(e);
			if (e.button !== 0) {
				this.curAction = '';
				this.removeBoxSelectListen();
				return;
			}
			let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());
			intersects = intersects.filter(item => this.isLine(item.object) || this.isFace(item.object) || this.isBjPoint(item.object) ||
				this.isYsLine(item.object));
			if (intersects.length === 0) {
				this.curAction = ActionEnum.boxSelect;
				this.initBoxSelectEvent(e);
			} else {
				this.curAction = '';
				this.removeBoxSelectListen();
			}
		},

		/**
		 * 鼠标点击处理
		 */
		onMouseClick (e) {
			logger.log('onMouseClick', e);
			// preventDefaults(e);
			this.rightMenuVisible = false; // 关闭右键菜单
			this.removePointerEvent();

			if (this.taskInfoLoading) return;

			if (selectionBox) return;

			// 拾取选中拖动时，禁止单选操作
			// if (this.selectTarget) return;
			this.updatePointer(e);
			if (this.curPrePlaneMode) {
				this.onPrePlaneHandler(e);
			} else if (this.curDraw) {
				this.onDrawHandler(e);
			} else if (this.mirrorCurve.curve) {
				this.onDrawLineHandler(e);
			} else if (this.translate.curve) {
				this.onDrawPointHandler(e);
			} else if (this.canTransform) {
				let intersects = this.selectPartsHandle(e, true);
				logger.log('onMouseClick intersects.length: ', intersects.length);
				if (this.chooseLine) return;
				intersects = intersects.filter(
					item => ((this.isYsLine(item.object, true) || (this.isYsLine(item.object, false) && item.object.parent) || this.isCircle(item.object))) && !item.object.nickname?.startsWith('zyx'));
				if (!intersects.length) return;

				this.currentActiveItem = intersects[0].object;
				this.initTransFormControls(this.currentActiveItem);

				// if (!intersects.length) {
				// 	logger.log('初始化框选事件.');
				// 	this.initBoxSelectEvent();
				// } else {
				// 	logger.log('取消框选事件.');
				// 	this.removeBoxSelectListen();
				// }
			}
		},

		/**
		 * 绘图处理
		 * @param e
		 */
		onDrawHandler (e) {
			if ([DrawEnum.curve, DrawEnum.brokenLine].includes(this.curDraw)) { // 绘制曲线
				// if (!this.curActiveObj || this.curActiveObj.key !== 'ys_line') {
				// 	this.$message.warn('请先点选预设线参数');
				// 	return;
				// }
				this.drawCurve();
			} else if (this.curDraw === DrawEnum.point) { // 绘制点
				if (!this.curActiveObj || this.curActiveObj.key !== 'bj_point') {
					this.$message.info('请先点选变径点参数');
					return;
				}
				if (this.curActiveObj.key === 'bj_point') { // 变径点只能在管线上绘制
					const intersects = this.getPipeLineIntersects(e);
					if (intersects.length) {
						this.pointCount++;
						const point = intersects[0].point;
						const nameSuffix = intersects[0].object.nickname.replace(waterCoolingResultKeyMap.pipeline, '');
						const groupName = 'points_group' + nameSuffix;
						const nickname = 'point_' + this.pointCount;
						this.drawBjPointSphere(point.clone(), { nickname, groupName });
						this.$emit('updateBjPoint', { nickname, groupName, value: pick(point.clone(), ['x', 'y', 'z']) });
					} else {
						this.$message.info('变径点只能在管线上绘制，并且需避开其他变径点');
					}
				}
			} else if (this.curDraw === DrawEnum.circle) { // 绘制圆
				const intersects = this.getIntersects(this.clientX, this.clientY);
				if (intersects.length) {
					const point = intersects[0].point;
					this.drawCircle(this.getPoint(point), intersects[0]);
				}
			} else if ([DrawEnum.singleSpiralLine, DrawEnum.doubleHelixLine].includes(this.curDraw)) { // 绘制圆
				const intersects = this.getIntersects(this.clientX, this.clientY);
				if (intersects.length) {
					const point = intersects[0].point;
					this.drawCircularHelix(this.getPoint(point), intersects[0]);
				}
			}
		},

		onDrawPointHandler (e) {
			this.$message.destroy();
			const curveType = this.getTypeByNickname(this.translate.curve.nickname);
			const curveName = `${curveType}_${this[`${curveType}Count`] + 1}`;
			if (!this.translate.tempObj) {
				this.translate.tempObj = new THREE.Object3D();
				this.getScene().add(this.translate.tempObj);
			}
			if (!this.translate.points.length) {
				this.rebackAdd(this.currentFunc, () => {
					this.$message.destroy();
					this.modalEdit = false;
					this.translate.closeFunc && this.translate.closeFunc();
					this.translate.points = [];
					this.removeSceneObj(this.translate.currentItem);
				});
				this.currentFunc = ((ev) => () => {
					const pointVector = this.getCurrPoint(ev, (intersect) => {
						return intersect.object?.type === 'GridHelper';
					});
					if (!pointVector) return;
					this.translate.points.push(pointVector);
					this.translate.closeFunc = this.addMousemoveEventListener(({ clientX, clientY }) => {
						const pointVector = this.getCurrPoint({ clientX, clientY }, (intersect) => {
							return intersect.object?.type === 'GridHelper';
						});
						if (!pointVector) return;
						this.translate.points = [this.translate.points[0], pointVector];
					});
					this.$message.info('请选择参考点', 0);
				})(e);
				this.currentFunc();

			} else if (this.translate.points.length === 2) {
				this.rebackAdd(this.currentFunc, () => {
					this.modalEdit = false;
					enterCallback = null;
				});
				this.modalEdit = true;
				this.translate.closeFunc && this.translate.closeFunc();
				this.translate.closeFunc = null;
				enterCallback = () => {
					this.controlling = false;
					this.$emit('closeActionControl');
					this.$message.destroy();
					// this.curveCount++;
					this.drawCurve(curveName, this.curvesMap[curveName].clickPoints);
					this.resetTranslate();
					this.resetColorHandle();
					this.translate.showSetting = false;
					enterCallback = null;
					rebackList = [];
				};
			}
		},

		resetTranslate () {
			this.translate.closeFunc && this.translate.closeFunc();
			this.removeSceneObj([this.translate.tempObj, this.translate.currentItem]);
			this.translate = {
				points: [],
				showSetting: false,
				closeFunc: null,
				curve: '',
				tempObj: null,
				currentItem: null,
			};
			escCallback = null;
		},

		resetScale () {

			this.removeSceneObj(this.scaleCurve.currentItem);
			this.removeSceneObj(this.scaleCurve.tempObj);
			this.scaleCurve = {
				point: null,
				currentItem: null,
				curve: null,
				showSetting: false,
			};
			enterCallback = null;
		},

		onDrawLineHandler (e) {
			let mirrorPoint;
			this.$message.destroy();
			const curveType = this.getTypeByNickname(this.mirrorCurve.curve.nickname);
			const curveName = `${curveType}_${this[`${curveType}Count`] + 1}`;
			if (!this.mirrorCurve.tempObj) {
				this.mirrorCurve.tempObj = new THREE.Object3D();
				this.getScene().add(this.mirrorCurve.tempObj);
			}
			if (this.mirrorCurve.points.length < 2) {
				const mirrorPointVector = this.getCurrPoint({ clientX: e.clientX, clientY: e.clientY }, (intersect) => {
					return intersect.object?.type === 'GridHelper';
				});
				if (!mirrorPointVector) return;
				mirrorPoint = this.drawSphere2(mirrorPointVector);
				this.mirrorCurve.points.push(mirrorPointVector);
				this.mirrorCurve.tempObj.add(mirrorPoint);
			}
			if (this.mirrorCurve.points.length === 1) {
				let line = null;
				this.rebackAdd(this.currentFunc, () => {
					this.mirrorCurve.closeFunc && this.mirrorCurve.closeFunc();
					this.removeSceneObj(this.mirrorCurve.tempObj);
					this.removeSceneObj(this.mirrorCurve.mirrorItem);
				});
				this.currentFunc = () => {
					this.$message.destroy();
					this.$message.info('请选择参考点', 0);
					this.mirrorCurve.closeFunc = this.addMousemoveEventListener(({ clientX, clientY }) => {
						const pointerVector = this.getCurrPoint({ clientX, clientY }, (intersect) => {
							return intersect.object?.type === 'GridHelper';
						});
						if (!pointerVector) return;
						const lineGeometry = new LineGeometry();
						const points = [...this.mirrorCurve.points, pointerVector];
						const bufferGeometry = new THREE.BufferGeometry().setFromPoints(points);
						lineGeometry.setPositions(bufferGeometry.attributes.position.array);
						const lineMaterial = getLineMaterial(ColorEnum.black, this.getCanvas(), 2);
						if (line) this.mirrorCurve.tempObj?.remove(line);
						line = new Line2(lineGeometry, lineMaterial);
						this.mirrorCurve.tempObj?.add(line);
						this.drawMirror(points, curveName);
					});
				};
				this.currentFunc();
			} else if (this.mirrorCurve.points.length === 2) {
				this.rebackAdd(this.currentFunc, ((mirrorPoint) => () => {
					this.mirrorCurve.tempObj.remove(mirrorPoint);
					this.mirrorCurve.points.length = 1;
					enterCallback = null;
				})(mirrorPoint));
				this.mirrorCurve.closeFunc && this.mirrorCurve.closeFunc();
				this.mirrorCurve.closeFunc = null;
				enterCallback = () => {
					// this.curveCount++;
					this.controlling = false;
					this.$emit('closeActionControl');
					this.$message.destroy();
					this.drawCurve(curveName, this.curvesMap[curveName].clickPoints);
					this.resetMirror();
					this.resetColorHandle();
					enterCallback = null;
					rebackList = [];
				};
			}
		},

		resetMirror () {
			this.mirrorCurve.closeFunc && this.mirrorCurve.closeFunc();
			this.removeSceneObj([this.mirrorCurve.tempObj, this.mirrorCurve.mirrorItem]);
			this.mirrorCurve = {
				points: [],
				showMirrorSetting: false,
				closeFunc: null,
				curve: '',
				tempObj: null,
				mirrorItem: null,
			};
			escCallback = null;
		},

		drawMirror (points, curveName) {
			const { clickPoints } = this.curvesMap[this.mirrorCurve.curve.nickname];
			const { x, y, z, distance } = this.getMirrorPlaneNormal(points);
			const mirrorMatrix = new THREE.Matrix4();
			mirrorMatrix.set(
				1 - 2 * x * x, -2 * x * y, -2 * x * z, -2 * x * distance,
				-2 * x * y, 1 - 2 * y * y, -2 * y * z, -2 * y * distance,
				-2 * x * z, -2 * y * z, 1 - 2 * z * z, -2 * z * distance,
				0, 0, 0, 1,
			);
			const newClickPoints = clickPoints.map(item => item.clone().applyMatrix4(mirrorMatrix));

			this.curvesMap[curveName] = {
				spheres: [],
				clickPoints: newClickPoints,
			};
			const mesh = this.mirrorCurve.curve;
			const geometry = mesh.geometry.clone();
			geometry.applyMatrix4(mirrorMatrix);
			const newMesh = new Line2(geometry, mesh.material);
			this.getScene().add(newMesh);
			this.mirrorCurve.mirrorItem && this.removeSceneObj(this.mirrorCurve.mirrorItem);
			this.mirrorCurve.mirrorItem = newMesh;
		},

		getMirrorPlaneNormal (points) {
			const point = new THREE.Vector3();
			new THREE.Line3(...points).closestPointToPoint(this.camCenter, false, point);
			const vec = new THREE.Vector3();
			vec.subVectors(...points);
			const { x, y, z } = point;
			const diyNormal = this.gridHelper.diyNormal.clone().normalize();
			const nVector = diyNormal.cross(vec.normalize());
			const distance = -nVector.x * x - nVector.y * y - nVector.z * z;
			return { ...nVector, distance };
		},

		resetRotate () {
			this.rotation.currentItems.forEach(item => this.removeSceneObj(item));
			this.removeSceneObj(this.centerPoint.object);
			this.rotation = {
				showSetting: 0,
				point: null,
				currentItems: [],
				curve: null,
			};
		},

		/**
		 * 鼠标移动处理
		 */
		onMouseMove (e) {

			if (selectionBox) {
				return false;
			}

			if (!this.curCamera) {
				return false;
			}

			if (!this.curActiveObj) {
				return false;
			}

			if (this.drawing) {
				return false;
			}

			if (this.currentActiveItem) {
				return false;
			}

			let object;
			const intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());
			if (intersects?.length) {
				object = intersects[0].object;
			}
			this.$emit('tip', object);
		},

		/**
		 * 鼠标右键事件
		 */
		onContextmenu (e) {
			e.preventDefault();
			this.updatePointer(e);

			if (this.taskInfoLoading) return;

			this.isMoved = false;
			// this.contextMenuObject = null;

			const intersects = this.selectPartsHandle(e, false);
			if (intersects.length && intersects[0]?.object?.nickname) {
				logger.log('鼠标右键事件 intersects: ', intersects[0]);
				// this.contextMenuObject = intersects[0];
			}
			// this.rightMenuVisible = true;
		},

		/**
		 * 添加指针事件
		 */
		addPointerEvent () {
			this.removePointerEvent();
			const domElement = document.querySelector('#mainCanvas');
			// addEventHandler(domElement, 'pointerdown', (event) => this.pointerdownHandler(event));
			addEventHandler(domElement, 'pointermove', (event) => this.pointermoveHandler(event));
			addEventHandler(domElement, 'pointerup', (event) => this.pointerupHandler(event));
		},

		/**
		 * 删除指针事件
		 */
		removePointerEvent () {
			this.isMoved = false;
			const domElement = document.querySelector('#mainCanvas');
			// removeEventHandler(domElement, 'pointerdown', (event) => this.pointerdownHandler(event));
			removeEventHandler(domElement, 'pointermove', (event) => this.pointermoveHandler(event));
			removeEventHandler(domElement, 'pointerup', (event) => this.pointerupHandler(event));
		},

		/**
		 * 鼠标滚轮事件处理
		 */
		onMouseWheel (event) {
			if (this.curControl.zoomSpeed > 5) {
				return;
			}
			this.scrolled = true;
			const deltaY = event.deltaY;
			const vector3Mesh = this.getIntersects(event.clientX, event.clientY)[0]?.point;
			const { x, y } = this.getPosition(event);
			const vector = vector3Mesh || new THREE.Vector3(x, y, 1);
			this.initDistance();
			if (vector) {
				const AE = new THREE.Vector3().subVectors(vector, this.curCamera.position);
				const AB = AE.clone().normalize().multiplyScalar(-deltaY * 0.1);
				const OB = new THREE.Vector3().addVectors(this.curCamera.position, AB);
				const distance = OB.distanceTo(vector);
				const AC = new THREE.Vector3().subVectors(this.curControl.target, this.curCamera.position);
				const BE = new THREE.Vector3().subVectors(AE, AB);
				const BF = new THREE.Vector3().multiplyVectors(AC, BE).divide(AE);
				const OF = new THREE.Vector3().addVectors(this.curCamera.position, AB).add(BF);
				if (distance < -deltaY * 0.1) return;
				this.curCamera.position.add(AB);
				this.curControl.position0.add(AB);
				this.curControl.target = OF;
				this.curControl.update();
				const distance1 = this.curCamera.position.distanceTo(OF);
				this.updateRuler(false, this.distanceLong / distance1);
			}
		},

		/**
		 * 复制
		 */
		onCopy (name) {
			if (!name) return;
			const newKey = name + '_copy';
			this.nickname = newKey;
			this.curvesMap[newKey] = {
				spheres: [],
				clickPoints: [],
			};

			const curveMap = this.curvesMap[newKey];
			const { spheres, ysLine } = this.getYsLineObjectAndSphereByGroupName(name);
			const newYsLine = this.copyObj(ysLine);

			spheres.forEach(sphere => {
				const copySphere = this.copyObj(sphere);
				curveMap.spheres.push(copySphere);
				curveMap.clickPoints.push(copySphere.position.clone());
				raycasterObjs.push(copySphere);
				this.intersectObjects.push(copySphere); // 存入相交对象列表
			});
			if (curveMap.spheres.length) {
				this.getScene().add(...curveMap.spheres);
				this.requestRender();
			}
			if (newYsLine) {
				raycasterObjs.push(newYsLine);
				const ysLineGroup = this.getGroupByName(YS_LINE_GROUP);
				ysLineGroup.add(newYsLine);
				this.requestRender();
			}
			logger.log('复制成功');
		},

		copyObj (source) {
			const type = source.geometry.type;
			if (type === 'BufferGeometry') {
				return this.copyFace(source);
			}
			if (type === 'LineGeometry') {
				return this.copyLine(source);
			}
			if (type === 'SphereGeometry') {
				return this.copySphere(source);
			}
		},

		copyFace (source) {
			const cloneObj = source.clone(true);

			this.updateAttr(source, cloneObj);

			const material = this.getMeshMaterial(ColorEnum.red, 100);
			cloneObj.material = material;

			// 网格模型沿着x轴正方向平移20
			cloneObj.translateX(20);

			return cloneObj;
		},

		copyLine (source) {

			const cloneObj = source.clone(true);

			this.updateAttr(source, cloneObj);

			cloneObj.material = getLineMaterial(ColorEnum.red, this.getCanvas(), 2);

			// 网格模型沿着x轴正方向平移20
			cloneObj.translateX(20);

			return cloneObj;
		},

		copySphere (source) {

			const cloneObj = source.clone(true);

			this.updateAttr(source, cloneObj);

			const material = new THREE.MeshBasicMaterial({ color: ColorEnum.yellow });
			cloneObj.material = material;

			// 网格模型沿着x轴正方向平移20
			cloneObj.translateX(20);

			return cloneObj;
		},

		updateAttr (source, cloneObj) {
			const type = cloneObj.geometry.type;
			if (type === 'LineGeometry' && source.isCurve) {
				cloneObj.isCurve = true;
			}
			if (type === 'BufferGeometry' && source.isFace) {
				cloneObj.isFace = true;
			}
			if (type === 'SphereGeometry' && source.isSphere) {
				cloneObj.isSphere = true;
			}
			cloneObj.nickname = source.nickname ? source.nickname + '_copy' : source.nickname;
			cloneObj.groupName = source.groupName ? source.groupName + '_copy' : source.groupName;
		},

		/**
		 * 更新指针缓存坐标
		 */
		updatePointer (e) {
			const { x, y } = this.getPosition(e);
			this.pointer.x = x;
			this.pointer.y = y;
			this.clientX = e.clientX;
			this.clientY = e.clientY;
		},

		/**
		 * 选择部件处理
		 */
		selectPartsHandle (e, isAdd) {
			if (!this.curCamera) return;
			let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas()).filter(item => item.object.type !== 'GridHelper' && !this.isModelWireframe(item.object));
			const mode = this.curActiveObj?.mode || this.selectMode;
			if (mode === SelectModeEnum.point) {
				intersects = intersects.filter(item => this.isSphere(item.object));
			} else if (mode === SelectModeEnum.line && !this.curDraw) {
				intersects = intersects.filter(item => (this.isLine(item.object) || this.isSphere(item.object)) && !this.isModelWireframe(item.object));
			} else if (mode === SelectModeEnum.surface && !this.curDraw) {
				intersects = intersects.filter(item => this.isFace(item.object));
			}
			if (!intersects.length) {
				!this.curActiveObj && !this.transformControls && this.resetColorHandle();
				return [];
			};
			if (!this.curDraw) {
				const clickCurrent = intersects[0];
				const clickCurrentObj = clickCurrent.object;
				const nickname = clickCurrentObj.nickname || clickCurrentObj.groupName || clickCurrentObj.userData.nickname;
				if (!nickname) return intersects;
				const selectColor = this.curActiveObj ? toHexColor(this.curActiveObj?.color) : ColorEnum.white;
				const changeItem = () => {
					if (!clickCurrentObj.isLackFace) {
						clickCurrentObj.material.color.set(isAdd ? new THREE.Color(selectColor) : this.originColor);
						if (mode === SelectModeEnum.surface && this.isFace(clickCurrentObj) && isAdd) {
							clickCurrentObj.material.emissive.set(new THREE.Color(0x333333));
						}
					}
					clickCurrentObj.material.needsUpdate = true;
					this.requestRender();
				};
				if (this.curActiveObj) {	// 左侧为点选状态
					if (this.curActiveObj.value.includes(nickname) && !isAdd) {

						const color = this.isLine(clickCurrentObj) ? ColorEnum.black : this.originColor;
						this.updateObjAttr(clickCurrentObj, { color: color });
					} else if (isAdd) {
						changeItem();
					}
					this.$emit('singleSelectedHandler', { name: nickname, isAdd });
				} else {
					changeItem();
				}
			}
			return intersects;
		},

		/**
		 * 选择部件处理
		 */
		// selectPartsHandle1 (e, isAdd) {

		// 	if (!this.curCamera) {
		// 		return false;
		// 	}
		// 	let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());

		// 	if (this.curActiveObj?.mode) {
		// 		intersects = intersects.filter(item => item.object.type !== 'GridHelper');

		// 		// 选点直接返回
		// 		if (this.curActiveObj.mode === SelectModeEnum.point) {
		// 			intersects = intersects.filter(item => this.isSphere(item.object));
		// 		}

		// 		// 过滤出所有线
		// 		if (this.curActiveObj.mode === SelectModeEnum.line && !this.curDraw) {
		// 			// logger.log('selectPartsHandle intersects', raycasterObjs.map(i => i.nickname || i.userData.nickname || i.groupName));
		// 			intersects = intersects.filter(item => this.isLine(item.object) || this.isSphere(item.object));
		// 			if (this.curActiveObj) { // 过滤出所有线(不包含边框线)
		// 				intersects = intersects.filter(item => !this.isModelWireframe(item.object));
		// 			}
		// 		}

		// 		// 过滤出所有面
		// 		if (this.curActiveObj.mode === SelectModeEnum.surface && !this.curDraw) {
		// 			intersects = intersects.filter(item => this.isFace(item.object));
		// 		}
		// 	} else {
		// 		// 选点直接返回
		// 		if (this.selectMode === SelectModeEnum.point) {
		// 			intersects = intersects.filter(item => this.isSphere(item.object));
		// 		}

		// 		// 过滤出所有线
		// 		if (this.selectMode === SelectModeEnum.line && !this.curDraw) {
		// 			// logger.log('selectPartsHandle intersects', raycasterObjs.map(i => i.nickname || i.userData.nickname || i.groupName));
		// 			intersects = intersects.filter(item => this.isLine(item.object) || this.isYsLine(item.object, true));
		// 			if (this.curActiveObj) { // 过滤出所有线(不包含边框线)
		// 				intersects = intersects.filter(item => !this.isModelWireframe(item.object));
		// 			}
		// 		}

		// 		// 过滤出所有面
		// 		if (this.selectMode === SelectModeEnum.surface && !this.curDraw) {
		// 			intersects = intersects.filter(item => this.isFace(item.object));
		// 		}
		// 	}

		// 	if (!this.curDraw) {
		// 		if (intersects.length > 0) {
		// 			// 多个相交物体获取离镜头最近的
		// 			const clickVisibleObj = intersects[0];
		// 			const clickVisibleMesh = clickVisibleObj.object; // 需操作的网格模型
		// 			const nickname = clickVisibleMesh.nickname || clickVisibleMesh.groupName;
		// 			if (!this.curActiveObj) {
		// 				this.resetColorHandle();
		// 			} else if (this.curActiveObj?.value.includes(nickname)) {
		// 				if (this.isYsLine(clickVisibleMesh, true) || !isAdd) {
		// 					const color = this.isLine(clickVisibleMesh) ? ColorEnum.black : this.originColor;
		// 					this.updateObjAttr(clickVisibleMesh, { color });
		// 					if (nickname) {
		// 						this.$emit('singleSelectedHandler', { name: nickname, isAdd });
		// 					}
		// 				}
		// 				return intersects;
		// 			}

		// 			if (!isAdd) {
		// 				return intersects;
		// 			}

		// 			const selectColor = toHexColor(this.curActiveObj?.color);
		// 			const chooseColor = this.curActiveObj ? selectColor : ColorEnum.white;

		// 			if (this.curActiveObj?.mode) {

		// 				// 面
		// 				if (this.curActiveObj.mode === SelectModeEnum.surface && this.isFace(clickVisibleMesh)) {
		// 					if (!clickVisibleMesh.isLackFace) { // 非坏面(缺失的面)才可操作
		// 						clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
		// 						clickVisibleMesh.material.emissive.set(new THREE.Color(0x333333));
		// 					}
		// 				}

		// 				// 线
		// 				if (this.curActiveObj.mode === SelectModeEnum.line && this.isLine(clickVisibleMesh)) {
		// 					clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
		// 				}
		// 			} else {
		// 				// 体
		// 				if (this.selectMode === SelectModeEnum.solid) {
		// 					this.updateColorHandle(clickVisibleObj);
		// 				}

		// 				// 面
		// 				if (this.selectMode === SelectModeEnum.surface && this.isFace(clickVisibleMesh)) {
		// 					if (!clickVisibleMesh.isLackFace) { // 非坏面(缺失的面)才可操作
		// 						clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
		// 						clickVisibleMesh.material.emissive.set(new THREE.Color(0x333333));
		// 					}
		// 				}

		// 				// 线
		// 				if (this.selectMode === SelectModeEnum.line && this.isLine(clickVisibleMesh)) {
		// 					clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
		// 				}
		// 			}

		// 			logger.log('selectPartsHandle', clickVisibleMesh);
		// 			clickVisibleMesh.material.needsUpdate = true;
		// 			this.requestRender();

		// 			if (clickVisibleMesh) {
		// 				const nickname = clickVisibleMesh.nickname || clickVisibleMesh.userData.nickname;
		// 				logger.log('selectPartsHandle', nickname);

		// 				if (nickname) {
		// 					this.$emit('singleSelectedHandler', { name: nickname, isAdd });
		// 				}
		// 				if (selectSwitch === TagSwitchEnum.ON && nickname) {
		// 					this.createTag(clickVisibleMesh);
		// 				}
		// 			}
		// 		} else {
		// 			if (!this.curActiveObj) {
		// 				this.resetColorHandle();
		// 				// } else {
		// 				// 	this.onMouseDown(e);
		// 			}
		// 			this.clearTag();
		// 		}
		// 	}

		// 	return intersects;
		// },

		/**
		 * 判断是否面
		 */
		isFace (object) {
			return object?.material?.type === 'MeshPhongMaterial';
		},

		/**
		 * 判断是否线
		 */
		isLine (object, filter = () => true) {
			return (object?.material?.type === 'LineMaterial' || this.isModelEdge(object) || this.isYsLine(object, true) || this.isCircle(object)) && filter(object);
		},

		/**
		 * 判断是否球体
		 */
		isSphere (object) {
			return object?.geometry?.type === 'SphereGeometry';
		},

		/**
		 * 颜色重置处理程序
		 */
		resetColorHandle () {
			for (const raycasterObj of raycasterObjs) {
				if (!raycasterObj.material) {
					break;
				}
				if (this.isModelFace(raycasterObj)) {
					if (!raycasterObj.isLackFace) { // 非坏面(缺失的面)才可操作
						raycasterObj.material.color.set(new THREE.Color(this.originColor));
						raycasterObj.material.emissive.set(new THREE.Color(0x333333));
					}
				} else if (this.isLine(raycasterObj)) {
					raycasterObj.material.color.set(new THREE.Color(ColorEnum.black));
				}
				raycasterObj.material.needsUpdate = true;
			}
			this.requestRender();
		},

		/**
		 * 透明度重置处理程序
		 */
		resetOpacityHandle () {
			for (const raycasterObj of raycasterObjs) {
				if (this.isModelFace(raycasterObj)) {
					raycasterObj.material.opacity = this.originOpacity;
				}
				raycasterObj.material.needsUpdate = true;
			}
			this.requestRender();
		},

		/**
		 * 可见重置处理程序
		 */
		resetVisibleHandle () {
			this.changeRenderMode(this.curRenderMode);
		},

		/**
		 * 颜色更新处理程序
		 */
		updateColorHandle (intersect) {
			for (const raycasterObj of raycasterObjs) {
				if (raycasterObj.solidGroup === intersect.object.solidGroup && raycasterObj.material) {
					if (this.isFace(raycasterObj) && !raycasterObj.isLackFace) { // 非坏面(缺失的面)才可操作
						raycasterObj.material.color.set(new THREE.Color(ColorEnum.white));
						raycasterObj.material.emissive.set(new THREE.Color(0x333333));
					}
					raycasterObj.material.needsUpdate = true;
				}
			}
			this.requestRender();
		},

		/**
		 * 添加标签
		 */
		createTag (object) {
			const canvas = this.getCanvas();
			const w = canvas.clientWidth / 2;
			const h = canvas.clientHeight / 2;
			const x = Math.round(this.pointer.x * w + w); // 标准设备坐标转屏幕坐标
			const y = Math.round(-this.pointer.y * h + h) - 35;

			// 修改 div 的位置
			const div = document.querySelector('#label');
			div.style.display = 'block';
			div.style.padding = '5px';
			div.style.position = 'absolute';
			div.style.backgroundColor = 'rgba(155, 0, 155, 0.8)';
			div.style.left = x + 'px';
			div.style.top = y + 'px';
			div.innerHTML = object.nickname; // 显示模型信息
		},

		/**
		 * 清除标签
		 */
		clearTag () {
			const divDom = document.querySelector('#label');
			divDom.style.display = 'none';
			divDom.innerHTML = '';
		},

		requestRender () {
			if (!this.renderRequested) {
				this.renderRequested = true;
				if (this.openAnimate) {
					this.render();
				} else {
					cancelAnimationFrame(animationId);
					animationId = requestAnimationFrame(this.render);
				}
			}
		},

		animate () {
			if (this.openAnimate) {
				cancelAnimationFrame(animationId);
				animationId = requestAnimationFrame(this.animate);
				this.render();
			}
		},

		render () {
			this.renderRequested = false;

			const scene = this.getScene();
			const renderer = this.getRenderer();
			const cameraType = this.getCameraType();
			const arrowScene = this.getArrowScene();
			const arrowCamera = this.getArrowCamera();
			const arrowRenderer = this.getArrowRenderer();
			const perspectiveCamera = this.getPerspectiveCamera();
			const orthographicCamera = this.getOrthographicCamera();
			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();
			perspectiveControls?.update();
			orthographicControls?.update();

			if (arrowCamera) {
				if (cameraType === 'Perspective' && perspectiveControls) {
					arrowCamera.position.copy(perspectiveCamera.position);
					arrowCamera.position.sub(perspectiveControls.target);
				}
				if (cameraType === 'Orthographic' && orthographicControls) {
					arrowCamera.position.copy(orthographicCamera.position);
					arrowCamera.position.sub(orthographicControls.target);
				}
				arrowCamera.lookAt(arrowScene.position);
				arrowCamera.position.setLength(200);
			}

			this.stats?.begin();
			if (cameraType === 'Perspective' && perspectiveControls) {
				renderer?.render(scene, perspectiveCamera);
			}
			if (cameraType === 'Orthographic' && orthographicControls) {
				renderer?.render(scene, orthographicCamera);
			}
			arrowRenderer?.render(arrowScene, arrowCamera);
			this.stats?.end();
		},

		/**
		 * 解析数据
		 */
		parseData (data) {
			if (data?.compressed) {
				// Decode from base90 and distribute the floats
				for (const obj of data.objects) {
					obj.floats = JSON.parse('[' + floatDecode(data, obj.floats) + ']');
					obj.verts = baseDecode(data, obj.verts).map(x => obj.floats[x]);
					obj.facets = baseDecode(data, obj.facets);
					obj.wires = obj.wires.map(w => baseDecode(data, w).map(x => obj.floats[x]));
					obj.facesToFacets = obj.facesToFacets.map(x => baseDecode(data, x));
				}
			}
			return data;
		},

		/**
		 * 模具的面或边
		 */
		isModelFaceOrEdge (obj) {
			return this.isModelFace(obj) || this.isModelWireframe(obj);
		},

		/**
		 * 模具的面
		 */
		isModelFace (obj) {
			return obj.nickname?.startsWith('face_');
		},

		/**
		 * 模具的边
		 */
		isModelEdge (obj) {
			return (obj.nickname?.startsWith('e_') && obj.allowSelect) || obj.nickname?.startsWith('circle_');
		},

		/**
		 * 模具的线框
		 */
		isModelWireframe (obj) {
			return obj.groupName === WIREFRAME || obj.isWireframe;
		},

		/**
		 * 变径点
		 */
		isBjPoint (obj) {
			return (obj.nickname || obj.groupName)?.startsWith('points_');
		},

		/**
		 * 预设线
		 */
		isYsLine (obj, isLine) {
			if (isLine === obj.isSphere) {
				return false;
			}
			return this.isYsLineName(obj.nickname || obj.groupName || obj.userData.nickname);
		},

		isOtherLine (obj) {
			return obj.nickname?.startsWith('zyx_');
		},

		isCircle (obj) {
			return obj.nickname?.startsWith('circle_');
		},

		/**
		 * 预设线名称
		 */
		isYsLineName (name) {
			return name?.match(/^curve_|^brokenLine_|^singleSpiralLine_|^doubleHelixLine_|^zyx_/);
		},

		/**
		 * 重置视窗大小
		 */
		resizeCurrentView () {
			this.jsViewWidth = this.collapsed ? 'calc(100vw)' : 'calc(100vw - 320px)';
			this.$nextTick(() => this.onMainCanvasResize());
		},

		/**
		 * 开启加载中
		 */
		openLoading (loadingMsg = '', resetCount = false) {
			this.tip = loadingMsg || '加载中...';
			this.loading = true;
			if (resetCount) {
				this.loadingCount = 0;
			}
			this.loadingCount++;
			// logger.log('openLoading loadingCount: ', this.loadingCount);
		},

		/**
		 * 关闭加载中
		 */
		closeLoading () {
			this.loadingCount--;
			// logger.log('closeLoading loadingCount: ', this.loadingCount);
			if (this.loadingCount <= 0) {
				this.loadingCount = 0;
				this.loading = false;
				this.tip = '';
			}
		},

		changeViewCamera () {
			this.resetCamera(1);
			const type = this.direction === ViewEnum.default ? 'Perspective' : 'Orthographic';
			this.setCameraType(type);
			this.cameraChange(type, true);
		},

		/**
		 * 更新视图
		 */
		updateView (direction) {
			this.direction = direction;
			this.changeViewCamera();
			// 禁用缩放、旋转，允许平移
			if (this.curControl instanceof OrbitControls) {
				this.curControl.enableZoom = direction === ViewEnum.default;
				this.curControl.enableRotate = direction === ViewEnum.default;
			}
			if (this.curControl instanceof TrackballControls) {
				this.curControl.noZoom = direction !== ViewEnum.default;
				this.curControl.noRotate = direction !== ViewEnum.default;
			}

			this.mouse.RIGHT = THREE.MOUSE.PAN;
			switch (direction) {
				case ViewEnum.front:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, -1, 0]);
					break;
				case ViewEnum.back:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, 1, 0]);
					break;
				case ViewEnum.left:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([-1, 0, 0]);
					break;
				case ViewEnum.right:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([1, 0, 0]);
					break;
				case ViewEnum.top:
					this.curCamera.up.set(0, 1, 0);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, -0.0001, 1]);
					break;
				case ViewEnum.bottom:
					this.curCamera.up.set(0, 1, 0);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, -0.0001, -1]);	// 不加0.0001的偏差 会产生旋转不及预期
					break;
				default:
					this.mouse.RIGHT = THREE.MOUSE.ROTATE;
					const vec = new THREE.Vector3(0, -0.46, 0.2).normalize();
					this.navChange([vec.x, vec.y, vec.z], true);
					break;
			}

			this.getArrowCamera().up = this.curCamera.up;
			this.getArrowCamera().lookAt(0, 0, 0);
			this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 更改投影模式
		 */
		changeCameraMode (type) {
			if (type === CameraModeEnum.perspective) {
				this.cameraChange('Perspective');
			} else if (type === CameraModeEnum.orthographic) {
				this.cameraChange('Orthographic');
			}
			this.resetCamera();
		},

		/**
		 * 选择模型(点、线、面、体)
		 */
		selectModeHandle (selectMode) {
			this.selectMode = selectMode;
		},

		/**
		 * 右键菜单点击事件
		 */
		onContextMenuClick (type) {

			this.rightMenuVisible = false;

			if (type === 'show') {
				this.hiddenObjectMap = {};
				this.updateAllVisible(true);
				this.updateYsLineAllVisible(true);
				this.updateBjPointAllVisible(true);
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'copy') {
				this.onCopy(this.copyByName);
			}
			// if (type === 'move') {
			// 	const line = this.getGroupByName(YS_LINE_GROUP).children.find(child => child.nickname === this.copyByName);
			// 	// this.intersectObject = line;
			// 	// this.initTransFormControls();
			// 	this.initDragControls([line]);
			// }
			// if (type === 'rotate') {
			// 	const line = this.getGroupByName(YS_LINE_GROUP).children.find(child => child.nickname === this.copyByName);
			// 	// this.intersectObject = line;
			// 	// this.initTransFormControls({ mode: 'rotate' });
			// 	// this.initDragControls([line]);
			// }
			if (type === 'hidden') {
				this.hiddenObjectMap[this.contextMenuObject.object.nickname] = true;
				this.updateVisibleByName(this.contextMenuObject.object.nickname, false);
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hiddenAllFace') {
				const allFaceMesh = this.getSceneAllFace();
				for (const faceMesh of allFaceMesh) {
					this.hiddenObjectMap[faceMesh.nickname] = true;
					this.updateObjAttr(faceMesh, { visible: false });
				}
				this.requestRender();
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hiddenWireframe') {
				const allWireframeMesh = this.getSceneAllWireframe();
				for (const wireframeMesh of allWireframeMesh) {
					this.hiddenObjectMap[wireframeMesh.nickname] = true;
					this.updateObjAttr(wireframeMesh, { visible: false });
				}
				this.requestRender();
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hiddenAllYsLine') {
				const { spheres, ysLines } = this.getAllYsLineAndSpheres();
				const allDispersedLine = this.getSceneAllDispersedLine();
				for (const obj of [...ysLines, ...spheres, ...allDispersedLine]) {
					if (obj.nickname) this.hiddenObjectMap[obj.nickname] = true;
					this.updateObjAttr(obj, { visible: false });
				}
				this.requestRender();
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'screenshot') {
				this.screenshot(this.taskId, false);
			}
		},

		/**
		 * 绘制曲线
		 */
		drawCurve (nickname, clickPoints, isInitDrawCurve = false) {
			return new Promise(async (resolve) => {

				this.isInitDrawCurve = isInitDrawCurve;

				this.nickname = nickname || this.nickname;

				const resetCurvesMap = () => {
					raycasterObjs = raycasterObjs.filter(item => item.nickname !== nickname && item.groupName !== nickname);
					this.removeSceneObj(this.curvesMap[this.nickname]?.spheres);
					this.curvesMap[this.nickname] = {
						spheres: [],
						clickPoints: [],
					};
				};
				const commonHandler = (pointVector, isTemporary) => {
					if ([DrawEnum.curve, DrawEnum.brokenLine].includes(this.getTypeByNickname(this.nickname))) {
						const sphere = this.drawSphere(pointVector);
						if (sphere) {
							sphere.isTemporary = isTemporary;
							raycasterObjs.push(sphere);
							this.curvesMap[this.nickname].spheres.push(sphere);
							this.curvesMap[this.nickname].clickPoints.push(pointVector);
						}
					} else {
						this.curvesMap[this.nickname].clickPoints.push(pointVector);
					}

				};

				const setCount = () => {
					if (this.nickname && this.nickname.includes('_')) {
						[DrawEnum.curve, DrawEnum.brokenLine, DrawEnum.singleSpiralLine,
						DrawEnum.doubleHelixLine, DrawEnum.circle,
						].forEach(i => {
							if (this.nickname.startsWith(i)) {
								this[i + 'Count'] = this.getCurrentYSXCount(i);
							}
						});
					}
				};

				if (isArray(clickPoints) && clickPoints.length) {
					resetCurvesMap();
					for (const point of clickPoints) {
						const pointVector = new THREE.Vector3(point.x, point.y, point.z);
						commonHandler(pointVector, false);

					}
					this.initCurveEnterAndRollbackEvent();
					enterCallback();
				} else {
					if (!this.curvesMap[this.nickname] || !this.drawing) {
						resetCurvesMap();
					}
					this.drawing = true;
					const pointVector = this.getCurrPoint({ clientX: this.clientX, clientY: this.clientY });
					if (pointVector) {
						this.rebackAdd(() => {
							if (this.curvesMap[this.nickname]?.clickPoints.length > 1) {
								this.curvesMap[this.nickname].clickPoints.pop();
								const sphere = this.curvesMap[this.nickname].spheres.pop();
								this.removeSceneObj(sphere);
								raycasterObjs = raycasterObjs.filter(obj => obj.uuid !== sphere.uuid);
								this.mouseMoveDrawCurve({ clientX: this.clientX, clientY: this.clientY });
								this.requestRender();
							} else {
								this.$message.info('已回退到最后一步啦!');
							}
						});
						commonHandler(pointVector);
						if (!this.inited && this.curDraw) {
							this.inited = true;
							this.throttleMouseMoveHandler = throttle(this.mouseMoveDrawCurve, 10);
							addEventHandler(this.getRenderer().domElement, 'mousemove', this.throttleMouseMoveHandler, true);
							this.initCurveEnterAndRollbackEvent();
						}
					}
				}
				setCount();
				return resolve();
			});
		},

		/**
		 * 添加控制点
		 * @param {string} nickname
		 */
		addControlPointHandler (nickname) {
			return new Promise((resolve, reject) => {

				const commonHandler = (pointVector) => {
					const sphere = this.drawSphere(pointVector, false, 1, nickname);
					if (sphere) {
						// raycasterObjs.push(sphere);			// 临时注释，添加该行代码导致drawCurve中重复添加，导致过滤出问题，若注释有副作用再修改
						this.curvesMap[nickname].spheres.push(sphere);
						this.curvesMap[nickname].clickPoints.push(pointVector);
					}
				};

				const pointVector = this.getCurrPoint({ clientX: this.clientX, clientY: this.clientY }, (item) => this.isYsLine(item.object, true));

				if (pointVector) {

					commonHandler(pointVector);

					const points = [...this.curvesMap[nickname].clickPoints];
					points.pop();
					const tmpMap = {};
					const out = new THREE.Vector3(); // 开辟临时空间
					if (points.length >= 2) {
						// 根据贝塞尔曲线上的点反算T值
						for (let i = 0; i < points.length; i++) {
							const t = Number(bezier.closestPoint(out, points, points[i]).toFixed(2));
							tmpMap[i] = {
								t: t,
								point: points[i].clone(),
							};
						}
						const t = Number(bezier.closestPoint(out, points, pointVector).toFixed(2));
						tmpMap[Object.keys(tmpMap).length] = {
							t: t,
							point: pointVector.clone(),
						};

						const ts = Object.keys(tmpMap).map(k => tmpMap[k]).sort((a, b) => a.t - b.t);
						const newPoints = ts.map(item => item.point);

						setTimeout(() => {
							this.clearDrawnCurveByNickname(nickname);
							this.drawCurve(nickname, newPoints);

							return resolve(newPoints);
						}, 1);
					}
				} else {
					reject();
				}
			});
		},

		/**
		 * 鼠标移动绘制曲线
		 */
		mouseMoveDrawCurve ({ clientX, clientY }) {
			this.clientX = clientX;
			this.clientY = clientY;
			const pointVector = this.getCurrPoint({ clientX, clientY });
			if (!pointVector) return;
			this.confirmDrawCurve(pointVector, true);
		},

		/**
		 * 确认绘制曲线
		 */
		confirmDrawCurve (pointVector = null, isTemporary = true) {

			const pointMap = this.curvesMap[this.nickname];
			if (!pointMap) return;

			const filter = (child) => child && child.nickname !== this.nickname;
			this.updateSceneYsLine(null, filter);

			const clickPoints = pointMap.clickPoints || [];
			const pointVectors = clickPoints.map(p => p.clone());
			if (pointVector) pointVectors.push(pointVector.clone());

			if (pointVectors.length < 2) return;

			const res = this.makeCurve(pointVectors, this.nickname, isTemporary);
			this.requestRender();

			return res;
		},

		/**
		 * 更新绘制曲线
		 */
		updateDrawCurve (pointVector) {
			if (!this.currentActiveItem) return;
			const groupName = this.currentActiveItem.groupName;
			const curvesMap = this.curvesMap[groupName];
			if (!curvesMap) return;
			const filter = (child) => child && child.groupName !== groupName;
			this.updateSceneYsLine(null, filter);
			raycasterObjs = raycasterObjs.filter(item => item.groupName !== groupName);

			const index = curvesMap.spheres.findIndex(sphere => sphere.uuid === this.currentActiveItem.uuid);
			if (index === -1) {
				this.removeSceneObj(this.currentActiveItem);
				raycasterObjs = raycasterObjs.filter(item => item !== this.currentActiveItem);
				this.detachSelected();
			} else {
				curvesMap.clickPoints[index] = pointVector.clone();
			}

			const res = this.makeCurve(curvesMap.clickPoints, groupName, null);
			raycasterObjs.push(res.line);
			raycasterObjs.push(...curvesMap.spheres);
			this.requestRender();
		},

		/**
		 * 制作曲线
		 */
		makeCurve (pointVectors, groupName, isTemporary) {
			let curve;
			let curvePoints;
			const drawType = this.getTypeByNickname(groupName) ?? DrawEnum.curve;
			if (drawType === DrawEnum.curve) {
				curve = new THREE.CatmullRomCurve3(pointVectors, false, 'centripetal');
				curvePoints = curve.getPoints(pointVectors.length >= 6 ? 100 : 50);
			} else if (drawType === DrawEnum.brokenLine) {
				curve = new THREE.Shape(pointVectors);
				curve.autoClose = false;
				curvePoints = pointVectors;
			} else {
				curve = new THREE.CatmullRomCurve3(pointVectors, false, 'centripetal');
				curvePoints = pointVectors;
			}

			const geometry = new THREE.BufferGeometry().setFromPoints(curvePoints);
			const lineGeometry = new LineGeometry();
			lineGeometry.setPositions(geometry.attributes.position.array);
			let color = ColorEnum.black;
			if (['ys_line'].includes(this.curActiveObj?.key)) {
				const hasYsLineActivate = this.curActiveObj.extra.some(extraItem => extraItem.ysLineActivate);
				if (hasYsLineActivate) {
					color = ColorEnum.cyanBlue;
				}
			}
			const lineMaterial = getLineMaterial(color, this.getCanvas(), 2);
			const line = new Line2(lineGeometry, lineMaterial);
			line.isCurve = true;
			line.visible = this.originVisible;
			// line.nickname = this.nickname;
			line.nickname = groupName;
			line.groupName = groupName;
			line.isTemporary = isTemporary;

			this.updateSceneYsLine(line);
			this.requestRender();

			return { curve, line };
		},

		/**
		 * 更新场景预设线
		 * @param {Line2} ysLine
		 * @param {Function=} filter
		 */
		updateSceneYsLine (ysLine, filter = (child) => child) {
			const ysLineGroup = this.createOrUpdateGroup(YS_LINE_GROUP);
			if (ysLine) {
				// 插入新数据
				ysLineGroup.children.push(ysLine);
			}
			ysLineGroup.children = ysLineGroup.children.filter(filter);
			this.requestRender();
		},

		/**
		 * 删除场景预设线
		 * @param {Array<Line2>} nameList
		 * @param {Function=} filter
		 */
		removeSceneYsLineByNameList (nameList, filter = (child) => child) {
			const ysLineGroup = this.getGroupByName(YS_LINE_GROUP);
			if (ysLineGroup) {
				const needsUpdateChildren = ysLineGroup.children.filter(child => !nameList.includes(child.nickname));
				ysLineGroup.children = needsUpdateChildren.filter(filter);

				// 删除预设线对应的小球
				this.removeSceneSphereByYsLines(nameList);

				nameList = nameList.filter(name => this.isYsLineName(name));
				// 删除拾取列表中对应的预设线
				raycasterObjs = raycasterObjs.filter(obj => !nameList.includes(obj.nickname));
				// 删除缓存中的预设线
				nameList.forEach(name => delete this.curvesMap[name]);

				this.requestRender();
			}
		},

		/**
		 * 按组名删除场景预设线
		 * @param {String} groupName
		 * @param {Function=} filter
		 */
		removeSceneYsLineByGroupName (groupName, filter = (child) => child) {
			const ysLineGroup = this.getGroupByName(YS_LINE_GROUP);
			if (ysLineGroup) {
				const needsUpdateChildren = ysLineGroup.children.filter(child => groupName !== child.nickname);
				// 更新数据
				ysLineGroup.children = needsUpdateChildren.filter(filter);

				// 删除预设线对应的小球
				this.removeSceneSphereByYsLines([groupName]);

				if (this.isYsLineName(groupName)) {
					// 删除拾取列表中对应的预设线
					raycasterObjs = raycasterObjs.filter(obj => groupName !== obj.nickname);
					// 删除缓存中的预设线
					delete this.curvesMap[groupName];
				}

				this.requestRender();
			}
		},

		/**
		 * 按预设线名称删除场景球体
		 */
		removeSceneSphereByYsLines (ysLineNameList) {
			ysLineNameList = ysLineNameList || [];
			let spheres = [];
			ysLineNameList.forEach(ysLineName => {
				const { spheres: objs } = this.getYsLineObjectAndSphereByGroupName(ysLineName);
				spheres.push(...objs);
			});
			this.removeSceneObjByArray(spheres);
			this.removeIntersectObjByList(spheres);
			this.requestRender();
			spheres = null;
		},

		/**
		 * 按列表删除相交对象
		 */
		removeIntersectObjByList (spheres = []) {
			spheres.forEach(sphere => {
				this.intersectObjects = this.intersectObjects.filter(obj => obj.uuid !== sphere.uuid);
			});
		},

		/**
		 * 初始化曲线回车和回退事件
		 */
		initCurveEnterAndRollbackEvent () {
			enterCallback = (event) => {
				preventDefaults(event);
				this.stopDrawn();
				this.detachSelected();
				this.$emit('clearDraw');
				if (this.curControl) {
					this.curControl.enabled = true;
				}

				this.inited = false;
				this.drawing = false; // 绘制结束
				if (this.getRenderer()?.domElement) {
					removeEventHandler(this.getRenderer().domElement, 'mousemove', this.throttleMouseMoveHandler);
					this.throttleMouseMoveHandler = null;
				}

				const pointMap = this.curvesMap[this.nickname];
				if (!pointMap) return;

				const res = this.confirmDrawCurve(null, false);
				if (res) {
					raycasterObjs.push(res.line);
					this.requestRender();
				}
				if (!this.isInitDrawCurve) {
					this.emitCurveData('ys_line');
				}
				this.isInitDrawCurve = false;

				// this.initCurveDrawEvent();
				this.detachSelected();
				enterCallback = null;
			};

			escCallback = () => {
				// 分离选定
				this.detachSelected();
				// 清除绘制线
				if ([DrawEnum.curve, DrawEnum.brokenLine].includes(this.curDraw) && this.curvesMap[this.nickname]) {
					const spheres = this.curvesMap[this.nickname].spheres;
					if (spheres.length) {
						this.removeSceneYsLineByGroupName(spheres[0].groupName);
					}
				}
			};

			// 回退至上一步
			// ctrlZCallback = () => {
			// 	// 绘制完毕后不允许回退
			// 	if (!this.drawing) return;

			// 	if (this.curvesMap[this.nickname].clickPoints.length > 1) {
			// 		this.curvesMap[this.nickname].clickPoints.pop();
			// 		const sphere = this.curvesMap[this.nickname].spheres.pop();
			// 		this.getScene().remove(sphere);
			// 		raycasterObjs = raycasterObjs.filter(obj => obj.uuid !== sphere.uuid);
			// 		this.mouseMoveDrawCurve({ clientX: this.clientX, clientY: this.clientY });
			// 		this.requestRender();
			// 	} else {
			// 		this.$message.info('已回退到最后一步啦!');
			// 	}
			// };
		},

		/**
		 * 发出更新曲线数据
		 */
		emitCurveData (key) {
			const points = this.curvesMap[this.nickname]?.clickPoints.map(point => this.getPoint(point.clone()));
			this.$emit('updateCurve', {
				key: this.curActiveObj?.key || key,
				nickname: this.nickname,
				value: points,
			});
			// this.$emit('updateDiyShapeList', {
			// 	key: this.nickname,
			// 	value: points,
			// 	type: this.getTypeByNickname(this.nickname)
			// });
		},

		/**
		 * 发出更新圆数据（只在删除用）
		 */
		emitCircleData () {
			this.$emit('updateCircle', {
				key: 'ys_line',
				nickname: this.nickname,
			});
		},

		/**
		 * 初始化曲线绘制事件
		 */
		// initCurveDrawEvent () {
		// 	dragCallback = (event) => {
		// 		// 锁定模型，防止拖动小球是视图跟着转动
		// 		// this.curControl.enabled = false;

		// 		if (!event.value) {
		// 			const selectSphere = event.target?.children[1]?.object;
		// 			this.updateDrawCurve(selectSphere.position);
		// 		}
		// 	};

		// 	this.initDrawEvent(this.curvesMap[this.nickname].spheres);
		// },

		/**
		 * 绘制圆
		 */
		async drawCircle (point, intersect) {

			if (!this.direction) {
				this.$modal.warning({ content: '请先选择视图模式' });
				return;
			}

			if (this.circleSetting.point) {
				return;
				// this.tempCurvesGroup.clear();
				// this.removeSceneObjByArray([this.circleSetting.point]);
			}
			const sphere = this.drawSphere(point, true);
			this.circleSetting = {
				showSetting: true,
				_point: point,
				point: sphere,
				newPoint: null,
				drawResult: null,
				radius: 10,
			};
			this.curveCount++;

			// switch (this.direction) {
			// 	case ViewEnum.front:
			// 	case ViewEnum.back: {
			// 		this.setPlaneMode(PlaneModeEnum.planeDiyXZ, point, new THREE.Vector3(0, 0, 1));
			// 		break;
			// 	}
			// 	case ViewEnum.left:
			// 	case ViewEnum.right: {
			// 		this.setPlaneMode(PlaneModeEnum.planeDiyYZ, point, new THREE.Vector3(1, 0, 0));
			// 		break;
			// 	}
			// 	case ViewEnum.top:
			// 	case ViewEnum.bottom: {
			// 		this.setPlaneMode(PlaneModeEnum.planeDiyXY, point, new THREE.Vector3(0, 1, 0));
			// 		break;
			// 	}
			// 	case ViewEnum.default: {
			// 		this.setPlaneMode(PlaneModeEnum.curveTangentPlane, point, intersect?.face?.normal ?? new THREE.Vector3(0, 0, 1));
			// 		break;
			// 	}
			// 	default:
			// 		this.$message.warning('请选择视图');
			// 		// logger.error('请选择视图');
			// 		break;
			// }

			escCallback = () => {

				this.$message.destroy();
				this.stopDrawn();
				this.detachSelected();
				this.$emit('clearDraw');
				this.removeSceneObjByArray([this.circleSetting.drawResult.circle, this.circleSetting.point]);
				this.circleSetting = {
					showSetting: false,
					_point: null,
					point: null,
					newPoint: null,
					drawResult: null,
					radius: 10,
				};
				this.tempCurvesGroup.clear();
				delete this.curvesMap[this.nickname];
				if (this.curControl) {
					this.curControl.enabled = true;
				}
				this.inited = false;
				this.drawing = false; // 绘制结束
				// if (this.getRenderer()?.domElement) {
				// 	removeEventHandler(this.getRenderer().domElement, 'pointermove', onMousemove);
				// }
				// this.setPlaneMode();
				this.requestRender();
				enterCallback = null;
				escCallback = null;
			};

			enterCallback = (event) => {

				this.$message.destroy();
				preventDefaults(event);
				this.stopDrawn();
				this.detachSelected();
				this.$emit('clearDraw');
				if (this.curControl) {
					this.curControl.enabled = true;
				}

				this.inited = false;
				this.drawing = false; // 绘制结束
				// if (this.getRenderer()?.domElement) {
				// 	removeEventHandler(this.getRenderer().domElement, 'pointermove', onMousemove);
				// }

				const pointMap = this.curvesMap[this.nickname];
				if (!pointMap) return;

				if (this.circleSetting.drawResult) {
					this.drawCurve(this.nickname, this.circleSetting.drawResult.circlePoints);
					this.tempCurvesGroup.clear();
					this.removeSceneObjByArray([this.circleSetting.drawResult.circle, sphere]);
					// logger.log('drawCircle enterCallback', this.curvesGroup, this.tempCurvesGroup, pointMap.clickPoints);

					// this.setPlaneMode();
					this.requestRender();
					// if (!this.isInitDrawCurve) {
					// 	this.emitCircleData();
					// }
					this.isInitDrawCurve = false;
				}

				this.circleSetting = {
					showSetting: false,
					point: null,
					newPoint: null,
					drawResult: null,
					radius: 10,
				};
				this.detachSelected();
				escCallback = null;
				enterCallback = null;
			};

			// const onMousemove = (event) => {
			// 	const scene = this.getScene();
			// 	for (const child of scene.children) {
			// 		if (child && child.isTemporary && !child.isCenterPoint) {
			// 			sleep(1, () => scene.remove(child));
			// 		}
			// 	}

			// 	this.circleSetting.newPoint = null;
			// 	this.circleSetting.drawResult = {
			// 		circlePoints: [],
			// 	};

			// 	let intersects = this.getCanvasIntersects(event, scene, this.curCamera, this.getCanvas());
			// 	intersects = intersects.filter(i => i.object.type === 'GridHelper');
			// 	if (intersects.length) {
			// 		this.circleSetting.newPoint = this.getPoint(intersects[0].point);
			// 		sleep(1, () => {
			// 			if (point && this.circleSetting.newPoint) {
			// 				this.circleSetting.drawResult = this.drawCircleByCondition(point, this.circleSetting.newPoint);
			// 			}
			// 		});
			// 	}
			// };
			// this.getRenderer().domElement.addEventListener('pointermove', onMousemove, false);

			this.requestRender();

			this.$message.destroy();
			this.$message.info('在弹窗修改半径，按【Enter回车】绘制完成，按【ESC】取消绘制', 0);
		},

		/**
		 * 修改圆参数
		 */
		handleCircleSetting ({ radius }) {
			if (!this.circleSetting.point) {
				return;
			}
			this.circleSetting.radius = radius;
			this.circleSetting.drawResult = this.drawCircleNext({
				position: this.circleSetting._point,
				radius,
				normal: this.gridHelper?.diyNormal || new THREE.Vector3(0, 0, 1),
				nickname: this.nickname
			});
		},

		/**
		 * 按条件画圆
		 */
		drawCircleByCondition (point, newPoint) {
			logger.log('drawCircleByCondition', point, newPoint);
			const radius = new THREE.Vector3(newPoint.x, newPoint.y, newPoint.z).distanceTo(new THREE.Vector3(point.x, point.y, point.z));

			return this.drawCircleNext({ position: point, newPoint, radius, normal: this.gridHelper?.diyNormal || new THREE.Vector3(0, 0, 1), nickname: this.nickname });
		},

		/**
		 * 画圆下一步
		 */
		drawCircleNext ({ position, newPoint, radius, normal = this.gridHelper?.diyNormal || new THREE.Vector3(0, 0, 1), nickname = DrawEnum.circle + '_' + this.circleCount } = {}) {
			if (!position.clone) {
				position = new THREE.Vector3(position.x, position.y, position.z);
			}
			logger.log('drawCircleNext', normal);
			if (!normal.clone) {
				normal = new THREE.Vector3(normal.x, normal.y, normal.z);
			}

			const data = {
				innerRadius: radius,
				outerRadius: radius,
				thetaSegments: 50,
				phiSegments: 8,
				thetaStart: 0,
				thetaLength: Math.PI * 2,
			};
			const geometry = new THREE.RingGeometry(
				data.innerRadius,
				data.outerRadius,
				data.thetaSegments,
				data.phiSegments,
				data.thetaStart,
				data.thetaLength,
			);
			const points = geometry.attributes.position.array;

			const lineGeometry = new LineGeometry();
			lineGeometry.setPositions(points);
			const material = getLineMaterial(ColorEnum.black, this.getCanvas(), 2);
			const circle = new Line2(lineGeometry, material);
			circle.computeLineDistances();
			circle.userData.nickname = nickname;
			circle.name = circle.uuid;
			circle.nickname = nickname;

			// 绘制方向箭头
			const startPoint = this.diyPoint;
			const endPoint = position.clone(); // 圆的中心点
			// 方向
			const direction = new THREE.Vector3();
			direction.subVectors(endPoint, startPoint);

			// 执行lookAt方法之前，需要先设置circle的位置
			// circle.position.copy(startPoint);
			// 设置circle对象看向的位置(位置向量和切线向量相加即为所需朝向的点向量)
			// const lookAtVec = direction.clone().add(startPoint);
			// circle.lookAt(lookAtVec);
			circle.applyMatrix4(this.gridHelper.matrix);
			circle.position.copy(endPoint);
			circle.rotateX(Math.PI / 2);

			this.nickname = circle.nickname;
			this.curvesMap[this.nickname] = {
				clickPoints: [],
			};
			circle.isTemporary = true;
			this.tempCurvesGroup.clear();
			this.tempCurvesGroup.add(circle);

			// 销毁过渡用的geo
			geometry.dispose();

			// 该语句默认在threeJs渲染的过程中执行, 如果想获得世界矩阵属性、世界位置属性等属性，需要手动更新
			circle.updateMatrixWorld(true);

			const circlePoints = [];
			for (let i = 0; i < points.length; i += 3) {
				const x = points[i];
				const y = points[i + 1];
				const z = points[i + 2];
				// 本地坐标转世界坐标
				const newVector3 = circle.localToWorld(new THREE.Vector3(x, y, z));
				circlePoints.push(newVector3);
			}

			this.requestRender();

			return { circle, circlePoints, normal, position: circle.position, radius };
		},

		/**
		 * 绘制螺旋线
		 */
		async drawCircularHelix (point, intersect) {
			if (!this.direction) {
				this.$modal.warning({ content: '请先选择视图模式' });
				return;
			}

			if (this.helixSetting.drawEnd) {
				return;
			}

			escCallback = () => {

				this.$message.destroy();
				this.stopDrawn();
				this.detachSelected();
				this.$emit('clearDraw');
				this.removeSceneObjByArray([this.helixSetting.point]);
				this.tempCurvesGroup.clear();
				if (this.curControl) {
					this.curControl.enabled = true;
				}
				this.inited = false;
				this.drawing = false; // 绘制结束
				if (this.getRenderer()?.domElement) {
					removeEventHandler(this.getRenderer().domElement, 'pointermove', onMousemove);
				}
				// this.setPlaneMode();
				this.requestRender();
				this.helixSetting = {
					_point: null,
					drawEnd: false,
					showSetting: false,
					turnsNum: 2,
					radius: 10,
					point: null,
					newPoint: null,
					drawResult: null,
				};
				enterCallback = null;
				escCallback = null;
			};

			enterCallback = (event) => {

				this.$message.destroy();
				preventDefaults(event);
				this.stopDrawn();
				this.detachSelected();
				this.$emit('clearDraw');
				if (this.curControl) {
					this.curControl.enabled = true;
				}

				this.inited = false;
				this.drawing = false; // 绘制结束
				this.helixSetting.showSetting = false;
				if (this.getRenderer()?.domElement) {
					removeEventHandler(this.getRenderer().domElement, 'pointermove', onMousemove);
				}

				if (this.helixSetting.drawResult) {
					this.drawCurve(this.nickname, this.helixSetting.drawResult.fullPoints);

					this.tempCurvesGroup.clear();
					this.removeSceneObjByArray([this.helixSetting.drawResult.fullLine, this.helixSetting.point]);
					// this.setPlaneMode();
					this.requestRender();
					this.isInitDrawCurve = false;
				}

				this.helixSetting = {
					_point: null,
					drawEnd: false,
					showSetting: false,
					turnsNum: 2,
					radius: 10,
					point: null,
					newPoint: null,
					drawResult: null,
				};

				this.detachSelected();
				escCallback = null;
				enterCallback = null;
			};

			const onMousemove = (event) => {
				if (this.helixSetting.drawEnd) {
					if (this.getRenderer()?.domElement) {
						removeEventHandler(this.getRenderer().domElement, 'pointermove', onMousemove);
					}
					return;
				}
				const scene = this.getScene();
				for (const child of scene.children) {
					if (child && child.isTemporary && !child.isCenterPoint) {
						scene.remove(child);
					}
				}

				this.helixSetting.newPoint = null;
				this.helixSetting.drawResult = null;
				let intersects = this.getCanvasIntersects(event, scene, this.curCamera, this.getCanvas());
				intersects = intersects.filter(i => i.object.type === 'GridHelper');
				if (intersects.length) {
					this.helixSetting.newPoint = this.getPoint(intersects[0].point);
					if (this.helixSetting._point && this.helixSetting.newPoint) {
						this.helixSetting.drawResult = this.drawCircularHelixByCondition(this.helixSetting._point, this.helixSetting.newPoint);
					}
				}
			};

			if (this.helixSetting.point) {
				this.helixSetting.newPoint = this.getPoint(point);
				this.helixSetting.drawResult = this.drawCircularHelixByCondition(this.helixSetting._point, this.helixSetting.newPoint);
				this.helixSetting.drawEnd = true;
			} else {
				this.helixSetting._point = point;
				this.helixSetting.point = this.drawSphere(point, true, 1, 'center');
				this.helixSetting.newPoint = null;

				this.getRenderer().domElement.addEventListener('pointermove', onMousemove, false);

				this.requestRender();

				this.helixSetting.showSetting = true;

				this.$message.destroy();
				this.$message.info('点击开始点和结束点，可以在右侧窗口修改圈数和半径，按【Enter回车】绘制完成，按【ESC】取消绘制', 0);
			}

		},

		/**
		 * 修改螺旋线参数
		 */
		handleHelixSetting ({ turnsNum, radius }) {
			if (!this.helixSetting.point || !this.helixSetting.newPoint) {
				return;
			}
			this.helixSetting.turnsNum = turnsNum;
			this.helixSetting.radius = radius;
			this.helixSetting.drawResult = this.drawCircularHelixByCondition(this.helixSetting._point, this.helixSetting.newPoint);
		},

		/**
		 * 按条件画螺旋线
		 */
		drawCircularHelixByCondition (point, newPoint) {
			const distance = new THREE.Vector3(newPoint.x, newPoint.y, newPoint.z).distanceTo(new THREE.Vector3(point.x, point.y, point.z));

			// 圈数
			const turnsNum = this.helixSetting.turnsNum;
			const trimTurnsNum = Math.floor(turnsNum);
			// 螺距
			const interval = distance / turnsNum;
			const radius = this.helixSetting.radius;
			const count = POINT_NUMBER;

			return this.drawCircularHelixNext({ position: point, newPoint, interval, radius, count, trimTurnsNum, type: this.curDraw, nickname: this.nickname });
		},

		drawCircularHelixNext ({ position, newPoint, interval, radius, count, trimTurnsNum, type = DrawEnum.doubleHelixLine, nickname = DrawEnum.doubleHelixLine + '_' + this.doubleHelixLineCount } = {}, isInit) {

			if (!newPoint.clone) {
				newPoint = new THREE.Vector3(newPoint.x, newPoint.y, newPoint.z);
			}

			if (!position.clone) {
				position = new THREE.Vector3(position.x, position.y, position.z);
			}

			const direction = new THREE.Vector3();
			direction.subVectors(newPoint, position);

			if (type === DrawEnum.doubleHelixLine) {
				const { cirLine, points, lastPoint } = this.drawCircularHelixDetail({ position, direction, interval, radius, count, angle: 0, trimTurnsNum });
				const { cirLine: cirLine2, points: points2, lastPoint: lastPoint2 } = this.drawCircularHelixDetail({ position, direction, interval, radius, count, angle: Math.PI, trimTurnsNum });

				const fullPoints = [...points, ...points2.reverse()];
				const fullLine = makeLineByPoints(fullPoints); // 结尾线是曲线
				fullLine.nickname = nickname;
				fullLine.name = nickname;
				fullLine.userData.nickname = nickname;
				this.nickname = fullLine.nickname;
				this.curvesMap[this.nickname] = {
					clickPoints: fullPoints,
				};
				fullLine.isTemporary = true;
				this.tempCurvesGroup.clear();
				this.tempCurvesGroup.add(fullLine);

				return { fullLine, fullPoints, position, newPoint, direction, interval, radius, count, trimTurnsNum, drawType: type, nickname };

			} else if (type === DrawEnum.singleSpiralLine) {
				const { cirLine, points, lastPoint } = this.drawCircularHelixDetail({ position, direction, interval, radius, count, angle: 0, trimTurnsNum });
				const fullLine = makeLineByPoints(points);
				fullLine.nickname = nickname;
				fullLine.userData.nickname = nickname;
				this.nickname = fullLine.nickname;
				this.curvesMap[this.nickname] = {
					clickPoints: points,
				};
				fullLine.isTemporary = true;
				this.tempCurvesGroup.clear();
				this.tempCurvesGroup.add(fullLine);
				if (isInit) {
					fullLine.isTemporary = false;
					this.curvesGroup.add(fullLine);
					this.tempCurvesGroup.clear();
					raycasterObjs.push(fullLine);
				}
				return { fullLine, fullPoints: points, position, newPoint, direction, interval, radius, count, trimTurnsNum, drawType: type, nickname };

			}

			function makeLineByPoints (points) {
				const geometry = new THREE.BufferGeometry().setFromPoints(points);
				const material = new THREE.LineBasicMaterial({ color: ColorEnum.black, linewidth: 2 });
				const line = new THREE.Line(geometry, material);
				return line;
			}

		},

		/**
		 * 画螺旋线下一步
		 */
		drawCircularHelixDetail ({ position, direction, interval, radius, count, angle = null, trimTurnsNum } = {}) {
			// 根据平面画圆（方向、位置一致）
			const circleGeo = drawCircleByPlane({ position, radius, count });
			if (angle) circleGeo.rotateZ(angle);

			// 根据法线方向、间隔(螺距)求当前方向
			const curDirection = setAmplitude(direction, interval);
			// 根据圆形几何体、当前方向、圈数、半径求几何体(eg: 3圈，按固定间隔，绘制3个圆，并返回)
			const geos = arrLinear(circleGeo, curDirection, trimTurnsNum, radius);

			// 创建一系列数字
			const results = range(interval, count);
			// logger.log('results: ', results);
			const vectors = setAmplitudeByList(direction, results);
			// logger.log('vectors: ', vectors);

			const points = [];
			// let index = 0;
			const spheres = [];
			for (const geo of geos) {
				const geoPoints = divide(geo);
				const movedPoints = move(geoPoints, vectors);
				points.push(...movedPoints);
				// if (showSphere) {
				// 	for (const movedPoint of movedPoints) {
				// 		const p = drawSphere(movedPoint, { radius: 0.5 });
				// 		spheres.push(p);
				// 	}
				// }
				// index++;
			}

			// if (showSphere) mainObject.add(...spheres);

			const lastPoint = points[points.length - 1]; // 最后一个坐标点

			const cirLine = drawLine(points, 1, geos[0]);

			// 销毁过渡用的geo
			// geometry.dispose();

			// 该语句默认在threeJs渲染的过程中执行, 如果想获得世界矩阵属性、世界位置属性等属性，需要手动更新
			cirLine.updateMatrixWorld(true);

			this.requestRender();

			function drawCircleByPlane ({ position, count, radius = 10, startAngle = 0, endAngle = 2 * Math.PI, clockwise = true } = {}) {
				// 创建圆弧对象 ArcCurve
				// 参数：
				//  aX – 圆弧坐标原点X坐标，默认值为0。
				//  aY – 圆弧坐标原点Y坐标，默认值为0。
				//  radius – 圆弧半径。
				//  startAngle – 圆弧起始的角度。默认值为 0。
				//  endAngle – 圆弧终止的角度。默认值为 2 x Math.PI
				//  clockwise – 圆弧是否按顺时针方向绘制。默认为 false
				const curve = new THREE.ArcCurve(0, 0, radius, startAngle, endAngle, clockwise);

				// getPoints是基类Curve的方法，返回一个vector2对象作为元素组成的数组
				// setFromPoints方法从points中提取数据改变几何体的顶点属性vertices
				const curvePoints = curve.getPoints(POINT_NUMBER * count);
				const geometry = new THREE.BufferGeometry().setFromPoints(curvePoints);
				const material = new THREE.LineBasicMaterial({ color: ColorEnum.black, linewidth: 2 });
				const circle = new THREE.Line(geometry, material);

				// 执行lookAt方法之前，需要先设置circle的位置
				const origin = position;
				circle.position.copy(origin);
				// 设置circle对象看向的位置(位置向量和切线向量相加即为所需朝向的点向量)
				const lookAtVec = direction.clone().add(origin);
				circle.lookAt(lookAtVec);

				return circle;
			}

			function setAmplitude (vector3, amp) {
				const nor = vector3.clone().normalize(); // 变成法向量(归一化)
				const newVector3 = new THREE.Vector3(nor.x * amp, nor.y * amp, nor.z * amp);
				// const newVector3 = vector3.clone().multiplyScalar(amp); // 将向量与所传入的标量amp进行相乘
				return newVector3;
			}

			function setAmplitudeByList (direction, motions) {
				const points = [];
				const dirNor = direction.clone().normalize(); // 变成法向量(归一化)
				for (const motion of motions) {
					const newVector3 = new THREE.Vector3((dirNor.x * motion), (dirNor.y * motion), (dirNor.z * motion));
					// const newVector3 = direction.clone().multiplyScalar(motion); // 将向量与所传入的标量amp进行相乘
					points.push(newVector3);
				}
				return points;
			}

			/**
			 * 创建一系列数字
			 * @param interval
			 * @param steps
			 * @return {*[]}
			 */
			function range (interval, steps = 10) {
				const result = [];
				const initValue = interval / steps;
				for (let i = 0; i <= steps; i++) {
					result.push((initValue * i));
				}
				return result;
			}

			/**
			 * 创建几何的线性阵列
			 * @param G circle Geometry 圆形几何
			 * @param D direction 方向
			 * @param N turnsNum 圈数
			 * @param R radius 半径
			 * @return {Array<Object>} geos
			 */
			function arrLinear (G, D, N, R) {

				const origin = G.position.clone();
				const rotation = G.rotation.clone();
				const radius = parseInt(R);

				const geos = [];

				const handler = (originPoint, p0, p1) => {
					const { circle } = drawCircleCurve(p0, p1, { radius: radius, turnsNum: N });
					circle.rotation.copy(rotation);
					// mainObject.add(circle);
					geos.push(circle);
				};

				const p0 = origin.clone();
				for (let i = 0; i < N; i++) {
					let p1;
					if (i === 0) {
						p1 = p0.clone();
					} else {
						p1 = move([origin], [D])[0];
					}
					handler(origin, p0, p1);
				}

				return geos;
			}

			/**
			 * 将曲线分成等长的段
			 * @param curve
			 * @return {*[]}
			 */
			function divide (curve) {
				// 根据圆生成路径上控制点(10个,可控制数量)
				const points = pointsByCircleCurve(curve);
				return points;
			}

			/**
			 *
			 * @param curve 曲线几何体
			 * @param isDrawSphere 是否绘制小球
			 * @return {*[]}
			 */
			function pointsByCircleCurve (curve, isDrawSphere = false) {

				// 调用localToWorld前需要先更新世界矩阵属性
				// 如果想获得世界矩阵属性、世界位置属性等属性，需要手动更新
				curve.updateMatrixWorld(true);

				const points = [];
				const positions = curve.geometry.attributes.position.array;
				for (let i = 0; i < positions.length; i += 3) {
					const x = positions[i + 0];
					const y = positions[i + 1];
					const z = positions[i + 2];
					// 本地坐标转世界坐标
					const point = curve.localToWorld(new THREE.Vector3(x, y, z));
					points.push(point);
				}

				// if (isDrawSphere) {
				// 	drawSphereByPoints(points, curve);
				// }

				return points;
			}

			function move (points, vectors) {
				const newPoints = [];
				let index = 0;
				for (const point of points) {
					const motion = vectors[index];
					// 两点相加
					const v3 = point.add(motion);
					newPoints.push(new THREE.Vector3(v3.x, v3.y, v3.z));
					index++;
				}
				return newPoints;
			}

			function drawCircleCurve (startPoint, endPoint, { turnsNum, radius = 10, startAngle = 0, endAngle = 2 * Math.PI, clockwise = true } = {}) {
				// 创建圆弧对象 ArcCurve
				// 参数：
				//  aX – 圆弧坐标原点X坐标，默认值为0。
				//  aY – 圆弧坐标原点Y坐标，默认值为0。
				//  radius – 圆弧半径。
				//  startAngle – 圆弧起始的角度。默认值为 0。
				//  endAngle – 圆弧终止的角度。默认值为 2 x Math.PI
				//  clockwise – 圆弧是否按顺时针方向绘制。默认为 false
				const curve = new THREE.ArcCurve(0, 0, radius, startAngle, endAngle, clockwise);

				// getPoints是基类Curve的方法，返回一个vector2对象作为元素组成的数组
				// setFromPoints方法从points中提取数据改变几何体的顶点属性vertices
				const curvePoints = curve.getPoints(POINT_NUMBER);
				const geometry = new THREE.BufferGeometry().setFromPoints(curvePoints);
				const material = new THREE.LineBasicMaterial({ color: ColorEnum.black, linewidth: 2 });
				const circle = new THREE.Line(geometry, material);

				// 执行lookAt方法之前，需要先设置circleLine的位置
				circle.position.copy(endPoint);
				// 设置circleLine对象看向的位置
				circle.lookAt(startPoint);

				return { curve, curvePoints, circle };
			}

			function drawLine (pointVectors, scale = 1) {
				const inPoints = [];
				for (const pointVector of pointVectors) {
					// const point = pointVector.multiply(new THREE.Vector3(scale, scale, scale));
					// 将向量与所传入的标量scale进行相乘
					const point = pointVector.multiplyScalar(scale);
					inPoints.push(point);
				}
				// 三维样条曲线
				const curve = new THREE.CatmullRomCurve3(inPoints);
				const points = curve.getPoints(POINT_NUMBER);
				const line = makeLineByPoints(points);

				return line;
			}

			function makeLineByPoints (points) {
				const geometry = new THREE.BufferGeometry().setFromPoints(points);
				const material = new THREE.LineBasicMaterial({ color: ColorEnum.black, linewidth: 2 });
				const line = new THREE.Line(geometry, material);
				return line;
			}

			return { cirLine, points, lastPoint };
		},

		/**
		 * 绘制点
		 */
		drawPoint (point, name) {
			if (!this.drawing) {
				this.pointList = [];
			}
			if (point) {
				this.rebackAdd(() => {
					const sphere = this.pointList.pop();
					this.removeSceneObj(sphere);
				});
				const sphere = this.drawSphere(point, false, 1, name);
				this.pointList.push(sphere);
			} else {
				const intersects = this.getIntersects(this.clientX, this.clientY);
				if (intersects.length) {
					this.drawing = true; // 绘制中
					this.rebackAdd(() => {
						const sphere = this.pointList.pop();
						this.removeSceneObj(sphere);
					});
					const sphere = this.drawSphere(intersects[0].point, false, 1, name);
					this.pointList.push(sphere);
				}
			}
			if (!this.inited && this.curDraw) {
				this.inited = true;
				this.initEnterAndRollback = false;
				this.initPointEnterAndRollbackEvent();
			}
		},

		/**
		 * 绘制小球
		 */
		drawSphere (point, isCenterPoint, radius = 1, groupName = this.nickname) {
			if (!point) return;
			if (!this.sphereCount) this.sphereCount = 0;
			this.sphereCount++;
			radius = this.getSphereRadius(radius);
			const geometry = new THREE.SphereGeometry(radius, 100, 100);
			const material = new THREE.MeshBasicMaterial({ color: ColorEnum.yellow });
			const sphere = new THREE.Mesh(geometry, material);
			sphere.groupName = groupName;
			sphere.position.copy(new THREE.Vector3(point.x, point.y, point.z));
			sphere.isCenterPoint = !!isCenterPoint; // 是中心点
			sphere.isTemporary = true; // 是临时的
			sphere.isSphere = true;
			sphere.visible = false;
			sphere.visible = this.originVisible;
			this.getScene().add(sphere);
			this.requestRender();
			return sphere;
		},

		/**
		 * 绘制小球
		 */
		drawSphere2 (point, { radius = 1, color = ColorEnum.yellow } = {}) {
			if (!point) return;
			const geometry = new THREE.SphereGeometry(radius, 100, 100);
			const material = new THREE.MeshBasicMaterial({ color });
			const sphere = new THREE.Mesh(geometry, material);
			sphere.position.copy(new THREE.Vector3(point.x, point.y, point.z));
			return sphere;
		},

		/**
		 * 绘制变径点小球
		 */
		drawBjPointSphere (point, { nickname, groupName, radius = 2 } = {}) {
			if (!point) return;
			radius = this.getSphereRadius(radius);
			const geometry = new THREE.SphereGeometry(radius, 100, 100);
			const material = new THREE.MeshBasicMaterial({
				color: ColorEnum.yellow,
				polygonOffset: true, // 是否使用多边形偏移，默认值为false
				// 当这两个参数都为负时（深度减小），网格将被拉向摄影机(位于前面);
				// 当两个参数都为正（增加深度）时，网格会被推离摄影机(会落在后面)
				polygonOffsetFactor: -1, // 偏移系数，与相机距离减2
				polygonOffsetUnits: -40, // 偏移的单位
			});
			const sphere = new THREE.Mesh(geometry, material);
			sphere.nickname = nickname;
			sphere.groupName = groupName;
			sphere.position.copy(new THREE.Vector3(point.x, point.y, point.z));
			sphere.isSphere = true;
			sphere.visible = true;
			sphere.renderOrder = RenderOrderEnum.point;

			this.updateSceneBjPoint(sphere);

			return sphere;
		},

		/**
		 * 获取场景所有变径点
		 * @param {Function=} condition
		 */
		getSceneAllBjPoint (condition = (child) => child) {
			const group = this.createOrUpdateGroup(BJ_POINT_GROUP);
			if (!group) return [];
			return group.children.filter(child => condition(child));
		},

		/**
		 * 更新场景变径点
		 * @param sphere
		 * @param {Function=} filter
		 */
		updateSceneBjPoint (sphere, filter = (child) => child) {
			const bjPointGroup = this.getGroupByName(BJ_POINT_GROUP);
			if (bjPointGroup) {
				if (sphere) { // 更新数据
					bjPointGroup.children.push(sphere);
				}
				bjPointGroup.children = bjPointGroup.children.filter(filter);
				this.requestRender();
			}
		},

		/**
		 * 获取球体半径
		 */
		getSphereRadius (radius) {
			if (this.modelLong >= 170 && this.modelLong < 500) {
				return 2;
			}
			if (this.modelLong >= 500 && this.modelLong <= 700) {
				return 3;
			}
			if (this.modelLong >= 50000) {
				return 500;
			}
			return radius;
		},

		// /**
		//  * 初始化绘制事件
		//  */
		// initDrawEvent (objects) {

		// 	if (!this.intersectObjects) {
		// 		this.intersectObjects = [];
		// 	}
		// 	this.intersectObjects = [...this.intersectObjects, ...objects];

		// 	this.currentActiveItem = null;

		// 	if (this.initDrawEvented) {
		// 		return;
		// 	}
		// 	this.initDrawEvented = true;

		// 	const renderer = this.getRenderer();
		// 	this.dragControls = new TransformControls(this.curCamera, renderer.domElement);

		// 	const drawRayCaster = new THREE.Raycaster();

		// 	const onDrawPointerDown = (event) => {
		// 		if (this.curDraw) return;
		// 		const { x, y } = this.getPosition(event);
		// 		const mouse = new THREE.Vector3(x, y, 0.5); // 标准设备坐标
		// 		this.updatePointer(event);
		// 		drawRayCaster.setFromCamera(mouse, this.curCamera);
		// 		const intersects = drawRayCaster.intersectObjects(this.intersectObjects, false);
		// 		if (intersects.length) {
		// 			// this.curControl.enabled = false;
		// 			if (this.curActiveObj?.key !== 'ys_line' || !this.curActiveObj.activate) {
		// 				this.curControl.enabled = true;
		// 				this.detachSelected(this.dragControls);
		// 				return;
		// 			}

		// 			const target = intersects[0].object;
		// 			this.currentActiveItem = intersects[0];
		// 			this.selectTarget = target;
		// 			this.nickname = target.groupName || '';
		// 			this.dragControls.attach(target);
		// 			this.getScene().add(this.dragControls);
		// 			this.requestRender();

		// 			enterCallback = () => {
		// 				this.detachSelected(this.dragControls);
		// 				enterCallback = null;
		// 				this.selectTarget = null;
		// 				this.curControl.enabled = true;
		// 				this.reInitControls();

		// 				if (this.nickname && this.isYsLineName(this.nickname)) {
		// 					this.emitCurveData();
		// 				}
		// 				this.$emit('onUnlock'); // 解除锁定
		// 			};
		// 		}
		// 	};

		// 	const drawDraggingChanged = (event) => {
		// 		// 锁定模型，防止拖动小球是视图跟着转动
		// 		this.curControl.enabled = !event.value;

		// 		if (!event.value) {
		// 			dragCallback && dragCallback(event);
		// 		}
		// 	};

		// 	addEventHandler(this.getRenderer().domElement, 'pointerdown', onDrawPointerDown, true);
		// 	addEventHandler(this.dragControls, 'dragging-changed', drawDraggingChanged, true);
		// },

		/**
		 * 删除场景变径点
		 * @param {Array<Line2>} nameList
		 * @param {Function=} filter
		 */
		removeSceneBjPointByNameList (nameList, filter = (child) => child) {
			const bjPointGroup = this.getGroupByName(BJ_POINT_GROUP);
			if (bjPointGroup) {
				const needsUpdateChildren = bjPointGroup.children.filter(child => !nameList.includes(child.nickname));
				// 更新数据
				bjPointGroup.children = needsUpdateChildren.filter(filter);

				this.requestRender();
			}
		},

		/**
		 * 绘图
		 * type: 画点、画线、画圆
		 */
		onDraw (type) {
			this.curDraw = DrawEnum[type];
			if (this.curDraw === DrawEnum.point) {
				this.nickname = this.getNewNickname(this.curDraw);
			} else if ([DrawEnum.curve, DrawEnum.brokenLine].includes(this.curDraw)) {

				this.nickname = this.getNewNickname(this.curDraw);
				// 绘制时，创建网格面板，便于坐标拾取（画完后需要销毁）
				// type && this.createGridPlane();
			} else if ([DrawEnum.circle, DrawEnum.singleSpiralLine, DrawEnum.doubleHelixLine].includes(this.curDraw)) {
				this.nickname = this.getNewNickname(this.curDraw);
				// type && this.createGridPlane();
				this.$message.destroy();
				this.$message.info('请在平面上点击确定原点', 0);
			}

			if (!type) {
				this.stopDrawn();
			}
			if (this.gridHelper) {
				this.gridHelper.visible = true;
			}
		},

		/**
		 * 清除所有绘制
		 */
		async clearAllDrawn (marked = true) {
			this.stopDrawn();
			await this.clearDrawnCurve();
			await this.clearDrawnBJPoint();
			marked && this.$emit('clearCurve');
		},

		/**
		 * 控制操作
		 * type: 添加控制点、复制、移动
		 */
		async onAction (type) {
			this.modalEdit = false;
			rebackList = [];
			escCallback && escCallback();
			mitt.emit('resetEvent');
			mitt.clear();
			this.curAction = ActionEnum[type];
			if (type === ActionEnum.boxSelect) {
				this.onBoxSelect(type);
				this.controlling = false;
			} else {
				if (!Object.keys(this.curvesMap).length && type !== ActionEnum.curveZoom) {
					this.$message.info('缺少预设线目标，请先绘制预设线！');
					this.$nextTick(() => {
						this.$emit('closeActionControl', { type }); // 取消操作
					});
					return;
				}
				if (!this.checkYsLineExists()) {
					this.$nextTick(() => {
						this.$emit('closeActionControl', { type }); // 取消操作
					});
					return;
				}
				this.controlling = true;
			}

			// 添加控制点
			if (type === ActionEnum.addControlPoints) {
				this.$message.destroy(); // 关闭所有气泡提示
				const closeTip = this.$message.info('选择需要添加控制点的目标', 0); // 设为 0 时不自动关闭
				const closeClickListener = this.addClickEventListener(async (event) => {
					this.updatePointer(event);
					let intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());
					// 过滤出所有线
					intersects = intersects.filter(item => this.isLine(item.object) && !item.object.isWireframe && (item.object?.nickname?.startsWith('curve') || item.object?.nickname?.startsWith('brokenLine')));
					let points;
					if (intersects.length) {
						closeTip(); // 关闭气泡提示
						closeClickListener(); // 关闭点击事件监听
						points = await this.addControlPointHandler(intersects[0].object.nickname);
						this.controlling = false;
						this.$emit('closeActionControl', { type, points }); // 取消添加控制点操作
					}
				}, false);

			}

			// 框选与内插点之外的操作出现参考平面
			if (![ActionEnum.boxSelect, ActionEnum.addControlPoints].includes(type)) {
				this.gridHelper.visible = true;
			}

			// 曲线缩放
			if (type === ActionEnum.curveZoom) {
				this.currentFunc = () => {
					this.$message.destroy();
					let closeClickListener;
					this.scaleCurve.showSetting = false;
					this.resetColorHandle();
					const closeTip = this.$message.info('请选择要缩放的曲线', 0);
					closeClickListener = this.addClickEventListener(event => {
						this.updatePointer(event);
						let intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());

						intersects = intersects.filter(item =>
							(this.isYsLine(item.object, true) || this.isOtherLine(item.object) || this.isCircle(item.object)),
						);
						if (intersects.length) {
							closeTip();
							closeClickListener();
							const needScaleCurve = intersects[0].object;
							needScaleCurve.material.color = new THREE.Color(ColorEnum.yellow);
							needScaleCurve.material.needsUpdate = true;
							this.scaleCurve.curve = needScaleCurve;
							this.rebackAdd(this.currentFunc, () => {
								closeClickListener();
								this.scaleCurve.point = null;
								delete this.curvesMap[this.scaleCurve.curveName];
							});
							this.currentFunc = () => {
								closeClickListener();
								this.scaleCurve.showSetting = false;
								this.$message.destroy();
								mitt.off('addPlaneEnd', resetScale);
								const closeTip = this.$message.info('请选择基准点', 0);
								this.scaleCurve.curveName = this.createPoint(needScaleCurve);
								closeClickListener = this.addClickEventListener(event => {
									this.scaleCurve.point = this.getCurrPoint(event);
									if (!this.scaleCurve.point) return;
									closeTip();
									closeClickListener();
									this.scaleCurve.showSetting = true;
									this.rebackAdd(this.currentFunc, () => {
										this.scaleCurve.point = null;
										this.removeSceneObj(this.scaleCurve.tempObj);
										this.removeSceneObj(this.scaleCurve.currentItem);
									});
								}, false);
								mitt.on('addPlaneEnd', resetScale);
							};
							const func = this.currentFunc;
							const resetScale = () => {
								if (this.scaleCurve.curve) {
									this.scaleCurve.point = null;
									this.removeSceneObj(this.scaleCurve.tempObj);
									this.removeSceneObj(this.scaleCurve.currentItem);
									func();
								}
							};
							this.currentFunc();
						}
					}, false);
					escCallback = () => {
						this.$message.destroy();
						this.$emit('closeActionControl', { type });
						this.removeSceneObj(this.scaleCurve.curve);
						this.resetScale();
						this.resetColorHandle();
						closeClickListener();
						this.controlling = false;
						escCallback = null;
					};
				};
				this.currentFunc();
				// if (!this.checkYsLineExists()) {
				// 	this.$nextTick(() => {
				// 		this.$emit('closeActionControl', { type }); // 取消操作
				// 	});
				// 	return;
				// }
				// this.$message.destroy(); // 关闭所有气泡提示
				// const closeTip = this.$message.warn('请选择需要缩放的曲线', 0); // 设为 0 时不自动关闭
				// const closeClickListener = this.addClickEventListener((event) => {
				// 	this.updatePointer(event);
				// 	// 1、选择需要缩放的曲线
				// 	let intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());
				// 	// 过滤出所有线
				// 	intersects = intersects.filter(item => this.isLine(item.object));
				// 	if (intersects.length) {
				// 		closeTip(); // 关闭气泡提示
				// 		closeClickListener(); // 关闭点击事件监听

				// 		const needZoomCurve = intersects[0].object;
				// 		needZoomCurve.material.color = new THREE.Color(ColorEnum.yellow);
				// 		needZoomCurve.material.needsUpdate = true;
				// 		this.render();

				// 		const originPoints = this.curvesMap[needZoomCurve.nickname]?.clickPoints;

				// 		// 2、选择基准点
				// 		let datumSphere;
				// 		let referenceSphere;
				// 		let eventFlag = false;
				// 		const closeDatumTip = this.$message.warn('请选择基准点', 0);
				// 		this.addClickEventListener((event) => {
				// 			closeDatumTip();
				// 			const datumPointVector = this.getCurrPoint({ clientX: event.clientX, clientY: event.clientY });
				// 			datumSphere = this.drawSphere2(datumPointVector); // 绘制基准点小球

				// 			const tempObj = new THREE.Object3D();
				// 			tempObj.add(datumSphere);
				// 			this.getScene().add(tempObj);

				// 			const zoomLineObj = new THREE.Object3D();
				// 			this.getScene().add(zoomLineObj);

				// 			this.requestRender();

				// 			// 绘制基准线
				// 			if (!eventFlag) {
				// 				eventFlag = true;
				// 				let datumLine;
				// 				const closeDatumLineMouseMoveListener = this.addMousemoveEventListener((event) => {
				// 					const mousePointVector = this.getCurrPoint({ clientX: event.clientX, clientY: event.clientY });
				// 					if (!mousePointVector) return;

				// 					if (datumLine) tempObj.remove(datumLine);   // 清理 基准线 旧数据
				// 					datumLine = this.drawLine([datumPointVector, mousePointVector]);
				// 					tempObj.add(datumLine); // 插入新的 基准线 数据
				// 					this.requestRender();
				// 				});

				// 				// 2.1、选择参考点
				// 				const closeReferenceTip = this.$message.warn('请选择参考点', 0);
				// 				this.addClickEventListener((event) => {
				// 					closeReferenceTip();
				// 					closeDatumLineMouseMoveListener(); // 关闭基准线鼠标移动事件监听
				// 					const referencePointVector = this.getCurrPoint({ clientX: event.clientX, clientY: event.clientY });
				// 					referenceSphere = this.drawSphere2(referencePointVector); // 绘制参考点小球
				// 					tempObj.add(referenceSphere);

				// 					// 基准线长度
				// 					const datumLineLength = this.distanceTo(datumPointVector, referencePointVector);

				// 					// 2.2、添加鼠标移动事件监听器
				// 					let referenceLine;
				// 					let eventInvited = false;
				// 					let drawZoomCurveResponse;
				// 					let scale;
				// 					const closeReferenceLineMouseMoveListener = this.addMousemoveEventListener(async (event) => {
				// 						const mousePointVector = this.getCurrPoint({ clientX: event.clientX, clientY: event.clientY });
				// 						if (!mousePointVector) return;

				// 						if (!needZoomCurve) {
				// 							logger.log('need zoom curve miss!');
				// 							return;
				// 						}

				// 						if (referenceLine) tempObj.remove(referenceLine);
				// 						referenceLine = this.drawLine([referencePointVector, mousePointVector]);
				// 						tempObj.add(referenceLine); // 插入新的 参考线 数据
				// 						this.requestRender();

				// 						// 参考线长度
				// 						const referenceLineLength = this.distanceTo(referencePointVector, mousePointVector);
				// 						// 长度计算比例
				// 						scale = referenceLineLength / datumLineLength;

				// 						zoomLineObj.clear();
				// 						drawZoomCurveResponse = null;
				// 						drawZoomCurveResponse = await this.getRhinoJsonDataByPoints(originPoints, { scale, lineWidth: needZoomCurve.material.linewidth });
				// 						if (drawZoomCurveResponse?.line2) {
				// 							zoomLineObj.add(drawZoomCurveResponse?.line2); // 插入新的数据
				// 						}

				// 						if (!eventInvited) {
				// 							eventInvited = true;
				// 							const closeConfirmTip = this.$message.warn('点击后确认', 0);
				// 							this.addClickEventListener(async () => {
				// 								closeConfirmTip();
				// 								closeReferenceLineMouseMoveListener(); // 关闭参考线鼠标移动监听器

				// 								tempObj.clear(); // 清空临时数据
				// 								zoomLineObj.clear();

				// 								let rhinoInputData;
				// 								if (drawZoomCurveResponse?.line && drawZoomCurveResponse?.line2) {
				// 									// zoomLineObj.add(drawZoomCurveResponse.line2); // 插入新的数据

				// 									const zoomControlPoints = this.getControlPoints(originPoints, scale);
				// 									// const spheres = this.drawSpheres(zoomControlPoints, { radius: 2 });
				// 									// zoomLineObj.add(...spheres);
				// 									// this.requestRender();

				// 									rhinoInputData = JSON.stringify(zoomControlPoints);

				// 									// 临时预设线转移至正式空间
				// 									this.curveCount++;
				// 									const nickname = 'curve_' + this.curveCount;
				// 									await this.drawCurve(nickname, zoomControlPoints, true);
				// 								}

				// 								this.$emit('closeActionControl', { type, rhinoInputData }); // 取消曲线缩放

				// 								// saveString(rhinoInputData, 'zoomLine.json');
				// 								// saveString(JSON.stringify(originPoints), 'originLine.json');
				// 							});
				// 						}
				// 					});
				// 				});
				// 			}
				// 		});
				// 	}
				// }, false);
			}

			if (type === ActionEnum.mirrorCurve) {
				this.currentFunc = () => {
					this.$message.destroy();
					this.mirrorCurve.showMirrorSetting = false;
					this.resetColorHandle();
					const closeTip = this.$message.info('请选择要镜像的曲线', 0);
					const closeClickListener = this.addClickEventListener(event => {
						this.updatePointer(event);
						let intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());
						intersects = intersects.filter(item => (this.isYsLine(item.object, true) || this.isCircle(item.object)) && !item.object.nickname?.startsWith('zyx'));
						if (intersects.length) {
							closeTip();
							closeClickListener();
							const needMirrorCurve = intersects[0].object;
							needMirrorCurve.material.color = new THREE.Color(ColorEnum.yellow);
							needMirrorCurve.material.needsUpdate = true;
							this.mirrorCurve.curve = needMirrorCurve;
							this.mirrorCurve.showMirrorSetting = true;
						}
					}, false);
					escCallback = () => {
						this.controlling = false;
						this.$emit('closeActionControl', { type });
						this.resetMirror();
						this.resetColorHandle();
						this.$message.destroy();
						escCallback = null;
					};
				};
				this.currentFunc();
			}

			if (type === ActionEnum.rotateCopy || type === ActionEnum.circularArray) {
				const modalFlag = type === ActionEnum.rotateCopy ? 1 : 2;
				this.currentFunc = () => {
					this.rotation.showSetting = 0;
					this.$message.destroy();
					this.resetColorHandle();
					const msg = type === ActionEnum.rotateCopy ? '请选择要旋转的曲线' : '请选择要阵列的曲线';
					const closeTip = this.$message.info(msg, 0);
					const closeClickListener = this.addClickEventListener((event) => {
						this.updatePointer(event);
						let intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());
						intersects = intersects.filter(item => (this.isYsLine(item.object, true) || this.isCircle(item.object)) && !item.object.nickname?.startsWith('zyx'));
						if (intersects.length) {
							closeTip();
							closeClickListener();
							const needRotateCurve = intersects[0].object;
							needRotateCurve.material.color = new THREE.Color(ColorEnum.yellow);
							needRotateCurve.material.needsUpdate = true;
							this.rotation.curve = needRotateCurve;
							const showRotateModal = (params) => {
								this.showAngleModal(modalFlag, params);
							};
							this.rebackAdd(this.currentFunc, () => {
								this.resetRotateFunc();
								enterCallback = null;
								mitt.off('addPlaneEnd', showRotateModal);
							});
							mitt.on('addPlaneEnd', showRotateModal);
							this.showAngleModal(modalFlag, {
								plane: this.gridHelper,
								point: this.camCenter,
							});
						}
					}, false);

					escCallback = () => {
						this.controlling = false;
						this.$emit('closeActionControl', { type });
						this.resetRotate();
						this.resetColorHandle();
						this.$message.destroy();
						escCallback = null;
					};
				};
				this.currentFunc();
			}

			if (type === ActionEnum.moveCopy) {
				this.currentFunc = () => {
					this.$message.destroy();
					this.translate.showSetting = false;
					this.modalEdit = false;
					this.resetColorHandle();
					const closeTip = this.$message.info('请选择要平移的曲线', 0);
					const closeClickListener = this.addClickEventListener(event => {
						this.updatePointer(event);
						let intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());
						intersects = intersects.filter(item => (this.isYsLine(item.object, true) || this.isCircle(item.object)) && !item.object.nickname?.startsWith('zyx'));
						if (intersects.length) {
							closeTip();
							closeClickListener();
							const needTranslateCurve = intersects[0].object;
							needTranslateCurve.material.color = new THREE.Color(ColorEnum.yellow);
							needTranslateCurve.material.needsUpdate = true;
							this.translate.curve = needTranslateCurve;
							this.translate.showSetting = true;
						}
					}, false);
					escCallback = () => {
						this.controlling = false;
						this.$emit('closeActionControl', { type });
						this.resetTranslate();
						this.resetColorHandle();
						this.$message.destroy();
						escCallback = null;
						this.modalEdit = false;
					};
				};
				this.currentFunc();
			}
		},
		showAngleModal (flag, params) {
			this.$message.destroy();
			this.rotation.showSetting = flag;
			this.rotation = { ...this.rotation, ...params };
		},
		resetMirrorPlane () {
			if (this.mirrorCurve.curve) {
				this.removeSceneObj([this.mirrorCurve.tempObj, this.mirrorCurve.mirrorItem]);
				this.mirrorCurve = Object.assign(this.mirrorCurve, {
					points: [],
					tempObj: null,
					mirrorItem: null,
				});
			}
		},
		setMirror ({ type }) {
			this.rebackAdd(this.currentFunc, () => {
				this.resetMirrorPlane();
			});
			this.currentFunc = () => {
				this.$emit('updateModeSelect', type);
				this.resetMirrorPlane();
				this.$message.destroy();
				this.$message.info('请选择基准点', 0);
			};
			this.currentFunc();

		},
		resetLinePlane () {
			if (this.translate.curve) {
				this.removeSceneObj([this.translate.tempObj, this.translate.currentItem]);
				this.translate.tempObj = null;
				this.translate.points = [];
			}
		},
		setLinePlane (type) {
			this.rebackAdd(this.currentFunc, () => {
				this.resetLinePlane();
			});
			this.currentFunc = () => {
				this.$emit('updateModeSelect', type);
				this.$message.destroy();
				this.resetLinePlane();
				this.$message.info('请选择基准点', 0);
			};
			this.currentFunc();
		},
		setLineAxes (points) {
			this.translate.tempObj && this.translate.tempObj.clear();
			if (this.translate.currentItem) {
				this.removeSceneObj(this.translate.currentItem);
			}
			if (!points.length) return;
			const lineGeometry = new LineGeometry();
			const bufferGeometry = new THREE.BufferGeometry().setFromPoints(points);
			lineGeometry.setPositions(bufferGeometry.attributes.position.array);
			const lineMaterial = getLineMaterial(ColorEnum.black, this.getCanvas(), 2);
			const line = new Line2(lineGeometry, lineMaterial);
			this.translate.tempObj?.add(line);
			points.forEach(item => {
				const point = this.drawSphere2(item);
				this.translate.tempObj?.add(point);
			});
			const [vecStart, vecEnd] = points;
			const distance = vecStart.distanceTo(vecEnd);
			const normal = new THREE.Vector3().subVectors(vecEnd, vecStart);
			const { clickPoints } = this.curvesMap[this.translate.curve.nickname];
			const curveType = this.getTypeByNickname(this.translate.curve.nickname);
			this.curvesMap[`${curveType}_${this[`${curveType}Count`] + 1}`] = {
				spheres: [],
				clickPoints: clickPoints.map(item => new THREE.Vector3().addVectors(item.clone(), normal)),
			};

			const mesh = this.translate.curve;
			const geometry = mesh.geometry.clone();
			const newMesh = new THREE.Mesh(geometry, mesh.material);
			this.translate.currentItem = newMesh;
			this.translate.currentItem.translateOnAxis(normal.normalize(), distance);
			this.getScene().add(newMesh);
		},
		resetRotateFunc () {
			this.removeSceneObj(this.rotation.currentItems);
			this.rotation.currentItems = [];
		},
		setRotateFunc (count, angle) {

			this.resetRotateFunc();
			const { clickPoints } = this.curvesMap[this.rotation.curve.nickname];
			const vec = this.rotation.point;
			const { x, y, z } = this.gridHelper.diyNormal.clone().normalize();
			const mesh = this.rotation.curve;

			for (let i = 0; i < count; i++) {
				const geometry = mesh.geometry.clone();
				const theta = (Math.PI * angle * (i + 1)) / (180 * count);
				const matrix = new THREE.Matrix4();
				matrix.set(
					x * x * (1 - Math.cos(theta)) + Math.cos(theta), x * y * (1 - Math.cos(theta)) - z * Math.sin(theta), x * z * (1 - Math.cos(theta)) + y * Math.sin(theta), (vec.x * (1 - x * x) - x * (vec.y * y + vec.z * z)) * (1 - Math.cos(theta)) + (vec.y * z - vec.z * y) * Math.sin(theta),
					x * y * (1 - Math.cos(theta)) + z * Math.sin(theta), y * y * (1 - Math.cos(theta)) + Math.cos(theta), y * z * (1 - Math.cos(theta)) - x * Math.sin(theta), (vec.y * (1 - y * y) - y * (vec.x * x + vec.z * z)) * (1 - Math.cos(theta)) + (vec.z * x - vec.x * z) * Math.sin(theta),
					x * z * (1 - Math.cos(theta)) - y * Math.sin(theta), y * z * (1 - Math.cos(theta)) + x * Math.sin(theta), z * z * (1 - Math.cos(theta)) + Math.cos(theta), (vec.z * (1 - z * z) - z * (vec.x * x + vec.y * y)) * (1 - Math.cos(theta)) + (vec.x * y - vec.y * x) * Math.sin(theta),
					0, 0, 0, 1,
				);
				geometry.applyMatrix4(matrix);
				const newClickPoints = clickPoints.map(item => item.clone().applyMatrix4(matrix));
				const curveType = this.getTypeByNickname(this.rotation.curve.nickname);
				const curveName = `${curveType}_${this[`${curveType}Count`] + i + 1}`;
				this.curvesMap[curveName] = {
					spheres: [],
					clickPoints: newClickPoints,
				};
				const newMesh = new THREE.Mesh(geometry, mesh.material);
				newMesh.nickname = curveName;
				this.rotation.currentItems.push(newMesh);
				this.getScene().add(newMesh);
			}

			enterCallback = () => {
				this.controlling = false;
				this.$message.destroy();
				this.$emit('closeActionControl');
				this.rotation.currentItems.forEach(item => {
					this.drawCurve(item.nickname, this.curvesMap[item.nickname].clickPoints);
				});
				mitt.offType('addPlaneEnd');
				this.resetRotate();
				this.resetColorHandle();
				rebackList = [];
				enterCallback = null;
			};
		},

		resetScaleLine () {
			if (this.scaleCurve.currentItem) {
				this.removeSceneObj(this.scaleCurve.currentItem);
			}
			const mesh = this.scaleCurve.curve;
			const geometry = mesh.geometry.clone();
			const newMesh = new THREE.Mesh(geometry, mesh.material);
			this.getScene().add(newMesh);
			this.scaleCurve.currentItem = newMesh;
			return this.scaleCurve.currentItem;
		},

		/**
		 * 取得新昵称
		 * @param nickname 原昵称或类型都可以
		 * @param increase 自增长（默认true）
		 * @param count 自增长数量（默认1）
		 */
		getNewNickname (nickname, increase = true, count = 1) {
			let newNickname;
			[DrawEnum.curve, DrawEnum.brokenLine, DrawEnum.circle, DrawEnum.singleSpiralLine, DrawEnum.doubleHelixLine, DrawEnum.point].forEach(i => {
				if (nickname === i || nickname.startsWith(i + '_')) {
					if (increase) {
						this[i + 'Count'] = this[i + 'Count'] + count;
					}
					newNickname = i + '_' + (this[i + 'Count']);
				}
			});
			return newNickname;
		},

		syncNicknameCount (nickname) {
			if (nickname && nickname.match('_')) {
				const names = nickname.split('_')[0];
				this[names[0] + 'Count'] = Math.max(this[names[0] + 'Count'], +names[1]);
			}
		},
		/**
		 * 根据昵称取类型
		 * @param nickname 昵称
		 */
		getTypeByNickname (nickname) {
			return nickname?.replace(/_.*$/, '');
		},
		setCurveMap (curveName, baseMesh, callback) {
			const clickPoints = baseMesh.nickname.startsWith('zyx') ? this.scaleCurve.clickPoints : this.curvesMap[baseMesh.nickname].clickPoints;
			this.curvesMap[curveName] = {
				spheres: [],
				clickPoints: [],
			};
			callback(clickPoints, this.curvesMap[curveName]);
		},

		setScaleEnterCallback (name) {
			enterCallback = () => {
				this.controlling = false;
				this.deleteRaycasterObjByGroupName(raycasterObjs, this.scaleCurve.curve.nickname);
				if (name.startsWith('zyx')) {
					this.drawCurve(name, this.curvesMap[name].clickPoints);
				} else {
					this.drawCurve(this.scaleCurve.curve.nickname, this.curvesMap[name].clickPoints);
					delete this.curvesMap[name];
				}

				this.emitCurveData('ys_line');
				this.resetScale();
				this.resetColorHandle();
				this.scaleCurve.showSetting = false;
				mitt.offType('addPlaneEnd');
				this.$emit('closeActionControl');
				rebackList = [];
			};
		},

		setScaleLineAxes ({ x, y, z, scale }) {
			if (this.scaleCurve.tempObj) {
				this.removeSceneObj(this.scaleCurve.tempObj);
			}
			this.scaleCurve.tempObj = this.drawSphere2({ x, y, z });
			this.getScene().add(this.scaleCurve.tempObj);
			const radios = scale;
			this.scaleCurve.point = new THREE.Vector3(x, y, z);	// 基准点
			const currentItem = this.resetScaleLine();
			currentItem.geometry.scale(radios, radios, radios);
			const normal = this.scaleCurve.point.clone().normalize();
			const distance = new THREE.Vector3(0, 0, 0).distanceTo(this.scaleCurve.point) * (1 - radios);
			this.setCurveMap(this.scaleCurve.curveName, this.scaleCurve.curve, (points, maps) => {
				// 按向量移动小球
				maps.clickPoints = points.map(item =>
					item.clone().multiplyScalar(radios).add(
						this.scaleCurve.point.clone().multiplyScalar(1 - radios),
					));
				// 按向量移动预设线
				currentItem.translateOnAxis(normal, distance);
			});
			this.setScaleEnterCallback(this.scaleCurve.curveName);
		},

		getPoints (line) {
			const points = [];
			const positions = line.geometry.attributes.position.array;
			for (let i = 0; i < positions.length; i += 3) {
				const x = positions[i];
				const y = positions[i + 1];
				const z = positions[i + 2];
				points.push({ x, y, z });
			}
			return points;
		},

		drawSpheres (points, { radius = 1, color = ColorEnum.yellow } = {}) {
			const spheres = [];
			const geometry = new THREE.SphereGeometry(radius, 100, 100);
			const material = new THREE.MeshBasicMaterial({ color });

			for (const point of points) {
				const sphere = new THREE.Mesh(geometry.clone(), material);
				sphere.position.set(point.x, point.y, point.z);
				spheres.push(sphere);
			}

			return spheres;
		},

		/**
		 * 检查 预设线 是否存在
		 * @return {boolean}
		 */
		checkYsLineExists () {
			if (!Object.keys(this.curvesMap).length && !this.getSceneAllDispersedLine().length) {
				this.$message.info('缺少预设线或自由线目标，请先绘制预设线！');
				return false;
			}
			return true;
		},

		getCenterPoint (mesh) {
			const geometry = mesh.geometry;
			geometry.computeBoundingBox();
			const center = new THREE.Vector3();
			geometry.boundingBox.getCenter(center);
			mesh.localToWorld(center);
			return center;
		},

		getWorldPoints (mesh) {
			const points = [];
			mesh.updateMatrixWorld(true);
			const position = mesh.geometry.attributes.position.array;
			for (let i = 0; i < position.length; i += 3) {
				const x = position[i];
				const y = position[i + 1];
				const z = position[i + 2];
				const point = new THREE.Vector3();
				point.set(x, y, z);
				mesh.localToWorld(point);
				points.push(point);
			}
			return points;
		},

		/**
		 * 计算该向量到所传入v间的距离(向量平方和,再开方)
		 * @param {THREE.Vector3} v1
		 * @param {THREE.Vector3} v2
		 */
		distanceTo (v1, v2) {
			return v1.distanceTo(v2);
		},

		/**
		 * 添加点击事件监听器
		 * @param {Function} callback 事件监听回调函数
		 * @param {Boolean} immediatelyClose 立即关闭
		 * @param {Element|String} selector 监听节点对象
		 * @return {Function} closeHandler 关闭监听函数
		 */
		addClickEventListener (callback = (event) => event, immediatelyClose = true, selector = null) {

			let el;
			if (isEmpty(selector)) {
				el = document.querySelector('#mainCanvas');
			} else if (isString(selector)) {
				el = document.querySelector(selector);
			} else {
				el = selector;
			}

			const clickHandler = (event) => {
				event.stopPropagation();
				if (immediatelyClose) closeHandler();
				return callback(event);
			};
			el.addEventListener('click', clickHandler, false);

			const closeHandler = () => {
				el.removeEventListener('click', clickHandler);
			};
			mitt.on('resetEvent', closeHandler);

			return closeHandler;
		},

		/**
		 * 添加鼠标移动事件监听器
		 * @param {Function} callback 事件监听回调函数
		 * @param {Boolean} immediatelyClose 立即关闭
		 * @param {Element|String} selector 监听节点对象
		 * @return {Function} closeHandler 关闭监听函数
		 */
		addMousemoveEventListener (callback = (event) => event, immediatelyClose = false, selector = null) {

			let el;
			if (isEmpty(selector)) {
				el = document.querySelector('#mainCanvas');
			} else if (isString(selector)) {
				el = document.querySelector(selector);
			} else {
				el = selector;
			}

			const mousemoveHandler = (event) => {
				if (immediatelyClose) closeHandler();
				return callback(event);
			};
			el.addEventListener('mousemove', mousemoveHandler, false);

			const closeHandler = () => {
				el.removeEventListener('mousemove', mousemoveHandler);
			};

			return closeHandler;
		},

		/**
		 * 添加回车事件监听器
		 * @param {Function} callback 事件监听回调函数
		 * @param {Boolean} immediatelyClose 立即关闭
		 * @param {Element|String} selector 监听节点对象
		 * @return {Function} closeHandler 关闭监听函数
		 */
		addEnterEventListener (callback = (event) => event, immediatelyClose = true, selector = null) {

			let el;
			if (isEmpty(selector)) {
				el = document.querySelector('#mainCanvas');
			} else if (isString(selector)) {
				el = document.querySelector(selector);
			} else {
				el = selector;
			}

			const enterHandler = (event) => {
				const oEvent = event || window.event;
				// 获取键盘的keyCode值
				const keyCode = oEvent.keyCode || oEvent.which || oEvent.charCode;

				if (immediatelyClose) closeHandler();

				if (keyCode === 13) { // Enter
					return callback(event);
				}
			};
			el.addEventListener('keydown', enterHandler, false);

			const closeHandler = () => {
				el.removeEventListener('keydown', enterHandler);
			};

			return closeHandler;
		},

		/**
		 * 添加按键事件监听器
		 * @param {Function} callback 事件监听回调函数
		 * @param {Boolean} immediatelyClose 立即关闭
		 * @param {Element|String} selector 监听节点对象
		 * @return {Function} closeHandler 关闭监听函数
		 */
		addKeyDownEventListener (callback = (event) => event, immediatelyClose = true, selector = null) {

			let el;
			if (isEmpty(selector)) {
				el = document.querySelector('#mainCanvas');
			} else if (isString(selector)) {
				el = document.querySelector(selector);
			} else {
				el = selector;
			}

			const enterHandler = (event) => {
				const oEvent = event || window.event;
				// 获取键盘的keyCode值
				const keyCode = oEvent.keyCode || oEvent.which || oEvent.charCode;

				if (immediatelyClose) closeHandler();

				return callback({ keyCode, event });
			};
			el.addEventListener('keydown', enterHandler, false);

			const closeHandler = () => {
				el.removeEventListener('keydown', enterHandler);
			};

			return closeHandler;
		},

		/**
		 * 画线
		 */
		drawLine (pointVectors, { color = ColorEnum.black, lineWidth = 2 } = {}) {
			const curve = new THREE.CatmullRomCurve3(pointVectors, false, 'centripetal');
			const geometry = new THREE.BufferGeometry().setFromPoints(curve.getPoints(50));
			const lineGeometry = new LineGeometry();
			lineGeometry.setPositions(geometry.attributes.position.array);
			const lineMaterial = getLineMaterial(color, this.getCanvas(), lineWidth);
			const line = new Line2(lineGeometry, lineMaterial);
			line.computeLineDistances();
			return line;
		},

		/**
		 * 框选
		 */
		onBoxSelect (action) {
			this.curAction = ActionEnum[action];
			if (action) {
				logger.log('初始化框选事件.');
				this.initBoxSelectEvent();
			} else {
				logger.log('删除框选监听.');
				this.removeBoxSelectListen();
			}
		},

		createPoint (curveItem) {
			if (curveItem.nickname?.startsWith('zyx')) {
				const point = curveItem.geometry.attributes.instanceEnd.data.array;
				var arr = [];
				const newPoints = [];
				for (let i = 0; i < point.length; i++) {
					arr.push(point[i]);
					if ((i + 1) % 3 === 0) {
						newPoints.push(new THREE.Vector3(...arr));
						arr = [];
					}
				}
				const name = curveItem.nickname.split('_')[0];
				const num = this.getCurrenZYXCount();
				this.scaleCurve.clickPoints = newPoints;
				return `${name}_${+num + 1}`;
			}
			const curveType = this.getTypeByNickname(curveItem.nickname);
			return `${curveType}_${this[`${curveType}Count`] + 1}`;
		},

		getCurrenZYXCount () {
			let num = 0;
			const zyxList = this.getSceneAllDispersedLine().map(item => item.nickname);
			const zyxDiyList = Object.entries(this.curvesMap).map(item => {
				return item[0];
			}).filter(item => item.startsWith('zyx'));
			const list = [...zyxList, ...zyxDiyList];
			for (const item of list) {
				const [_, count] = item.split('_');
				if (+count > num) {
					num = count;
				}
			}
			return +num;
		},

		getCurrentYSXCount (type) {
			let num = 0;
			const ysxList = Object.entries(this.curvesMap).map(item => {
				return item[0];
			}).filter(item => item.startsWith(type));
			for (const item of ysxList) {
				const [_, count] = item.split('_');
				if (+count > num) {
					num = count;
				}
			}
			return +num;
		},

		/**
		 * 停止绘制
		 */
		stopDrawn () {
			this.curDraw = '';
			this.drawing = false;
		},

		/**
		 * 分离选定
		 */
		detachSelected (drawControl) {
			drawControl?.detach();
			drawControl = null;
			this.currentActiveItem = null;
		},

		/**
		 * 按组名删除对象
		 */
		removeObjByGroupName (groupName, conditionHandler) {
			logger.log('按组名删除对象:', groupName);
			this.removeObjByGroupNameComeFromCommon(groupName, conditionHandler);
			raycasterObjs = this.deleteRaycasterObjByGroupName(raycasterObjs, groupName);
		},

		// ---------------------------------------------------------------------------------------------------------------------
		/**
		 * 按名称列表更新预设线属性(多个)
		 * @param {Array} lineNameList
		 * @param selected
		 * @param color
		 * @param opacity
		 * @param visible
		 * @param onlyYsLine
		 */
		updateYsLineAttrByNameList (lineNameList, { selected, color, opacity, visible, onlyYsLine }) {
			color = selected ? toHexColor(color) : ColorEnum.black;
			const ysLines = this.getSceneChildrenByCondition(YS_LINE_GROUP);
			const lines = [...ysLines];
			if (!onlyYsLine) {
				const dispersedLines = this.getSceneChildrenByCondition(DISPERSED_LINE_GROUP);
				lines.push(...dispersedLines);
			}
			for (const child of lines) {
				if (lineNameList.includes(child.nickname)) {
					this.updateObjAttr(child, { color, opacity, visible });
				}
			}
			this.requestRender();
		},

		/**
		 * 按名称列表更新变径点属性(多个)
		 * @param {Array} pointNameList
		 * @param selected
		 * @param color
		 * @param opacity
		 * @param visible
		 */
		updateBjPointAttrByNameList (pointNameList, { selected, color, opacity, visible }) {
			color = selected ? toHexColor(color) : ColorEnum.black;
			const points = this.getSceneChildrenByCondition(BJ_POINT_GROUP);
			for (const child of points) {
				if (pointNameList.includes(child.nickname)) {
					this.updateObjAttr(child, { color, opacity, visible });
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象属性(所有)
		 */
		updateAllAttr ({ color, opacity, visible }) {
			for (const child of this.getSceneAllFaceAndAllWireframe()) {
				this.updateObjAttr(child, { color, opacity, visible });
			}
			this.requestRender();
		},

		/**
		 * 根据传入名称列表更新对象颜色(多个)
		 */
		updateAttrByNameList (nameList, { selected, color, opacity, visible }) {
			color = selected ? toHexColor(color) : this.originColor;
			for (const child of this.getSceneAllFaceAndAllWireframe()) {
				if (nameList.includes(child.nickname) || nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { color, opacity, visible });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据名称更新对象颜色(单个)
		 */
		updateColorByName (name, selected) {
			const color = selected ? this.selectedColor : this.originColor;
			for (const child of this.getSceneAllFaceAndAllWireframe()) {
				if (name && (child.nickname === name || child.groupName === name)) {
					this.updateObjAttr(child, { color });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据名称更新对象可见性(单个)
		 */
		updateVisibleByName (name, visible, isRefreshView = true) {
			for (const child of this.getSceneAllFaceAndAllWireframe()) {
				if (name && (child.nickname === name || child.groupName === name)) {
					this.updateObjAttr(child, { visible });
				}
			}
			isRefreshView && this.requestRender();
		},

		/**
		 * 更新对象颜色(所有)
		 */
		updateAllColor (color) {
			for (const child of this.getSceneAllFaceAndAllWireframe()) {
				if (this.isModelFace(child)) { // 非坏面(缺失的面)才可操作
					this.updateObjAttr(child, { color });
				}
				if (this.isModelEdge(child)) {
					this.updateObjAttr(child, { color: ColorEnum.black });
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象不透明度(所有)
		 */
		updateAllOpacity (opacity) {
			for (const child of this.getSceneAllFaceAndAllWireframe()) {
				this.updateObjAttr(child, { opacity });
			}
			this.requestRender();
		},

		/**
		 * 更新对象可见性(所有)
		 */
		updateAllVisible (visible, hiddenYsLine = false) {
			const allFaceAndWireframe = this.getSceneAllFaceAndAllWireframe();
			const allDispersedLine = !hiddenYsLine ? this.getSceneAllDispersedLine() : [];
			for (const child of [...allFaceAndWireframe, ...allDispersedLine]) {
				this.updateObjAttr(child, { visible });
			}
			this.requestRender();
		},

		/**
		 * 更新自由线(预设线)对象可见性(所有)
		 */
		updateYsLineAllVisible (visible) {
			const { spheres, ysLines } = this.getAllYsLineAndSpheres();
			for (const obj of [...ysLines, ...spheres]) {
				this.updateObjAttr(obj, { visible });
			}
			this.requestRender();
		},

		/**
		 * 更新离散线对象可见性(所有)
		 */
		updateDispersedLineAllVisible (visible) {
			const lines = this.getSceneAllDispersedLine();
			for (const line of lines) {
				this.updateObjAttr(line, { visible });
			}
			this.requestRender();
		},

		/**
		 * 更新变径点对象可见性(所有)
		 */
		updateBjPointAllVisible (visible) {
			const bjGroup = this.getGroupByName(BJ_POINT_GROUP);
			if (!bjGroup) return;
			for (const obj of bjGroup.children) {
				this.updateObjAttr(obj, { visible });
			}
			this.requestRender();
		},

		/**
		 * 更新所有结果可见性(所有)
		 */
		updateAllResultVisible (visible) {
			for (const child of this.getSceneChildrenByCondition('resultWlLineGroup')) {
				this.updateObjAttr(child, { visible });
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象颜色(多个)
		 */
		updateColorByList (nameList, selected, color) {
			color = selected ? toHexColor(color) : this.originColor;
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (nameList.includes(child.nickname) || nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { color });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象不透明度(多个)
		 */
		updateOpacityByList (nameList, opacity) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (nameList.includes(child.nickname) || nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { opacity });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象可见性(多个)
		 */
		updateVisibleByList (nameList, visible) {
			const allFacesAndWireframe = this.getSceneAllFaceAndAllWireframe();
			const { ysLines, spheres } = this.getAllYsLineAndSpheres();
			const allDispersedLine = this.getSceneAllDispersedLine();
			for (const child of [...allFacesAndWireframe, ...ysLines, ...spheres, ...allDispersedLine]) {
				if (nameList.includes(child.nickname) ||
					nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { visible });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象线宽(多个)
		 */
		updateLineWidthByList (nameList, lineWidth) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if ((nameList.includes(child.nickname) || nameList.includes(child.groupName)) && this.isModelEdge(child)) {
					this.updateObjAttr(child, { lineWidth });
				}
			}
			this.requestRender();
		},
		// ---------------------------------------------------------------------------------------------------------------------

		/**
		 * 获取鼠标位置
		 */
		getPosition (event) {
			const sliderWidth = 320;
			const headerHeight = 50;
			const navigationHeight = 0 + 2;
			const canvas = this.getCanvas();
			// 屏幕坐标转标准设备坐标
			const x = ((event.clientX - (this.collapsed ? 0 : sliderWidth)) / canvas.clientWidth) * 2 - 1;  // 标准设备横坐标
			const y = -((event.clientY - (headerHeight + navigationHeight)) / canvas.clientHeight) * 2 + 1; // 标准设备纵坐标
			return { x, y };
		},

		/**
		 * 通过世界坐标转换为屏幕坐标
		 *  3D坐标转换成2D坐标
		 * @return {Object}
		 */
		worldVectorToScreenVector (worldVector) {
			const vector = worldVector.project(this.curCamera); // 通过世界坐标获取转标准设备坐标
			const w = window.innerWidth / 2;
			const h = window.innerHeight / 2;
			const screenX = Math.round(vector.x * w + w); // 标准设备坐标转屏幕坐标
			const screenY = Math.round(-vector.y * h + h);
			logger.log('屏幕坐标X: ', screenX);
			logger.log('屏幕坐标Y: ', screenY);
			return { screenX, screenY };
		},

		/**
		 * 获取当前世界坐标
		 *  将鼠标坐标转换为3D空间坐标
		 *  @param pointer
		 *  @return {THREE.Vector3}
		 */
		getWorldVector (pointer) {
			// 屏幕坐标转标准设备坐标
			// 标准设备坐标(z=0.5表示深度)
			const vector = new THREE.Vector3(pointer.x, pointer.y, 0.5);
			// 标准设备坐标转为世界坐标(函数vector.unproject(camera)则可以从屏幕2d坐标转换为3d空间坐标)
			const worldVector = vector.unproject(this.curCamera);
			logger.log('世界坐标 worldVector: ', worldVector);
			return worldVector;
		},

		/**
		 * 获取当前点坐标
		 *  @param event
		 *  @return {THREE.Vector3}
		 */
		getCurrPoint (event, filter = () => true) {
			const intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas()).filter(filter);
			return intersects[0]?.point;
		},

		/**
		 * 重置视图
		 */
		resetView () {
			this.direction = ViewEnum.default;
			this.changeViewCamera();
			this.mouse.RIGHT = THREE.MOUSE.ROTATE;
			const vec = new THREE.Vector3(0, -0.46, 0.2).normalize();
			this.navChange([vec.x, vec.y, vec.z], true);
			this.curControl.enabled = true;
			// 禁用缩放、旋转，允许平移
			if (this.curControl instanceof OrbitControls) {
				this.curControl.enableZoom = true;
				this.curControl.enableRotate = true;
			}
			if (this.curControl instanceof TrackballControls) {
				this.curControl.noZoom = false;
				this.curControl.noRotate = false;
			}
			this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 重置相机
		 */
		resetCamera (zoom = null) {
			// this.curControl.target0.copy(this.curControl.target);
			// this.curControl.position0.copy(this.curControl.object.position);
			// this.curControl.zoom0 = zoom ?? (this.curControl.object instanceof PerspectiveCamera ? this.curControl.object.zoom : 1);
			// this.curControl.update();
			const zoom0 = zoom ?? this.curCamera.zoom;
			this.curControl.zoom0 = zoom0;
			this.curControl.reset();
		},

		// /**
		//  * 根据相关参数复位相机
		//  */
		// restoreCamera (position, rotation, controlCenter) {
		// 	this.curCamera.position.set(position.x, position.y, position.z);
		// 	this.curCamera.rotation.set(rotation.x, rotation.y, rotation.z);
		//
		// 	this.curControl.target.set(controlCenter.x, controlCenter.y, controlCenter.z);
		// 	this.curControl.update();
		//
		// 	this.requestRender();
		// },

		/**
		 * 创建网格平面
		 */
		// createGridPlane () {
		// 	if (this.gridHelper) return;
		// 	this.gridHelper = new THREE.GridHelper(this.modelLong, 35);
		// 	this.gridHelper.isHelper = true;
		// 	this.updateGridPlane();
		// 	this.getScene().add(this.gridHelper);
		// 	this.requestRender();
		// },

		/**
		 * 更新网格平面
		 */
		// updateGridPlane () {
		// 	if (this.gridHelper) {
		// 		this.gridHelper.position.copy(this.camCenter.clone());
		// 		this.gridHelper.rotation.copy(this.curCamera.rotation.clone());
		// 		this.gridHelper.rotateX(Math.PI / 2);
		// 		this.gridHelper.updateMatrix();
		// 	}
		// },

		/**
		 * 删除网格平面
		 */
		// removeGridPlane () {
		// 	if (this.gridHelper) {
		// 		this.removeSceneObj(this.gridHelper);
		// 		this.gridHelper = null;
		// 		this.requestRender();
		// 	}
		// },

		/**
		 * 创建部件边框
		 */
		createWidgetBorder () {
			// 在3D空间中表示一个盒子或立方体。其主要用于表示物体在世界坐标中的边界框
			const box = new THREE.Box3();
			// 通过传入的object3D对象来返回当前模型的最小大小，值可以使一个mesh也可以使group
			const group = new THREE.Group();
			raycasterObjs.forEach(mesh => group.add(mesh));
			this.getScene().add(group);
			box.expandByObject(group);
			const boxHelper = new THREE.BoxHelper(group, 0xffff00);
			logger.log('boxHelper: ', boxHelper);
			this.getScene().add(boxHelper);
			boxHelper.geometry.computeBoundingBox();
			logger.log('boundingBox: ', boxHelper.geometry.boundingBox);
			const centerVector = boxHelper.geometry.boundingSphere.center;
			logger.log('centerVector: ', centerVector);
		},

		/**
		 * 位置数组 转 文本（Rhino grasshopper call）
		 * @param positionArray
		 * @return {string}
		 */
		positionArray2Txt (positionArray) {
			const points = [];
			for (let i = 0; i < positionArray.length; i += 3) {
				const x = positionArray[i];
				const y = positionArray[i + 1];
				const z = positionArray[i + 2];
				points.push({ x, y, z });
			}
			return JSON.stringify(points);
		},

		/**
		 * 通过mesh获取世界坐标
		 */
		getWorldCoordinatesByMesh (mesh) {
			// 该语句默认在threeJs渲染的过程中执行  如果想获得世界矩阵属性、世界位置属性等属性，需要手动更新
			mesh.updateMatrixWorld(true);
			// 声明一个三维向量用来保存网格模型的世界坐标
			const worldPosition = new THREE.Vector3();
			// 获得世界坐标，执行getWorldPosition方法，提取网格模型的世界坐标结果保存到参数worldPosition中
			mesh.getWorldPosition(worldPosition);
			logger.log('查看网格模型', mesh);
			logger.log('查看网格模型世界坐标', worldPosition);
			logger.log('查看网格模型本地坐标', mesh.position);
			return worldPosition;
		},

		/**
		 * 获取点坐标
		 * @param vector3
		 * @return {{}}
		 */
		getPoint (vector3) {
			return pick(vector3, ['x', 'y', 'z']);
		},

		/**
		 * 获取点击对象
		 */
		getIntersects (clientX, clientY) {
			return this.getCanvasIntersects({ clientX, clientY }, this.getScene(), this.curCamera, this.getCanvas());
		},

		/**
		 * 获取拾取对象
		 */
		getRaycasterObjs () {
			return raycasterObjs;
		},

		/**
		 * 当canvas不占满整屏时射线拾取存在偏差，获取点击对象
		 * @param event 事件对象
		 * @param scene 场景对象
		 * @param camera 镜头对象
		 * @param canvas 绘制盒子
		 * @param isFilter 是否过滤掉隐藏的部件
		 */
		getCanvasIntersects (event, scene, camera, canvas, isFilter = true) {
			preventDefaults(event);
			// 获取元素的大小及其相对于视口的位置
			const getBoundingClientRect = canvas.getBoundingClientRect();
			// 屏幕坐标转标准设备坐标
			const x = ((event.clientX - getBoundingClientRect.left) / canvas.offsetWidth) * 2 - 1; // 标准设备横坐标
			const y = -((event.clientY - getBoundingClientRect.top) / canvas.offsetHeight) * 2 + 1; // 标准设备纵坐标
			const vector = new THREE.Vector3(x, y, 0.5); // 标准设备坐标

			// 创建射线投射器对象
			const raycaster = new THREE.Raycaster();
			raycaster.params.Line = { threshold: 6 };
			raycaster.params.Line2 = { threshold: 6 };
			raycaster.params.Points = { threshold: 6 };
			raycaster.setFromCamera(vector, camera);
			// 返回射线选中的对象 第二个参数如果不填 默认是false
			const objects = [...raycasterObjs];
			if (this.gridHelper && this.curDraw !== DrawEnum.point) {
				objects.push(this.gridHelper);
			}
			let intersects = raycaster.intersectObjects(objects, true);
			// 过滤掉隐藏的部件
			if (isFilter) {
				intersects = intersects.filter(item => item.object && item.object.visible);
			}
			// 返回选中的对象数组
			return intersects;
		},

		/**
		 * 当canvas不占满整屏时射线拾取存在偏差，获取点击管道对象
		 * @param event 事件对象
		 */
		getPipeLineIntersects (event) {
			if (event && event.preventDefault) {
				event.preventDefault();
			}
			const canvas = this.getCanvas();
			// 获取元素的大小及其相对于视口的位置
			const getBoundingClientRect = canvas.getBoundingClientRect();
			// 屏幕坐标转标准设备坐标
			const x = ((event.clientX - getBoundingClientRect.left) / canvas.offsetWidth) * 2 - 1; // 标准设备横坐标
			const y = -((event.clientY - getBoundingClientRect.top) / canvas.offsetHeight) * 2 + 1; // 标准设备纵坐标
			const vector = new THREE.Vector3(x, y, 0.5); // 标准设备坐标
			// 创建射线投射器对象
			const raycaster = new THREE.Raycaster();
			raycaster.params.Line = { threshold: 4 };
			raycaster.params.Line2 = { threshold: 8 };
			raycaster.params.Points = { threshold: 4 };
			raycaster.setFromCamera(vector, this.curCamera);
			const objects = [];
			const children = this.getSceneAllResultObjs(child => child.groupName === waterCoolingResultKeyMap.pipeline);
			const children2 = this.getSceneAllBjPoint();
			// logger.log('MoldView getPipeLineIntersects children2:', children2);
			if (children?.length) objects.push(...children, ...children2);
			// 返回射线选中的对象 第二个参数如果不填 默认是false
			const intersects = raycaster.intersectObjects(objects);
			// logger.log('MoldView getPipeLineIntersects intersects:', intersects);
			if (intersects.some(i => i.object.groupName?.match('points_group'))) {
				return [];
			}
			// 返回选中的对象数组
			return intersects.filter(i => !i.object.groupName?.match('points_group'));
		},

		/**
		 * 选择参考平面模式
		 * @param {*} key
		 */
		selectPlaneMode (key) {
			this.curPlaneMode = key;
			if (key.match(/diy|curve/i)) {
				// 自定义和曲面进入相应等待选点模式
				this.curPrePlaneMode = key;
				if (this.centerPoint.object) {
					this.centerPoint.object.visible = false;
				}
				this.centerPoint.start = null;
				this.centerPoint.end = null;
				this.removeGridPlane();
				this.$message.destroy();
				if (this.curPlaneMode === PlaneModeEnum.curveNormalPlane) {
					// 法平面是在线上确定
					this.$message.info('请以鼠标点击线上的点作为原点', 0);
				} else {
					// 切平面及其他是在面上确定
					this.$message.info('请以鼠标点击面上的点作为原点', 0);
				}
			} else {
				// 普通模式开始更新绘制
				this.updateGridPlane(key);
				mitt.emit('addPlaneEnd', {
					plane: this.gridHelper,
					point: this.camCenter,
				});
			}
		},

		/**
		 * 参考平面模式前处理
		 * @param {*} e
		 */
		onPrePlaneHandler (e) {
			enterCallback = (event) => {
				preventDefaults(event);
				if (!this.centerPoint.xyz) {
					return;
				}
				this.pointModalCallback(this.centerPoint.xyz ? this.centerPoint.start?.position : {});
				mitt.emit('addPlaneEnd', {
					plane: this.gridHelper,
					point: this.centerPoint.xyz,
				});
			};

			// 切平面只过滤面，法平面只过滤线
			let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());
			if (this.curPlaneMode === PlaneModeEnum.curveNormalPlane) {
				// 法平面只过滤线
				intersects = intersects?.filter(item => this.isLine(item.object));
			} else if (this.curPlaneMode === PlaneModeEnum.curveTangentPlane) {
				// 切平面只过滤面
				intersects = intersects?.filter(item => this.isFace(item.object));
			}

			// mouseclick事件中，有相交点就绘制点
			if (intersects[0]?.point) {
				logger.log('onPrePlaneHandler', intersects[0]);
				if (this.centerPoint.object) {
					this.removeSceneObjByArray([this.centerPoint.object]);
				}
				if (this.curPlaneMode.match(/XY|YZ|XZ/)) {
					this.centerPoint.object = this.drawSphere(intersects[0].point, null, 2, 'center');
					this.centerPoint.object.visible = true;
					this.centerPoint.xyz = intersects[0].point;
				} else if (this.curPlaneMode === PlaneModeEnum.curveTangentPlane) {
					this.centerPoint.object = this.drawSphere(intersects[0].point, null, 2, 'center');
					this.centerPoint.object.visible = true;
					this.centerPoint.xyz = intersects[0].point;
					this.centerPoint.normal = intersects[0].face.normal;
					this.$message.destroy();
					this.$message.info('回车后显示参考平面', 0);
				} else if (this.curPlaneMode === PlaneModeEnum.curveNormalPlane) {
					this.centerPoint.object = this.drawSphere(intersects[0].point, null, 2, 'center');
					this.centerPoint.object.visible = true;
					this.centerPoint.xyz = intersects[0].point;
					const pointOnLine = intersects[0].pointOnLine;
					const arr = intersects[0].object.geometry.attributes.instanceStart.data.array;
					this.centerPoint.normal = null;

					for (let i = 0; i < arr.length; i = i + 3) {
						if (pointOnLine.x === arr[i] && pointOnLine.y === arr[i + 1] && pointOnLine.z === arr[i + 2]) {
							this.centerPoint.normal = new THREE.Vector3(arr[i + 3] - arr[i - 3], arr[i + 4] - arr[i - 2], arr[i + 5] - arr[i - 1]);
							logger.log('normal', this.centerPoint.normal);
						}
					}
					if (!this.centerPoint.normal) {
						this.centerPoint.normal = new THREE.Vector3(arr[arr.length - 3] - arr[0], arr[arr.length - 2] - arr[1], arr[arr.length - 1] - arr[2]);
						logger.log('normal', this.centerPoint.normal);
					}

					this.$message.destroy();
					this.$message.info('回车后显示参考平面', 0);
				} else {
					this.$message.destroy();
					this.$message.info('回车后显示参考平面', 0);
				}

			}
		},

		/**
		 * 参考平面后处理
		 */
		pointModalCallback () {
			// 参考平面绘制完毕
			this.curPrePlaneMode = null;
			// 清除回车事件
			enterCallback = null;
			// 渲染参考平面
			this.updateGridPlane(this.curPlaneMode);
			// 清除对应图形
			const arr = [this.centerPoint.object].filter(i => i);
			arr.forEach(i => i.visible = false);
			this.removeSceneObjByArray(arr);
			this.centerPoint.object = null;
			// 清除消息
			this.$message.destroy();
		},

		rebackAdd (prevFunc, rebackFunc) {
			rebackList.push(() => {
				const reback = () => {
					this.currentFunc = prevFunc;
					rebackFunc && rebackFunc();
				};

				reback();
				prevFunc && prevFunc();
			});
		},
		resetScenes () {
			this.clearSceneAll();
			this.clearRaycasterAll(raycasterObjs);
			this.clearResult();
		},
		/**
		 * 重置
		 */
		async reset () {
			// 清空场景

			this.resetScenes();
			// // 重置data所有属性
			// const data = deepCopy(defaultData);
			// for (const $dataKey in this.$data) {
			// 	if (hasOwnProperty(this.$data, $dataKey) && hasOwnProperty(data, $dataKey)) {
			// 		this.$data[$dataKey] = null;
			// 	}
			// }

			// 重置全局对象
			raycasterObjs = [];
			delCallback = null;
			enterCallback = null;
			escCallback = null;
			ctrlSCallback = null;
			ctrlCallback = null;
			ctrlCCallback = null;
			ctrlVCallback = null;
			ctrlZCallback = null;
			dragCallback = null;

			this.requestRender();
		},

		/**
		 * 销毁数据
		 */
		destroy () {
			this.$nextTick(() => {
				this.$message.destroy();
				this.$destroy();
			});
		},
	},
	beforeDestroy () {
		this.reset();
		removeEventHandler(window, 'keydown', onKeydown);
		removeEventHandler(window, 'resize', this.onMainCanvasResize);
		const renderer = this.getRenderer();
		if (renderer) {
			removeEventHandler(renderer.domElement, 'click', this.onMouseClick);
			removeEventHandler(renderer.domElement, 'wheel', this.onMouseWheel);
			removeEventHandler(renderer.domElement, 'mousemove', this.onMouseMove);

			const gl = renderer.domElement.getContext('webgl');
			gl && gl.getExtension('WEBGL_lose_context').loseContext();
		}
		this.clearRenderer();
		this.clearAxesRenderer();
		this.destroyScene();
		THREE.Cache.clear();
		cancelAnimationFrame(animationId); // 去除animationFrame
		this.destroy();
	},
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/base.less';
@import '~@/assets/styles/mixins/variables.less';

canvas {
	display: block;
}

#mainCanvas {
	width: 100%;
	height: 100%;
	background-image: linear-gradient(180deg, #e0e0ff 0%, #e3eaee 100%);
}

#arrowCanvas {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 150px;
	height: 150px;
	z-index: 100;
}

select {
	width: 170px;
}

.view-container {
	position: relative;

	.view-loading {
		top: 50%;
		left: 50%;
		z-index: 1;
		width: 200px;
		font-weight: bold;
		position: absolute;
		transform: translate(-50%, -50%);

		/deep/ .ant-spin-text {
			display: block;
			font-size: 24px;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
			font-weight: bold;
			color: @primary;
		}

		/deep/ .ant-spin-dot-item {
			opacity: 1;
			background-color: @primary;
		}
	}

	.scale-plate {
		position: absolute;
		bottom: 0;
		pointer-events: none;
		.disable-selection();
	}

	.scalar-bar {
		position: absolute;
		top: 88%;
		width: 100%;
		height: 36px;
		line-height: 36px;

		.box {
			display: flex;
			justify-content: center;

			.row {
				display: flex;
				flex-direction: column;
				align-items: center;

				img {
					width: 14px;
					margin: 0 3px;
					cursor: pointer;
				}

				.link {
					cursor: pointer;
					margin-left: 5px;
					height: 36px;
					color: rgba(0, 0, 0, 0.65);
					border-bottom: 1px solid #cccccc;
				}

				.text {
					display: inline-block;
					width: 80px;
				}
			}
		}
	}

	.mirrorSetting {
		position: absolute;
		bottom: 50px;
		right: 10px;
		border-radius: 6px;
	}
}

.js-view {
	position: relative;
	top: 0;
	left: 0;
	width: calc(100vw - 320px);
	min-width: 700px;
	height: calc(100vh - 50px - 2px);
	overflow: hidden;

	.js-frame {
		display: flex;
		justify-content: center;
		position: absolute;
		top: 0;
		width: 100%;
		background: transparent;
	}

	.lutImage {
		width: 100%;
		height: 20px;
		border: solid 1px #eee;
		transform: rotate(-90deg);
	}
}

/deep/ .selectBox {
	border: 1px solid #55aaff;
	background-color: rgba(75, 160, 255, 0.3);
	position: fixed;
}
</style>
