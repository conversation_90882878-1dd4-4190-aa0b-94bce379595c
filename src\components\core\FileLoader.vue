<template>
	<div>
		<a-icon v-if="showType === 'icon'" :type="loading ? 'loading' : 'upload'" :style="{ color: '#6362FF' }" :disabled="disabled || loading" @click="uploadFile" />
		<a-button v-else-if="showType === 'button' || !showType" type="primary" style="width: 100%; color:white" :disabled="disabled || loading" @click="uploadFile">
			<span v-if="loading"><a-icon type="loading" />&nbsp;加载中...</span>
			<span v-else><a-icon type="upload" />&nbsp;{{ btnText }}</span>
		</a-button>
	</div>
</template>

<script>
/* ===================================
 * 文件加载器
 * Created by cjking on 2022/02/21.
 * Copyright 2022, Inc.
 * =================================== */
let filesCallback = null;

function handleFile (e) {
	if (filesCallback) {
		filesCallback(e.target.files);
	}
	filesCallback = null;
}

const HIDDEN_FILE_ELEMENT = document.createElement('input');
HIDDEN_FILE_ELEMENT.setAttribute('type', 'file');
HIDDEN_FILE_ELEMENT.setAttribute('multiple', 'multiple');
HIDDEN_FILE_ELEMENT.addEventListener('change', handleFile);

function openFiles (extensions, onFilesCallback) {
	filesCallback = onFilesCallback;
	HIDDEN_FILE_ELEMENT.setAttribute('accept', extensions.map((t) => `.${ t }`).join(','));
	HIDDEN_FILE_ELEMENT.value = null;
	HIDDEN_FILE_ELEMENT.click();
}

export default {
	name: 'FileLoader',
	props: {
		exts: {
			type: Array,
			required: false,
			default: () => {
				return ['stp', 'step', 'STP', 'STEP'];
			},
		},
		btnText: {
			type: String,
			required: false,
			default: '上传',
		},
		loading: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: false,
		},
		showType: {
			type: [String, undefined],
			required: false,
			default: undefined,
		}
	},
	data () {
		return {};
	},
	methods: {
		/**
		 * 上传文件
		 */
		async uploadFile (event) {

			event.preventDefault();
			event.stopPropagation();

			if (this.loading || this.disabled) {
				return;
			}

			const files = await this.promptLocal();
			const file = files[0];

			this.$emit('ok', file);
		},

		/**
		 * 提示本地文件选择弹窗
		 * @param {Array<String>} exts
		 * @return {Promise<*>}
		 */
		promptLocal (exts = this.exts) {
			return new Promise((resolve) =>
				openFiles(exts, (files) => {
					resolve(Array.from(files));
				}),
			);
		},
	},
};
</script>

<style lang="less" scoped>

</style>
