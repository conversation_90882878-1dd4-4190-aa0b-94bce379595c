import * as vMath from 'vmath';

/**
 * @desc 贝塞尔曲线算法，包含了3阶贝塞尔，根据贝塞尔曲线上的点反算T值
 */
class Bezier {
	/**
	 * @desc 获取点，这里可以设置点的个数
	 * @param {number} num 点个数
	 * @param {Array} p1 点坐标
	 * @param {Array} p2 点坐标
	 * @param {Array} p3 点坐标
	 * @param {Array} p4 点坐标
	 * 如果参数是 num, p1, p2 为一阶贝塞尔
	 * 如果参数是 num, p1, c1, p2 为二阶贝塞尔
	 * 如果参数是 num, p1, c1, c2, p2 为三阶贝塞尔
	 */
	getBezierPoints (num = 100, p1, p2, p3, p4) {
		let func = function () {};
		const points = [];
		if (!p3 && !p4) {
			func = this.oneBezier;
		} else if (p3 && !p4) {
			func = this.twoBezier;
		} else if (p3 && p4) {
			func = this.threeBezier;
		}
		for (let i = 0; i < num; i++) {
			points.push(func(i / num, p1, p2, p3, p4));
		}
		if (p4) {
			points.push([...p4]);
		} else if (p3) {
			points.push([...p3]);
		}
		return points;
	}

	/**
	 * @desc 一阶贝塞尔
	 * @param {number} t 当前百分比
	 * @param {Array} p1 起点坐标
	 * @param {Array} p2 终点坐标
	 */
	oneBezier (t, p1, p2) {
		const [x1, y1] = p1;
		const [x2, y2] = p2;
		const x = x1 + (x2 - x1) * t;
		const y = y1 + (y2 - y1) * t;
		return [x, y];
	}

	/**
	 * @desc 二阶贝塞尔
	 * @param {number} t 当前百分比
	 * @param {Array} p1 起点坐标
	 * @param {Array} p2 终点坐标
	 * @param {Array} cp 控制点
	 */
	twoBezier (t, p1, cp, p2) {
		const [x1, y1] = p1;
		const [cx, cy] = cp;
		const [x2, y2] = p2;
		const x = (1 - t) * (1 - t) * x1 + 2 * t * (1 - t) * cx + t * t * x2;
		const y = (1 - t) * (1 - t) * y1 + 2 * t * (1 - t) * cy + t * t * y2;
		return [x, y];
	}

	/**
	 * @desc 三阶贝塞尔
	 * @param {number} t 当前百分比
	 * @param {Array} p1 起点坐标
	 * @param {Array} p2 终点坐标
	 * @param {Array} cp1 控制点1
	 * @param {Array} cp2 控制点2
	 */
	threeBezier (t, p1, cp1, cp2, p2) {
		const [x1, y1] = p1;
		const [x2, y2] = p2;
		const [cx1, cy1] = cp1;
		const [cx2, cy2] = cp2;
		const x =
			x1 * (1 - t) * (1 - t) * (1 - t) +
			3 * cx1 * t * (1 - t) * (1 - t) +
			3 * cx2 * t * t * (1 - t) +
			x2 * t * t * t;
		const y =
			y1 * (1 - t) * (1 - t) * (1 - t) +
			3 * cy1 * t * (1 - t) * (1 - t) +
			3 * cy2 * t * t * (1 - t) +
			y2 * t * t * t;
		return [x, y];
	}

	/** Find the ~closest point on a Bézier curve to a point you supply. (在贝塞尔曲线上找到最接近您提供的点的点)
	 * out          : A vector to modify to be the point on the curve
	 * curve        : Array of vectors representing control points for a Bézier curve (any degree)
	 * pt           : The point (vector) you want to find out to be near
	 * tmpVectors   : Array of additional temporary vectors (optional, reduces memory allocations)
	 * returns: The parameter t representing the location of `out`
	 */
	closestPoint (out, curve, pt, tmpVectors) {
		let mIndex;
		const samples = 25; // More samples increases the chance of being correct (costing additional calls to bézierPoint).
		const vec = vMath['w' in curve[0] ? 'vec4' : 'z' in curve[0] ? 'vec3' : 'vec2'];
		for (let min = Infinity, i = samples + 1; i--;) {
			const d2 = vec.squaredDistance(pt, this.bezierPoint(out, curve, i / samples, tmpVectors));
			if (d2 < min) {
				min = d2;
				mIndex = i;
			}
		}
		return this.localMinimum(Math.max((mIndex - 1) / samples, 0), Math.min((mIndex + 1) / samples, 1), t => vec.squaredDistance(pt, this.bezierPoint(out, curve, t, tmpVectors)), 1e-4);
	}

	/** Find a minimum point for a bounded function. May be a local minimum. (找到有界函数的最小值。可能是局部最小值。)
	 * minX   : the smallest input value
	 * maxX   : the largest input value
	 * ƒ      : a function that returns a value `y` given an `x`
	 * ε      : how close in `x` the bounds must be before returning
	 * returns: the `x` value that produces the smallest `y`
	 */
	localMinimum (minX, maxX, ƒ, ε) {
		if (ε === undefined) ε = 1e-10;
		let m = minX;
		let n = maxX;
		let k;
		while ((n - m) > ε) {
			k = (n + m) / 2;
			if (ƒ(k - ε) < ƒ(k + ε)) n = k;
			else m = k;
		}
		return k;
	}

	/** Calculate a point along a Bézier curve.
	 * out          : A vector to modify to be the point on the curve
	 * curve        : Array of vectors representing control points for a Bézier curve (any degree)
	 * t            : A percent value [0,1] for how far along the curve the point should be
	 * tmpVectors   : Array of additional temporary vectors (optional, reduces memory allocations)
	 * returns: out (the vector that was modified)
	 */
	bezierPoint (out, curve, t, tmpVectors) {
		// eslint-disable-next-line no-console
		if (curve.length < 2) console.error('At least two control points are required');
		const vec = vMath['w' in curve[0] ? 'vec4' : 'z' in curve[0] ? 'vec3' : 'vec2'];
		if (!tmpVectors) tmpVectors = curve.map(pt => vec.clone(pt));
		else tmpVectors.forEach((pt, i) => { vec.copy(pt, curve[i]); });
		for (let degree = curve.length - 1; degree--;) {
			for (let i = 0; i <= degree; ++i) vec.lerp(tmpVectors[i], tmpVectors[i], tmpVectors[i + 1], t);
		}
		return vec.copy(out, tmpVectors[0]);
	}
}

export default new Bezier();
