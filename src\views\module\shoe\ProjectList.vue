<template>
	<a-layout class="layout-basic">
		<GlobalHeader :menu="ModuleEnum.shoe" />
		<HeaderTip :module-object="moduleObject" :style="{ marginTop: '80px', marginBottom: '-80px'}" />
		<a-layout style="margin-top: 80px;">
			<a-layout-content class="container">
				<div class="main">
					<div class="action">
						<div class="actions">
							<div>
								<span class="label">项目中心</span>
							</div>
							<div>
								<a-button
									:disabled="!moduleObject.canCreate"
									class="btn"
									icon="plus"
									type="primary"
									size="large"
									@click="openCreateProjectModal()"
								>创建项目
								</a-button>
							</div>
						</div>
					</div>
					<a-list
						:grid="{ gutter: 16, sm: 2, md: 2 }"
						:pagination="pagination"
						:data-source="dataSource"
						class="list"
					>
						<a-list-item slot="renderItem" slot-scope="record">
							<a-card :bodyStyle="{padding:0}">
								<a-row>
									<a-col
										:span="18"
										:xl="20"
										class="card-left"
										:style="{height: '166px'}"
									>
										<div style="display:flex;align-items:center">
											<router-link :to="'/shoe/project/detail/' + record.id">
												<h1>
													<b>
														<j-ellipsis
															:value="record.projectName"
															:length="18"
														>
														</j-ellipsis>
													</b>
												</h1>
											</router-link>
											<div v-if="userInfo.id === record.createBy" @click="openEditProjectModal(record.id)">
												<svg-icon
													type="edit"
													class-name="edit"
													:vStyle="{width:'20px',height:'20px'}"
													title="编辑"
												></svg-icon>
											</div>
											<div v-if="userInfo.id === record.createBy" @click="delProject(record)">
												<svg-icon
													type="remove"
													class-name="remove"
													:vStyle="{width:'20px',height:'20px'}"
													title="删除"
												></svg-icon>
											</div>
										</div>
										<div class="secondary">
											{{ record.createByName }}&nbsp;&nbsp;&nbsp;&nbsp;{{ record.createTime }}
										</div>
										<div class="content">
											<line-ellipsis
												:text="record.content"
												nomore
												:limitLine="2"
												:lineHeight="30"
											>
											</line-ellipsis>
										</div>
									</a-col>
									<a-col
										:span="6"
										:xl="4"
										class="card-right"
										:style="{height: '166px'}"
									>
										<div>任务数</div>
										<router-link :to="'/shoe/project/detail/' + record.id">{{ record.taskCount || 0 }}</router-link>
									</a-col>
								</a-row>
							</a-card>
						</a-list-item>
					</a-list>
				</div>
				<!-- 新建项目弹窗 -->
				<create-project
					v-if="visible"
					ref="createProjectModal"
					@close="closeCreateCallback"
					@ok="createCallback"
				/>
			</a-layout-content>
		</a-layout>
	</a-layout>
</template>

<script>
/* ===================================
 * 我的项目
 * Created by cjking on 2021/06/21.
 * Copyright 2021, Inc.
 * =================================== */
import { mapGetters } from 'vuex';
import { logger } from '@/utils/logger';
import { delProject, getProjectList } from '@/api';
import { ModuleEnum, ProjectTypeEnum } from '@/constants';

import Config from '@/config/Config';
import JEllipsis from '@/components/common/JEllipsis';
import GlobalHeader from '@/components/page/GlobalHeader';
import LineEllipsis from '@/components/common/LineEllipsis';
import CreateProject from '@/views/module/shoe/CreateProject';
import HeaderTip from '@/components/common/HeaderTip';

import msgImg from '@/assets/img/msg.png';
import starImg from '@/assets/img/star.png';
import likeImg from '@/assets/img/like.png';
import notifyImg from '@/assets/img/notify.png';
import { mixinModule } from '@/mixins';

export default {
	name: 'ProjectList',
	components: {
		GlobalHeader,
		CreateProject,
		JEllipsis,
		LineEllipsis,
		HeaderTip,
	},
	mixins: [mixinModule],
	data () {
		return {
			msgImg,
			starImg,
			likeImg,
			notifyImg,
			ModuleEnum,
			ProjectTypeEnum,
			pathname: window?.location?.pathname, // 当前路由
			/* 分页参数 */
			pagination: {
				current: 1,
				pageSize: 8,
				pageSizeOptions: ['8', '16', '24'],
				showTotal: (total, range) => {
					return range[0] + '-' + range[1] + ' 共' + total + '条';
				},
				onChange: this.handlePageChange,
				onShowSizeChange: this.handlePageChange,
				showQuickJumper: false,
				showSizeChanger: true,
				total: 0,
			},
			visible: false,
			dataSource: [],
			projectInfo: {},
			currentType: ProjectTypeEnum.private, // 0:公开项目, 1:我的项目(私有项目)
			currentModule: '3',
		};
	},
	computed: {
		...mapGetters(['userInfo']),
	},
	// watch: {
	// 	'moduleObject.id': {
	// 		deep: true,
	// 		immediate: true,
	// 		handler () {
	// 			this.loadData();
	// 		}
	// 	}
	// },
	created () {
		this.loadData();
	},
	methods: {
		/**
		 * 获取用户项目列表
		 */
		async loadData (arg) {
			if (arg === 1) {
				this.pagination.current = 1;
			}
			if (!this.moduleObject.id) {
				return;
			}
			const res = await getProjectList({
				pageNo: this.pagination.current,
				pageSize: this.pagination.pageSize,
				moduleId: this.moduleObject.id,
				order: 'desc',
				column: 'createTime',
			});
			logger.log('获取用户项目列表 res: ', res);
			if (res?.success && res.result) {
				this.dataSource = res.result.records || [];
				this.pagination.total = res.result.total || 0;
			}
		},

		/**
		 * 打开新建项目弹窗
		 */
		openCreateProjectModal () {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.createProjectModal.showModal(this.moduleObject.id);
			});
		},

		/**
		 * 打开编辑项目弹窗
		 */
		openEditProjectModal (id) {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs.createProjectModal.editModal(this.moduleObject.id, id);
			});
		},

		/**
		 * 取消创建项目回调函数
		 */
		closeCreateCallback () {
			this.selectMenu = null;
			this.visible = false;
		},

		/**
		 * 创建项目后回调函数
		 */
		createCallback (projectInfo) {
			this.visible = false;
			logger.log('创建项目后回调函数 projectInfo: ', projectInfo);
			this.loadData();
		},

		/**
		 * 查看项目详情
		 */
		lookDetail (item) {
			this.$router.push('/shoe/project/detail/' + item.id);
		},

		/**
		 * 删除项目
		 */
		delProject (item) {
			logger.log('删除项目 item: ', item);
			this.$confirm({
				title: '提示',
				content: `真的要删除项目【${ item.projectName }】吗 ?`,
				okText: '确认',
				cancelText: '取消',
				onOk: async () => {
					const res = await delProject({ id: item.id });
					if (res?.success) {
						this.$message.success('删除项目成功');
						this.loadData(1);
					}
				},
			});
		},

		handlePageChange (page, pageSize) {
			this.pagination.current = page;
			this.pagination.pageSize = pageSize;
			this.loadData();
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.layout-basic {
	height: 100vh;
	background: #EDEDED;

	.ant-layout-sider {
		background: @white;
		color: #000000;
		box-shadow: 4px 0 10px 0 rgba(0, 0, 0, 0.1);
	}

	.ant-layout-content {
		color: #000000;
	}

	.container {
		background-color: @white;
		.main {
			max-width: 1600px;
			margin: 0 auto;
			padding: 10px 20px;
			.actions {
				width: 100%;
				height: 60px;
				background: @white;
				display: flex;
				justify-content: space-between;
				align-items: center;
				position: relative;

				.label {
					font-size: 18px;
					font-weight: bold;
				}

				.btn:not(:disabled) {
					background: @primary;
				}

			}
		}
	}
}

.card-left {
	padding: 10px 30px 30px 30px;
	h1 {
		font-size: 20px;
		&:hover {
			color: #1890FF;
			cursor: pointer;
		}
	}
	.secondary {
		margin-top: 0;
		color: #999999;
	}
	.content {
		width: 100%;
		margin-top: 16px;
		color: #666666;
	}
	position: relative;
}

.card-right {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: rgba(144, 144, 255, 0.04);
	font-size: 32px;
	div {
		font-size: 14px;
		color: #666666;
		line-height: 14px;
	}
	a {
		margin: 8px;
	}
}

@media screen and (max-width: 1200px) {
	.layout-basic {
		.project {
			.box {
				width: calc((100vw - 4.5vw - 200px - 60px - 8px) / 3);
			}
		}
	}
}

.list /deep/ .ant-spin-container {
	min-height: calc(100vh - 240px);
}

.svg-icon {
	color: @primary;
	margin-left: 10px;
	cursor: pointer;
}
</style>
