<template>
	<a-layout id="layout-basic">

		<GlobalHeader size="sm" :menu="ModuleEnum.shoe" justify-content="center">
			<template #leftAction>
				<div style="margin-left: 20px;margin-top: -2px;">
					<a v-href @click="goToView('/shoe/project/detail/' + projectId)">
						<img :src="backHomeImg" style="height: 30px; cursor: pointer;" alt="">
					</a>
				</div>
			</template>
			<template #logo>
				<div class="logo">
					<JEllipsis :value="taskName" :length="14" />
				</div>
			</template>
			<template #title>
				<!-- 导航 -->
				<Navigation
					:action="curAction"
					:selectDraw="curDraw"
					:selectMode="curChoose"
					:renderMode="curRenderMode"
					:disabledSelectFace="disabledSelectFace"
					:disabledSelectSolid="disabledSelectSolid"
					:disabled="solveLoading || submitLoading"
					config="视图模式|渲染模式|投影模式|平面裁切|鼠标按键"
					@resetCamera="resetCamera"
					@changeViewMode="changeViewMode"
					@changeRenderMode="changeRenderMode"
					@chooseViewHandle="chooseViewHandle"
					@updateOrientation="updateOrientation"
					@changeClipping="changeClipping"
					@mouseLeftSetting="mouseLeftSetting"
					@mouseRightSetting="mouseRightSetting"
				/>
				<HeaderTip :module-object="moduleObject" :style="{position: 'absolute', top: '50px', left: '0px'}" />

			</template>
		</GlobalHeader>

		<a-layout-sider
			ref="siderRef"
			class="project-sider"
			:width="siderWidth"
		>
			<CollapseTree
				showLine
				:style="{paddingTop: moduleObject.expired || moduleObject.zeroCount ? '40px' : '0'}"
				title="选择部位"
				tip=""
				panelKey="select"
				:treeData="[]"
				defaultExpandAll
				disabledRightMenu
			>
				<template #content>
					<a-tag>鞋楦</a-tag>
				</template>
			</CollapseTree>
			<!-- 模具 panel -->
			<CollapseTree
				showLine
				title="选择模型"
				tip=""
				panelKey="part"
				:treeData="[]"
				defaultExpandAll
				disabledRightMenu
			>
				<template #content>
					<ShoeSettings
						:dataList="partList"
						@onAllVisible="allVisibleHandler"
						@onAllColor="allColorHandler"
						@onAllOpacity="allOpacityHandler"
						@uploadCallback="uploadCallback"
					/>
				</template>
			</CollapseTree>

			<!-- 参数配置 panel -->
			<CollapseTree
				showLine
				title="参数配置"
				panelKey="model"
				defaultExpandAll
				:treeData="[]"
			>
				<template #content>
					<ShoeSettings
						v-if="settings.length"
						:dataList="settings"
						@onColor="colorHandler"
						@onVisible="visibleHandler"
						@onOpacity="opacityHandler"
						@valueChange="handleParamValueChange"
					/>
					<a-divider />
					<a-affix :offset-bottom="0">
						<div class="submit">
							<a-button type="primary" :disabled="disabled" @click="submitTask">提交计算</a-button>
						</div>
					</a-affix>
				</template>
			</CollapseTree>
		</a-layout-sider>

		<a-layout-content :style="{ background: DEFAULT_VIEW_BACKGROUND, position: 'relative' }">
			<!-- 蒙层 -->
			<div class="masking" v-if="solveLoading" />

			<!-- 视图渲染区 -->
			<ShoeView
				ref="shoeView"
				@tip="handleTip"
				@submit="submitTask"
			/>
		</a-layout-content>

		<div class="project-right" ref="rightRef">
			<!-- 结果 -->
			<ShoeResult
				:style="{paddingTop: moduleObject.expired || moduleObject.zeroCount ? '40px' : '0'}"
				:dataList="results"
				@onColor="resultColorHandler"
				@onVisible="resultVisibleHandler"
				@onOpacity="resultOpacityHandler"
			/>
		</div>

		<div class="tip-name">{{ tipName }}</div>
		<div class="network-status">网络状态：{{ online }}</div>

	</a-layout>
</template>

<script>
/* ===================================
 * 任务详情
 * Created by cjking on 2022/02/20.
 * Copyright 2022, Inc.
 * =================================== */
import { mapGetters } from 'vuex';
import { Crypt } from '@/utils/crypt';
import { logger } from '@/utils/logger';
import { mixinWebSocket, mixinModule } from '@/mixins';
import { getProjectTaskDetail, makeDispersed, submitRhino } from '@/api';
import { debounce, deepCopy, noop, checkIfLoaded, isEmpty } from '@/utils/utils';
import {
	SelectModeEnum, DEFAULT_VIEW_BACKGROUND, ProgressStatusEnum,
	RenderModeEnum, WsTypeEnum, TaskStatusEnum, ModuleEnum, DispersedWayEnum,
} from '@/constants';
import ShoeView from '@/components/core/ShoeView';
import Navigation from '@/components/core/Navigation';
import GlobalHeader from '@/components/page/GlobalHeader';
import CollapseTree from '@/components/common/CollapseTree';
import ShoeResult from '@/views/module/shoe/ShoeResult';
import FileLoader from '@/components/core/FileLoader';
import ImageDropdown from '@/components/common/ImageDropdown';
import ImageSlider from '@/components/common/ImageSlider';
import SliderInput from '@/components/common/SliderInput';
import SwitchInput from '@/components/common/SwitchInput';
import TextSlider from '@/components/common/TextSlider';
import LabelTitle from '@/components/common/LabelTitle';
import JEllipsis from '@/components/common/JEllipsis.vue';
import PartItem from '@/components/common/PartItem';
import ShoeSettings from '@/views/module/shoe/ShoeSettings';
import HeaderTip from '@/components/common/HeaderTip';

import backHomeImg from '@/assets/img/project/navigation/backHome.png';
import { defaultJsonData, defaultPartData } from '@/views/module/shoe/defaultJsonData';
import { IMessage } from '@/http/message';

const components = {
	ShoeView,
	Navigation,
	GlobalHeader,
	CollapseTree,
	ShoeResult,
	FileLoader,
	ImageDropdown,
	ImageSlider,
	SliderInput,
	TextSlider,
	SwitchInput,
	LabelTitle,
	PartItem,
	JEllipsis,
	ShoeSettings,
	HeaderTip,
};

const PanelEnum = {
	part: 'part',
	mesh: 'mesh',
	resolve: 'resolve',
};

const ActionTypeEnum = {
	submit: 'submit',
	saveData: 'saveData',
};

const check = (list, name, condition) => {
	return list.some(item => item.name === name && condition(item));
};

const getFirstItem = (list, defaultValue = undefined) => list?.length ? list[0] : defaultValue;

export default {
	name: 'TaskDetail',
	mixins: [
		mixinWebSocket, mixinModule
	],
	components: {
		...components,
	},
	data () {
		return {
			backHomeImg,
			ModuleEnum,
			PanelEnum,
			DEFAULT_VIEW_BACKGROUND,
			siderWidth: 320,
			curChoose: SelectModeEnum.surface,
			curRenderMode: RenderModeEnum.surfaceAndWireframe,
			curDraw: '',
			curAction: '',
			submitLoading: false,
			disabledSelectFace: false,
			disabledSelectSolid: false,
			partList: [...defaultPartData],
			settings: [...defaultJsonData],
			modelList: [],
			curParam: undefined,        // 当前激活的参数
			selectTag: '',				// 激活参数下的Tag
			taskInfo: {},
			fileName: '',
			calcParam: [],
			drawCurveParams: {},    // 绘制曲线参数（需提交保存，作为回显用）
			drawCircleParams: {},   // 绘制圆参数（需提交保存，作为回显用）
			online: '正常',
			downLink: '0 M/s',
			tipName: '',
			results: [],
			taskId: '',
			projectId: '',
			onceSubmit: true,
			calcParamMd5: null,
			solveLoading: false,
			noSync: true,
		};
	},
	computed: {
		...mapGetters([
			'isLogin', 'userInfo', 'collapsed',
			'solidNames', 'moduleList',
		]),

		taskName () {
			return this.taskInfo?.sysTask?.taskName || '';
		},
		disabled () {
			return this.userInfo.id !== this.taskInfo?.sysTask?.createBy;
		},
		path () {
			return `${ this.userInfo.orgCode }/${ this.projectId }/${ this.taskId }`;
		},
	},
	watch: {
		settings: {
			deep: true,
			immediate: true,
			handler (newVal) {
				const left_foot = newVal.find(i => i.key === 'left_foot').value === 1;
				this.calcParam = [
					{
						name: 's_name',
						text: getFirstItem(this.partList[0].value, ''),
					},
					{
						name: 'name_filter',
						text: getFirstItem(this.partList[left_foot ? 1 : 2].value, ''),
					},
					...newVal.filter(i => i.key && i.show !== false).map(item => {

						if (item.type === 'slider') {
							item.text = item.value;
						} else if (item.type === 'switch') {
							item.text = item.value ? 1 : 0;
						} else if (item.type === 'radio') {
							item.text = item.value;
						}

						return {
							text: item.value || 0,
							name: item.key.replace(/^(l_|r_)/, ''),
						};
					}),
				];
				this.calcParamMd5 = Crypt.md5(JSON.stringify(this.calcParam));
			},
		},
	},
	async mounted () {
		this.userId = this.userInfo.id || '';
		this.projectId = this.$route.params.id || '';
		this.taskId = this.$route.params.taskId || '';
		this.noSync = !!this.$route.query.noSync;

		await this.initProjectData();

		// 监听离散或计算数据
		this.listenDispersedOrCompute(false);

		this.initEvents();
	},
	methods: {
		/**
		 * 初始化缓存数据
		 */
		initCacheData () {
			if (this.partListCache?.length) {
				this.partList = this.partListCache;
			}
			if (this.settingsCache?.length) {
				this.settings = this.settingsCache;
			}
		},

		/**
		 * 初始化树
		 */
		initTree () {
			if (this.partList?.length) {
				this.allVisibleHandler(this.partList[0]);
				this.allColorHandler(this.partList[0]);
				this.allOpacityHandler(this.partList[0]);
			}
		},

		/**
		 * 提交任务
		 */
		submitTask: debounce(async function () {

			if (this.solveLoading || this.taskInfo?.sysTask?.status === TaskStatusEnum.waiting) {
				return;
			}

			if (this.onceSubmit) {
				this.onceSubmit = false;
				return;
			}

			if (!this.moduleObject.canCreate) {
				if (this.moduleObject.expired) {
					this.$message.warning('套餐已过期');
				} else if (this.moduleObject.zeroCount) {
					this.$message.warning('套餐剩余下载次数不足');
				}
				return;
			}

			this.solveLoading = true;
			this.openViewLoading('', true);

			if (this.disabled) {
				this.$message.warning('只有创建者可以提交');
				this.solveLoading = false;
				this.closeViewLoading();
				return;
			}

			logger.log('提交任务参数:', this.calcParam);

			if (check(this.calcParam, 'model_part', item => !item.text)) {
				this.$message.warning('请先上传文件');
				this.solveLoading = false;
				this.closeViewLoading();
				return;
			}

			// 清除加载的纹理结果数据
			this.$refs.shoeView?.clearShoeResult();
			this.results = [];

			// 增加path路径(后端用)
			const item = this.calcParam.find(item => item.name === 'path');
			if (item) {
				item.text = this.path;
			} else {
				this.calcParam.push({ name: 'path', text: this.path });
			}

			const params = {
				id: this.taskId,
				module: 'shoe',
				paramSets: this.calcParam,      // 计算值
				viewParamSets: this.settings,   // 页面显示值
			};

			const res = await submitRhino(params, { loading: false });
			logger.log('提交任务 res: ', res);
			if (res?.success && res.result) {
				if (res.result.execute === ActionTypeEnum.saveData) {
					this.closeViewLoading();
					this.solveLoading = false;
				} else {
					this.$message.success(res.message || '已提交数据');
				}
			}
		}, 300),

		/**
		 * 上传离散文件
		 */
		async uploadCallback ({ file }) {

			this.submitLoading = true;

			const part = this.partList[0];
			part.submitLoading = true;

			const formData = new FormData();
			formData.append('file', file);
			formData.append('taskId', this.taskId);
			formData.append('module', ModuleEnum.shoe);
			const res = await makeDispersed(formData).catch(() => part.submitLoading = false);
			logger.log('上传离散文件结果 res: ', res);

			if (res.code !== IMessage.OK.code) {
				this.$modal.error({ content: res.message });
				part.submitLoading = false;
				return;
			}

			// 必须用后端返回的名称，后缀被改了
			if (res?.result) this.fileName = res.result;
			this.updateFileName();
			logger.log('上传离散文件结果 fileName: ', this.fileName);

			this.openViewLoading();
		},

		/**
		 * 更新文件名
		 */
		updateFileName () {
			const part = this.partList[0];
			logger.log('updateFileName part:', part);
			part.value = this.fileName ? [this.fileName] : [];
			part.submitLoading = !this.fileName ? false : part.submitLoading;
			const model_part = this.calcParam.find(i => i.name === 'model_part');
			if (model_part) {
				model_part.text = this.fileName;
			}
		},

		/**
		 * 初始化项目数据
		 */
		async initProjectData () {
			try {
				await this.getTaskInfo();
			} catch (e) {
				logger.error('初始化项目数据 error: ', e);
			}
		},

		/**
		 * 获取任务详情
		 */
		async getTaskInfo () {
			const res = await getProjectTaskDetail({ id: this.taskId });
			logger.log('获取任务详情 res: ', res);

			if (res?.success && res?.result) {
				this.taskInfo = res?.result || {};
				const sysTask = this.taskInfo.sysTask || {};
				logger.log('获取任务详情 fileName: ', this.taskInfo.fileName);

				// 模型名称
				this.fileName = this.taskInfo.fileName || '';
				// if (!this.fileName) {
				// 	await this.resetCacheData();
				// } else {
				// 	this.initCacheData();
				// }
				this.updateFileName();

				if (this.taskInfo.viewParamSets) {
					this.settings = [...this.taskInfo.viewParamSets].map(setting => {
						return Object.assign(setting, { visible: this.partList[0].visible });
					});
				}

				// if (this.taskInfo.viewParamSets?.length > 0) {
				// 	const jsonData = deepCopy(this.settings);
				// 	let viewParamSets = this.taskInfo.viewParamSets;
				// 	jsonData.forEach(i => {
				// 		viewParamSets.forEach(j => {
				// 			if (i.name && i.name === j.name) {
				// 				i.value = j.value;
				// 				i.text = j.text;
				// 				if (i.config && j.config) {
				// 					i.config.show = j.config.show;
				// 				}
				// 				if (i.name === 'partition') {
				// 					i.config.mold = viewParamSets.find(k => k.name === 'mold').value;
				// 					this.$nextTick(() => {
				// 						this.$refs.imageSlider[0].computDisable();
				// 					});
				// 				}
				// 			}
				// 		});
				// 	});
				// 	this.settings = jsonData;
				// 	viewParamSets = null;
				// }

				// 初始化树
				this.initTree();

				// 解析离散数据
				if (sysTask.type) {
					const dispersedResult = sysTask.dispersedResult || '';
					logger.log('纹理离散数据（Rhino离散）dispersedResult: ', dispersedResult);
					if (dispersedResult) {
						let dispersedPath = sysTask.dispersedResult?.filePath || '';
						logger.log('纹理离散数据（C++离散）dispersedPath: ', dispersedPath);
						if (dispersedPath) {
							if (!dispersedPath.startsWith(this.userInfo.orgCode)) {
								dispersedPath = this.userInfo.orgCode + '/' + dispersedPath;
							}
							const dispersedType = sysTask.dispersedResult.dispersedType;
							logger.log('纹理离散方式: ', dispersedType);
							if (dispersedType === DispersedWayEnum.original) { // 自身文件(不离散), 3dm、stl
								await this.$refs.shoeView.loadData3DMOrSTL(dispersedPath);
							}
							if (dispersedType === DispersedWayEnum.dispersed) { // c++离散, step、iges
								await this.$refs.shoeView.loadData(dispersedPath);
							}
							if (dispersedType === DispersedWayEnum.rhino) { // 犀牛电池离散，ai、pdf
								// await this.$refs.shoeView.loadDataAIOrPDF(dispersedPath);
							}
						}
					}
				}

				// 解析求解数据
				if (sysTask.type && sysTask.resultJson) {
					// const onewayChangeData = this.getSetting('oneway_change');
					// const children = onewayChangeData['children0'];
					// const canLoadPoint = !!children.find(child => child.key === 'guided_point').value;
					// logger.log('引导点开关是否开启：', canLoadPoint);
					// const results = await this.$refs.shoeView.loadShoeComputeData(sysTask, canLoadPoint);
					const results = await this.$refs.shoeView.loadShoeComputeData(sysTask);
					this.results = results || [];
				}

				const progressLoading = sysTask.status === ProgressStatusEnum.LOADING;

				// 离散中
				this.submitLoading = (sysTask.type === WsTypeEnum.dispersed && progressLoading) || (sysTask.type === WsTypeEnum.resolve && progressLoading);
				if (this.submitLoading) {
					this.openViewLoading();
				}

				// 计算中
				this.solveLoading = sysTask.type === WsTypeEnum.resolve && progressLoading;
				if (this.solveLoading) {
					this.openViewLoading();
				}
			}
		},

		/**
		 * 监听离散或计算数据
		 */
		listenDispersedOrCompute (loading = true) {
			const params = {};
			this.listenWsStatus(params, {
				loading: loading,
				callback: async (res) => {
					logger.log('监听离散或计算数据: ', res);

					this.solveLoading = false;
					this.submitLoading = false;
					this.partList[0].submitLoading = false;

					// 轮询检测，等待shoeView页面加载完毕
					if (!loading) {
						await checkIfLoaded(!!this.$refs.shoeView);
					}

					if (res.type === WsTypeEnum.dispersed) {
						logger.log('监听纹理离散数据（Rhino离散）dispersedResultPath: ', res.dispersedResultPath);
						if (res.dispersedResult) {
							await this.$refs.shoeView.loadData(res.dispersedResult);
						}
					}

					// 纹理计算数据
					if (res.type === WsTypeEnum.resolve) {
						// const canLoadPoint = this.checkGuidedPoint();
						// logger.log('引导点开关是否开启：', canLoadPoint);
						// const results = await this.$refs.shoeView.loadShoeComputeData(res, canLoadPoint);
						const results = await this.$refs.shoeView.loadShoeComputeData(res);
						this.results = results || [];
					}
				},
				errCallback: (res) => {
					this.submitLoading = false;
					this.solveLoading = false;
					const action = res.type === WsTypeEnum.resolve ? '求解' : '离散';
					this.$modal.error({ content: res.errors || `${ action }失败，请稍后重试!` });
				},
			});
		},

		/**
		 * 监听websocket状态
		 */
		async listenWsStatus (params, options) {
			const loading = options.loading ?? true;
			const loadingMsg = options.loadingMsg ?? '';
			const callback = options.callback || noop;
			const errCallback = options.errCallback || noop;
			const progressCallback = options.progressCallback || noop;

			// const key = Date.now();
			const key = this.taskId;
			if (loading) this.openViewLoading(loadingMsg);
			const socket = await this.initWebsocket(key);
			// socket.send(params);
			this.$root.$on(`${ key }Callback`, res => {
				// status:状态 0:未开始，1:创建中，8:完成，9:失败
				// type:类型 dispersed:离散数据，resolve:求解器数据
				if (res.status === ProgressStatusEnum.LOADING) {
					progressCallback(res);
				}
				if (res.status === ProgressStatusEnum.SUCCESS) {
					this.closeViewLoading();
					// socket.onClose(res.type);
					callback(res);
				}
				if (res.status === ProgressStatusEnum.FAIL) {
					this.closeViewLoading();
					errCallback(res);
				}
			});
		},

		/**
		 * 切换折叠
		 */
		toggleCollapsed () {
			const collapsed = !this.collapsed;
			this.siderWidth = collapsed ? 0 : 320;
			this.$nextTick(() => {
				this.$store.dispatch('setCollapsed', collapsed);
			});
		},

		/**
		 * 开启加载中
		 */
		openViewLoading (loadingMsg = '', resetCount) {
			this.submitLoading = true;
			this.$refs.shoeView.openLoading(loadingMsg, resetCount);
		},

		/**
		 * 关闭加载中
		 */
		closeViewLoading () {
			this.submitLoading = false;
			this.$refs.shoeView?.closeLoading();
		},

		/**
		 * 重置相机
		 */
		resetCamera () {
			this.$refs.shoeView?.resetCamera();
		},

		/**
		 * 更改投影模式
		 */
		changeViewMode (mode) {
			if (this.checkAction()) return;
			this.$refs.shoeView?.changeViewMode(mode);
		},

		/**
		 * 更改渲染模式
		 * @param mode
		 */
		changeRenderMode (mode) {
			if (this.checkAction()) return;
			this.curRenderMode = mode;
			this.$refs.shoeView?.changeRenderMode(mode);
		},

		/**
		 * 更新方向
		 */
		updateOrientation (direction) {
			if (this.checkAction()) return;
			this.$refs.shoeView?.updateOrientation(direction);
		},

		/**
		 * 选择视图
		 */
		chooseViewHandle (choose) {
			if (this.checkAction()) return;
			this.$refs.shoeView?.chooseViewHandle(choose);
		},

		/**
		 * 检查动作
		 */
		checkAction (message = '正在执行拖动等动作，请先释放对象') {
			if (this.$refs.shoeView?.selectTarget) {
				this.$message.warning(message);
				return true;
			}
			return false;
		},

		/**
		 * 更新颜色
		 */
		updateAllColor (value) {
			this.$refs.shoeView?.updateAllColor(Number(value.replace(/^#/, '0x')));
		},

		/**
		 * 更新透明度
		 */
		updateAllOpacity (value) {
			this.$refs.shoeView?.updateAllOpacity(value / 100);
		},

		/**
		 * 更新颜色
		 */
		updateAllVisible (value) {
			this.$refs.shoeView?.updateAllVisible(!!value);
		},

		/**
		 * 更新切面
		 * @params 对象{切面，反转xyz方向，反转正反方向}
		 */
		changeClipping ({ clip, clippingReverse = {} }) {
			this.$refs.shoeView?.updateClipping(clip, clippingReverse);
		},

		/**
		 * 鼠标左键设置
		 * @param value
		 */
		mouseLeftSetting (value) {
			this.$refs.shoeView.mouseLeftSetting(value);
		},

		/**
		 * 鼠标右键设置
		 * @param value
		 */
		mouseRightSetting (value) {
			this.$refs.shoeView.mouseRightSetting(value);
		},

		/**
		 * 左侧参数值更新
		 */
		handleParamValueChange ({ value, key, invalid }) {
			if (this.checkAction()) return;

			logger.log('handleParamValueChange', 'value:', value, 'key:', key);
			const t1 = performance.now();
			const param = this.getSetting(key);
			param.value = value;

			if (key === 'left_foot') { // 方向选择 0:右, 1:左
				// 显示隐藏切换
				this.settings.filter(i => i.key?.match(/^l_/)).forEach(i => {
					i.config.show = value === 1;
				});
				this.settings.filter(i => i.key?.match(/^r_/)).forEach(i => {
					i.config.show = value === 0;
				});
				this.settings.find(i => i.label?.match(/设置(左|右)脚参数/)).label = `设置${ value === 1 ? '左' : '右' }脚参数`;
			}
			const t2 = performance.now();
			logger.log(`  ${ Math.round(t2 - t1) } ms: handleParamValueChange (${ key } ${ value })`);

			this.paramValueChangeSubmit();
		},

		paramValueChangeSubmit: debounce(function () {
			// 实时提交计算
			if (!this.noSync) {
				this.submitTask();
			}
		}, 1000),

		/**
		 * 模具删除
		 */
		handlePartRemove ({ index }) {
			if (this.checkAction()) return;
			this.reset(); // 重置参数
			this.partList.splice(index, 1);
			this.$refs.shoeView.reset();
			// this.$store.dispatch('savePartList', this.partList);
		},

		/**
		 * 选中/取消选中 图形
		 */
		singleSelectedHandler (name) {
			// if (this.checkAction()) return;
			// this.$refs.shoeView.updateVisibleByName(name);
		},

		getSetting (name) {
			return this.settings.find(i => i.key === name);
		},

		handleTip (obj) {
			if (obj.nickname) {
				this.tipName = `名称：${ obj.nickname }`;
			} else if (obj.groupName) {
				this.tipName = `组名称：${ obj.groupName }`;
			} else {
				this.tipName = '';
			}
		},

		updateOnline () {
			this.online = navigator.onLine ? '正常' : '断开';
		},

		/**
		 * 所有显隐处理
		 */
		allVisibleHandler ({ key, visible }, isInit = false) {
			logger.log('所有显隐处理 key, visible: ', key, visible);

			if (this.checkAction()) return;

			const children = this.getSceneAllSolidAndAllWireframe();
			const shoeView = this.$refs.shoeView;
			shoeView.originVisible = visible;
			[...children].forEach(child => {
				shoeView.updateObjAttr(child, { visible });
			});
			shoeView.requestRender();

			// 同步参数菜单所有显隐功能
			this.updateSettings(null, 'visible', visible);

			// if (!isInit) this.savePartListToCache();
		},

		/**
		 * 所有颜色处理
		 */
		allColorHandler ({ key, color }, isInit = false) {
			logger.log('所有颜色处理 key, color: ', key, color);

			if (this.checkAction()) return;

			const children = this.getSceneAllSolidAndAllWireframe();
			const shoeView = this.$refs.shoeView;
			shoeView.originColor = color;
			children.forEach(child => {
				if (!shoeView.isLine(child)) {
					shoeView.updateObjAttr(child, { color });
				}
			});
			shoeView.requestRender();

			// if (!isInit) this.savePartListToCache();
		},

		/**
		 * 所有透明度处理
		 */
		allOpacityHandler ({ key, opacity }, isInit = false) {
			logger.log('所有透明度处理 key, opacity: ', key, opacity);

			if (this.checkAction()) return;

			const children = this.getSceneAllSolidAndAllWireframe();
			const shoeView = this.$refs.shoeView;
			shoeView.originOpacity = opacity;
			children.forEach(child => {
				shoeView.updateObjAttr(child, { opacity });
			});

			// if (!isInit) this.savePartListToCache();
		},

		/**
		 * 显示/隐藏处理
		 */
		visibleHandler ({ key, visible }) {
			logger.log('显示/隐藏处理 key, visible: ', key, visible);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);

			if (setting && setting.value?.length) {
				this.updateAttrByNameList([...setting.value], true, setting.color, visible);
			}
		},

		/**
		 * 结果显示/隐藏处理
		 */
		resultVisibleHandler ({ type, visible }) {
			logger.log('显示/隐藏处理 type, visible: ', type, visible);
			const children = this.getResultWlLines(type);
			const shoeView = this.$refs.shoeView;
			children.forEach(child => {
				shoeView.updateObjAttr(child, { visible });
			});
			shoeView.requestRender();
		},

		/**
		 * 结果颜色处理
		 */
		resultColorHandler ({ type, color }) {
			logger.log('颜色处理 type, color: ', type, color);
			const children = this.getResultWlLines(type);
			const shoeView = this.$refs.shoeView;
			children.forEach(child => {
				shoeView.updateObjAttr(child, { color });
			});
			shoeView.requestRender();
		},

		/**
		 * 结果透明度处理
		 */
		resultOpacityHandler ({ type, opacity }) {
			logger.log('透明度处理 type, opacity: ', type, opacity);
			const children = this.getResultWlLines(type);
			const shoeView = this.$refs.shoeView;
			children.forEach(child => {
				shoeView.updateObjAttr(child, { opacity });
			});
			shoeView.requestRender();
		},

		/**
		 * 颜色处理
		 */
		colorHandler ({ key, color }) {
			logger.log('颜色处理 key, color: ', key, color);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);
			if (setting.value?.length && setting.activate) {
				this.$refs.shoeView.updateAttrByNameList([...setting.value], {
					selected: true,
					color,
				});
			}
		},

		/**
		 * 透明度处理
		 */
		opacityHandler ({ key, opacity }) {
			logger.log('透明度处理 key, opacity: ', key, opacity);

			if (this.checkAction()) return;

			const setting = this.getSetting(key);
			if (setting.value?.length && setting.activate) {
				this.$refs.shoeView.updateAttrByNameList([...setting.value], {
					selected: true,
					color: setting.color,
					opacity,
				});
			}
		},

		/**
		 * 获取场景全部实体和全部线框
		 * @returns {Object3D[]|*[]}
		 */
		getSceneAllSolidAndAllWireframe () {
			return this.$refs.shoeView.getSceneAllSolidAndAllWireframe();
		},

		/**
		 * 将参数设置保存到缓存
		 */
		saveSettingsToCache: debounce(function () {
			this.$store.dispatch('saveSettings', this.settings);
		}, 10),

		/**
		 * 将部件列表保存到缓存
		 */
		savePartListToCache: debounce(function () {
			this.$store.dispatch('savePartList', this.partList);
		}, 10),

		/**
		 * 重置缓存数据
		 */
		async resetCacheData () {
			this.partList = [...defaultPartData];
			this.settings = [...defaultJsonData];
			await Promise.all([
				this.$store.dispatch('setLastTaskId', this.taskInfo.sysTask.id),
				this.$store.dispatch('savePartList', this.partList),
				this.$store.dispatch('saveSettings', this.settings),
				this.$store.dispatch('saveCollapseResult', []),
			]);
		},

		/**
		 * 更新参数设置
		 */
		updateSettings (key, attrName, attrValue) {
			const settings = [...this.settings];
			this.settings = [];
			settings.forEach(setting => {
				if (setting.key === key) {
					setting[attrName] = attrValue;
					if (attrName === 'checked') {
						setting['value'] = attrValue ? 1 : 0;
					}
				}
				if (isEmpty(key)) { // key为空时，更新所有
					setting[attrName] = attrValue;
				}
			});
			this.settings = settings;
		},

		/**
		 * 获取结果纹理线
		 * @returns {Object3D[]|*[]}
		 */
		getResultWlLines (type) {
			return this.$refs.shoeView.getSceneChildrenByCondition('resultWlLineGroup', child => child.shoeResultType === type);
		},

		reset () {
			this.$nextTick(() => {
				this.settings = deepCopy(defaultJsonData);
			});
		},

		goToView (path) {
			this.destroy();
			this.wsInstance?.onclose();
			this.$refs.shoeView.reset(true);
			this.$router.push(path);
		},

		initEvents () {
			window.addEventListener('online', this.updateOnline);
			window.addEventListener('offline', this.updateOnline);
			// navigator.connection.addEventListener('change', this.measureBW);
			// const beforeunloadHandler = (e) => {
			// 	// chrome不支持自定义显示文字，固定为
			// 	// "要重新加载该网站吗？
			// 	// 系统可能不会保存您所做的修改"
			// 	// 而firefox和safari就会正常使用return value里面的文字
			// 	const dialogText = 'Dialog text here';
			// 	e.returnValue = dialogText;
			// 	return dialogText;
			// };
			// addEventHandler(window, 'beforeunload', (e) => beforeunloadHandler(e));
		},

		/**
		 * 销毁数据
		 */
		destroy () {
			this.$nextTick(() => {
				this.$destroy();
			});
		},
	},
	beforeDestroy () {
		this.destroy();
		this.wsInstance?.onclose();
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/grid.less";
@import "~@/assets/styles/mixins/variables.less";

#layout-basic {
	position: relative;
	background: @primary-background;
	height: 100vh;

	.logo {
		font-size: 16px;
		font-weight: 500;
		color: #111111;
		margin-left: 20px;
	}

	.header-title {
		display: flex;
		width: 100%;

		span {
			font-size: 16px;
			font-weight: bold;
			text-align: center;
			width: 260px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			margin: 0 auto;
		}
	}

	.ant-layout-sider {
		margin-top: 52px;
	}

	.ant-layout-content {
		margin-top: 52px;
	}

	.project-sider {
		overflow: auto;
		height: 100%;
		max-height: calc(100vh - 50px - 2px);
		overflow-x: hidden;
		background: transparent;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			left: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.project-right {
		overflow: auto;
		height: auto;
		overflow-x: hidden;
		background: linear-gradient(#C7D8E1, #E3EAEE);
		position: absolute;
		right: 0;
		top: 52px;

		.collapse_panel {
			margin-bottom: 1px;

			&.relative {
				position: relative;
			}
		}

		.masking {
			position: fixed;
			top: 52px;
			right: 0;
			width: 310px;
			height: calc(100vh - 50px - 2px);
			background: rgba(0, 0, 0, 0.05);
			z-index: 999;
			cursor: not-allowed;
		}
	}

	.ant-empty-normal {
		margin: 0 0;
	}

	.list {
		img {
			width: 20px;

			&.w18 {
				width: 18px;
				height: 18px;
			}
		}

		.item-meta {
			display: flex;
			align-items: center;
			cursor: pointer;

			&:hover {
				color: @primary;
			}

			.active {
				color: @primary;
			}
		}
	}

	.progress-item {
		.progress-bar {
			max-width: 160px;
			margin-left: 10px;
			min-width: 160px;
		}

		.ant-list-item-action {
			margin-left: 10px;
		}

		.ant-list-item-meta-title {
			display: flex;
			align-items: center;
		}

		.progress-title {
			display: flex;
			align-items: center;
			height: 22px;

			img {
				height: inherit;
				margin-right: 5px;
			}
		}

		.ant-progress {
			line-height: 0;
		}
	}

	.masking {
		position: fixed;
		top: 52px;
		left: 0;
		width: 315px;
		height: calc(100vh - 50px - 2px);
		background: rgba(0, 0, 0, 0.05);
		z-index: 999;
		cursor: not-allowed;
	}

	.tip-name {
		position: fixed;
		right: 200px;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.network-status {
		position: fixed;
		right: 0;
		bottom: 0;
		width: 140px;
		height: 40px;
		pointer-events: none;
		.disable-selection();
	}

	.upload-btn {
		width: 280px;

		/deep/ .ant-btn {
			border-radius: 3px;
			background-color: #6362FF;
		}
	}

	.submit {
		height: 52px;
		width: 296px;
		padding: 10px 4px;
		background-color: white;

		.ant-btn {
			width: 280px;
			height: 32px;
		}
	}
}
</style>
