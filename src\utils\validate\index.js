/* ===================================
 * 校验规则
 * Created by cjking on 2020/05/02.
 * Copyright 2020, Inc.
 * =================================== */
import { isCarId } from './modules/idCard';
import { debounce, deleteNullAttr, isEmpty, isEmptyObject, isFullObject, noop } from '@/utils/utils';
import { logger } from '@/utils/logger';
// import { storage } from '@/utils/storage';
// import { duplicateCheck } from '@/api';
// import { USER_INFO } from '@/constants';
import { IMessage } from '@/http/message';

export const EmailReg = /^([a-zA-Z0-9]+[_|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
export const AmountReg = /(^[1-9]([0-9]{1,9})?(\.[0-9]{1,2})?$)|(^(0)$)|(^[0-9]\.[0-9]([0-9])?$)/;
export const AllNumberReg = /^\d+$/;
export const FloatNumberReg = /^[0-9.]+$/;
export const PhoneReg = /^[1][3-9][0-9]{9}$/;
export const TelReg = /[0-9\\-]{11,12}/;
export const IdCardReg = isCarId;
export const BankCardReg = /^([1-9])(\d{15}|\d{18})$/;
export const DepartmentNameReg = /^[A-Za-z0-9_.\-()（）\u4e00-\u9fa5]+$/; // 单位名
export const UserNameReg = /^[A-Za-z0-9_.\-\u4e00-\u9fa5]+$/;            // 只支持中文、数字、英文、下划线、中横线和小数点，不可包含其他特殊字符
export const RoleCodeReg = /^\w+$/;                                      // 只能由英文、数字、下划线组成
// eslint-disable-next-line camelcase
export const HasZH_CNReg = /[\u4e00-\u9fa5]/gm;                          // 匹配中文字符
// eslint-disable-next-line camelcase
export const OnlyCN_EN_NumReg = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/i;          // 只能输入汉字、英文字母和数字
export const SCIENTIFIC_COUNTING_METHOD = /^[+-]?[\d]*([.][\d]+)?([Ee][+-]?[\d]+)?$/; // 科学计数法 可以识别正负数，以及科学计数法的表示
export const ExponentReg = /^[0-9.]*(x?10[⁺|⁻]?([⁰¹²³⁴⁵⁶⁷⁸⁹])+)/;        // 科学计数法形式的字符串
export const PasswordReg = /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)[0-9a-zA-Z\\[\]【】!@#$%^&?*>_\-/、。~()￥（）—+《》？："「」|,.:'`]{8,16}$/;  // 密码长度为8~16位，数字、字母、特殊字符至少包含两种

const TypeMap = {
	phone: PhoneReg,
	tel: TelReg,
	idCard: IdCardReg,
	bankCard: BankCardReg,
	email: EmailReg,
	allNumber: AllNumberReg,
	amount: AmountReg,
	departmentName: DepartmentNameReg,
	userName: UserNameReg,
};

/**
 * form表单验证规则
 */
export const formValidate = {
	/**
	 * 非空校验
	 * @param {string} message                  验证错误提示
	 * @param {object} option                   可选参数
	 * @param {string=} option.trigger          触发类型 有 change、blur、['change', 'blur'], 默认 'change'
	 */
	noEmpty: (message, option = {}) => [
		{
			required: true,
			trigger: option.trigger || 'change',
			asyncValidator: (rule, value) => {
				return new Promise((resolve, reject) => {
					if (isEmpty(value) || (Array.isArray(value) && !value.length) || (typeof value === 'object' && isEmptyObject(deleteNullAttr(value)))) {
						return reject(message || '请输入');
					}
					return resolve();
				});
			},
		},
	],
	name: (message, trigger = 'change') => [
		{
			required: true,
			message: message || '请输入名称',
			trigger,
		},
		// 名称支持特殊字符，如新疆名称等
		{
			type: 'string',
			validator: /^[a-zA-Z\u4e00-\u9fa5]*$/,
			message: '只能输入中英文字符',
			trigger,
		},
	],
	maxLength: (max, trigger = 'change') => [
		{
			type: 'string',
			max,
			message: `长度不能超过${ max }位`,
			trigger,
		},
	],
	minLength: (min, trigger = 'change') => [
		{
			type: 'string',
			min,
			message: `长度不能小于${ min }位`,
			trigger,
		},
	],

	/**
	 * 手机号验证
	 * @param {string} message                  验证错误提示
	 * @param {object} option                   可选参数
	 * @param {boolean=} option.checkUniqueness 是否验证唯一性 默认:false
	 * @param {string=} option.trigger          触发类型 有 change、blur、['change', 'blur'], 默认 'change'
	 */
	phone: (message, option = {}) => [
		{
			required: true,
			message: message || '请输入手机号',
		},
		{
			type: 'string',
			min: 11,
			message: `手机号长度错误，请重新输入`,
			trigger: option.trigger || 'change',
		},
		{
			type: 'string',
			max: 11,
			message: `手机号长度错误，请重新输入`,
			trigger: option.trigger || 'change',
		},
		{
			type: 'string',
			validator: (rule, value) => /^\d+$/.test(value),
			message: '只能输入数字',
			trigger: option.trigger || 'change',
		},
		{
			type: 'string',
			validator: (rule, value) => /^[1][3-9][0-9]{9}$/.test(value),
			message: '手机号错误，请重新输入',
			trigger: option.trigger || 'change',
		},
		{
			trigger: option.trigger || 'change',
			asyncValidator: debounce(function (rule, phone) { // debounce时，外层不能用箭头函数，不然this指向错误！
				option.checkUniqueness = option.checkUniqueness ?? false;
				return new Promise((resolve, reject) => {
					if (!baseValidate.phone(phone)) {
						return reject('请输入正确格式的手机号码!');
					}
					if (!option.checkUniqueness) {
						return resolve();
					}
					// const userInfo = storage.get(USER_INFO);
					// const params = {
					// 	tableName: 'sys_user',
					// 	fieldName: 'phone',
					// 	fieldVal: phone,
					// 	dataId: userInfo?.id,
					// };
					// duplicateCheck(params, { interceptRes: false }).then(res => {
					// 	if (res?.code === IMessage.OK.code) {
					// 		return resolve();
					// 	}
					// 	return reject('手机号已存在!');
					// });
				});
			}, 300),
		},
	],
	tel: (msg, trigger = 'change') => [
		{
			required: true,
			message: msg || '请输入电话号码',
		},
		{
			type: 'string',
			validator: (rule, value) => /[0-9\\-]{11,12}/.test(value),
			message: '请输入正确的电话号码',
			trigger,
		},
	],
	idCard: (msg, trigger = 'change') => [
		{
			required: true,
			message: msg || '请输入身份证号码',
		},
		{
			type: 'string',
			validator: (rule, value) => isCarId(value),
			message: '请输入正确的身份证号码',
			trigger,
		},
	],
	bankCard: (msg, trigger = 'change') => [
		{
			required: true,
			message: msg || '请输入银行卡号',
		},
		{
			type: 'string',
			validator: (rule, value) => /^([1-9])(\d{15}|\d{18})$/.test(value),
			message: '请输入正确的银行卡号',
			trigger,
		},
	],
	email: (msg, trigger = 'change') => [
		{
			required: true,
			message: msg || '请输入邮箱账号',
		},
		{
			type: 'string',
			validator: (rule, value) => EmailReg.test(value),
			message: '请输入正确的邮箱账号',
			trigger,
		},
	],
	amount: (msg, trigger = 'change') => [
		{
			required: true,
			message: msg || '请输入金额',
		},
		{
			type: 'string',
			validator: (rule, value) => AmountReg.test(value),
			message: '请输入正确的金额',
			trigger,
		},
	],
	/**
	 * 次数校验
	 * @param {object} option
	 * @param {boolean=} option.required        是否验证必填
	 * @param {string=} option.message          验证错误提示
	 * @param {number=} option.min              最小值
	 * @param {number=} option.max              最大值
	 * @param {string=} option.trigger          触发类型 有 change、blur、['change', 'blur'], 默认 'change'
	 */
	count: (option = {}) => [
		{
			trigger: option.trigger || 'change',
			required: option.required ?? true,
			asyncValidator: (rule, value) => {
				return new Promise((resolve, reject) => {
					if (!value) {
						return reject(option.message || '请输入次数');
					}
					if (!AllNumberReg.test(value)) {
						return reject(option.message || '请输入正确的次数');
					}
					if (!isEmpty(option.min) && value < option.min) {
						return reject(`不能小于最小值[${ option.min }]!`);
					}
					if (!isEmpty(option.max) && value > option.max) {
						return reject(`不能大于最大值[${ option.max }]!`);
					}
					return resolve();
				});
			},
		},
	],

	/**
	 * 输入时验证（非必填验证）
	 * @param {string} msg
	 * @param {string} type 验证类型 有 phone、tel、idCard、bankCard、email、amount、allNumber、departmentName、userName
	 * @param {string} trigger 触发类型 有 change、blur、['change', 'blur']
	 */
	inputValidation: (msg, type, trigger = 'change') => [{
		trigger,
		required: false,
		asyncValidator: (rule, value) => {
			return new Promise((resolve, reject) => {
				if (!value) {
					return resolve();
				}
				if (TypeMap[type]?.test(value)) {
					return resolve();
				}
				return reject(msg || '请确认输入的值是否正确');
			});
		},
	}],

	/**
	 * 行验证 (有输入时验证)
	 * 1、行内未填入任何数据视为通过
	 * 2、isFull:true, 行内所有数据都填入视为通过
	 * 3、isFull:false, 行内数据只要不为空即视为通过
	 * @param {object} option
	 * @param {string} option.message           验证错误提示
	 * @param {function} option.asyncArgs       异步参数
	 * @param {function=} option.condition      异步验证条件
	 * @param {string=} option.message          提示信息
	 * @param {boolean=} option.isFull          是否完整对象验证（是否属性都有值）, 默认 false
	 * @param {string=} option.trigger          触发类型 有 change、blur、['change', 'blur'], 默认 'change'
	 */
	rowValidation: (option = {}) => [{
		required: false,
		trigger: option.trigger || 'change',
		asyncValidator: () => {
			option.isFull = option.isFull ?? false;
			return new Promise((resolve, reject) => {
				if (typeof option.asyncArgs !== 'function') {
					throw Error('asyncArgs必须是一个Function');
				}
				if (option.condition && typeof option.condition !== 'function') {
					throw Error('condition必须是一个Function');
				}
				const params = option.asyncArgs();
				const isEmptyObj = isEmptyObject(deleteNullAttr(params));
				const pass = option.isFull ? isFullObject(params) : true;
				const condition = option.condition ? option.condition() : true;
				if (option.condition && condition) return resolve();
				if (isEmptyObj || pass) {
					return resolve();
				}
				return reject(option.message ?? '');
			});
		},
	}],

	/**
	 * 上传
	 * @param {string} msg
	 * @param {string} trigger 触发类型 有 change、blur、['change', 'blur']
	 * @param {function=} callback
	 */
	upload: (msg, trigger = 'change', callback) => [{
		trigger,
		required: true,
		asyncValidator: (rule, value) => {
			typeof callback === 'function' && callback(value);
			return new Promise((resolve, reject) => {
				if (!value || !value.length) {
					return reject(msg || '请上传');
				}
				return resolve();
			});
		},
	}],

	/**
	 * 长度校验
	 * @param {object} option
	 * @param {boolean=} option.required        是否验证必填
	 * @param {string=} option.fieldDesc        字段名称描述
	 * @param {number=} option.minLength        是否验证最小长度
	 * @param {number=} option.maxLength        是否验证最大长度
	 * @param {string=} option.trigger          触发类型 有 change、blur、['change', 'blur'], 默认 'change'
	 */
	checkLength: (option = {}) => [{
		trigger: option.trigger || 'change',
		required: option.required,
		asyncValidator: debounce(function (rule, value) { // debounce时，外层不能用箭头函数，不然this指向错误！
			logger.log('rule, value: ', rule, value);
			return new Promise((resolve, reject) => {
				if (option.required && !baseValidate.required(value)) {
					return reject(`请输入${ option.fieldDesc || '' }!`);
				}
				if (!isEmpty(option.minLength) && value && !baseValidate.minlength(value, option.minLength)) {
					return reject(`不能少于${ option.minLength }个字符!`);
				}
				if (!isEmpty(option.maxLength) && value && !baseValidate.maxlength(value, option.maxLength)) {
					return reject(`不能大于${ option.maxLength }个字符!`);
				}
				return resolve();
			});
		}, 300),
	}],

	/**
	 * 重复检查(唯一性验证)
	 * @param {object} option
	 * @param {string} option.fieldName         字段名称
	 * @param {string} option.fieldDesc         字段名称描述
	 * @param {string=} option.fieldDescPrefix  字段名称前缀
	 * @param {string=} option.tableName        表名
	 * @param {boolean=} option.isPhone         是否验证手机号
	 * @param {boolean=} option.isUsername      是否验证用户名(账号)
	 * @param {boolean=} option.isEmail         是否验证邮箱
	 * @param {boolean=} option.isWorkNo        是否验证学号
	 * @param {function=} option.isEdit         编辑模式下是否验证唯一性(通过function调用获取，避免作用域问题)
	 * @param {function=} option.initValue      初始值(通过function调用获取，避免作用域问题)
	 * @param {boolean=} option.noCN            不含中文字符
	 * @param {boolean=} option.noSpecial       不含特殊字符
	 * @param {boolean=} option.required        是否验证必填
	 * @param {string=} option.workNoDesc       学号错误提示信息
	 * @param {number=} option.minLength        是否验证最小长度
	 * @param {number=} option.maxLength        是否验证最大长度
	 * @param {number=} option.reg              自定义验证规则
	 * @param {string=} option.trigger          触发类型 有 change、blur、['change', 'blur'], 默认 'blur'
	 * @param {function=} option.callback       验证成功后回调函数
	 * @param {object=} additionalParams        附加参数
	 */
	duplicateCheck: (option = {}, additionalParams = {}) => [{
		trigger: option.trigger || 'change',
		required: option.required,
		asyncValidator: debounce(function (rule, value) { // debounce时，外层不能用箭头函数，不然this指向错误！
			return new Promise((resolve, reject) => {
				const prefix = option.fieldDescPrefix ? option.fieldDescPrefix : '请输入';
				option.callback = option.callback ? option.callback : noop;
				if (option.required && value?.trim().length === 0) { // 全为空格校验
					option.callback(false);
					return reject(`${ prefix }${ option.fieldDesc }!`);
				}
				if (option.required && !baseValidate.required(value)) {
					option.callback(false);
					return reject(`${ prefix }${ option.fieldDesc }!`);
				}
				if (option.isPhone && value && !baseValidate.phone(value)) {
					option.callback(false);
					return reject(`${ prefix }正确的${ option.fieldDesc }!`);
				}
				if (option.isUsername && value && !baseValidate.name(value)) {
					option.callback(false);
					return reject(`${ prefix }正确的${ option.fieldDesc }!`);
				}
				if (option.isEmail && value && !baseValidate.checkEmail(value)) {
					option.callback(false);
					return reject(`${ prefix }正确的${ option.fieldDesc }!`);
				}
				if (option.isWorkNo && value && !baseValidate.length(value, 12)) {
					option.callback(false);
					return reject(option.workNoDesc || `${ prefix }正确的学号!`);
				}
				if (option.noCN && value && baseValidate.hasZH_CN(value)) {
					option.callback(false);
					return reject(`${ prefix }正确的${ option.fieldDesc }，不能含有中文!`);
				}
				if (option.noSpecial && value && baseValidate.noSpecial(value)) {
					option.callback(false);
					return reject(`${ prefix }正确的${ option.fieldDesc }，不能含有特殊字符!`);
				}
				if (option.reg && value && !option.reg.test(value)) {
					option.callback(false);
					return reject(`${ prefix }正确的${ option.fieldDesc }!`);
				}
				if (!isEmpty(option.minLength) && value && !baseValidate.minlength(value, option.minLength)) {
					option.callback(false);
					return reject(`不能少于${ option.minLength }个字符!`);
				}
				if (!isEmpty(option.maxLength) && value && !baseValidate.maxlength(value, option.maxLength)) {
					option.callback(false);
					return reject(`不能大于${ option.maxLength }个字符!`);
				}

				if (option.isEdit && option.isEdit() && option.initValue && option.initValue() === value) {
					option.callback(true);
					return resolve();
				}

				// const userInfo = storage.get(USER_INFO);
				// const params = {
				// 	tableName: option.tableName || 'sys_user',
				// 	fieldName: option.fieldName,
				// 	fieldVal: value,
				// 	dataId: userInfo?.userId,
				// };
				// for (const prop in additionalParams) {
				// 	if (Object.prototype.hasOwnProperty.call(additionalParams, prop) && !Object.prototype.hasOwnProperty.call(params, prop)) {
				// 		params[prop] = additionalParams[prop];
				// 	}
				// }
				// duplicateCheck(params, { interceptRes: false }).then(res => {
				// 	logger.log('唯一性验证 res:', res);
				// 	if (res?.code === IMessage.OK.code) {
				// 		option.callback(true);
				// 		return resolve();
				// 	} else if (res?.code === IMessage.Fail.code) {
				// 		option.callback(false);
				// 		reject(res.message);
				// 		return;
				// 	}
				// 	option.callback(false);
				// 	return reject(`${ option.fieldDesc }已存在!`);
				// });
			});
		}, 300),
	}],

	/**
	 * 重复检查(自定义方法)
	 * @param {object} option
	 * @param {string} option.message          描述
	 * @param {string} option.message2         描述2
	 * @param {function} option.function       自定义函数
	 * @param {function} option.paramsFunc     请求参数
	 * @param {function=} option.continueFunc  是否继续下一步函数
	 * @param {boolean=} option.required       是否验证必填
	 * @param {boolean=} option.success        成功回调
	 * @param {boolean=} option.fail           失败回调
	 * @param {string=} option.trigger         触发类型 有 change、blur、['change', 'blur'], 默认 'blur'
	 */
	duplicateCheck_func: (option = {}) => [{
		trigger: option.trigger ?? 'change',
		required: option.required ?? true,
		asyncValidator: debounce(function (rule, value) { // debounce时，外层不能用箭头函数，不然this指向错误！
			option.success = option.success || function () {};
			option.fail = option.fail || function () {};
			return new Promise((resolve, reject) => {
				if (value?.trim().length === 0) { // 全为空格校验
					return reject(`${ option.message }!`);
				}
				if (option.required && !baseValidate.required(value)) {
					return reject(`${ option.message }!`);
				}
				if (typeof option.function !== 'function') {
					throw Error('option.function必须是一个Function');
				}
				if (typeof option.paramsFunc !== 'function') {
					throw Error('option.paramsFunc必须是一个Function');
				}

				const params = option.paramsFunc() || {};

				if (typeof option.continueFunc === 'function' && !option.continueFunc()) {
					return resolve();
				}

				option.function(params).then(res => {
					if (res?.code === IMessage.OK.code && res.result) {
						option.success(res);
						return resolve();
					}
					option.fail(res);
					return reject(option.message2 || '');
				});
			});
		}, 300),
	}],

	/**
	 * 自定义验证(异步)
	 * @param {function} callback
	 * @param {string} trigger 触发类型 有 change、blur、['change', 'blur']
	 */
	customAsync: (callback, trigger = 'change') => [{
		trigger,
		required: true,
		asyncValidator: callback,
	}],

	/**
	 * 两次输入比较验证
	 * @param {Object} option
	 * @param {function} option.lastValue   上次输入的值
	 * @param {string=} option.message      提示信息
	 * @param {boolean=} option.required    是否验证必填
	 * @param {string=} option.trigger      触发类型 有 change、blur、['change', 'blur'], 默认 'blur'
	 */
	compare: (option) => [{
		trigger: option.trigger || 'change',
		required: option.required,
		asyncValidator: debounce(function (rule, value) {
			return new Promise((resolve, reject) => {
				if (!value) {
					return reject(`${ option.message }不能为空!`);
				}
				if (option.lastValue && option.lastValue() === value) {
					return resolve();
				}
				reject(`两次输入不一致!`);
			});
		}, 300),
	}],

	/**
	 * 自定义验证条件
	 * @param {object} option
	 * @param {function} option.condition   验证条件
	 * @param {string=} option.message      提示信息
	 * @param {boolean=} option.required    是否验证必填
	 * @param {boolean=} option.debounce    是否开启防抖动
	 * @param {number=} option.interval     间隔时间
	 * @param {string=} option.trigger      触发类型 有 change、blur、['change', 'blur'], 默认 'blur'
	 * @param {function=} option.validator  验证处理
	 * @param {function=} option.asyncValidator  异步验证处理
	 */
	customCondition: (option) => {
		option.trigger = option.trigger ?? 'change';
		option.debounce = option.debounce ?? true;

		const handler = (rule, value, callback) => {
			if (typeof option.condition !== 'function') {
				return callback(new Error('condition必须是一个Function'));
			}
			if (option.condition && option.condition()) {
				return callback();
			} else {
				return callback(new Error(option.message ?? ''));
			}
		};
		if (!option.validator && !option.asyncValidator) {
			// debounce时，外层不能用箭头函数，不然this指向错误！
			const debounceFun = debounce(function (rule, value, callback) {
				return handler(rule, value, callback);
			}, option.interval ?? 300);
			option.asyncValidator = option.debounce ? debounceFun : handler;
		}

		return [{
			trigger: option.trigger,
			required: option.required,
			validator: option.validator,
			asyncValidator: option.asyncValidator,
		}];
	},
};

/**
 * 基本验证规则
 */
export const baseValidate = {
	/**
	 * 必填
	 * @param {string} value
	 * @returns {boolean}
	 */
	required: (value) => !!value,

	/**
	 * 比较长度
	 * @param {string} value
	 * @param {number} len
	 * @returns {boolean}
	 */
	length: (value, len) => (value || '').trim().length === len,

	/**
	 * 最小长度
	 * @param {string} value
	 * @param {number} len
	 * @returns {boolean}
	 */
	minlength: (value, len) => (value || '').trim().length >= len,

	/**
	 * 最大长度
	 * @param {string} value
	 * @param {number} len
	 * @returns {boolean}
	 */
	maxlength: (value, len) => (value || '').trim().length <= len,

	/**
	 * 名称验证
	 * @param {string} value
	 * @return {boolean}
	 */
	name: (value) => UserNameReg.test(value),

	/**
	 * 手机号验证
	 * @param {string} value
	 * @return {boolean}
	 */
	phone: (value) => PhoneReg.test(value),

	/**
	 * 电话号码验证
	 * @param {string} value
	 * @return {boolean}
	 */
	tel: (value) => TelReg.test(value),

	/**
	 * 座机号码验证
	 * @param {string} value
	 * @return {boolean}
	 */
	specialPlane: (value) => /^0\d{2,3}-[1-9]\d{6,7}$/.test(value),

	/**
	 * 身份证验证
	 * @param {string} value
	 * @return {boolean}
	 */
	idCard: (value) => isCarId(value),

	/**
	 * 验证码验证
	 * @param {string} value
	 * @return {boolean}
	 */
	captcha: (value) => /[a-z0-9A-Z]{4,6}/.test(value),

	/**
	 * 金额验证
	 * @param {string} value
	 * @return {boolean}
	 */
	amount: (value) => AmountReg.test(value),

	/**
	 * 金额验证
	 * @param {string} value
	 * @return {boolean}
	 */
	isNumber: (value) => AllNumberReg.test(value),

	/**
	 * 工作时长：整数两位数，小数保留两位数
	 * @param {string} value
	 * @returns {boolean}
	 */
	workTime: (value) => /(^[0-9]([0-9])?(\.[0-9]{1,2})?$)/.test(value),

	/**
	 * 邮箱校验
	 * @param {string} value
	 * @returns {boolean}
	 */
	checkEmail: (value) => EmailReg.test(value),

	/**
	 * URL地址校验
	 * @param {string} value
	 */
	isURL: (value) => /^http[s]?:\/\/.*/.test(value),

	/**
	 * 角色编码校验
	 *  只能由英文、数字、下划线组成
	 * @param {string} value
	 */
	roleCode: (value) => RoleCodeReg.test(value),

	/**
	 * 匹配中文字符
	 * @param {string} value
	 */
	hasZH_CN: (value) => HasZH_CNReg.test(value),

	/**
	 * 只能输入汉字、英文字母和数字
	 * @param {string} value
	 */
	onlyCnEn: (value) => OnlyCN_EN_NumReg.test(value),

	/**
	 * 不能含有特殊字符
	 * @param {string} value
	 */
	noSpecial: (value) => !OnlyCN_EN_NumReg.test(value),
};
