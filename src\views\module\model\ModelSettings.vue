<template>
	<div class="params-settings" v-if="!loading">
		<template v-for="(data, index) in inDataList">

			<div class="box" :key="'parts_' + data.key + index" v-if="isParts(data)">
				<div class="column upload" v-if="data.value && data.value.length">
					<div class="content">
						<div class="item percent-65">
							<span class="label">
								<a-tooltip placement="top" :title="data.value.join(',')">
									{{ data.value.join(',') | Ellipsis(8) }}
								</a-tooltip>
							</span>
						</div>

						<div class="item right">

							<div class="mr-10" @click="allVisibleHandler(data)">
								<svg-icon :type="data.visible ? 'param-visible' : 'param-hidden'" />
							</div>

							<div class="mr-10">
								<input type="color" v-model="data.color" @input="allColorHandler(data)">
							</div>

							<a-popover :title="null" placement="topRight">
								<template #content>
									<div class="display-flex-center">
										<a-slider
											:min="0"
											:max="100"
											:step="1"
											v-model="data.opacity"
											:style="{ width: '100px', margin: '10px 0 0 10px' }"
											@change="allOpacityHandler(data)"
										/>
										<div class="opacity">{{ data.opacity }}%</div>
									</div>
								</template>
								<svg-icon type="param-opacity" />
							</a-popover>
						</div>
					</div>
				</div>
				<div class="column upload" v-else>
					<div class="select-box">
						<FileLoader
							class="upload-btn"
							:disabled="data.disabled"
							:loading="data.submitLoading"
							:exts="['3dm', 'stp', 'step', 'iges', 'igs', 'stl', 'pdf', 'ai']"
							@ok="uploadCallbackHandler($event, data)"
						/>
					</div>
				</div>
			</div>

			<div class="box" :key="'slider_' + data.key + index" v-if="isSlider(data)">
				<div class="column">
					<div class="box-header">
						<div class="box-grid">
							<div class="box-grid-cell" v-if="data.group && data.group.visible">
								<span class="group">{{ data.group.label }}<span class="colon">:</span></span>
								<a-tooltip v-if="data.group.tip" placement="top" :title="data.group.tip">
									<a-icon type="question-circle" />
								</a-tooltip>
							</div>
							<div class="box-grid-cell pt10" v-if="data.label">
								<span class="label">{{ data.label }}<span class="colon">:</span></span>
								<a-tooltip v-if="data.tip" placement="top" :title="data.tip">
									<a-icon type="question-circle" />
								</a-tooltip>
							</div>
						</div>
					</div>
				</div>

				<div class="column slider-wrapper">
					<div class="select-box">
						<div class="select-item slider">
							<a-slider
								:value="data.value"
								:min="data.min"
								:max="data.max"
								:step="data.step"
								:marks="{ [data.min]: data.min, [data.max]: data.max }"
								@change="sliderValueChange($event, data)"
							/>
						</div>
						<div class="select-item u-60">
							<a-input-number
								size="small"
								:value="data.value"
								:min="data.min"
								:max="data.max"
								:step="data.step"
								@change="sliderValueChange($event, data)"
							/>
						</div>
					</div>
				</div>

			</div>

			<div class="box" :key="'carousel_' + data.key + index" v-if="isCarousel(data)">
				<div class="column">
					<div class="box-header">
						<div class="box-grid">
							<div class="box-grid-cell" v-if="data.group && data.group.visible">
								<span class="group">{{ data.group.label }}<span class="colon">:</span></span>
								<a-tooltip v-if="data.group.tip" placement="top" :title="data.group.tip">
									<a-icon type="question-circle" />
								</a-tooltip>
							</div>
							<div class="box-grid-cell pt10" v-if="data.label">
								<span class="label">{{ data.label }}<span class="colon">:</span></span>
								<a-tooltip v-if="data.tip" placement="top" :title="data.tip">
									<a-icon type="question-circle" />
								</a-tooltip>
							</div>
						</div>
					</div>
				</div>

				<div class="column" v-if="data.mode === 'popover'">
					<a-popover
						trigger="click"
						placement="right"
						:visible="data.visible"
						:arrowPointAtCenter="true"
						:overlayStyle="{ maxWidth: '420px' }"
					>
						<template slot="title">
							<div class="popover-title">
								<b>{{ data.popoverTitle }}</b>
								<a-icon type="close" @click="closePopover(data)" />
							</div>
						</template>
						<template slot="content">
							<div class="popover-list">
								<a-list :grid="{ gutter: 0, column: 3 }" :pagination="data.pagination" :data-source="data.dataList">
									<a-list-item slot="renderItem" slot-scope="item">
										<div
											class="image-card"
											:class="{
												'active': selectedItem && selectedItem.name === item.name,
												'disabled': item.disabled
											}"
											@click="handlerSelected(item, data)"
										>
											<img :src="item.url">
											<div>{{ item.title }}</div>
										</div>
									</a-list-item>
								</a-list>
							</div>
						</template>

						<a-carousel :ref="data.key + 'CarouselRef'" arrows :dots="false" :initialSlide="data.value" :afterChange="e => patternValueChangeHandler(e, data)">
							<div slot="prevArrow" class="custom-slick-arrow" style="left: 10px;">
								<a-icon type="left" @click="handlerArrowClick(data)" />
							</div>
							<div slot="nextArrow" class="custom-slick-arrow right" style="right: 10px;">
								<a-icon type="right" @click="handlerArrowClick(data)" />
							</div>
							<template v-for="(item, itemIndex) in data.dataList">
								<div class="carousel-content" :class="{ 'disabled': item.disabled }" :key="itemIndex">
									<div class="carousel-box" @click="popoverHandler(data)">
										<div class="carousel-cell u-75">
											<img :src="item.url">
										</div>
										<div class="carousel-cell u-auto pl10">
											<h4>{{ item.title }}</h4>
											<span :title="item.desc">{{ item.desc | Ellipsis(8) }}</span>
										</div>
									</div>
								</div>
							</template>
						</a-carousel>
					</a-popover>
				</div>

				<div class="column" v-if="data.mode === 'text'">
					<a-carousel :ref="data.key + 'CarouselRef'" class="carousel-text" arrows :dots="false" :initialSlide="data.value" :afterChange="e => valueChangeHandler(e, data)">
						<div slot="prevArrow" class="custom-slick-arrow" style="left: 10px;">
							<a-icon type="left" />
						</div>
						<div slot="nextArrow" class="custom-slick-arrow right" style="right: 10px;">
							<a-icon type="right" />
						</div>
						<template v-for="(item, itemIndex) in data.dataList">
							<div class="text" :key="itemIndex">
								<h4>{{ item.title }}</h4>
							</div>
						</template>
					</a-carousel>
				</div>
			</div>

			<div class="box pt10" :key="'switch_' + data.key + index" v-if="isSwitch(data)">
				<div class="column">
					<div class="content">
						<div class="item percent-55">
							<span class="label" :class="{ 'required': data.required }">{{ data.label }}</span>
							<span class="colon">:</span>
							<a-tooltip v-if="data.tip" placement="top" :title="data.tip">
								<a-icon type="question-circle" style="cursor: pointer" />
							</a-tooltip>
						</div>

						<div class="item right">
							<a-switch
								v-model="data.checked"
								checked-children="开启"
								un-checked-children="关闭"
								:disabled="data.disabled"
								@change="switchValueChange($event, data)"
							/>
						</div>
					</div>
				</div>
			</div>

			<a-divider :key="data.key + index" v-if="data.showDivider" style="margin: 10px 0;" />

		</template>
	</div>
</template>

<script>
/* ===================================
 * 模型-参数设置页
 * Created by cjking on 2022/07/20.
 * Copyright 2022, Inc.
 * =================================== */
import { isArray } from '@/utils/utils';
import FileLoader from '@/components/core/FileLoader';

export default {
	name: 'ModelSettings',
	components: {
		FileLoader,
	},
	props: {
		activeKey: {
			type: Array,
			required: false,
			default: () => ['1'],
		},
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	data () {
		return {
			loading: false,
			/* 被选中的对象 */
			selectedItem: null,
			inDataList: [],
			/* 关系对应集合 */
			disabledMap: {
				// 纹理类型值:[细分变种值]
				0: [],
				1: [7],
				2: [3, 4],
				3: [0, 4],
				4: [1, 2, 3, 4, 6],
				5: [1, 7],
				6: [4],
				7: [4],
				8: [3, 4],
				9: [3, 4],
				10: [1, 4],
				11: [2, 4, 5, 6, 7],
				12: [],
				13: [4],
				14: [4],
				15: [4, 6],
				16: [4],
				17: [1, 4],
			},
		};
	},
	watch: {
		dataList: {
			immediate: true,
			handler () {
				this.initData();
				this.checkVariantValid();
				this.initCarouselData();
			},
		},
	},
	methods: {
		isParts (data) {
			return data.type === 'parts';
		},

		isSlider (data) {
			return data.type === 'slider';
		},

		isSwitch (data) {
			return data.type === 'switch';
		},

		isCarousel (data) {
			return data.type === 'carousel';
		},

		/**
		 * 初始化数据
		 */
		initData () {
			this.inDataList = [...this.dataList];
			const data = this.dataList.find(data => data.key === 'oneway_change');
			if (data) {
				const extraDataList = data['children' + data.value];
				if (extraDataList && isArray(extraDataList)) {
					this.inDataList.push(...extraDataList);
				}
			}
		},

		/**
		 * 初始化轮播数据
		 */
		initCarouselData () {
			const moldData = this.dataList.find(data => data.key === 'mold');
			if (moldData) {
				// 切换到指定面板
				const index = moldData.dataList.findIndex(inItem => inItem.value === moldData.value);
				this.$refs[moldData.key + 'CarouselRef']?.[0]?.goTo(index);
			}
			const partitionData = this.dataList.find(data => data.key === 'partition');
			if (partitionData) {
				// 切换到指定面板
				const index = partitionData.dataList.findIndex(inItem => inItem.value === partitionData.value);
				this.$refs[partitionData.key + 'CarouselRef']?.[0]?.goTo(index);
			}
			const onewayChangeData = this.dataList.find(data => data.key === 'oneway_change');
			if (onewayChangeData) {
				// 切换到指定面板
				const index = onewayChangeData.dataList.findIndex(inItem => inItem.value === onewayChangeData.value);
				this.$refs[onewayChangeData.key + 'CarouselRef']?.[0]?.goTo(index);
			}
		},

		/**
		 * 上传回调处理
		 */
		uploadCallbackHandler (file, data) {
			this.$emit('uploadCallback', { file, data });
		},

		/**
		 * 所有显隐处理
		 */
		allVisibleHandler (data) {
			data.visible = !data.visible;
			this.$emit('onAllVisible', data);
		},

		/**
		 * 所有颜色处理
		 */
		allColorHandler (data) {
			this.$emit('onAllColor', data);
		},

		/**
		 * 所有透明度处理
		 */
		allOpacityHandler (data) {
			this.$emit('onAllOpacity', data);
		},

		/**
		 * 滑块值更改
		 */
		sliderValueChange (value, data) {
			if (!isNaN(value)) {
				if (value < data.min) {
					data.value = data.min;
				} else if (value > data.max) {
					data.value = data.max;
				} else {
					data.value = value;
				}
			}
			this.$emit('sliderValueChange', data);
		},

		/**
		 * 开关值变化
		 */
		switchValueChange (value, data) {
			data.value = value ? 1 : 0;
			this.$emit('switchValueChange', data);
		},

		/**
		 * 已选择处理程序
		 */
		handlerSelected (item, data) {
			this.selectedItem = item;
			data.visible = false;

			// 切换到指定面板
			const index = data.dataList.findIndex(inItem => inItem.name === item.name);
			this.$refs[data.key + 'CarouselRef']?.[0]?.goTo(index);
		},

		/**
		 * 弹出处理程序
		 */
		popoverHandler (data) {
			data.visible = !data.visible;
			this.inDataList.forEach(inData => {
				inData.visible = data.key === inData.key ? data.visible : false;
			});
			this.dataList.forEach(dataSource => {
				dataSource.visible = data.key === dataSource.key ? data.visible : false;
			});
		},

		/**
		 * 关闭弹出框
		 */
		closePopover (data) {
			data.visible = false;
		},

		/**
		 * 处理程序箭头单击
		 */
		handlerArrowClick (data) {
			data.visible = false;
		},

		/**
		 * 值变更处理
		 */
		valueChangeHandler (currentIndex, data) {
			data.value = Number(currentIndex);
			const inData = this.inDataList.find(inData => inData.key === data.key);
			if (inData) {
				inData.value = data.value;
				if (data.key === 'oneway_change') {
					this.initData();
				}
			}
			const dataSource = this.dataList.find(dataSource => dataSource.key === data.key);
			if (dataSource) {
				dataSource.value = data.value;
			}

			this.$emit('carouselValueChange', data);
		},

		/**
		 * 图案值变更处理
		 */
		patternValueChangeHandler (currentIndex, data) {
			data.value = Number(currentIndex);
			const inData = this.inDataList.find(inData => inData.key === data.key);
			if (inData) {
				inData.value = data.value;
			}
			const dataSource = this.dataList.find(dataSource => dataSource.key === data.key);
			if (dataSource) {
				dataSource.value = data.value;
			}
			this.checkVariantValid();

			this.$emit('carouselValueChange', data);
		},

		/**
		 * 检查细分变种有效性
		 */
		checkVariantValid () {
			const moldData = this.inDataList.find(inData => inData.key === 'mold');
			const partitionData = this.inDataList.find(inData => inData.key === 'partition');
			if (moldData && partitionData) {
				const variantValues = this.disabledMap[moldData.value];
				if (!variantValues) {
					this.$message.error(`disabledMap 配置中未找到对应值【${ moldData.value }】的相关配置！`);
					return;
				}
				partitionData.invalid = variantValues.includes(partitionData.value);

				partitionData.dataList.forEach(childData => {
					childData.disabled = variantValues.includes(childData.value);
				});

				// 同步数据源
				const partitionSource = this.dataList.find(partitionSource => partitionSource.key === 'partition');
				if (partitionSource) {
					partitionSource.dataList = partitionData.dataList;
				}
			}
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.pt10 {
	padding-top: 10px;
}

.box-header {
	.box-grid {
		display: flex;
		width: 100%;
		flex-direction: column;

		.box-grid-cell {
			flex: 1;

			&.pt10 {
				padding-top: 10px;
			}

			.anticon {
				cursor: pointer;
			}
		}
	}
}

.popover-title {
	display: flex;
	justify-content: space-between;
	align-content: center;
	align-items: center;
	line-height: 36px;

	.anticon {
		color: #AAAAAA;
	}
}

.popover-list {
	.image-card {
		position: relative;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-content: center;
		align-items: center;
		margin: 0 4px;
		border: solid #CCCCCC 1px;
		border-radius: 1px;
		cursor: pointer;

		&:hover {
			border-color: #FF7020;
		}

		&.active {
			border-color: #FF7020;
		}

		&.disabled {
			img, div {
				opacity: 0.5;
				border: 1px solid red;
				cursor: not-allowed;
			}
		}

		img {
			width: 120px;
			height: 80px;
			opacity: 1;
		}

		div {
			position: absolute;
			left: 0;
			bottom: 0;
			width: 118px;
			height: 24px;
			text-align: center;
			z-index: 200;
			color: #FFFFFF;
			opacity: 1;
			background-color: rgba(0, 0, 0, 0.6);
		}
	}

	/deep/ .ant-list-pagination {
		margin-top: 12px;
		text-align: center;
	}
}

.params-settings {
	.group {
		display: inline-block;
		font-size: 18px;
		font-weight: 600;
		color: #333333;
	}

	.label {
		font-size: 14px;
		font-weight: bold;
		color: #333333;
	}

	.colon {
		display: inline-block;
		margin-right: 5px;
		font-weight: bold;
	}

	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}

	.ant-carousel {
		width: 280px;
		border: 1px solid #CCCCCC;

		/deep/ .slick-slide {
			text-align: center;
			height: 72px;
			background: #FFFFFF;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		/deep/ .custom-slick-arrow {
			width: 14px;
			height: 14px;
			font-size: 14px;
			color: #333333;
			top: 40px;
			z-index: 1;

			&:before {
				display: none;
			}

			&:hover {
				opacity: 0.5;
			}
		}

		.carousel-content {
			&.disabled {
				img, div {
					opacity: 0.5;
					cursor: not-allowed;
				}
			}

			.carousel-box {
				display: flex;
				align-items: center;
				margin-top: 5px;

				&:hover {
					cursor: pointer;
				}

				.carousel-cell {
					text-align: left;

					img {
						width: 75px;
						height: 50px;

						&:hover {
							cursor: pointer;
							border: solid #FF7020 1px;
						}
					}

					.u-75 {
						flex: 0 0 75px;
					}

					.u-auto {
						flex: 0 0 auto;
					}

					&.pl10 {
						padding-left: 10px;
					}
				}
			}
		}

		&.carousel-text {
			/deep/ .slick-slide {
				height: 40px;
			}

			/deep/ .custom-slick-arrow {
				top: 22px;
			}

			.text {
				height: 40px;
				line-height: 40px;
			}
		}
	}

	.box {
		margin-left: 4px;

		&.mtb-16 {
			margin: 16px 0;
		}

		.content {
			display: flex;
			width: 100%;

			.item {
				flex: 1;

				&.percent-55 {
					flex: 0 0 55%;
				}

				&.percent-65 {
					flex: 0 0 65%;
				}

				svg {
					width: 20px;
					height: 20px;
					color: @primary;
				}

				.icon {
					padding: 0 12px;
					color: @primary;
					cursor: pointer;

					&.right {
						text-align: right;
					}
				}

				&.right {
					display: flex;
					justify-content: flex-end;
					align-items: center;
					margin-left: -10px;

					svg, input[type="color"] {
						cursor: pointer;
					}

					.mr-10 {
						margin-right: 10px;
					}
				}
			}
		}

		.column {
			padding: 5px 0;

			&.upload {
				padding: 0;

				.select-box {
					padding: 0;
				}

				.upload-btn {
					width: 280px;

					/deep/ .ant-btn {
						border-radius: 3px;
						background-color: #6362FF;
					}
				}
			}

			&.group {

				/deep/ .ant-btn {
					font-size: 12px;
				}

				.group-container {
					width: 100%;
					display: flex;
					flex-direction: column;

					.activate {
						color: #8E8AFF;
						border-color: #8E8AFF;
					}

					.select-box {
						padding: 0 10px 5px 10px;

						&:first-child {
							padding: 10px 10px 5px 10px;
						}
					}

					.select-item {
						overflow: hidden;
						display: flex;

						&.h22 {
							height: 24px;
							line-height: 22px;
						}

						.ant-btn {
							min-width: 102px;
						}
					}

					.btn-add {
						padding: 10px;

						.ant-btn {
							width: 100%;
							height: 35px;
							min-height: 35px;
							border-radius: 3px;
						}
					}

					.ant-tag {
						margin-right: 0;
						margin-bottom: 0;
						height: 24px;
						line-height: 22px;
						cursor: pointer;
						min-width: 102px;
						max-width: 102px;
						text-align: center;
						overflow: hidden;

						display: flex;
						align-items: center;
						justify-content: center;
					}

					.ant-btn-sm {
						padding: 0 5px;
					}

					.extra-tag {
						width: 115px;
						min-width: 115px;
						overflow: hidden;
						/*文本不会换行*/
						white-space: nowrap;
					}
				}

				.point {
					&.ant-tag {
						min-width: 116px;
					}
				}
			}

			&.slider-wrapper {
				padding: 5px 0;

				.select-box {
					background: transparent;
					height: 46px;
					line-height: 46px;

					/deep/ .slider {
						.ant-slider {
							width: 140px;
							margin-left: 10px;
						}
					}

					.select-item {
						overflow: hidden;
					}
				}
			}

			.select-box {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				background: #F9F9F9;
				padding: 10px;

				.select-item {
					flex: 1;
					min-height: 22px;
					max-height: 60px;
					overflow: auto;
				}

				.select-item.u-22 {
					flex: 0 0 22px;
					margin: 0 0 0 5px;
				}

				/deep/ .select-item.u-60 {
					flex: 0 0 60px;

					.ant-input-number {
						width: 60px;
					}
				}

				.icon {
					color: @primary;
					cursor: pointer;

					.clear {
						width: 20px;
						height: 20px;
						vertical-align: -5px;
					}
				}
			}
		}
	}
}
</style>
