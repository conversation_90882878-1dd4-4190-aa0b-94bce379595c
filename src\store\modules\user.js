import { notification } from 'ant-design-vue';
import { login, logout, queryDepartModulePackageList } from '@/api';
import { logger } from '@/utils/logger';
import { storage } from '@/utils/storage';
import { ACCESS_TOKEN, IS_LOGIN, TOKEN_TYPE, USER_INFO, MODULE_LIST } from '@/constants';

/**
 * 保存用户信息
 * @param result
 * @param commit
 * @param oneWeek
 */
const saveUserInfo = (result, commit, oneWeek) => {
	const userInfo = result.userInfo;
	const token = result.token;
	// const tokenType = result.token_type;
	storage.set(ACCESS_TOKEN, token, oneWeek);
	// storage.set(TOKEN_TYPE, tokenType, oneWeek);
	storage.set(USER_INFO, userInfo, oneWeek);
	storage.set(IS_LOGIN, true, oneWeek);
	// commit('SET_TOKEN_TYPE', tokenType);
	commit('SET_USERINFO', userInfo);
	commit('SET_IS_LOGIN', true);
};

/**
 * 清除本地登录数据
 * @return {boolean}
 */
const clearLoginData = (commit) => {
	// storage.remove(ACCESS_TOKEN);
	// // storage.remove(TOKEN_TYPE);
	// storage.remove(USER_INFO);
	// storage.remove(IS_LOGIN);
	// storage.remove(MODULE_LIST);
	storage.clear();
	commit('SET_TOKEN');
	// commit('SET_TOKEN_TYPE');
	commit('SET_USERINFO');
	commit('SET_IS_LOGIN');
	commit('SET_MODULE_LIST');
	return true;
};

const user = {
	state: {
		token: '',      // 登录凭证
		tokenType: '',  // 凭证类型 jwt、bearer
		isLogin: false, // 是否登录
		userInfo: {},   // 用户登录信息
		oneWeek: 7 * 24 * 60, // 一周(分钟)
		moduleList: [],
	},
	mutations: {
		SET_TOKEN: (state, token) => {
			state.token = token || '';
		},
		SET_TOKEN_TYPE: (state, tokenType) => {
			state.tokenType = tokenType || '';
		},
		SET_USERINFO: (state, userInfo) => {
			state.userInfo = userInfo || {};
		},
		SET_IS_LOGIN: (state, bool) => {
			state.isLogin = bool || false;
		},
		SET_MODULE_LIST: (state, module) => {
			state.moduleList = module || [];
		},
	},
	actions: {
		/**
		 * 登录
		 */
		Login ({ commit, state }, params) {
			return new Promise(async (resolve, reject) => {
				try {
					logger.log('登录参数 params: ', params);
					const response = await login(params);
					logger.log('登录 res: ', response);
					const result = response?.result;
					if (!result) return reject();
					saveUserInfo(result, commit, state.oneWeek);
					return resolve(response);
				} catch (error) {
					logger.error('登录 error: ', error);
					return reject(error);
				}
			});
		},

		/**
		 * 登出
		 * @return {boolean}
		 */
		async Logout ({ commit }) {
			try {
				const response = await logout();
				logger.log('登出 res: ', response);
				clearLoginData(commit);
				return true;
			} catch (error) {
				clearLoginData(commit);
				return true;
			}
		},

		/**
		 * 清除本地登录数据
		 * @return {boolean}
		 */
		ClearLoginData ({ commit }) {
			clearLoginData(commit);
			return true;
		},

		/**
		 * 保存本地登录数据
		 * @return {boolean}
		 */
		SaveUserInfo ({ commit, state }, result) {
			saveUserInfo(result, commit, state.oneWeek);
			return true;
		},

		/**
		 * 初始化权限列表
		 */
		initPermissionList ({ commit, state }) {
			return new Promise(async (resolve, reject) => {
				try {
					const res = await queryDepartModulePackageList();

					// if (!res.result || res.result.length === 0) {
					// 	notification?.error({ message: '温馨提示', description: '权限模块为空，请联系管理员!' });
					// 	storage.set(MODULE_LIST, [], state.oneWeek);
					// 	return reject(false);
					// }
					storage.set(MODULE_LIST, res.result, state.oneWeek);
					commit('SET_MODULE_LIST', res.result);
					return resolve(true);
				} catch (error) {
					logger.error('initPermissionList error: ', error);
					// storage.dispatch('Logout');
					return reject(false);
				}
			});
		},
	},
};

export default user;
