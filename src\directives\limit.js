/* ===================================
 * 指令：input最大及最小值限制
 * Created by cjking on 2021/10/20.
 * Copyright 2021, Inc.
 * =================================== */
import { isEmpty } from '@/utils/utils';

const addListener = function (el, type, fn) {
	el.addEventListener(type, fn, false);
};

const handler = function (el, binding) {
	const { min, max } = binding.value;

	addListener(el, 'blur', () => {
		if (!isEmpty(el.value)) {
			if (el.value < min || isNaN(el.value)) {
				el.value = min;
			}
			if (el.value > max) {
				el.value = max;
			}
		}
		el.dispatchEvent(new Event('input'));
	});
};

export const LimitDirective = Vue => Vue.directive('limit', {
	bind (el, binding, vnode) {
		if (el.tagName.toLowerCase() !== 'input') {
			el = el.getElementsByTagName('input')[0];
		}
		handler(el, binding);
	},
});
