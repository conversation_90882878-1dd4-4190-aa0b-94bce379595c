<template>
	<a-modal v-model="visible" title="自定义参考平面" @ok="handleOk" @cancel="close" @keydown.8.stop>
		<a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
			<a-form-model-item label="原点坐标">
				<a-form-model-item label="X">
					<a-input-number v-model="form.x" :style="{width:'80%'}" />
				</a-form-model-item>
				<a-form-model-item label="Y">
					<a-input-number v-model="form.y" :style="{width:'80%'}" />
				</a-form-model-item>
				<a-form-model-item label="Z">
					<a-input-number v-model="form.z" :style="{width:'80%'}" />
				</a-form-model-item>
			</a-form-model-item>
			<!-- <a-form-model-item label="法向量">
				<a-form-model-item label="X方向" :label-col="labelCol" :wrapper-col="wrapperCol">
					<a-row>
						<a-col :span="16">
							<a-slider v-model="form.vx" :min="-1" :max="1" :step="0.01" :marks="{'-1': '-1', '0': '0', '1': '1'}" :style="{width:'180px'}" />
						</a-col>
						<a-col :span="8">
							<a-input-number v-model="form.vx" :min="-1" :max="1" :step="0.01" :style="{width:'80px'}" />
						</a-col>
					</a-row>
				</a-form-model-item>
				<a-form-model-item label="Y方向" :label-col="labelCol" :wrapper-col="wrapperCol">
					<a-row>
						<a-col :span="16">
							<a-slider v-model="form.vy" :min="-1" :max="1" :step="0.01" :marks="{'-1': '-1', '0': '0', '1': '1'}" :style="{width:'180px'}" />
						</a-col>
						<a-col :span="8">
							<a-input-number v-model="form.vy" :min="-1" :max="1" :step="0.01" :style="{width:'80px'}" />
						</a-col>
					</a-row>
				</a-form-model-item>
				<a-form-model-item label="Z方向" :label-col="labelCol" :wrapper-col="wrapperCol">
					<a-row>
						<a-col :span="16">
							<a-slider v-model="form.vz" :min="-1" :max="1" :step="0.01" :marks="{'-1': '-1', '0': '0', '1': '1'}" :style="{width:'180px'}" />
						</a-col>
						<a-col :span="8">
							<a-input-number v-model="form.vz" :min="-1" :max="1" :step="0.01" :style="{width:'80px'}" />
						</a-col>
					</a-row>
				</a-form-model-item>
			</a-form-model-item> -->

		</a-form-model>
	</a-modal>

</template>

<script>
// import { PlaneModeEnum } from '@/constants';

export default {
	data () {
		return {
			visible: false,
			labelCol: { span: 4 },
			wrapperCol: { span: 20 },
			form: {
				x: undefined,
				y: undefined,
				z: undefined,
				// vx: undefined,
				// vy: undefined,
				// vz: undefined,
			},
		};
	},
	methods: {
		handleOk () {
			this.visible = false;
			this.$emit('ok', this.form);
		},
		open ({ x = 0, y = 0, z = 0 }, planeMode) {
			this.visible = true;
			this.form.x = x;
			this.form.y = y;
			this.form.z = z;
			// this.form.vx = planeMode === PlaneModeEnum.planeDiyYZ ? -1 : 0;
			// this.form.vy = planeMode === PlaneModeEnum.planeDiyXZ ? -1 : 0;
			// this.form.vz = planeMode === PlaneModeEnum.planeDiyXY ? -1 : 0;
		},
		close () {
			this.visible = false;
		}
	},
};
</script>

<style>

</style>
