# client-frontend

#### 项目名称：创客云用户端（前端）

---

### 安装NodeJs版本管理工具`nvm`

```
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.37.2/install.sh | bash

source ~/.bashrc

nvm -h

```

如果安装失败：curl: (7) Failed to connect to raw.githubusercontent.com port 443: Connection refused 的问题

解决方案 打开 https://www.ipaddress.com/ 输入访问不了的域名（raw.githubusercontent.com）

![](https://cjking-public.oss-cn-beijing.aliyuncs.com/static/markdown/20201027105308.png)

在本机(或者服务器)的 hosts 文件中添加(vim /etc/hosts):

```text
*************** raw.githubusercontent.com
*************** user-images.githubusercontent.com
*************** avatars2.githubusercontent.com
*************** avatars1.githubusercontent.com
*************** github.global.ssl.fastly.net
************ github.com
```

添加以上几条 host 配置，页面的图片展示就正常了，homebrew 也能装了，nvm 也行动灵活了

![](https://cjking-public.oss-cn-beijing.aliyuncs.com/static/markdown/20201027105437.png)

可以到这里下载安装最新稳定版本
[https://github.com/creationix/nvm](https://github.com/creationix/nvm)

### 安装NodeJs

```shell script
$ nvm ls-remote --lts   # 查看nodeJS最新稳定版本
# 1、在线安装(速度较慢)
$ nvm install v10.20.0 (此项目v10以上即可)

# 2、离线安装，推荐（本地事先准备好node安装包：node-v10.20.0-linux-x64.tar.xz）
# node安装包下载地址：https://nodejs.org/dist/v10.20.0/node-v10.20.0-linux-x64.tar.xz
$ mkdir -p ~/.nvm/.cache/bin/node-v10.20.0-linux-x64
$ cd ~/.nvm/.cache/bin/node-v10.20.0-linux-x64
$ rz # 上传node-v10.20.0-linux-x64.tar.xz到当前目录，rz命令如果不存在，需要先安装lrzsz（centos: yum install -y lrzsz, ubuntu: apt-get install lrzsz）
$ nvm install v10.20.0

$ nvm alias default v10.20.0  # 设置为默认版本
$ node -v   # 查看node版本，检测node是否安装正常
$ npm -v    # 查看npm版本，检测npm是否安装正常
```

### 切换私服(避免npm install慢问题)

```shell script
$ nrm use sf
```

关于 `添加镜像私服地址` 请查看当前项目根目录下 [NPM私服说明文档.md](NPM私服说明文档.md)

### 安装Npm依赖包

```shell script
$ cd client-frontend
$ npm install    #安装node依赖包
```

## Python环境安装

### 安装python依赖包(3.8以上)

1、安装 `python3.8.x` [参考](https://www.cnblogs.com/yuan3/p/10972411.html)

- 如果是Windows, 还需要进入到python安装目录，复制一个python.exe为python3.exe，命令如下（用Git Bash）：

```shell script
cd C:\\Python38 && cp python.exe python3.exe
```

2、安装依赖

Mac or Linux:

```shell script
cd client-frontend/script/python
sudo pip3 install -r requirements.txt # 注意：是pip3，不是pip!!!
```

Windows:

```shell script
cd client-frontend/script/python
pip install -r requirements.txt
```

### 打包发布

打包过后文件会放到dist目录下，请拷贝此目录下的所有文件

#### 打包编译

1、编译为生产环境

```shell script
$ cd client-frontend
$ npm run build:prod

# 如果是测试人员，请用makefile编译，适用于jenkins
$ make build-prod
```

2、编译为测试环境

```shell script
$ cd client-frontend
$ npm run build:test

# 如果是测试人员，请用makefile编译，适用于jenkins
$ make build-test
```

3、编译为开发环境

```shell script
$ cd client-frontend
$ npm run build:dev

# 如果是测试人员，请用makefile编译，适用于jenkins
$ make build-dev
```

4、开发者本地源码启动

```shell script
$ cd client-frontend
$ npm run serve
```

#### 发布

> 1、发布项目到服务器，需要支持ssh免登录 [参考](https://juejin.im/post/6844903749719162887)
>
> 2、本地docker需要处于开启中, `本地`和`服务器`都需要docker已登录

docker登录命令：

```shell script
# 格式：docker login -u=${username} -p ${pwd} ${REPOSITORY_URL}
docker login https://docker-registry.fmock.cn
or
docker login -u=admin -p 123456 https://docker-registry.fmock.cn
```

1、发布到生产环境

```shell script
$ cd client-frontend
$ npm run publish:prod-14
or
$ make publish-prod-14
```

```shell script
$ cd client-frontend
$ npm run publish:prod-8
or
$ make publish-prod-8
```

2、发布到测试环境

```shell script
$ cd client-frontend
$ npm run publish:test
or
$ make publish-test
```

3、发布到开发环境

```shell script
$ cd client-frontend
$ npm run publish:dev
or
$ make publish-prod
```

## 其他

### Nginx配置

1、http配置

```shell
# 静态资源代理(开发环境、测试环境、生产环境 通用)
server {
    listen       8901;
    server_name  localhost;
    charset utf-8;
    client_max_body_size 100M;

    location / {
        add_header 'Access-Control-Allow-Origin' "*";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS,  PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range,X-Access-Token,X-From-Source,x-from-source';
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        root /data/resource/; # 上传服务器资源存放路径
        autoindex on;
    }
}
```

2、https配置

```shell
server {
    listen  443 ssl http2;
    server_name  example.com;
    ssl_certificate /etc/letsencrypt/live/example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/example.com/chain.pem;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;
    
    client_max_body_size 100M;
    
    location / {
        add_header 'Access-Control-Allow-Origin' "*";
        add_header 'Access-Control-Allow-Credentials' "true";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS,  PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range,X-Access-Token,X-From-Source,x-from-source';
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        root /data/resource/; # 上传服务器资源存放路径
        autoindex on;
    }
}

server {
    listen 80; # redirect to 443
    server_name a.example.cn b.example.cn;
    rewrite ^(.*)$  https://$host$1 permanent;
}
```

```shell
map $http_upgrade $connection_upgrade {
	default upgrade;
	'' close;
}
upstream wsbackend {
	#server ip:port;
	server *************:8098;
	keepalive 1000;
}
 
server {
    listen  80;
    server_name  ws.example.cn;
	location / {
		proxy_http_version 1.1;
		proxy_pass http://wsbackend;
		proxy_redirect off;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_read_timeout 3600s;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection $connection_upgrade;
	}
}
```

3、客户端Nginx配置

```shell
server {
    listen  80;
    server_name  www.example.cn;
	location / {
		proxy_pass http://ip:port;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}
}
```

4、管理端Nginx配置

```shell
server {
    listen  80;
    server_name  manage.example.cn;
	location / {
		proxy_pass http://ip:port;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}
}
```

5、线上Nginx配置

```shell
# websocket 定义
map $http_upgrade $connection_upgrade {
	default upgrade;
	'' close;
}

# websocket 配置
upstream wsbackend {
	server ************:8098;
	keepalive 1000;
}

server {
    listen  80;
    server_name  client.generativethings.com;
    charset utf-8;
    client_max_body_size 100M;

	location / {
		proxy_pass http://localhost:8102;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}
    location /prod/static/ {
		proxy_pass http://************:8098/sys/common/static/;
	}
    location /prod/socket/ {
		proxy_http_version 1.1;
		proxy_pass http://wsbackend/;
		proxy_redirect off;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_read_timeout 3600s;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection $connection_upgrade;
	}
}

server {
    listen  80;
    server_name  manage.generativethings.com;
    charset utf-8;
    client_max_body_size 100M;

	location / {
		proxy_pass http://localhost:8103;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}
    location /prod/static/ {
		proxy_pass http://************:8099/sys/common/static/;
	}
}

server {
    listen  80;
    server_name  www.generativethings.com;
    charset utf-8;

	location / {
		proxy_pass http://localhost:3000;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}
}
```

location 匹配规则

![](https://cjking-public.oss-cn-beijing.aliyuncs.com/static/markdown/20201117142652.png)
