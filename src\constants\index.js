/* ===================================
 * 全局常量配置文件
 * Created by cjking on 2020/05/02.
 * Copyright 2020, Inc.
 * =================================== */
export const IS_LOGIN = 'IsLogin';
export const USER_INFO = 'UserInfo';
export const TOKEN_TYPE = 'TokenType';
export const ACCESS_TOKEN = 'AccessToken';
export const MODULE_LIST = 'ModuleList';
export const WIREFRAME = 'Wireframe';
export const FACE_GROUP = 'FaceGroup';
export const SOLID_GROUP = 'SolidGroup';
export const YS_LINE_GROUP = 'YsLineGroup';
export const BJ_POINT_GROUP = 'BjPointGroup';
export const DISPERSED_LINE_GROUP = 'DispersedLineGroup';
export const RESULT_WATER_COOLING_GROUP = 'resultWaterCoolingGroup';
export const TEXTURE_GUIDED_POINT = 'textureGuidedPoint';
export const RESULT_WL_LINE_GROUP = 'resultWlLineGroup';

// 颜色枚举
export const ColorEnum = {
	default: 0xCCCCCC,  // 灰白色
	origin: 0xC8C8C8,   // 灰色
	white: 0xffffff,    // 白色
	black: 0x000000,    // 黑色
	red: 0xFF0000,      // 红色
	pink: 0xFFA7A7,     // 粉红色
	green: 0x00FF00,    // 绿色
	blue: 0x6362FF,     // 蓝色
	yellow: 0xffff00,   // 黄色
	cyanBlue: 0x00FFFF, // 青蓝色
};

export const GradientRamp = [
	{ start: '#333', end: '#999' },
	{ start: '#85A8FF', end: '#B7C6FF' },
	{ start: '#00002A', end: '#52576E' }, // from ParaView
	{ start: '#C7D8E1', end: '#E3EAEE' },
	{ start: '#E0E0FF', end: '#E3EAEE' },
];
export const DEFAULT_VIEW_BACKGROUND = `linear-gradient(180deg, ${ GradientRamp[4].start } 0%, ${ GradientRamp[4].end } 100%)`;

export const VIEW_ORIENTATIONS = {
	default: {
		axis: 1,
		orientation: -1,
		viewUp: [0, 0, 1],
	},
	front: {
		axis: 2,
		orientation: 1,
		viewUp: [0, 1, 0],
	},
	back: {
		axis: 2,
		orientation: -1,
		viewUp: [0, 1, 0],
	},
	top: {
		axis: 1,
		orientation: 1,
		viewUp: [0, 0, -1],
	},
	bottom: {
		axis: 1,
		orientation: -1,
		viewUp: [0, 0, 1],
	},
	left: {
		axis: 0,
		orientation: -1,
		viewUp: [0, 1, 0],
	},
	right: {
		axis: 0,
		orientation: 1,
		viewUp: [0, 1, 0],
	},
};

export const ViewEnum = {
	default: 'default', // 默认视图
	front: 'front',     // 前视图(主视图)
	back: 'back',       // 后视图
	top: 'top',         // 俯视图
	bottom: 'bottom',   // 仰视图
	left: 'left',       // 左视图
	right: 'right',     // 右视图
};

export const ViewOrientationEnum = {
	front: VIEW_ORIENTATIONS.front.viewUp,      // 前视图(主视图)
	back: VIEW_ORIENTATIONS.back.viewUp,        // 后视图
	top: VIEW_ORIENTATIONS.top.viewUp,          // 俯视图
	bottom: VIEW_ORIENTATIONS.bottom.viewUp,    // 仰视图
	left: VIEW_ORIENTATIONS.left.viewUp,        // 左视图
	right: VIEW_ORIENTATIONS.right.viewUp,      // 右视图
};

export const ModeTypeEnum = {
	solid: 'solid',                 // 体
	face: 'face',                   // 面
	wireframe: 'wireframe',         // 线框
	disperseLine: 'disperseLine',   // 离散线
	point: 'point',                 // 点
};

export const SelectModeEnum = {
	solid: 'solid',     // 选择体
	surface: 'surface', // 选择面
	line: 'line',       // 选择线
	point: 'point',     // 选择点
};

export const RenderModeEnum = {
	surface: 'surface',     // 体
	wireframe: 'wireframe', // 线框
	surfaceAndWireframe: 'surfaceAndWireframe', // 体+线框
	translucent: 'translucent',                 // 体+线框+半透明
};

export const CameraModeEnum = {
	perspective: 'perspective',     // 透视摄像机
	orthographic: 'orthographic',   // 正交摄像机
};

export const DrawEnum = {
	point: 'point',                         // 绘制点
	curve: 'curve',                         // 绘制曲线
	brokenLine: 'brokenLine',               // 绘制折线
	singleSpiralLine: 'singleSpiralLine',   // 绘单螺旋线
	doubleHelixLine: 'doubleHelixLine',     // 绘双螺旋线
	circle: 'circle',                       // 绘制圆
	zyx: 'zyx',                             // 自由线
};

export const ActionEnum = {
	boxSelect: 'boxSelect',                 // 框选
	addControlPoints: 'addControlPoints',   // 添加控制点
	moveCopy: 'moveCopy',                   // 移动复制
	mirrorCurve: 'mirrorCurve',             // 镜像曲线
	rotateCopy: 'rotateCopy',               // 旋转复制
	circularArray: 'circularArray',         // 圆周阵列
	curveZoom: 'curveZoom',                 // 曲线缩放
};

export const WsTypeEnum = { // type:类型 dispersed:离散数据，resolve:求解数据
	dispersed: 'dispersed',         // 离散数据
	dispersedFace: 'dispersedFace', // 离散面数据
	resolve: 'resolve',             // 求解数据
	download: 'exportFile',         // 下载step文件
	resultExportStatus: 'resultExportStatus', // 下载step文件的生成状态
};

// 进度状态文案
export const ProgressDescEnum = {
	0: '未开始',
	1: '进行中',
	8: '成功',
	9: '失败',
};

// 进度状态
export const ProgressStatusEnum = {
	NOT_STARTED: 0, // 未开始
	LOADING: 1,     // 进行中
	SUCCESS: 8,     // 成功
	FAIL: 9,        // 失败
};

// 项目类型
export const ProjectTypeEnum = {
	public: 0,  // 公开项目
	private: 1, // 私有项目
};

// 渲染排序枚举
export const RenderOrderEnum = {
	result: {
		spaceOfPipe: 10,
		pipeline: 30,
		makePipe: 40,
		waterModel: 50,
		textureLine: 60,
		pipeLinePreview: 70,
	},
	solid: 100,
	face: 200,
	line: 300,
	point: 5,
	grid: 1,
};

// 任务状态枚举
export const TaskStatusEnum = {
	created: 0,	// 新建
	waiting: 1,	// 等待计算或离散中
	success: 8, // 成功
	error: 9,	// 失败
};

// 模块类型枚举
export const ModuleEnum = {
	mold: 'mold',	    // 随形冷却
	texture: 'texture', // 数字纹理
	shoe: 'shoe',       // 客制化鞋
};

// 纹理结果文件类型枚举
export const TextureResultTypeEnum = {
	visible2dWlLine: 'visible2dWlLine',     // 2D纹理线条模型
	visible2dWlVector: 'visible2dWlVector', // 2D纹理线条矢量
};

// 文件类型枚举
export const TextureFileTypeEnum = {
	'3dm': '3dm',
	'txt': 'txt',
	'ai': 'ai',
	'pdf': 'pdf',
	'iges': 'iges',
	'step': 'step',
	'stl': 'stl',
	'png': 'png',
};

// 离散方式枚举
export const DispersedWayEnum = {
	original: 'original',   // 自身文件(不离散)
	dispersed: 'dispersed', // c++离散
	rhino: 'rhino',         // 犀牛电池离散
};

// 水冷结果键
export const waterCoolingResultKeyMap = {
	pipeline: 'result_pipe_line',                           // 管线
	pipelineLength: 'pipelineLength',                       // 管线长度
	pipelineDesignSpace: 'result_design_space_of_pipe',     // 管线设计空间
	waterwayModel: 'result_include_water_model',            // 含水路模型
	waterwayModelLine: 'result_include_water_model_line',   // 含水路模型边框
	generatePipeline: 'result_make_pipe',                   // 生成管路
	generateTubePreview: 'result_pipe_line_preview',        // 生成管预览
};

// 参考平面
export const PlaneModeEnum = {
	planeXY: 'planeXY',
	planeYZ: 'planeYZ',
	planeXZ: 'planeXZ',
	planeDiyXY: 'planeDiyXY',
	planeDiyYZ: 'planeDiyYZ',
	planeDiyXZ: 'planeDiyXZ',
	curveNormalPlane: 'curveNormalPlane',
	curveTangentPlane: 'curveTangentPlane',
};
