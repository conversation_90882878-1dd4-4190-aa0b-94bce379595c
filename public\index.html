<!DOCTYPE html>
<html lang="en">
<head>
	<title>创客云客户端</title>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1.0">
	<link rel="icon" href="<%= BASE_URL %>favicon.ico">
	<link rel="stylesheet" type="text/css" href="<%= BASE_URL %>index.css">

	<!-- Include these early and directly so they happen first -->
	<script>
		// // <PERSON>gins loading the CAD Kernel Web Worker
		// if (window.Worker) {
		// 	window.mainWorker = new window.Worker('/js/test.worker.js', { type: 'module' });
		// 	window.messageHandlers = {};
		// 	window.mainWorker.onmessage = function (event) {
		// 		if (event.data.type in window.messageHandlers) {
		// 			// const response = window.messageHandlers[event.data.type](event.data.payload);
		// 			// if (response) { window.mainWorker.postMessage({ 'type': event.data.type, payload: response }); }
		// 		}
		// 	};
		// }
	</script>

	<script type="text/javascript">
		const sourceUrlList = [];

		function request (url) {
			return new Promise((resolve, reject) => {
				const xhr = new XMLHttpRequest();
				xhr.open('get', url, true);
				xhr.onreadystatechange = function () {
					if (xhr.readyState === 4) {
						if ((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304) {
							return resolve();
						}
						return reject();
					}
				};
				xhr.send(null);
			});
		}

		function loadCss (url) {
			return new Promise((resolve, reject) => {
				const newLinkTag = document.createElement('link');
				newLinkTag.rel = 'stylesheet';
				newLinkTag.href = url;
				newLinkTag.onload = resolve;
				newLinkTag.onerror = reject;
				document.head.appendChild(newLinkTag);
			});
		}

		function loadScript (url, el = undefined) {
			return new Promise((resolve, reject) => {
				const newScriptTag = document.createElement('script');
				newScriptTag.type = 'text/javascript';
				newScriptTag.src = url;
				newScriptTag.onload = resolve;
				newScriptTag.onerror = reject;
				if (el) {
					el.appendChild(newScriptTag);
				} else {
					document.head.appendChild(newScriptTag);
				}
			});
		}

		function init (type, urls, backupUrls) {
			urls = urls.split(',');
			backupUrls = backupUrls.split(',');

			return new Promise(resolve => {
				const checkUrl = urls[0];
				const urlLen = urls.length - 1;
				const backupUrlLen = backupUrls.length - 1;
				const done = (isDone) => isDone && resolve();

				const commonHandle = (i, url, totalLen) => {
					if (type === 'css') {
						loadCss(url).then(() => done(i === totalLen));
					} else {
						loadScript(url).then(() => done(i === totalLen));
					}
				};

				const commonForHandle = (list, totalLen) => {
					for (let i = 0; i < list.length; i++) {
						commonHandle(i, list[i], totalLen);
					}
				};

				request(checkUrl)
					.then(() => commonForHandle(urls, urlLen))
					.catch(() => commonForHandle(backupUrls, backupUrlLen));
			});
		}

		function resetScript () {
			const scriptEls = document.body.querySelectorAll('script') || [];
			scriptEls.forEach(el => {
				if (el && el.type) {
					const url = el.getAttribute('src');
					sourceUrlList.push(url);
					el.remove();
				}
			});
		}
	</script>
</head>
<body>
<% if (htmlWebpackPlugin.options.OPEN_CDN) { %>
<script>
		init('css', '<%= htmlWebpackPlugin.options.cdn.css %>', '<%= htmlWebpackPlugin.options.cdn.backupCss %>');
		setTimeout(() => resetScript(), 150);
		init('js', '<%= htmlWebpackPlugin.options.cdn.js %>', '<%= htmlWebpackPlugin.options.cdn.backupJs %>').then(() => {
			const timer = setInterval(() => {
				if (window.Vue && window.VueRouter && window.Vuex && window.axios && window.CryptoJS && window.NProgress) {
					for (const sourceUrl of sourceUrlList) {
						loadScript(sourceUrl, document.body);
					}
					clearInterval(timer);
				}
			}, 50);
		});
	</script>
<% } %>

<noscript>
	<strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
</noscript>

<div id="app">
	<div class="first-loading-wrp">
		<div class="loading-wrp">
			<span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
		</div>
		<div class="loading-title">正在加载，请耐心等待</div>
	</div>
</div>

<!-- require libs assets -->
<% for (var i in htmlWebpackPlugin.options.libs && htmlWebpackPlugin.options.libs.js) { %>
<script type="text/javascript" src="<%= htmlWebpackPlugin.options.libs.js[i] %>"></script>
<% } %>

<!-- built files will be auto injected -->
</body>
</html>
