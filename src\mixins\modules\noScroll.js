/* ===================================
 * 去除滚动条
 * Created by cjking on 2020/07/14.
 * Copyright 2020, Inc.
 * =================================== */
export const mixinNoScrollbar = {
	data () {
		return {
			disabledNoScrollInit: false,
		};
	},
	mounted () {
		if (!this.disabledNoScrollInit) {
			document.body.classList.add('no-scroll');
			// 通过hook监听组件销毁钩子函数
			this.$once('hook:beforeDestroy', () => {
				this.resetScrollBar();
			});
		}
	},
	methods: {
		/**
		 * 重置滚动条
		 */
		resetScrollBar () {
			document.body.classList.remove('no-scroll');
			setTimeout(() => {
				document.body.style.overflowY = 'auto';
			}, 300);
		},
	},
};
