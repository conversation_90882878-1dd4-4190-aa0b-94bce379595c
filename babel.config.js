// 按需加载
module.exports = {
	presets: [
		[
			'@vue/babel-preset-app',
			{
				'useBuiltIns': 'entry',
			},
		],
	],
	plugins: [
		['import', { 'libraryName': 'ant-design-vue', 'libraryDirectory': 'es', 'style': true }],
		// async/await 支持 npm install --save-dev babel-plugin-import
		'@babel/plugin-syntax-dynamic-import',
		// "非undefined且非null"判断 a ?? b
		'@babel/plugin-proposal-nullish-coalescing-operator',
		// Optional 可选链，核心作用是允许读取一个被连接对象的深层次的属性的值而无需明确校验链条上每一个引用的有效性
		// 可选链 使用 ?. 作为操作符 npm install --save-dev @babel/plugin-proposal-optional-chaining (不用自己安装？！)
		'@babel/plugin-proposal-optional-chaining',
	],
};
