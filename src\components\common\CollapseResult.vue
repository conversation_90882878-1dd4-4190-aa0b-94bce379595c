<template>
	<div class="collapse-attr">
		<a-collapse v-model="innerActiveKeys" expandIconPosition="right">
			<a-collapse-panel class="collapse-panel" :key="panelKey" :disabled="disabled">

				<template slot="header">
					<div class="collapse-panel-header">
						<!-- <div class="icon">
							<img :src="headerIcon" v-if="headerIcon">
						</div> -->
						<div class="title">{{ title }}</div>
						<div class="extra" @click="e => preventDefaults(e)">
							<slot name="extra"></slot>
						</div>
					</div>
				</template>

				<!-- 内容区域 -->
				<slot name="content">
					<attr-item
						v-for="attr in innerDataList"
						:key="attr.label + 'key'"
						:label="attr.label"
						:type="attr.type"
						:value="attr.value"
						:url="attr.url"
						@change="handleValueChange"
					/>
				</slot>

			</a-collapse-panel>
		</a-collapse>
	</div>
</template>

<script>
/* ===================================
 * 属性面板
 * Created by zhangzheng on 2022/3/2.
 * Copyright 2022, Inc.
 * =================================== */
import { deepCopy, isBase64 } from '@/utils/utils';
import AttrItem from '@/components/common/AttrItem.vue';

export default {
	name: 'CollapseTree',
	components: {
		AttrItem,
	},
	props: {
		title: {
			type: String,
			required: true,
		},
		dataList: {
			type: Array,
			required: true,
		},
		activeKeys: {
			type: Array,
			required: false,
			default: () => [],
		},
		panelKey: {
			type: String,
			required: true,
		},
		icon: {
			type: [String, undefined],
			required: false,
			default: undefined,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabledRightMenu: {
			type: Boolean,
			required: false,
			default: false,
		},
	},
	data () {
		return {
			innerDataList: [],
			innerActiveKeys: [],
		};
	},
	watch: {
		dataList: {
			immediate: true,
			deep: true,
			handler () {
				this.innerDataList = this.dataList;
			},
		},
		activeKeys: {
			immediate: true,
			handler (keys) {
				this.$nextTick(() => {
					if (this.defaultExpandAll) {
						this.innerActiveKeys = [this.panelKey];
					} else {
						this.innerActiveKeys = keys?.length ? keys : [];
					}
				});
			},
		},
	},
	computed: {
		headerIcon () {
			return this.getImgUrl(this.icon);
		},
	},
	methods: {
		/**
		 * 初始化数据
		 */
		initData (sourceList) {
			const tmpSourceList = deepCopy(sourceList);
			this.innerDataList = tmpSourceList;
		},
		/**
		 * 值变化
		 */
		handleValueChange (value) {
			this.$emit('result', value);
		},
		/**
		 * 根据文件名获取图片完整地址
		 */
		getImgUrl (filePath) {
			if (!filePath) {
				return '';
			}
			if (isBase64(filePath)) {
				return filePath;
			}
			// ../.. 相对于目录 src
			return require(`../../${ this.iconBasePath }/${ filePath }.png`);
		},
		/**
		 * 防止默认值
		 */
		preventDefaults (e) {
			e?.preventDefault();
			e?.stopPropagation();
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/mixins/variables.less";

.collapse-attr {

	/deep/ .ant-collapse > .ant-collapse-item {
		border-bottom: 3px solid #E5E5E5;
		.ant-collapse-header {
			border-left: 2px solid @primary;
			border-radius: 0;
			background: #F9F9FF;
			padding: 9px 16px;
			padding-right: 40px;
		}
	}

	.collapse-panel {

		.disable-selection();

		img {
			width: 20px;
			.disable-selection();

			&.w18 {
				width: 18px;
				height: 18px;
			}
		}

		.collapse-panel-header {
			display: flex;
			align-items: center;
			width: 280px;
			.icon {
				flex: 0 0 20px;
			}
			.title {
				flex: 1;
				margin-left: 8px;
				font-size: 16px;
				font-weight: bold;
				.disable-selection();
			}
			.extra {
				flex: 0 0 70px;
				text-align: end;
				.disable-selection();
			}
		}

		.tree-node-custom {
			display: flex;

			&.parent {
				width: 257px;
			}

			&.child {
				width: 231px;
			}

			.icon {
				flex: 0 0 20px;
			}
			.title {
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				&.ml8 {
					margin-left: 8px;
				}

				span {
					display: block;
					cursor: pointer;
					color: #000000;
					.disable-selection();
				}

				.opacity65 {
					color: rgba(0, 0, 0, 0.65);
				}

				.selected {
					color: @primary;
				}
			}
			.icon-actions {
				flex: 0 0 70px;
				text-align: end;

				img {
					margin-top: -2px;
					margin-right: 5px;
					cursor: pointer;

					&:last-child {
						margin-right: 0;
					}
				}
			}
		}
	}

	.line-vertical {
		position: absolute;
		left: 12px;
		top: -4px;
		width: 1px;
		height: 32px;
		background: rgba(58, 58, 58, 0.5);
		&.last {
			top: -8px;
			height: 20px;
		}
	}
	.line-horizontal {
		position: absolute;
		left: 12px;
		top: 12px;
		width: 14px;
		height: 1px;
		background: rgba(58, 58, 58, 0.5);
	}
}
</style>
