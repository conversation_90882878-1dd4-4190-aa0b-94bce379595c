import dayJs from 'dayjs';

export const storage = {
	/**
	 * key 保存键
	 * value 保存内容 localStorage 不能保存 Object 对象 需要使用 stringify 进行转换
	 * expired 失效时间 单位:分钟，默认60分钟
	 */
	set (key, value, expired) {
		/* 定义 source 临时 对象 临时存储 key value 赋值后 加入到 localStorage */
		const source = { key: key, value: value };

		/* now 获取当前时间 */
		const now = Date.now();
		/* 1分钟计算 (1000*60)  计算出总失效时间 (1000 * 60 * expired) now + 失效总分钟 算出最大存储时间*/
		if (expired) {
			source.value = JSON.stringify({ data: value, expired: now + (1000 * 60 * expired) });
		} else {
			source.value = JSON.stringify({ data: value, expired: now + (1000 * 60 * 60) });
		}
		localStorage.setItem(source.key, source.value);
	},
	get (key) {
		const now = Date.now();

		const source = { key: key, value: null };

		/* 获取 localStorage 存储信息 赋值给 source 对象 */
		source.value = JSON.parse(localStorage.getItem(source.key));

		/* 如果key有效  判断当前时间 是否超过 失效时间 */
		if (source.value) {
			if (now >= source.value.expired) {
				/* 超过失效时间 删除 储存内容 */
				this.remove(source.key);
				return undefined;
			} else {
				return source.value.data;
			}
		}
	},
	remove (key) {
		localStorage.removeItem(key);
	},
	clear () { // 清除本地存储中的所有值
		localStorage.clear();
	},
	expiredTime (key) {
		// 获取失效时间
		const now = Date.now();

		const source = { key: key, value: null };
		/* 从缓存中取出 信息*/
		source.value = JSON.parse(localStorage.getItem(source.key));

		/* 判断 key 是否失效 */
		if (source.value) {
			/* 获取失效时间 */
			const expired = source.value.expired;
			source.value.expired = source.value.expired = dayJs(expired).diff(dayJs(now), 'second');
			return source.value.expired;
		}
	},
};
