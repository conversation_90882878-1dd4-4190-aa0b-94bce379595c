/* ===================================
 * 指令：防抖函数 (如果一个函数持续地触发，那么只在它结束后过一段时间只执行一次)
 * Created by cjking on 2020/10/21.
 * Copyright 2020, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { debounce, noop, isObject, isFunction, isString } from '@/utils/utils';

export const DebounceDirective = Vue => Vue.directive('debounce', {
	bind (el, binding, vNode) {
		const defaultConfig = {
			fun: '',
			params: undefined,
			wait: 300,
			event: 'input',
		};
		if (isObject(binding.value)) {
			Object.assign(defaultConfig, binding.value);
		} else if (isFunction(binding.value)) {
			defaultConfig.fun = binding.expression;
		}
		const context = vNode.context;

		if (isString(defaultConfig.fun) && !isFunction(context[defaultConfig.fun])) {
			logger.warn(`方法名【${ defaultConfig.fun }】在组件中未定义`);
			return;
		}

		el.removeEventListener(defaultConfig.event, noop);
		el.addEventListener(defaultConfig.event, debounce(e => {
			if (isFunction(defaultConfig.fun)) {
				defaultConfig.fun.call(null, defaultConfig.params);
			} else {
				context[defaultConfig.fun].call(null, defaultConfig.params);
			}
		}, defaultConfig.wait));
	},
});
