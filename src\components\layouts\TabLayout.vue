<template>
	<div>
		<template v-if="type === LayoutTypeEnum.ONE">
			<a-layout>
				<global-header />
				<a-layout-content :style="{ padding: '0', marginTop: '70px' }">
					<router-view />
				</a-layout-content>
				<!-- <global-footer /> -->
			</a-layout>
		</template>
		<template v-else>
			<router-view />
		</template>
	</div>
</template>

<script>
/* ===================================
 * 共用布局页
 * Created by cjking on 2021/04/08.
 * Copyright 2021, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import GlobalHeader from '@/components/page/GlobalHeader';
// import GlobalFooter from '@/components/page/GlobalFooter';

const LayoutTypeEnum = {
	ONE: 1, // 布局含有页头、页脚
	TWO: 2, // 布局不含有页头、页脚
};

export default {
	name: 'TabLayout',
	components: {
		GlobalHeader,
		// GlobalFooter,
	},
	data () {
		return {
			LayoutTypeEnum,
		};
	},
	computed: {
		type () {
			logger.log('meta: ', this.$route.meta);
			return this.$route.meta?.type || LayoutTypeEnum.ONE;
		},
	},
};
</script>
