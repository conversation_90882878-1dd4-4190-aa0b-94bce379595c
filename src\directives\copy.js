import { message } from 'ant-design-vue';
import { copyToClipBoard, noop } from '@/utils/utils';

export const CopyDirective = Vue => Vue.directive('copy', {
	/**
	 * bind 钩子函数，第一次绑定时调用，可以在这里做初始化设置
	 * @param el 作用的 dom 对象
	 * @param value 传给指令的值，也就是我们要 copy 的值
	 */
	bind (el, { value }) {
		// el.$value = value; // 用一个全局属性来存传进来的值，因为这个值在别的钩子函数里还会用到
		const handler = () => {
			if (!value) { // 值为空的时候，给出提示
				message.warning('无复制内容');
				return;
			}
			copyToClipBoard(value);
		};
		el.removeEventListener('click', noop);
		// 绑定点击事件，就是所谓的一键copy
		el.addEventListener('click', handler);
	},

	/**
	 * 当传进来的值更新的时候触发
	 */
	// componentUpdated (el, { value }) {
	// 	el.$value = value;
	// },

	/**
	 * 指令与元素解绑的时候，移除事件绑定
	 */
	// unbind (el) {
	// 	el.removeEventListener('click', el.handler);
	// }
});
