<template>
	<div class="attr-item">
		<div class="attr-item-title" v-if="label">
			<div>
				<label style="margin-right: 6px;">
					<b><span
						v-if="required"
						style="margin-left: -6px; color:red"
					>*</span>{{ label }}{{ label ? ':' : '' }}</b>
				</label>
				<a-tooltip v-if="tip" placement="top">
					<template slot="title">
						{{ tip }}
					</template>
					<a-icon type="question-circle"></a-icon>
				</a-tooltip>
			</div>
		</div>
		<div class="attr-item-content">
			<div class="image-dropdown">
				<a-icon
					v-show="fieldValue > 0"
					type="left"
					@click="(e) => { handleValueChange(e, fieldValue - 1)}"
				/>
				<a-icon
					v-show="fieldValue === 0"
					type="left"
					:style="{color: '#CCCCCC'}"
				/>
				<a-popover v-model="visible" :destroyTooltipOnHide="true" :arrowPointAtCenter="true" trigger="click" :overlayStyle="{maxWidth: '960px'}" placement="right">
					<template slot="title">
						<div style="display:flex;justify-content:space-between;align-content:center;align-items:center;line-height: 36px">
							<b>请选择纹理类型</b>
							<a-icon type="close" @click="visible=false" :style="{color: '#AAAAAA'}"></a-icon>
						</div>
					</template>
					<template slot="content">
						<div class="dropdown-content">
							<a-list :grid="{ gutter: 0, column: 3 }" :pagination="pagination" :data-source="dataSource">
								<a-list-item slot="renderItem" slot-scope="item">
									<div
										:class="{ 'image-card2': true, 'image-card2-active': item.title === selectedValue.title }"
										@click="(e) => { handleValueChange(e, item.value) }"
									>
										<img :src="item.url" alt="">
										<div>{{ item.title }}</div>
									</div>
								</a-list-item>
							</a-list>
						</div>
					</template>
					<div class="image-card">
						<div>
							<img :src="selectedValue.url" alt="">
						</div>
						<div>
							<h4>{{ selectedValue.title }}</h4>
							<span :title="selectedValue.desc">{{ selectedValue.desc }}</span>
						</div>
					</div>
				</a-popover>
				<a-icon
					v-show="fieldValue < dataSource.length - 1"
					type="right"
					@click="(e) => { handleValueChange(e, fieldValue + 1)}"
				/>
				<a-icon
					v-show="fieldValue === dataSource.length - 1"
					type="right"
					:style="{color: '#CCCCCC'}"
				/>
			</div>

		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/4/26.
 * Copyright 2022, Inc.
 * =================================== */
import A01 from '@/assets/img/texture/A01.png';
import A02 from '@/assets/img/texture/A02.png';
import A03 from '@/assets/img/texture/A03.png';
import A04 from '@/assets/img/texture/A04.png';
import A05 from '@/assets/img/texture/A05.png';
import A06 from '@/assets/img/texture/A06.png';
import A07 from '@/assets/img/texture/A07.png';
import A08 from '@/assets/img/texture/A08.png';
import A09 from '@/assets/img/texture/A09.png';
import A10 from '@/assets/img/texture/A10.png';
import A11 from '@/assets/img/texture/A11.png';
import A12 from '@/assets/img/texture/A12.png';
import A13 from '@/assets/img/texture/A13.png';
import A14 from '@/assets/img/texture/A14.png';
import A15 from '@/assets/img/texture/A15.png';
import A16 from '@/assets/img/texture/A16.png';
import A17 from '@/assets/img/texture/A17.png';
import A18 from '@/assets/img/texture/A18.png';

export default {
	props: {
		required: {
			type: Boolean,
			required: false,
			default: false,
		},
		title: {
			type: String,
			required: false,
			default: '',
		},
		label: {
			type: String,
			required: false,
			default: '',
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
		name: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: Number,
			required: false,
			default: null,
		},
	},
	data () {
		return {
			visible: false,
			fieldValue: undefined,
			dataSource: [
				{
					url: A01,
					name: 'A01',
					value: 0,
					title: '波浪结构',
					desc: '菱形线网到波浪线网',
				},
				{
					url: A02,
					name: 'A02',
					value: 1,
					title: '线网结构',
					desc: '菱形线网到四边形线网',
				},
				{
					url: A03,
					name: 'A03',
					value: 2,
					title: '孔状结构',
					desc: '圆孔阵列到星形阵列',
				},
				{
					url: A04,
					name: 'A04',
					value: 3,
					title: '花形结构',
					desc: '闭合到绽放',
				},
				{
					url: A05,
					name: 'A05',
					value: 4,
					title: '脑纹结构',
					desc: '仅跟随单元边长变化',
				},
				{
					url: A06,
					name: 'A06',
					value: 5,
					title: '波点结构',
					desc: '圆点到圆角方点',
				},
				{
					url: A07,
					name: 'A07',
					value: 6,
					title: '拼花结构',
					desc: '元素拼花分布',
				},
				{
					url: A08,
					name: 'A08',
					value: 7,
					title: '网格结构',
					desc: '四边形变化到菱形',
				},
				{
					url: A09,
					name: 'A09',
					value: 8,
					title: '渐消结构',
					desc: '元素渐消分布',
				},
				{
					url: A10,
					name: 'A10',
					value: 9,
					title: '风车结构1',
					desc: '静态网格到旋转造型',
				},
				{
					url: A11,
					name: 'A11',
					value: 10,
					title: '风车结构2',
					desc: '旋转网格到四边形结构',
				},
				{
					url: A12,
					name: 'A12',
					value: 11,
					title: '风车结构3',
					desc: '十字网格到旋转造型',
				},
				{
					url: A13,
					name: 'A13',
					value: 12,
					title: '风车结构4',
					desc: '十字网格到龟裂纹',
				},
				{
					url: A14,
					name: 'A14',
					value: 13,
					title: '风车结构5',
					desc: '旋转造型到菱形网格',
				},
				{
					url: A15,
					name: 'A15',
					value: 14,
					title: '风车结构6',
					desc: '旋转变化到菱形网格',
				},
				{
					url: A16,
					name: 'A16',
					value: 15,
					title: '风车结构7',
					desc: '十字造型开始旋转',
				},
				{
					url: A17,
					name: 'A17',
					value: 16,
					title: '风车结构8',
					desc: '菱形进行旋转变换',
				},
				{
					url: A18,
					name: 'A18',
					value: 17,
					title: '风车结构9',
					desc: '旋转造型回缩至菱形网格',
				},
			],
			pagination: {
				onChange: page => {
					this.currentPage = page;
				},
				pageSize: 6,
			},
			selectedValue: {},
		};
	},
	watch: {
		value: {
			deep: true,
			immediate: true,
			handler (newVal) {
				this.fieldValue = newVal;
				if (!this.fieldValue) {
					this.fieldValue = 0;
				}
				this.selectedValue = this.dataSource.find(i => i.value === this.fieldValue);
			},
		},
	},
	methods: {
		handleValueChange (e, value) {
			this.$emit('valueChange', { value, key: this.name });
			this.visible = false;
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.attr-item {
	/deep/ .ant-slider-mark-text {
		top: -34px;
	}

	&-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	&-title-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;

		> * {
			margin-left: 10px;
		}
	}

	&-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 1px;
		border: 1px solid #CCCCCC;
		padding: 0 10px;
		margin-bottom: 16px;
		min-height: 40px;
	}

	&-icon {
		width: 20px;
		height: 20px;
	}

	&-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.svg-icon,
		input {
			margin-left: 10px;
			cursor: pointer;
		}
	}

	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}

	.svg-icon {
		color: @primary;
	}
}

.image-dropdown {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	align-content: center;
	align-items: center;
}

.dropdown-content {
	max-width: 388px;
	max-height: 268px;
	padding: 4px 0;

	.image-card2 {
		cursor: pointer;

		&:hover {
			border-color: #FF7020;
		}
	}

	.image-card2-active {
		border-color: #FF7020;
	}

	/deep/ .ant-list-pagination {
		margin-top: 12px;
		text-align: center;
	}
}

.image-card {
	width: 230px;
	height: 70px;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-content: center;
	align-items: center;

	div {
		margin: 4px;

		img {
			width: 75px;
			height: 50px;
			border: solid rgba(0, 0, 0, 0) 1px;

			&:hover {
				cursor: pointer;
				border: solid #FF7020 1px;
			}
		}

		h4 {
			font-weight: 600;
		}

		span {
			display: block;
			width: 140px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}

.image-card2 {
	position: relative;
	width: 120px;
	height: 80px;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-content: center;
	align-items: center;
	margin: 0 4px;
	border: solid #CCCCCC 1px;
	border-radius: 1px;

	img {
		width: 120px;
		height: 80px;
		z-index: 100;
	}

	div {
		position: absolute;
		left: 0;
		bottom: 0;
		width: 118px;
		height: 24px;
		text-align: center;
		z-index: 200;
		color: #FFFFFF;
		background-color: rgba(0, 0, 0, 0.6);
	}
}
</style>
