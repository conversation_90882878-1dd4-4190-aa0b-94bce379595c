<template>
	<a-input :placeholder="placeholder" :value="inputVal" @input="backValue" :allowClear="allowClear"></a-input>
</template>

<script>
import { trim } from '@/utils/utils';

const INPUT_QUERY_LIKE = 'like';	// 包含(模糊查询)
const INPUT_QUERY_NE = 'ne';		// 不等于
const INPUT_QUERY_GE = 'ge'; 		// 大于等于
const INPUT_QUERY_LE = 'le'; 		// 小于等于

export default {
	name: 'JInput',
	props: {
		value: {
			type: String,
			required: false,
			default: undefined,
		},
		type: {
			type: String,
			required: false,
			default: INPUT_QUERY_LIKE,
		},
		allowClear: {
			type: Boolean,
			required: false,
			default: true,
		},
		placeholder: {
			type: String,
			required: false,
			default: '',
		},
	},
	watch: {
		value: {
			immediate: true,
			handler () {
				this.initVal();
			},
		},
	},
	model: {
		prop: 'value',
		event: 'change.value',
	},
	data () {
		return {
			inputVal: '',
		};
	},
	methods: {
		initVal () {
			if (!this.value) {
				this.inputVal = '';
			} else {
				let text = this.value;
				switch (this.type) {
					case INPUT_QUERY_LIKE:
						text = text.substring(1, text.length - 1);
						break;
					case INPUT_QUERY_NE:
						text = text.substring(1);
						break;
					case INPUT_QUERY_GE:
						text = text.substring(2);
						break;
					case INPUT_QUERY_LE:
						text = text.substring(2);
						break;
					default:
				}
				this.inputVal = text;
			}
		},
		backValue (e) {
			let text = trim(e.target.value);
			switch (this.type) {
				case INPUT_QUERY_LIKE:
					text = text ? '*' + text + '*' : '';
					break;
				case INPUT_QUERY_NE:
					text = text ? '!' + text : '';
					break;
				case INPUT_QUERY_GE:
					text = text ? '>=' + text : '';
					break;
				case INPUT_QUERY_LE:
					text = text ? '<=' + text : '';
					break;
				default:
			}
			this.$emit('change', e);
			this.$emit('change.value', text);
		},
	},
};
</script>
