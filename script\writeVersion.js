const fs = require('fs');
const path = require('path');

const packageJson = path.resolve(__dirname, '..', 'package.json');
const distDir = path.resolve(__dirname, '..', 'dist');
const versionFile = path.resolve(distDir, 'version.js');

if (!fs.existsSync(distDir)) {
	fs.mkdirSync(distDir);
}

const version = require(packageJson).version || 'master';
fs.writeFileSync(versionFile, `window.GEN_GUEST_CLOUD_CLIENT_VERSION = "${ version }";\n`);
