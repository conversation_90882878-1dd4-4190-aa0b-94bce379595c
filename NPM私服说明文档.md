# NPM私服说明文档


## 安装镜像源管理工具`nrm`
1、安装

```
npm install -g nrm
```

2、添加镜像私服地址
```
nrm add sf http://192.168.70.204:5201
or
npm set registry http://192.168.70.204:5201 # 适合Jenkins

# 线上私服
nrm add sfProd https://npm-registry.fangzhenxiu.com
nrm add sfProd https://sf.fangzhenxiu.com (即将废弃)
or
npm set registry http://sf.fangzhenxiu.com # 适合Jenkins
```

2、使用私服地址
```
nrm use sf
```

3、完成，找个项目试试吧！
```
cd projectPath

npm cache clean --force     # 清理npm缓存

npm install                 # 安装npm包

```

其他：
```
$ nrm ls

* npm -----  https://registry.npmjs.org/
  yarn ----- https://registry.yarnpkg.com
  cnpm ----  http://r.cnpmjs.org/
  taobao --  https://registry.npm.taobao.org/
  nj ------  https://registry.nodejitsu.com/
  skimdb --  https://skimdb.npmjs.com/registry
  sf --      http://192.168.70.204:5201/
```
