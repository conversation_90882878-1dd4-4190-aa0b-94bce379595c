<template>
	<div>
		<div class="attr-item">
			<div class="attr-item-title" v-if="label">
				<div>
					<label style="margin-right: 6px;">
						<b><span
							v-if="required"
							style="margin-left: -6px;color:red"
						>*</span>{{ label }}{{ label ? ':' : '' }}</b>
					</label>
					<a-tooltip v-if="tip" placement="top">
						<template slot="title">
							{{ tip }}
						</template>
						<a-icon type="question-circle"></a-icon>
					</a-tooltip>
				</div>
			</div>
			<div class="attr-item-content">
				<a-radio-group v-model="fieldValue" @change="handleValueChange" :options="options">
					<!-- <a-radio v-for="option in config.options" :key="option.label" :value="option.value">
						{{ option.label }}
					</a-radio> -->
				</a-radio-group>
			</div>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/4/26.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
export default {
	components: {
	},
	props: {
		required: {
			type: Boolean,
			required: false,
			default: false,
		},
		title: {
			type: String,
			required: false,
			default: '',
		},
		label: {
			type: String,
			required: false,
			default: '',
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
		name: {
			type: String,
			required: false,
			default: '',
		},
		value: {
			type: [String, Number, Boolean],
			required: false,
			default: 0,
		},
		options: {
			type: Array,
			required: true,
		}
	},
	data () {
		return {
			fieldValue: false,
		};
	},
	watch: {
		value: {
			deep: true,
			immediate: true,
			handler (newVal) {
				this.fieldValue = newVal;
			},
		},
	},
	methods: {
		handleValueChange (e) {
			const value = e.target.value;
			this.$emit('valueChange', { value, name: this.name });
		}
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.attr-item {
	/deep/ .ant-slider-mark-text {
		top: -34px
	}
	&-title, &-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	&-title-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		> * {
			margin-left: 10px;
		}
	}
	&-content {
		background-color: white;
		padding: 10px;
		min-height: 40px;
	}
	&-content2 {
		padding: 10px;
		height: 40px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}
	&-icon {
		width: 20px;
		height: 20px;
	}
	&-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.svg-icon, input {
			margin-left: 10px;
			cursor: pointer;
		}
	}
	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}
	.svg-icon {
		color: @primary;
	}
}
</style>
