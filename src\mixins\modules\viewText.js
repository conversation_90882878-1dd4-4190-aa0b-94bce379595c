/* ===================================
 * vtkView视图文本设置
 * Created by cjking on 2022/03/11.
 * Copyright 2022, Inc.
 * =================================== */
import * as THREE from 'three';
import { hasOwnProperty } from '@/utils/utils';

export const mixinViewText = {
	methods: {
		/**
		 * 初始化文本
		 */
		initText () {
			// // 在3D空间中表示一个盒子或立方体。其主要用于表示物体在世界坐标中的边界框
			// const box = new THREE.Box3();
			// // 通过传入的object3D对象来返回当前模型的最小大小，值可以使一个mesh也可以使group
			// const group = new THREE.Group();
			// raycasterObjs.forEach(mesh => {
			//     group.add(mesh);
			// });
			// scene.add(group);
			// box.expandByObject(group);
			// const boxHelper = new THREE.BoxHelper(group, 0xffff00);
			// console.log('boxHelper: ', boxHelper)
			// scene.add(boxHelper);
			// boxHelper.geometry.computeBoundingBox();
			// console.log('boundingBox: ', boxHelper.geometry.boundingBox);
			//
			// const centerVector = boxHelper.geometry.boundingSphere.center;
			// console.log('centerVector: ', centerVector);
			// makeCenterSphere(centerVector.x, centerVector.y, centerVector.z);
			//
			// // 制作中心球体
			// function makeCenterSphere(x, y, z) {
			//     const geometry = new THREE.SphereGeometry(5, 100, 100);
			//     const material = new THREE.MeshBasicMaterial({color: 0xffff00});
			//     const sphere = new THREE.Mesh(geometry, material);
			//     sphere.position.x = x;
			//     sphere.position.y = y;
			//     sphere.position.z = z;
			//     scene.add(sphere);
			// }
			//
			// //////////////////////////////////////////////////// start //////////////////////////////////////////////////////////////////////
			// // 1米=10分米 1分米=10厘米 1厘米=10毫米 即：1米=10分米=100厘米=1000毫米
			// // const minVector = boxHelper.geometry.boundingBox.min;
			// // const maxVector = boxHelper.geometry.boundingBox.max;
			// // const minArr = [minVector.x, minVector.y, minVector.z]; // min（左下角，前面）
			// // const maxArr = [maxVector.x, maxVector.y, maxVector.z]; // max（右上角，后面）
			// // console.log('minArr: ', minArr);
			// // console.log('maxArr: ', maxArr);
			// // const long = Math.max(...[minArr[0], maxArr[0]]);
			// // const width = Math.max(...[minArr[1], maxArr[1]]);
			// // const height = Math.abs(Math.min(...[minArr[2], maxArr[2]]));
			//
			// const long = Math.abs(centerVector.x * 2);
			// const width = Math.abs(centerVector.y * 2);
			// const height = Math.abs(centerVector.z * 2);
			// makeMeshSize(scene, long, width, height, long >= 1000);
			// scene.remove(boxHelper);
			// ///////////////////////////////////////////////////// end ///////////////////////////////////////////////////////////////////////

			// 制作网格尺寸
			function makeMeshSize (scene, long, width, height, gte1000MM) {
				const hex = 0xFFFFFF;
				const headLength = gte1000MM ? 25 : 25 / 50;
				const headWidth = gte1000MM ? 10 : 10 / 50;

				// 长（原点）
				// const longOrigin = new THREE.Vector3(long / 2, 0, height + height * (1 / 100));
				const longOrigin = new THREE.Vector3(long / 2, 0, 40);
				// 长（左箭头）
				const longLeftArrow = new THREE.ArrowHelper(new THREE.Vector3(1, 0, 0).negate(), longOrigin, long / 2 - 1, hex, headLength, headWidth);
				// 长（右箭头）
				const longRightArrow = new THREE.ArrowHelper(new THREE.Vector3(1, 0, 0).normalize(), longOrigin, long / 2 - 1, hex, headLength, headWidth);
				scene.add(longLeftArrow, longRightArrow);

				// 宽（原点）
				const widthOrigin = new THREE.Vector3(0, width / 2, 40);
				// 宽（左箭头）
				const widthLeftArrow = new THREE.ArrowHelper(new THREE.Vector3(0, 1, 0).negate(), widthOrigin, width / 2 - 1, hex, headLength, headWidth);
				// 宽（右箭头）
				const widthRightArrow = new THREE.ArrowHelper(new THREE.Vector3(0, 1, 0).normalize(), widthOrigin, width / 2 - 1, hex, headLength, headWidth);
				scene.add(widthLeftArrow, widthRightArrow);

				// 高（原点）
				const heightOrigin = new THREE.Vector3(0, -40, -height / 2);
				// 高（左箭头）
				const heightLeftArrow = new THREE.ArrowHelper(new THREE.Vector3(0, 0, 1).negate(), heightOrigin, height / 2 - 1, hex, headLength, headWidth);
				// 高（右箭头）
				const heightRightArrow = new THREE.ArrowHelper(new THREE.Vector3(0, 0, 1).normalize(), heightOrigin, height / 2 - 1, hex, headLength, headWidth);
				scene.add(heightLeftArrow, heightRightArrow);

				const textColor = { r: 255, g: 255, b: 255, a: 1 };
				makeTextSprite(mmToM(long, gte1000MM), { textColor, position: { x: long / 2, y: 20, z: 40 }, fontSize: gte1000MM ? 30 : 12 });
				makeTextSprite(mmToM(width, gte1000MM), { textColor, position: { x: 40, y: width / 2, z: 20 }, fontSize: gte1000MM ? 30 : 12 });
				makeTextSprite(mmToM(height, gte1000MM), { textColor, position: { x: 20, y: -40, z: -height / 2 }, fontSize: gte1000MM ? 30 : 12 });
			}

			// 制作文字
			function makeTextSprite (message, parameters) {
				if (!parameters) parameters = {};

				const fontFace = hasOwnProperty(parameters, 'fontFace') ? parameters['fontFace'] : 'Arial';

				const fontSize = hasOwnProperty(parameters, 'fontSize') ? parameters['fontSize'] : 30;

				const borderThickness = hasOwnProperty(parameters, 'borderThickness') ? parameters['borderThickness'] : 4;

				const borderColor = hasOwnProperty(parameters, 'borderColor') ? parameters['borderColor'] : { r: 0, g: 0, b: 0, a: 1.0 };

				const backgroundColor = hasOwnProperty(parameters, 'backgroundColor') ? parameters['backgroundColor'] : { r: 255, g: 255, b: 255, a: 1.0 };

				const textColor = hasOwnProperty(parameters, 'textColor') ? parameters['textColor'] : { r: 255, g: 255, b: 255, a: 1.0 };

				const position = hasOwnProperty(parameters, 'position') ? parameters['position'] : { x: 0, y: 0, z: 0 };

				const canvas = document.createElement('canvas');
				const context = canvas.getContext('2d');
				context.font = 'Bold ' + fontSize + 'px ' + fontFace;

				// background color
				context.fillStyle = 'rgba(' + backgroundColor.r + ',' + backgroundColor.g + ',' + backgroundColor.b + ',' + backgroundColor.a + ')';
				// border color
				context.strokeStyle = 'rgba(' + borderColor.r + ',' + borderColor.g + ',' + borderColor.b + ',' + borderColor.a + ')';

				context.lineWidth = borderThickness;

				// text color
				context.fillStyle = 'rgba(' + textColor.r + ',' + textColor.g + ',' + textColor.b + ',' + textColor.a + ')';

				context.fillText(message, borderThickness, fontSize + borderThickness);

				// canvas contents will be used for a texture
				const texture = new THREE.Texture(canvas);
				texture.needsUpdate = true;

				const spriteMaterial = new THREE.SpriteMaterial({ map: texture, useScreenCoordinates: false });
				const sprite = new THREE.Sprite(spriteMaterial);
				sprite.scale.set(100, 50, 1.0);

				sprite.position.x = position.x || 0;
				sprite.position.y = position.y || 0;
				sprite.position.z = position.z || 0;

				this.getScene().add(sprite);

				return sprite;
			}

			// 毫米转米, 是否转换
			function mmToM (mm, isConvert) {
				return isConvert ? (mm / 1000 + 'M') : (mm + 'MM');
			}
		},
	},
};
