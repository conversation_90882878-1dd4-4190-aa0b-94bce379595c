#!/usr/bin/python
# -*-coding:UTF-8-*-
###################################
# 日志工具类
# Created by cjking on 2020/07/05.
###################################
import color
import logging


# 颜色处理
def handleColor(self, p_color, **params):
	if not ("in_color" in params):
		params['in_color'] = p_color
	else:
		params['in_color'] = color.getColor(params['in_color'])
	self.fontColor(**params)


# 日志插件
class Logger(object):
	"""
	终端打印不同颜色的日志，在pycharm中如果强行规定了日志的颜色， 这个方法不会起作用， 但是
	对于终端，这个方法是可以打印不同颜色的日志的。
	"""

	# 设置日志级别，默认为logging.WARNNING；
	logging.root.setLevel(logging.DEBUG)

	# 输出到控制台
	# 在这里定义StreamHandler，可以实现单例， 所有的logger()共用一个StreamHandler
	ch = logging.StreamHandler()

	def __init__(self):
		self.logger = logging.getLogger()

	def debug(self, message, **params):
		handleColor(self, color.getColor('green'), **params)
		self.logger.debug(message)

	def info(self, message, **params):
		handleColor(self, color.getColor('green'), **params)
		self.logger.info(message)

	def warning(self, message, **params):
		handleColor(self, color.getColor('gray'), **params)
		self.logger.warning(message)

	def error(self, message, **params):
		handleColor(self, color.getColor('red'), **params)
		self.logger.error(message)

	def critical(self, message, **params):
		handleColor(self, '\033[0;35m%s\033[0m', **params)
		self.logger.critical(message)

	def choose(self, message, my_color=None):
		if my_color is None:
			my_color = 'green'
		handleColor(self, color.getColor(my_color), show_time_and_level=False)
		self.logger.info(message)

	def fontColor(self, **params):
		if not ("show_time_and_level" in params):
			params['show_time_and_level'] = True

		# 不同的日志输出不同的颜色
		if params['show_time_and_level']:
			formatter = logging.Formatter(params['in_color'] % '[%(asctime)s] - [%(levelname)s] - %(message)s')
		else:
			formatter = logging.Formatter(params['in_color'] % '%(message)s')

		# 设置日志输出格式
		self.ch.setFormatter(formatter)
		# 添加到logger对象里
		self.logger.addHandler(self.ch)

# Usage
# if __name__ == "__main__":
#     logger = Logger()
#     logger.info("12345")
#     logger.debug("12345")
#     logger.warning("12345")
#     logger.error("12345")
