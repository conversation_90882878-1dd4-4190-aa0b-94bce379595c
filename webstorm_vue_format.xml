<code_scheme name="webstorm_vue_format" version="173">
	<option name="OTHER_INDENT_OPTIONS">
		<value>
			<option name="USE_TAB_CHARACTER" value="true"/>
			<option name="SMART_TABS" value="true"/>
		</value>
	</option>
	<option name="RIGHT_MARGIN" value="240"/>
	<CssCodeStyleSettings>
		<option name="HEX_COLOR_UPPER_CASE" value="true"/>
		<option name="ENFORCE_QUOTES_ON_FORMAT" value="true"/>
	</CssCodeStyleSettings>
	<HTMLCodeStyleSettings>
		<option name="HTML_UNIFORM_INDENT" value="true"/>
		<option name="HTML_ATTRIBUTE_WRAP" value="0"/>
		<option name="HTML_KEEP_BLANK_LINES" value="1"/>
		<option name="HTML_SPACE_INSIDE_EMPTY_TAG" value="true"/>
		<option name="HTML_DO_NOT_INDENT_CHILDREN_OF" value="html"/>
		<option name="HTML_ENFORCE_QUOTES" value="true"/>
	</HTMLCodeStyleSettings>
	<JSCodeStyleSettings version="0">
		<option name="FORCE_SEMICOLON_STYLE" value="true"/>
		<option name="USE_DOUBLE_QUOTES" value="false"/>
		<option name="FORCE_QUOTE_STYlE" value="true"/>
		<option name="ENFORCE_TRAILING_COMMA" value="WhenMultiline"/>
		<option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true"/>
		<option name="SPACES_WITHIN_IMPORTS" value="true"/>
		<option name="USE_CHAINED_CALLS_GROUP_INDENTS" value="true"/>
		<option name="SPACES_WITHIN_INTERPOLATION_EXPRESSIONS" value="true"/>
	</JSCodeStyleSettings>
	<LessCodeStyleSettings>
		<option name="HEX_COLOR_UPPER_CASE" value="true"/>
		<option name="BLANK_LINES_AROUND_NESTED_SELECTOR" value="0"/>
		<option name="KEEP_SINGLE_LINE_BLOCKS" value="true"/>
		<option name="ENFORCE_QUOTES_ON_FORMAT" value="true"/>
	</LessCodeStyleSettings>
	<TypeScriptCodeStyleSettings version="0">
		<option name="ENFORCE_TRAILING_COMMA" value="WhenMultiline"/>
	</TypeScriptCodeStyleSettings>
	<codeStyleSettings language="HTML">
		<option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false"/>
		<option name="BLOCK_COMMENT_AT_FIRST_COLUMN" value="false"/>
		<indentOptions>
			<option name="CONTINUATION_INDENT_SIZE" value="4"/>
			<option name="USE_TAB_CHARACTER" value="true"/>
			<option name="SMART_TABS" value="true"/>
		</indentOptions>
	</codeStyleSettings>
	<codeStyleSettings language="JSON">
		<indentOptions>
			<option name="INDENT_SIZE" value="4"/>
			<option name="CONTINUATION_INDENT_SIZE" value="4"/>
			<option name="USE_TAB_CHARACTER" value="true"/>
			<option name="SMART_TABS" value="true"/>
		</indentOptions>
	</codeStyleSettings>
	<codeStyleSettings language="JavaScript">
		<option name="KEEP_FIRST_COLUMN_COMMENT" value="false"/>
		<option name="KEEP_BLANK_LINES_IN_CODE" value="1"/>
		<option name="SPACE_BEFORE_METHOD_PARENTHESES" value="true"/>
		<option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true"/>
		<option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true"/>
		<indentOptions>
			<option name="USE_TAB_CHARACTER" value="true"/>
			<option name="SMART_TABS" value="true"/>
		</indentOptions>
	</codeStyleSettings>
	<codeStyleSettings language="LESS">
		<option name="KEEP_BLANK_LINES_IN_CODE" value="1"/>
		<indentOptions>
			<option name="INDENT_SIZE" value="4"/>
			<option name="USE_TAB_CHARACTER" value="true"/>
			<option name="SMART_TABS" value="true"/>
		</indentOptions>
	</codeStyleSettings>
	<codeStyleSettings language="SCSS">
		<indentOptions>
			<option name="INDENT_SIZE" value="4"/>
		</indentOptions>
	</codeStyleSettings>
	<codeStyleSettings language="Shell Script">
		<indentOptions>
			<option name="INDENT_SIZE" value="4"/>
			<option name="TAB_SIZE" value="4"/>
			<option name="USE_TAB_CHARACTER" value="true"/>
		</indentOptions>
	</codeStyleSettings>
	<codeStyleSettings language="Stylus">
		<indentOptions>
			<option name="INDENT_SIZE" value="4"/>
		</indentOptions>
	</codeStyleSettings>
	<codeStyleSettings language="Vue">
		<indentOptions>
			<option name="INDENT_SIZE" value="4"/>
			<option name="TAB_SIZE" value="4"/>
			<option name="USE_TAB_CHARACTER" value="true"/>
			<option name="SMART_TABS" value="true"/>
		</indentOptions>
	</codeStyleSettings>
</code_scheme>
