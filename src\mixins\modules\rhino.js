/* ===================================
 * rhino计算
 * Created by cjking on 2022/03/11.
 * Copyright 2022, Inc.
 * =================================== */
import JSZip from 'jszip';
import * as THREE from 'three';
import * as Comlink from 'comlink';
import Config from '@/config/Config';
import { loadFile } from '@/api';
import { logger } from '@/utils/logger';
import {
	RenderOrderEnum, RESULT_WATER_COOLING_GROUP,
	TextureResultTypeEnum, WIREFRAME, waterCoolingResultKeyMap,
	ColorEnum, DISPERSED_LINE_GROUP, FACE_GROUP, TEXTURE_GUIDED_POINT, SOLID_GROUP, RESULT_WL_LINE_GROUP,
} from '@/constants';
import { sleep, isArray, isString, hasOwnProperty, basename, noop, isEmpty, isObject } from '@/utils/utils';
import { getWireframeByFace, getWireframeByFaces, makeLine, setPolygonOffset } from '@/components/core/ViewUtils';
import { Line2 } from 'three/examples/jsm/lines/Line2';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';
import { Rhino3dmLoader } from 'three/examples/jsm/loaders/3DMLoader.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { LineSegments2 } from 'three/examples/jsm/lines/LineSegments2';
import { LineSegmentsGeometry } from 'three/examples/jsm/lines/LineSegmentsGeometry';

function getMaterial ({ color, opacity = 1, visible = true }) {
	const meshPhongMaterial = new THREE.MeshPhongMaterial({
		visible,
		opacity,
		color: new THREE.Color(color),
		emissive: new THREE.Color(0x333333),
		side: THREE.DoubleSide,
		transparent: opacity !== 1,
		vertexColors: false,
		fog: false,
	});
	// fix: 模型重合相交部分闪烁
	setPolygonOffset(meshPhongMaterial, 1, 4);
	return meshPhongMaterial;
}

// 获取垫帽材料
function getMatCapMaterial () {
	// Load the Shiny Dull Metal MatCap Material
	const textureLoader = new THREE.TextureLoader();
	textureLoader.setCrossOrigin('');
	const texture = textureLoader.load('../../assets/img/texture/dullFrontLitMetal.png');
	// console.log('texture: ', texture);
	const matCapMaterial = new THREE.MeshMatcapMaterial({
		matcap: texture,
		side: THREE.DoubleSide,
		polygonOffset: true, // Push the mesh back for line drawing
		polygonOffsetFactor: 2.0,
		polygonOffsetUnits: 1.0,
	});
	return matCapMaterial;
}

function getLineMaterial ({ color, lineWidth = 4, opacity, visible = true }) {
	const lineMaterial = new LineMaterial({
		visible,
		opacity,
		color: new THREE.Color(color),
		transparent: opacity !== 1,
		linewidth: lineWidth,
		dashed: false,
		dashSize: 1,
		gapSize: 1,
		dashScale: 3,
	});
	// fix: 模型重合相交部分闪烁
	setPolygonOffset(lineMaterial, -2, -50);
	return lineMaterial;
}

// const loader = new Rhino3dmLoader();
// loader.setLibraryPath(`/npm/rhino3dm@7.11.1/`);

const createWorker = async (params = {}) => {
	const ffmpegWorker = Comlink.wrap(new Worker('/js/rhino.worker.js', { type: 'module' }));
	const result = await ffmpegWorker.getFrames(params);
	logger.log('来自子线程返回的结果 result: ', result);
	return result;
};

export const mixinRhino = {
	data () {
		return {
			resMaterialMap: {
				result_design_space_of_pipe: getMaterial({ color: '#00FF00', opacity: 1 }), // 管线设计空间
				result_pipe_line: getLineMaterial({ color: '#0090FF', lineWidth: 4, opacity: 1 }), // 管线
				result_pipe_line_preview: getMaterial({ color: '#009099', opacity: 1 }), // 管预览
				result_make_pipe: getMaterial({ color: '#FF00D5', opacity: 1 }), // 管路
				result_include_water_model: getMaterial({ color: '#CCCCCC', opacity: 1 }), // 含水路模型
				result_include_water_model_line: getLineMaterial({ color: '#000000', lineWidth: 1, opacity: 1 }), // 含水路模型边框线
			},
		};
	},
	methods: {
		initLoader () {
			const loader = new Rhino3dmLoader();
			// loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@7.11.1/');
			// loader.setLibraryPath(`${ Config.staticDomainURL }/npm/rhino3dm@7.11.1/`);
			loader.setLibraryPath(`/npm/rhino3dm@7.11.1/`);
			return loader;
		},

		/**
		 * 加载计算数据
		 */
		async loadComputeData (data, results, serverResults) {
			if (data?.resultJson?.result_dir) {
				const oldGroup = this.getSceneChildrenByCondition(RESULT_WATER_COOLING_GROUP);
				this.removeSceneObjByGroup(oldGroup);
				this.requestRender();

				const group = new THREE.Group();
				group.name = RESULT_WATER_COOLING_GROUP;

				this.openLoading('结果加载中...');
				logger.log('结果加载中...');

				const keys = [];

				logger.time('结果数据总加载时间');
				// logger.time('总数据解析时间');
				const todoMap = {
					0: ['设计空间', [waterCoolingResultKeyMap.pipelineDesignSpace, data, results, RenderOrderEnum.result.spaceOfPipe, { isMesh: true }]],
					1: ['管线', [waterCoolingResultKeyMap.pipeline, data, results, RenderOrderEnum.result.pipeline, { isLine: true }]],
					2: ['管预览', [waterCoolingResultKeyMap.generateTubePreview, data, results, RenderOrderEnum.result.pipeLinePreview, { isMesh: true }]],
					3: ['生成管路', [waterCoolingResultKeyMap.generatePipeline, data, results, RenderOrderEnum.result.makePipe, { isMesh: true }]],
					4: ['含水路模型', [waterCoolingResultKeyMap.waterwayModel, data, results, RenderOrderEnum.result.waterModel, { isMesh: true }]],
					5: ['含水路模型边框线', [waterCoolingResultKeyMap.waterwayModelLine, data, results, RenderOrderEnum.result.waterModel, { isLine: true }]],
				};

				// ----------------------------------- start -----------------------------------------------------------------------------------
				// 1、结果若只有设计空间生成，则：设计空间透明度为100%，并显示
				// 2、结果若只有设计空间、管线生成，则：管线透明度为100%，并显示，设计空间透明度为100%，且隐藏
				// 3、结果若有管预览生成，则：管线览透明度为100%，并显示(一般同管线一起显示)
				// 4、结果若只有设计空间、管线、管预览、生成管路，则：生成管路显示，透明度为100%，管预览、管线、设计空间透明度为100%，且隐藏
				// 5、结果有含水路模型，则：含水路模型透明度为100%，且隐藏
				const nextHandler = () => {
					if (group.children.length) {
						const check = (child, key) => child.nickname?.includes(key);
						group.children.forEach(child => {
							const serverResult = serverResults?.find(serverResult => child.groupName === serverResult.key);
							if (check(child, keys[keys.length - 1])) {
								child.visible = serverResult ? serverResult.visible : true;
							} else {
								child.visible = serverResult ? serverResult.visible : false;
							}
							child.material.opacity = serverResult ? (serverResult.opacity / 100) : 1;
							child.material.transparent = child.material.opacity < 1;
							const result = results.find(result => child.groupName === result.key);
							if (result) {
								result.visible = serverResult ? serverResult.visible : child.visible;
								result.opacity = serverResult ? serverResult.opacity : child.material.opacity * 100;
							}
						});
					}
					// ----------------------------------- end -----------------------------------------------------------------------------------

					const serverResult = serverResults?.find(serverResult => serverResult.key === waterCoolingResultKeyMap.waterwayModel);
					if (waterLineArray?.length) {
						waterLineArray.forEach(line => {
							line.visible = serverResult ? serverResult.visible : false;
							line.material.opacity = 1;
							line.material.transparent = false;
							group.add(line);
						});
					}
					if (waterMeshArray?.length) {
						waterMeshArray.forEach(mesh => {
							mesh.visible = serverResult ? serverResult.visible : false;
							mesh.material.opacity = serverResult ? (serverResult.opacity / 100) : 0.5;
							mesh.material.transparent = mesh.material.opacity < 1;
							group.add(mesh);
							const result = results.find(result => mesh.groupName === result.key);
							if (result) {
								result.visible = serverResult ? serverResult.visible : mesh.visible;
								result.opacity = serverResult ? serverResult.opacity : mesh.material.opacity * 100;
							}
						});
					}

					if (serverResults && serverResults.length) {
						results = serverResults;
					}

					results.forEach(i => {
						['result_make_pipe', 'result_pipe_line_preview', 'result_include_water_model', 'result_pipe_line'].forEach(j => {
							if (j === i.key) {
								i.status = data.resultJson[j + '_status'];
							}
						});
					});

					this.getScene().add(group);
					this.closeLoading();
					logger.timeEnd('结果数据总加载时间');
				};

				const sxlResults = [];
				const waterMeshArray = [];
				const waterLineArray = [];
				const handler = async ([title, args]) => {
					const timerName = `${ title }加载时间`;
					logger.time(timerName);
					const meshArray = await this.waterCoolingResultHandler(...args);
					logger.timeEnd(timerName);
					sxlResults.push(meshArray);
					if (['设计空间', '管线', '管预览', '生成管路'].includes(title)) {
						if (meshArray?.length) {
							group.add(...meshArray);
							keys.push(args[0]);
						}
					}
					if (['含水路模型'].includes(title)) {
						if (meshArray?.length) {
							waterMeshArray.push(...meshArray);
						}
					}
					if (['含水路模型边框线'].includes(title)) {
						if (meshArray?.length) {
							waterLineArray.push(...meshArray);
						}
					}

					const done = sxlResults.length === 6;
					if (done) {
						// logger.timeEnd('总数据解析时间');
						nextHandler();
					}
				};
				for (let i = 0; i < 6; i++) {
					await handler(todoMap[i]);
				}

				// 管线长度
				const key = 'length_of_pipe';
				if (data.resultJson[key]) {
					const result = results.find(r => r.key === key);
					if (result) {
						const format = (num) => Number(num).toFixed(2);
						result.value = data.resultJson[key].split(',').map(format).join(', ');
					}
				}
			}

			return results;
		},

		/**
		 * 水冷结果处理(Mesh、Line)
		 */
		async waterCoolingResultHandler (key, data, results, renderOrder, { isMesh, isLine }) {
			const meshArray = [];
			const result = results.find(r => r.key === key);
			if (data.resultJson[key]) {
				const fileNames = data.resultJson[key].toString().split(',');
				const basePath = `${ Config.staticDomainURL }/${ data.resultJson.result_dir }`;
				let index = 0;
				for (const fileName of fileNames) {
					index++;
					const filePath = `${ basePath }/${ fileName }`;

					// const childThreadResult = await createWorker({ filePath, isMesh, isLine, decompress: true, loadingMsg: '结果数据加载中...' });
					// logger.log('子线程结果 childThreadResult: ', childThreadResult);
					// const loader = new THREE.ObjectLoader();
					// for (const jsonObject of childThreadResult) {
					// 	const object = loader.parse(jsonObject);
					// 	this.getScene().add(object);
					// }

					const children = await this.rhinoJsonToThreeJsMesh(filePath, { isMesh, isLine, decompress: true, loadingMsg: '结果数据加载中...' });
					if (isMesh) {
						for (const child of children) {
							child.material = this.resMaterialMap[key];
							child.groupName = key;
							child.nickname = key + '_' + index;
							child.renderOrder = renderOrder;
							meshArray.push(child);
						}
					} else if (isLine) {
						// for (const child of children) {
						// 	const array = child.geometry.attributes.position.array;
						// 	const lineMaterial = this.resMaterialMap[key];
						// 	lineMaterial.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
						// 	const { line } = makeLine(this.getCanvas(), array, {
						// 		lineWidth: 1,
						// 		material: lineMaterial,
						// 	});
						// 	line.groupName = key;
						// 	line.renderOrder = renderOrder;
						// 	line.nickname = key + '_' + index;
						// 	meshArray.push(line);
						// }

						const lineMaterial = this.resMaterialMap[key];
						const line = this.mergeLines(children, { color: lineMaterial.color, material: lineMaterial });
						line.groupName = key;
						line.renderOrder = renderOrder;
						line.nickname = key + '_' + index;
						meshArray.push(line);
					}
				}
				if (result) result.visible = !!meshArray.length;
			}
			if (result && data.resultJson[key] && ![waterCoolingResultKeyMap.pipelineDesignSpace, waterCoolingResultKeyMap.generateTubePreview].includes(key)) {
				result.url = true;
			}
			return meshArray;
		},

		/**
		 * 加载纹理计算结果
		 * @param {Object} data
		 * @param {Boolean} canLoadPoint
		 */
		async loadTextureComputeData (data, canLoadPoint) {
			logger.log('加载纹理计算数据 data: ', data);

			if (data?.resultJson?.relative_path) {

				this.openLoading();

				const basePath = `${ Config.staticDomainURL }/${ data.resultJson.relative_path }`;

				// 纹理线(Curves)
				const key = 'result_wl_line_json';
				if (data.resultJson[key]) {

					// 清除上一次缓存结果
					let wlLineGroup = this.getGroupByName(RESULT_WL_LINE_GROUP);
					this.removeSceneObjByGroup(wlLineGroup);

					wlLineGroup = new THREE.Group();
					wlLineGroup.name = RESULT_WL_LINE_GROUP;

					const filePath = `${ basePath }/${ data.resultJson[key] }`;

					const lines = await this.loadRhinoLinesJson(filePath, { customLineAttributes: true, getOriginData: true });
					// 合并所有线段
					const line = this.mergeLines(lines, { color: '#0090FF' });
					line.canDelete = true;
					line.isWlRes = true; // 是否计算结果线
					line.visible = true;
					line.nickname = 'wl_' + 0;
					line.groupName = RESULT_WL_LINE_GROUP;
					line.renderOrder = RenderOrderEnum.line;
					line.textureResultType = TextureResultTypeEnum.visible2dWlLine;
					// 设置多边形偏移
					setPolygonOffset(line.material, -2, -1);
					wlLineGroup.add(line);

					this.getScene().add(wlLineGroup);
				}

				// 加载引导点
				let vertices = data.resultJson['position_of_point'];
				if (vertices) {
					if (isString(vertices)) {
						const verticesArray = vertices.split('\n').filter(item => item).map(str => str.replace(/[{|}\s]/g, '').split(','));
						vertices = [];
						verticesArray.forEach(arr => {
							arr = arr.map(Number);
							vertices.push(new THREE.Vector3(arr[0], arr[1], arr[2]));
						});
					}
					const childArray = this.getScene().children.filter(child => child.groupName === TEXTURE_GUIDED_POINT);
					this.clearCacheByArray(childArray);
					this.removeSceneObj(childArray);

					// 单向开关开启时显示
					vertices.forEach(vert => {
						const sphere = this.drawSphere(vert, false, 2, TEXTURE_GUIDED_POINT);
						sphere.visible = canLoadPoint;
					});
					this.requestRender();
				}

				const results = [
					{ label: '2D纹理线条模型', name: 'result_wl_line_3dm', url: '' },
					{ label: '2D纹理线条模型', name: 'result_wl_line_iges', url: '' },
					{ label: '2D纹理线条模型', name: 'result_wl_line_step', url: '' },
					{ label: '2D纹理线条矢量', name: 'result_wl_line_ai', url: '' },
					{ label: '2D纹理线条参数文档', name: 'result_wl_line_params', url: '' },
					{ label: 'PNG图片格式', name: 'result_wl_line_png', url: '' },
				];

				results.forEach(result => {
					if (hasOwnProperty(data.resultJson, result.name) && data.resultJson[result.name]) {
						result.url = `${ basePath }/${ data.resultJson[result.name] }`;
					}
				});

				this.closeLoading();

				return results;
			}
		},

		/**
		 * 加载3DM
		 * @param filePath
		 * @param options
		 * @return {THREE.Object3D}
		 */
		load3DM (filePath, options = { needChildren: false }) {
			return new Promise(async (resolve) => {

				const loader = this.initLoader();

				let object;
				try {
					if (!filePath.startsWith(Config.staticDomainURL)) {
						filePath = Config.staticDomainURL + '/' + filePath;
					}
					object = await loader.loadAsync(filePath + '?_=' + Date.now());
				} catch (e) {
					logger.error('加载3DM err: ', e);
				}

				// 关闭连接
				loader?.dispose?.();

				if (!object) return resolve();

				object.canDelete = true;

				if (options.needChildren) {
					return resolve(object.children);
				}

				resolve(object);
			});
		},

		/**
		 * 加载STL
		 * @param filePath
		 */
		loadSTL (filePath) {
			return new Promise(async (resolve) => {
				const loader = new STLLoader();
				loader.load(filePath, (geometry) => {
					logger.log('加载STL geometry: ', geometry);
					// 创建纹理
					const material = this.getMeshMaterial(ColorEnum.default, 1);
					const mesh = new THREE.Mesh(geometry, material);
					resolve(mesh);
				});
			});
		},

		/**
		 * 合并所有线段
		 * @param lines
		 * @param color
		 * @param lineWidth
		 * @return {THREE.LineSegments|LineSegments2}
		 */
		mergeLines (lines, { color, lineWidth = 1 } = {}) {
			// 合并所有线段
			const edges = [];
			const positions = [];
			lines.forEach(line => {
				const position = line.geometry.attributes.position.array;
				const edge = { vertex_coord: [] };
				for (let i = 0; i < position.length; i += 3) {
					edge.vertex_coord.push(position[i + 0], position[i + 1], position[i + 2]);
				}
				edges.push(edge);
			});

			// const lineVertices = [];
			// edges.forEach((edge) => {
			// 	for (let i = 0; i < edge.vertex_coord.length - 3; i += 3) {
			// 		// 当前坐标点
			// 		lineVertices.push(new THREE.Vector3(
			// 			edge.vertex_coord[i + 0],
			// 			edge.vertex_coord[i + 1],
			// 			edge.vertex_coord[i + 2],
			// 		));
			// 		// 下一个坐标点
			// 		lineVertices.push(new THREE.Vector3(
			// 			edge.vertex_coord[i + 0 + 3],
			// 			edge.vertex_coord[i + 1 + 3],
			// 			edge.vertex_coord[i + 2 + 3],
			// 		));
			// 	}
			// });
			// 方式一 (不可设置线宽)
			// const lineGeometry = new THREE.BufferGeometry().setFromPoints(lineVertices);
			// const lineColors = [];
			// color = new THREE.Color(color || '#000000');
			// for (let i = 0; i < lineVertices.length; i++) { lineColors.push(color.r, color.g, color.b); }
			// lineGeometry.setAttribute('color', new THREE.Float32BufferAttribute(lineColors, 3));
			// const lineMaterial = new THREE.LineBasicMaterial({ color });
			// const wireframe = new THREE.LineSegments(lineGeometry, lineMaterial);

			edges.forEach((edge) => {
				for (let i = 0; i < edge.vertex_coord.length - 3; i += 3) {
					// 当前坐标点
					positions.push(
						edge.vertex_coord[i + 0],
						edge.vertex_coord[i + 1],
						edge.vertex_coord[i + 2],
					);
					// 下一个坐标点
					positions.push(
						edge.vertex_coord[i + 0 + 3],
						edge.vertex_coord[i + 1 + 3],
						edge.vertex_coord[i + 2 + 3],
					);
				}
			});

			// 方式二 (可设置线宽)
			const lineGeometry = new LineSegmentsGeometry().setPositions(positions);
			const lineMaterial = new LineMaterial({ color, linewidth: lineWidth });
			const canvas = this.getCanvas();
			// 设置材质分辨率 (这句如果不加,宽度仍然无效，重要!!!)
			lineMaterial.resolution.set(canvas.clientWidth, canvas.clientHeight);
			// 创建 Line2
			const wireframe = new LineSegments2(lineGeometry, lineMaterial);

			// 释放内存
			// lines.length = 0;

			return wireframe;
		},

		/**
		 * 获取网格材质
		 */
		getMeshMaterial (color, opacity) {
			let depthWrite = false;
			let transparent = false;
			if (opacity !== 1) {
				transparent = true;
			} else {
				depthWrite = true;
			}
			const material = new THREE.MeshLambertMaterial({
				color: new THREE.Color(color), // 默认：#cccccc
				emissive: new THREE.Color(0x333333),
				side: THREE.DoubleSide,
				vertexColors: false,
				flatShading: false,
				opacity: opacity,
				depthWrite: depthWrite,
				transparent: transparent,
				fog: false,
			});
			return material;
		},

		/**
		 * 加载顶点
		 */
		loadVertices (vertices, material, { divisions = 1000, removeLastPoint = true } = {}) {
			if (isString(vertices)) {
				const verticesArray = vertices.split('\r\n').filter(item => item).map(str => str.replace(/[{|}\s]/g, '').split(','));
				vertices = [];
				verticesArray.forEach(arr => {
					arr = arr.map(Number);
					vertices.push(new THREE.Vector3(arr[0], arr[1], arr[2]));
				});
			}
			if (vertices.length < 2) {
				logger.log('vertices: ', vertices);
				this.$message.error('提交数据异常，请修改后重新计算');
				return;
			}
			if (removeLastPoint && vertices.length) {
				// vertices.pop();
			}

			return this.makeThreeJsCurve(vertices, material, { divisions });
		},

		/**
		 * 加载结果顶点
		 */
		loadResultVertices (vertices, material) {
			if (isArray(vertices)) {
				const verticesArray = vertices.filter(item => item).map(str => str.replace(/[{|}\s]/g, '').split(','));
				vertices = [];
				verticesArray.forEach(arr => {
					arr = arr.map(Number);
					vertices.push(new THREE.Vector3(arr[0], arr[1], arr[2]));
				});
			}
			return this.makeThreeJsCurve(vertices, material, { divisions: 100, closed: true });
		},

		makeThreeJsCurve (vertices, material, { divisions = 100, closed = false } = {}) {
			let curveObject;
			try {
				const curve = new THREE.CatmullRomCurve3(vertices, closed, 'centripetal');
				const points = curve.getPoints(divisions); // 值越大，光滑度越高
				const geometry = new THREE.BufferGeometry().setFromPoints(points);
				const lineGeometry = new LineGeometry();
				lineGeometry.setPositions(geometry.attributes.position.array);
				const lineMaterial = material;
				const canvas = this.getCanvas();
				lineMaterial.resolution.set(canvas.clientWidth, canvas.clientHeight); // 这句如果不加宽度仍然无效
				curveObject = new Line2(lineGeometry, lineMaterial);
				curveObject.computeLineDistances();
			} catch (e) {
				logger.error('loadResultVertices error: ', e);
			}
			return curveObject;
		},

		/**
		 * 加载3DM线
		 */
		async load3DMLines (filePath, {
			message,
			groupName,
			groupAttr,
			visible,
			lineWidth = 1,
			material = undefined,
			allowSelect = false,
			lineHandler = noop,
		} = {}) {
			this.openLoading();
			const children = await this.load3DM(filePath, { needChildren: true }).catch(logger.error);
			logger.log(`${ message } children：`, children);
			// if (children?.length > 10000) {
			// 	this.$modal.warning({ content: '模型线数数量过多，加载失败' });
			// 	return;
			// }
			if (children) {
				const group = new THREE.Group();
				group.name = groupName;
				group.visible = visible;
				group.allowSelect = allowSelect;
				if (isString(groupAttr)) {
					group[groupAttr] = true;
				}
				if (isArray(groupAttr)) {
					groupAttr.forEach(attr => group[attr] = true);
				}
				children.forEach((mesh, index) => {
					if (mesh instanceof THREE.Line) {
						const array = mesh.geometry.attributes.position.array;
						const { line } = makeLine(this.getCanvas(), array, {
							visible,
							groupName,
							allowSelect,
							lineWidth,
							material,
							nickname: mesh.name || mesh.uuid,
						});
						lineHandler(line, index);
						group.add(line);
						if (allowSelect) {
							this.getRaycasterObjs().push(line);
						}
					}
				});
				this.getScene().add(group);
				this.requestRender();
			}
			this.closeLoading();
		},

		/**
		 * 计算
		 */
		async initCompute (path, material) {
			try {
				logger.log('path: ', path);
				if (!window.rhino) {
					await this.initRhino();
				}

				let base64Str = await loadFile(path, 'text', false).catch(() => this.tipErrMsg(path, '结果'));
				if (!base64Str) return null;

				base64Str = JSON.stringify(base64Str);

				const timeComputeStart = performance.now();

				let data = JSON.parse(base64Str);
				let mesh = window.rhino.DracoCompression.decompressBase64String(data);
				const threeJsMesh = this.meshToThreeJs(mesh, material);

				// 释放引用
				if (mesh) {
					mesh.delete();
					mesh = null;
				}
				data = null;
				base64Str = null;

				const t1 = performance.now();
				const computeSolveTime = t1 - timeComputeStart;
				logger.log(`  ${ Math.round(computeSolveTime) } ms: appserver request`);

				return threeJsMesh;
			} catch (error) {
				logger.error('compute error: ', error);
			}
		},

		/**
		 * 加载离散面数据
		 */
		async loadRhinoFacesJson (type, result, isTest = false) {
			const objs = [];
			try {
				const timeComputeStart = performance.now();

				const fileName = result[type]; // file name and include suffix
				const filePath = result.relative_path + '/' + fileName;

				if (!filePath) {
					this.$modal.error({ content: 'loadRhinoFacesJson 文件地址丢失!' });
					return;
				}

				if (type === 'meshes' || type === 'faces') {
					const children = await this.rhinoJsonToThreeJsMesh(filePath, { isMesh: true, decompress: false, isTest });
					let meshIndex = 0;
					for (const child of children) {
						meshIndex++;
						child.material = getMaterial({ color: ColorEnum.default });
						// child.material = getMatCapMaterial();
						if (type === 'meshes') {
							child.isSolid = true;
							child.groupName = SOLID_GROUP;
							child.nickname = 'solid_' + meshIndex;
							child.renderOrder = RenderOrderEnum.solid;
						}
						if (type === 'faces') {
							child.isFace = true;
							child.groupName = FACE_GROUP;
							child.nickname = 'face_' + meshIndex;
							child.renderOrder = RenderOrderEnum.face;
						}
						child.canDelete = true;
						child.castShadow = true;
						child.solidGroup = 'solid_1'; // 目前仅支持单体

						// 根据离散面计算边框
						// const wireframe = getWireframeByFace(child, this.getCanvas(), 1);
						// wireframe.canDelete = true;
						// wireframe.isWireframe = true; // 是否线框
						// wireframe.groupName = WIREFRAME;
						// wireframe.nickname = 'w_' + meshIndex;
						// wireframe.renderOrder = RenderOrderEnum.line;
						// 插入边框
						// objs.push(wireframe);

						// 插入face
						objs.push(child);
					}

					// 获取线框（多个面）
					const wireframe = getWireframeByFaces(children, this.getCanvas(), 1);
					wireframe.canDelete = true;
					wireframe.isWireframe = true; // 是否线框
					wireframe.groupName = WIREFRAME;
					wireframe.nickname = 'w_' + meshIndex;
					wireframe.renderOrder = RenderOrderEnum.line;
					objs.push(wireframe);
				}

				const t1 = performance.now();
				const computeSolveTime = t1 - timeComputeStart;
				logger.log(`loadRhinoFacesJson 耗时: ${ Math.round(computeSolveTime) } ms`);
			} catch (error) {
				logger.error('loadRhinoFacesJson error: ', error);
			}
			return objs;
		},

		/**
		 * 加载离散线数据
		 */
		async loadRhinoLinesJson (filePath, {
			visible = true,
			lineWidth = 1,
			nicknamePrefix = undefined,
			material = undefined,
			customLineAttributes = false,
			decompress = true,
			getOriginData = false,
			loadingMsg = undefined,
		} = {}) {
			const objs = [];
			try {
				if (!filePath) {
					this.$modal.error({ content: 'loadRhinoLinesJson 文件地址丢失!' });
					return;
				}

				const timeComputeStart = performance.now();

				const children = await this.rhinoJsonToThreeJsMesh(filePath, { isLine: true, decompress, loadingMsg });

				if (getOriginData) {
					return children;
				}

				let lineIndex = 0;
				for (const child of children) {
					lineIndex++;
					const array = child.geometry.attributes.position.array;
					const lineMaterial = material || getLineMaterial({ color: ColorEnum.black, lineWidth, opacity: 1 });
					lineMaterial.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
					const { line } = makeLine(this.getCanvas(), array, {
						lineWidth,
						material: lineMaterial,
					});
					if (!customLineAttributes) {
						line.canDelete = true;
						line.isDispersed = true; // 是否离散线
						line.visible = visible;
						line.linewidth = lineWidth;
						line.linkName = child.linkName;
						line.groupName = DISPERSED_LINE_GROUP;
						line.renderOrder = RenderOrderEnum.line;
						line.nickname = (nicknamePrefix ?? 'e_') + lineIndex;
					}

					// 插入新的line
					objs.push(line);
				}

				const t1 = performance.now();
				const computeSolveTime = t1 - timeComputeStart;
				logger.log(`loadRhinoLinesJson 耗时: ${ Math.round(computeSolveTime) } ms`);
			} catch (error) {
				logger.error('loadRhinoLinesJson error: ', error);
			}
			return objs;
		},

		/**
		 * rhino json数据转换为 threeJs数据
		 */
		async rhinoJsonToThreeJsMesh (filePath, { decompress = false, isTest = false, loadingMsg = '数据加载中...' } = {}) {
			const objs = [];
			try {
				this.openLoading(loadingMsg);

				if (!window.rhino) {
					await this.initRhino();
				}

				let doc;

				// clear doc
				if (doc !== undefined) {
					doc.delete();
				}

				const getRhinoObject = (jsonData) => {
					if (jsonData && isObject(jsonData)) {
						jsonData.version = Number(jsonData.version);
						jsonData.archive3dm = Number(jsonData.archive3dm);
						jsonData.opennurbs = Number(jsonData.opennurbs);
					}
					const rhinoObject = window.rhino.CommonObject.decode(jsonData);
					return rhinoObject;
				};

				if (!isTest && !filePath.startsWith(Config.staticDomainURL)) {
					if (filePath.startsWith('/')) filePath = filePath.substring(1);
					filePath = Config.staticDomainURL + '/' + filePath;
				}

				let jsonDataList = [];
				if (decompress) {
					// zip解析并加载
					filePath = filePath.replace('.json', '.zip');
					const zipContent = await loadFile(filePath, 'arraybuffer', false).catch(() => this.tipErrMsg(filePath, '结果'));
					jsonDataList = await this.unzip(zipContent);
				} else {
					jsonDataList = await loadFile(filePath, 'json', false).catch(() => this.tipErrMsg(filePath, '结果'));
				}

				if (!jsonDataList.length) {
					this.closeLoading();
					return objs;
				}

				const lineHandler = (rhinoObjects, objs) => {
					return new Promise(resolve => {

						doc = new window.rhino.File3dm();

						for (const rhinoObject of rhinoObjects) {
							if (rhinoObject !== null) {
								doc.objects().add(rhinoObject, null);
							}
						}

						if (doc.objects().count < 1) {
							this.$modal.error({ content: 'No objects to load!' });
							this.closeLoading();
							return;
						}

						const buffer = new Uint8Array(doc.toByteArray()).buffer;

						const loader = new Rhino3dmLoader();
						loader.setLibraryPath(`/npm/rhino3dm@7.11.1/`);

						loader.parse(buffer, (object) => {
							let lineIndex = 0;
							object.traverse((child) => {
								if (child.isMesh) {
									objs.push(child);
								}
								if (child.isLine) {
									lineIndex++;
									const jsonData = jsonDataList[lineIndex - 1];
									child.linkName = jsonData ? jsonData.faceId : undefined;
									objs.push(child);
								}
							}, false);

							// 关闭连接
							loader?.dispose?.();

							this.closeLoading();

							setTimeout(() => {
								doc.delete();
								doc = null;
							}, 300);

							return resolve(objs);
						});
					});
				};

				const rhinoObjects = [];
				for (const jsonData of jsonDataList) {
					const rhinoObject = getRhinoObject(jsonData);
					rhinoObjects.push(rhinoObject);
				}

				// console.time('loader.parse耗时');
				await lineHandler(rhinoObjects, objs);
				// console.timeEnd('loader.parse耗时');
			} catch (error) {
				logger.error('rhinoJsonToThreeJsMesh error: ', error);
			}
			return objs;
		},

		/**
		 * 通过 points 获取 rhinoJson数据
		 *
		 * 顶点几何体 geometry.attributes.position
		 * 顶点颜色 geometry.attributes.color
		 * 顶点法向量 geometry.attributes.normal
		 * 顶点复用 geometry.index
		 */
		async getRhinoJsonDataByPoints (pointVectors, { scale = 1, lineWidth = 1 } = {}) {

			if (pointVectors?.length) {

				const curve = new THREE.CatmullRomCurve3(pointVectors);
				const divisions = 50; // 划分份数
				const geometry = new THREE.BufferGeometry().setFromPoints(curve.getPoints(divisions));
				geometry.scale(scale, scale, scale);
				const material = new THREE.LineBasicMaterial({ color: 0xff0000 });
				const line = new THREE.Line(geometry, material);

				// 如果需要绘制虚线，需要计算线条之间的距离
				// 否则不会出现虚线的效果
				// line.computeLineDistances();

				// 位置、角度、缩放属性值更新到矩阵属性matrix
				// 再强调一次，我们必须强制更新矩阵，才能实时看到效果
				line.updateMatrix();
				line.updateMatrixWorld();

				const line2 = this.createLine2ByLine(line, lineWidth);

				return { line, line2 };
			}

			return null;
		},

		createLine2ByLine (line, lineWidth = 1) {
			const geometry = new LineGeometry();
			geometry.fromLine(line);
			const matLine = getLineMaterial({ color: ColorEnum.black, lineWidth });
			matLine.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
			const line2 = new Line2(geometry, matLine);
			return line2;
		},

		createLine2ByPositions (positions, lineWidth = 1) {
			const geometry = new LineGeometry();
			geometry.setPositions(positions);
			const matLine = getLineMaterial({ color: ColorEnum.black, lineWidth });
			matLine.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
			const line2 = new Line2(geometry, matLine);
			return line2;
		},

		createLine2ByPoints (points, lineWidth = 1) {
			const positions = [];
			for (const point of points) {
				positions.push(point.x, point.y, point.z);
			}
			const geometry = new LineGeometry();
			geometry.setPositions(positions);
			const matLine = getLineMaterial({ color: ColorEnum.black, lineWidth });
			matLine.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
			const line2 = new Line2(geometry, matLine);
			return line2;
		},

		/**
		 * 获取曲线上的(世界)点
		 * @param curve
		 * @return {*[]}
		 */
		getWorldPointsByCurve (curve) {
			const points = [];
			curve.updateMatrix();
			const position = curve.geometry.attributes.position.array;
			for (let i = 0; i < position.length; i += 3) {
				const x = position[i];
				const y = position[i + 1];
				const z = position[i + 2];
				points.push(curve.localToWorld(new THREE.Vector3(x, y, z)));
			}
			return points;
		},

		/**
		 * 获取控制点
		 */
		getControlPoints (originControlPoints, scale) {
			const controlPoints = [];
			for (const originControlPoint of originControlPoints) {
				controlPoints.push(originControlPoint.clone().multiplyScalar(scale));
			}

			return controlPoints;
		},

		toFixed (value, fractionDigits = 6) {
			return Number(value.toFixed(fractionDigits));
		},

		/**
		 * 通过threeJsMesh 获取 rhinoJson数据
		 */
		async getRhinoJsonDataByThreeJsMesh (threeJsMesh, { scaleVector = new THREE.Vector3(0.5, 0.5, 0.5) } = {}) {

			try {

				if (!window.rhino) {
					await this.initRhino();
				}

				if (threeJsMesh.object instanceof THREE.Mesh) {
					const material = getMaterial({ color: ColorEnum.yellow });
					const child = new THREE.Mesh(threeJsMesh.object.geometry.clone(), material);
					child.groupName = FACE_GROUP;
					child.nickname = 'face_clone_1';
					child.renderOrder = RenderOrderEnum.face;
					child.solidGroup = 'solid_1'; // 目前仅支持单体

					// 根据离散面计算边框
					const wireframe = getWireframeByFace(child, this.getCanvas(), 1);
					wireframe.canDelete = true;
					wireframe.groupName = WIREFRAME;
					wireframe.nickname = 'w_clone_1';
					wireframe.renderOrder = RenderOrderEnum.line;

					// 插入face、插入边框
					// this.getScene().add(child, wireframe);

					const rhino_mesh = window.rhino.Mesh.createFromThreejsJSON({ data: child.geometry });
					// logger.log('rhino_mesh: ', rhino_mesh);

					// 0.5xMesh
					const material2 = getMaterial({ color: ColorEnum.green });
					const newChild = child.geometry.clone();
					// newChild.scale(0.5, 0.5, 0.5); // 缩小为原来0.5倍
					newChild.scale(scaleVector.x, scaleVector.y, scaleVector.z); // 缩小为原来n倍
					const newMesh = new THREE.Mesh(newChild, material2);
					// 根据离散面计算边框
					const wireframe2 = getWireframeByFace(newMesh, this.getCanvas(), 1);
					wireframe2.groupName = WIREFRAME;
					wireframe2.nickname = 'w_clone_2';
					wireframe2.renderOrder = RenderOrderEnum.line;
					// this.getScene().add(newMesh, wireframe2);

					const rhino_mesh2 = window.rhino.Mesh.createFromThreejsJSON({ data: newMesh.geometry });
					// logger.log('rhino_mesh2: ', rhino_mesh2);

					const doc = new window.rhino.File3dm();
					doc.objects().add(rhino_mesh, null);
					doc.objects().add(rhino_mesh2, null);
					const buffer = new Uint8Array(doc.toByteArray()).buffer;
					// logger.log('buffer: ', buffer);

					// logger.log('window.rhino', window.rhino);
					const uploadDoc = window.rhino.File3dm.fromByteArray(new Uint8Array(buffer));

					if (uploadDoc === null) {
						logger.error('uploadDoc 不能为空!');
						return;
					}

					// get geometry from file
					const inputGeometries = [];
					const objs = uploadDoc.objects();
					for (let i = 0; i < objs.count; i++) {
						const geom = objs.get(i).geometry();
						// filter for geometry of a specific type
						if (geom instanceof window.rhino.Mesh || geom instanceof window.rhino.Line) {
							inputGeometries.push(geom);
						}
					}

					// 传递给后端的数据
					logger.log('inputGeometries: ', inputGeometries);
					// saveString(JSON.stringify(inputGeometries), 'meshes.json');

					return [newMesh, inputGeometries];
				}
			} catch (error) {
				logger.error('getRhinoJsonDataByThreeJsMesh error: ', error);
			}
			return null;
		},

		async loadRhinoBase64 (type, result, { lineWidth = 1 } = {}) {
			const objs = [];
			try {
				if (!window.rhino) {
					await this.initRhino();
				}

				const timeComputeStart = performance.now();

				const fileName = result[type]; // file name and include suffix
				const basePath = Config.staticDomainURL + '/' + result.relative_path;
				const filePath = basePath + '/' + fileName;

				// -------------------------------- load faces ---------------------------------------------------------------
				if (type === 'solid' || type === 'faces') {
					// const meshTextContent = await loadFile(`/models/txt/faces.txt?_=` + Date.now(), 'text', false);
					let meshTextContent = await loadFile(filePath, 'text', false);
					let Base64StrArray = meshTextContent.split('\n').filter(s => s);
					let index = 0;
					for (const base64Str of Base64StrArray) {
						index++;
						let serializationBase64Str = JSON.stringify(base64Str);
						let data = JSON.parse(serializationBase64Str);
						let mesh = window.rhino.DracoCompression.decompressBase64String(data);

						let material = getMaterial({ color: '#CCCCCC', opacity: 1 });
						const threeJsMesh = this.meshToThreeJs(mesh, material);
						if (threeJsMesh) {
							if (type === 'solid') {
								threeJsMesh.isSolid = true;
								threeJsMesh.nickname = 'solid_' + index;
								threeJsMesh.groupName = SOLID_GROUP;
								threeJsMesh.renderOrder = RenderOrderEnum.solid;
							}
							if (type === 'faces') {
								threeJsMesh.isFace = true;
								threeJsMesh.nickname = 'face_' + index;
								threeJsMesh.groupName = FACE_GROUP;
								threeJsMesh.renderOrder = RenderOrderEnum.face;
							}
							threeJsMesh.canDelete = true;
							threeJsMesh.solidGroup = 'solid_1'; // 目前仅支持单体

							// 根据离散面计算边框
							const linePavement = getWireframeByFace(threeJsMesh, this.getCanvas(), 1);
							linePavement.canDelete = true;
							linePavement.isWireframe = true; // 是否线框
							linePavement.nickname = 'w_' + index;
							linePavement.groupName = WIREFRAME;
							linePavement.renderOrder = RenderOrderEnum.line;

							// 插入边框
							objs.push(linePavement);

							// 插入face
							objs.push(threeJsMesh);
						}

						// 释放，回收内存
						material = null;
						if (mesh) {
							mesh.delete();
							mesh = null;
						}
						data = null;
						serializationBase64Str = null;
						Base64StrArray = null;
						meshTextContent = null;
					}
				}
				// ------------------------------------------------------------------------------------------------------------

				// -------------------------------- load lines ----------------------------------------------------------------
				if (type === 'lines' || type === 'other_curve_points') {
					// const lineTextContent = await loadFile(`/models/txt/lines.txt`, 'text', false);
					let lineTextContent = await loadFile(filePath, 'text', false);
					let linePointsStrArray = lineTextContent.split('\n').filter(s => s);
					let lineIndex = 0;
					for (const linePointsStr of linePointsStrArray) {
						lineIndex++;
						let linePoints = linePointsStr.split(';');
						let lineMaterial = getLineMaterial({ color: '#000000', lineWidth, opacity: 1 });
						const threeJsLine = this.loadResultVertices(linePoints, lineMaterial);
						if (threeJsLine) {
							threeJsLine.canDelete = true;
							threeJsLine.isDispersed = true; // 是否离散线
							threeJsLine.nickname = 'e_' + lineIndex;
							threeJsLine.groupName = DISPERSED_LINE_GROUP;
							threeJsLine.renderOrder = RenderOrderEnum.line;

							// 插入新的line
							objs.push(threeJsLine);

							// 释放，回收内存
							lineMaterial = null;
							linePoints = null;
						}
					}

					// 释放，回收内存
					linePointsStrArray = null;
					lineTextContent = null;
				}
				// -------------------------------------------------------------------------------------------------------------

				const t1 = performance.now();
				const computeSolveTime = t1 - timeComputeStart;
				logger.log(`  ${ Math.round(computeSolveTime) } ms: appserver request`);
			} catch (error) {
				logger.error('compute error: ', error);
			}
			return objs;
		},

		meshToThreeJs (mesh, material) {
			const loader = new THREE.BufferGeometryLoader();
			const geometry = loader.parse(mesh.toThreejsJSON());
			return new THREE.Mesh(geometry, material);
		},

		/**
		 * 更新对象属性(单个)
		 */
		updateObjAttr (child, { color, opacity, visible, lineWidth } = {}, isSyncRaycasterObj = true) {
			if (child?.isLackFace) { // 坏面(缺失的面)不可操作
				return;
			}
			if (!isEmpty(visible)) {
				child.visible = !!visible;
				if (child?.parent instanceof THREE.Group) {
					child.parent.visible = true;
				}
			}
			if (child.material) {
				if (!isEmpty(color)) {
					child.material.color = new THREE.Color(color);
				}
				if (!isEmpty(opacity)) {
					// logger.log('updateObjAttr', child, opacity);
					// const isOpacity = opacity === 100;
					// opacity = opacity > 1 ? opacity / 100 : opacity;
					// if (!isOpacity && opacity === 1) opacity /= 10;
					child.material.opacity = Math.min(1, opacity / 100);
					child.material.depthWrite = (opacity / 100 === 1);
					child.material.transparent = (opacity / 100 !== 1);
				}
				if (!isEmpty(lineWidth) && hasOwnProperty(child.material, 'lineWidth')) {
					child.material.linewidth = lineWidth; // lineWidth原始属性是全小写，注意！
					child.material.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
				}
				child.material.needsUpdate = true;
			}

			// 根据视图对象同步拾取列表内对象属性
			isSyncRaycasterObj && this.syncRaycasterObjAttr(child);
		},

		/**
		 * 同步 Raycaster 对象属性
		 */
		syncRaycasterObjAttr (child) {
			if (child instanceof THREE.Object3D) {
				const raycasterObj = this.getRaycasterObjs().find(raycasterObj => raycasterObj.uuid === child.uuid);
				if (!raycasterObj) return;
				for (const childKey in child) {
					if (['visible'].includes(childKey)) {
						raycasterObj[childKey] = child[childKey];
					}
					if (raycasterObj.material) {
						if (['opacity', 'color'].includes(childKey)) {
							raycasterObj.material[childKey] = child[childKey];
						}
						if (childKey === 'opacity') {
							child.material.depthWrite = (child[childKey] === 1);
							child.material.transparent = (child[childKey] !== 1);
						}
						if (childKey === 'lineWidth') {
							raycasterObj.material[childKey.toLowerCase()] = child[childKey.toLowerCase()];
							child.material.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
						}
					}
				}
				raycasterObj.material.needsUpdate = true;
			}
		},

		/**
		 * 改结果展示(要废弃)
		 */
		handleResultChange ({ key, opacity, color, visible }) {
			if (!this.resMaterialMap[key]) {
				return;
			}
			this.resMaterialMap[key].color = new THREE.Color(color);
			this.resMaterialMap[key].opacity = opacity / 100 ?? 1;
			this.resMaterialMap[key].transparent = this.resMaterialMap[key].opacity !== 1;
			this.resMaterialMap[key].visible = visible ?? true;
			this.resMaterialMap[key].needsUpdate = true;
			this.requestRender();
		},

		/**
		 * 清除加载的结果数据
		 */
		clearResult () {
			const group = this.getScene()?.getObjectByName(RESULT_WATER_COOLING_GROUP);
			if (!group) return [];
			this.removeSceneObjByGroup(group);
		},

		/**
		 * 清除加载的纹理结果数据
		 */
		clearTextureResult () {
			const group = this.getScene()?.getObjectByName(RESULT_WL_LINE_GROUP);
			if (!group) return [];
			this.removeSceneObjByGroup(group);
		},

		/**
		 * 读取压缩包的内容
		 * @param zipData
		 * @return {Promise<Array>}
		 */
		unzip (zipData) {
			return new Promise(resolve => {
				const newZip = new JSZip();
				const checkCondition = (str) => !str.includes('__MACOSX') && !str.endsWith('/') && !str.includes('.DS_Store');
				newZip.loadAsync(zipData).then(zip => {
					zip.forEach((relativePath, zipEntry) => {
						if (checkCondition(relativePath) && relativePath.endsWith('.json')) {
							zipEntry.async('string').then(jsonStr => {
								let jsonDataList;
								if (isString(jsonStr)) {
									try {
										// eslint-disable-next-line no-new-func
										jsonDataList = (new Function('return ' + jsonStr))();
									} catch (e) {
										logger.error('zip loadAsync err: ', e);
									}
								}
								resolve(jsonDataList);
							});
						}
					});
				});
			});
		},

		/**
		 * 初始化 Rhino
		 * @return {Promise<Boolean>}
		 */
		initRhino () {
			return new Promise(async resolve => {
				if (await this.checkRhino3dm()) {
					if (!window.rhino) {
						window.rhino3dm().then((rhino) => {
							window.rhino = rhino;
							logger.log('ReLoaded rhino3dm.');
							return resolve(true);
						});
					} else {
						logger.log('Loaded rhino3dm.');
						return resolve(true);
					}
				}
			});
		},

		/**
		 * 检测 rhino3dm 脚本是否挂载
		 * @return {Promise<Boolean>}
		 */
		checkRhino3dm () {
			return new Promise(async resolve => {
				if (!window.rhino3dm) {
					await sleep(1000);
					return await this.checkRhino3dm();
				} else {
					return resolve(true);
				}
			});
		},

		/**
		 * 提示错误消息
		 * @param path
		 * @param {String=} prefixMsg
		 * @return {boolean}
		 */
		tipErrMsg (path, prefixMsg = '') {
			const name = basename(path);
			this.$modal.error({
				okText: '确定',
				content: `${ prefixMsg }文件【${ name }】未找到，请联系管理员`,
			});
			return false;
		},
	},
};
