<template>
	<div class="default-footer">
		<div class="default-footer-container">
			<a-row>
				<a-col :span="17">
					<img :src="logoImg" class="default-footer-container-logo" alt="">
					<div class="default-footer-container-copyright">
						<div>
							<span style="height: 20px; line-height: 20px;">© Copyright © 2020-2021 平台版权所有 未经许可 请勿转载 </span>
							<a href="https://beian.miit.gov.cn" target="_blank">京ICP备10010006号-11</a>
						</div>
						<div style="margin-left: 10px;">
							<a target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502047499">
								<img src="~@/assets/img/record.png" style="float:left;">
								<p style="float:left; height:20px; line-height:20px; margin: 0 0 0 5px; color:#939393;">京公网安备 11010502047499号</p>
							</a>
						</div>
					</div>
				</a-col>
				<a-col :span="7" style="position:relative;padding-left:60px">
					<div class="default-footer-container-text">联系我们</div>
					<div class="default-footer-container-text" style="margin-top: 12px;">
						<span>电话：400 6600 388</span>
					</div>
					<div class="default-footer-container-text" style="margin-top: 5px;">
						<span>邮箱：<EMAIL></span>
					</div>
				</a-col>
			</a-row>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 全局-公共页脚
 * Created by cjking on 2021/04/08.
 * Copyright 2021, Inc.
 * =================================== */
import logoImg from '@/assets/img/logo2.png';

export default {
	name: 'GlobalFooter',
	data () {
		return {
			logoImg,
		};
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/mixins/variables.less";

.default {
	&-footer {
		width: 100%;
		background: #333;

		&-container {
			margin: 0 auto;
			width: 1200px;
			color: #ABB4C3;
			position: relative;

			&-logo {
				width: 213px;
				height: 36px;
			}

			&-content {
				margin-top: 16px;
				font-size: 16px;
				font-weight: 400;
				color: rgba(255, 255, 255, 1);
				line-height: 26px;
			}

			&-tel-icon {
				margin-top: 20px;
				width: 12px;
				height: 13px;
			}

			&-tel {
				margin-top: 20px;
				width: 147px;
				height: 22px;
			}

			&-copyright {
				margin-top: 34px;
				color: #999999;
				font-size: 12px;
				display: flex;

				a {
					color: #999999;
				}
			}

			&-code {
				margin-top: 10px;
				margin-bottom: 40px;
				width: 70px;
				height: 70px;
			}

			&-text {
				color: #BEBEBE;
				font-size: 16px;
				span {
					color: #BEBEBE;
					font-size: 14px;
					&:hover {
						color: @primary;
					}
				}
			}

			&-right-link {
				a {
					color: #BEBEBE;
					font-size: 14px;
					&:hover {
						color: @primary;
					}
				}
			}

			/deep/ .ant-row {
				padding: 40px 0;
			}
		}
	}
}
</style>
