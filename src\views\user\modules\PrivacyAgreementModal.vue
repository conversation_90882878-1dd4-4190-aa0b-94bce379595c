<template>
	<a-modal
		:width="1200"
		:visible="visible"
		:confirmLoading="confirmLoading"
		@cancel="handleCancel"
		cancelText="关闭"
		:title="null"
		:footer="null"
	>
		<a-spin :spinning="confirmLoading">
			<div class="doc">
				<h1>隐私协议</h1>
				<p>安世亚太科技股份有限公司（以下简称“安世亚太”）是创成云平台（一款面向企业和团队用户的在线协同软件，以下简称“平台”或“我们”），即对应域名为“http://www.generativethings.com”及其相关二级域名）的合法运营者。</p>
				<p>安世亚太认可并重视平台用户的隐私。因此，安世亚太认致力于确保用户了解其个人信息在平台的使用方式，并且保证这些信息的安全。此隐私策略适用于平台通过显示此隐私策略的网站、移动应用、产品和服务（包括可能由平台离线提供的产品和服务）收集的信息。此隐私策略适用于安世亚太当前提供的所有平台上的产品和服务。</p>
				<p>除非此隐私策略第2节、第5节、第6节和第7节另有规定，否则未经您同意，安世亚太认不会与第三方共享您的任何与个人、团队和项目有关的信息。</p>
				<p>访问平台相关网站，或者使用或购买平台提供的产品和服务，即表示您接受此隐私策略所述惯例，并同意平台按照此隐私策略所述方式使用您的信息。</p>
				<p>________________________________________</p>
				<h3>1. 创成云平台收集的信息</h3>
				<p>平台可能向您收集有关您的身份、您在平台上创建/加入的团队信息，以及您对平台上的产品的个人喜好方面的信息。我们通常可能收集的个人信息包括：姓名、电子邮件地址、电话号码、IP地址；团队信息包括：公司/团队名称、Logo（或商标、企业标识）、公司/团队介绍、公司/团队通讯地址、电话号码、服务使用状态的相关信息，以及您在购买过程中可能会涉及到的帐户信息。
				</p><p>我们可能会采用多种不同方式来接收和收集您的个人信息。这些方式包括但不限于以下：
				</p><h4>来自您的信件</h4>
				<p>您给我们发送电子邮件或写信时，可能会在邮件中提供您的公司/团队信息、您的姓名、联系详细信息以及其他个人信息。</p>
				<h4>用户支持</h4>
				<p>您在打电话或发电子邮件给我们请求产品方面的技术帮助或索取有关我们产品的信息时，可能向我们提供企业/团队和个人信息。
				</p><h4>产品注册</h4>
				<p>在平台上注册产品时，系统会要求您提供信息，以便我们将您记录为平台上相关产品的所有者，从而有助于我们为您提供服务和信息。</p>
				<h4>购买</h4>
				<p>如果您希望通过我们的在线支付系统订购平台上的产品及服务，我们将需要足够的信息才能完成您的订购，这些信息包括您企业/团队的银行账户信息或个人的信用卡信息。</p>
				<h3>2. 创成云平台收集信息的用途</h3>
				<p>我们可能将您的个人信息用于以下用途：</p>
				<h4>产品注册</h4>
				<p>您注册时所提供的信息将用于创建您在平台上的团队和项目，并且在您联系平台相关用户服务部门时，这些信息还将协助我们为您提供支持。我们还将使用这些信息来通知您有关您可能感兴趣的任何升级、新产品、促销或其他信息。</p>
				<h4>产品的使用</h4>
				<p>我们可能将产品使用中收集的信息用于：定期验证您对产品的使用权利；向您发送新产品/新功能升级的通知前，确认您是否正在使用该产品/功能的较早版本或者是否会对您使用我们的产品或服务有所帮助；以及，从平台接收您请求获取的产品内部消息。</p>
				<h4>产品购买</h4>
				<p>如果您通过平台的在线支付系统购买产品，我们将使用您的信息来处理付款并将产品购买信息发送给您。</p>
				<h4>内部分析</h4>
				<p>我们可能会使用您提供给我们的信息进行内部统计和分析，从而评估并增强您对平台网站的体验，包括通过识别您的喜好和购买意向，进行营销以及与运营和开发相关的活动。</p>
				<h3>3. 自动收集的信息</h3>
				<p>无论何时，只要您通过平台进入我们的网站或访问任何在线信息，我们的Web服务器就会自动收集和汇总有关您的访问的信息（以下简称“自动信息”）。自动信息可能包括相关网站的URL或域、浏览器类型、操作系统、您所访问的页面以及访问日期和时间。</p>
				<h3>4. 用户文件</h3>
				<p>平台允许您存储、发送、接收、编辑、同步、共享或者以其他方式组织或管理文件和文件夹 （包括这些文件夹中存储的任何信息或数据）（以下统称“用户文件”）。我们可能获得您在平台上储存的所有用户文件的信息，因为我们需要了解您在平台上的实际使用情况，但我们绝不会获取、储存、共享您的任何用户文件。</p>
				<h3>5. 与第三方共享信息</h3>
				<p>除非第5节、第6节另有特别规定，否则未经您同意，我们不会与第三方共享任何个人信息（例如，通过登录您的用户帐户并选择某选项）。</p>
				<h4>内部运营</h4>
				<p>我们可能与独立审计师共享您向我们提供的信息，实现与我们的内部业务运营相关的收入审计目的，前提是此类审计公司必须遵守此隐私策略。</p>
				<h4>服务提供商</h4>
				<p>我们有时会雇用其他公司代表我们提供服务，比如处理交易、邮件的处理和寄送、提供用户支持、托管网站或者针对我们的产品或服务进行统计分析。我们有时需要将您企业/团队和个人的信息与其他公司共享，以便这些公司能够提供适用的服务。我们仅向这些公司提供其向您提供服务所需的信息，但未授权这些公司将您的信息用于平台之外的任何其他用途。</p>
				<h3>6. 法定披露</h3>
				<p>虽然我们会尽最大努力保护用户隐私，但当我们有理由相信只有公开个人信息才能遵循现行司法程序、法院指令或其他法律程序或者保护平台、平台用户或第三方的权利、财产或安全时，我们可能披露个人信息。</p>
				<h3>7. 安全</h3>
				<p>我们会采取合理的实际及电子手段以及规程保障措施来保护您的企业/团队和个人信息。 虽然通过因特网信息传输数据并非100%安全，但我们已经采取并将继续采取商业范畴内合理的努力来确保您的个人信息得到保护。</p>
				<h3>8. COOKIE</h3>
				<p>Cookie是仅限文本的信息字符串，网站会将这些字符串传输到计算机硬盘上浏览器的Cookie文件中，以便网站能够记住您的身份信息以及其他详细信息。Cookie可能由网站设置或来源自第三方，比如广告商。Cookie本身不用于或意图用于从用户的计算机读取任何信息（Cookie本身的内容除外）。Cookie只是网站所使用的最初将其放置在您硬盘上的标识符。同一台服务器可以检索到Cookie信息的实际内容，从而标识计算机并进而根据主服务器上存储的信息自定义、跟踪或控制站点的使用情况。</p>
				<p>我们可能会在平台网站上使用Cookie。使用Cookie，我们便能进行自动访问和使用我们网站的数据输入功能，以及在网站（如果有）上根据您的喜好或兴趣定制网站，或者自定义促销或营销活动时，关联您所进行的购买活动的在线订购信息。此外，我们还可以通过Cookie跟踪我们网站的使用情况，从而确定哪些功能有用或受欢迎，哪些功能并不能帮助我们有效地改进和更新我们的服务。大多数网络浏览器都会向您发出有关使用Cookie的提醒，或者完全拒绝接受Cookie。您可以通过修改浏览器设置， 接受或拒绝Cookie。但是，如果禁用Cookie，您就不能使用此网站的各项交互功能。</p>
				<h3>9. 链接</h3>
				<p>我们网站上可能会提供某些链接，这些链接可能指向我们选择提供共同品牌产品的第三方或者我们认为有助于您更好的使用平台上的服务的其他网站。第三方网站不受此隐私策略的约束，我们不对这些网站处理您的个人信息的相关做法进行任何说明。建议您在开始访问这些网站之前查阅其隐私策略，确定其隐私保护惯例。</p>
				<h3>10. 有害信息的过滤和删除</h3>
				<p>平台禁止用户创建和储存一切有害信息，包括但不限于下列行为：</p>
				<ul>
					<li>（1）违反中国宪法确定的基本原则的；</li>
					<li>（2）危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；</li>
					<li>（3）损害国家荣誉和利益，攻击党和政府的；</li>
					<li>（4）煽动民族仇恨、民族歧视，破坏民族团结的；</li>
					<li>（5）破坏国家、地区间友好关系的；</li>
					<li>（6）违背中华民族传统美德、社会公德、论理道德、以及社会主义精神文明的；</li>
					<li>（7）破坏国家宗教政策，宣扬邪教和封建迷信的；</li>
					<li>（8）散布谣言或不实消息，扰乱社会秩序，破坏社会稳定的；</li>
					<li>（9）煽动、组织、教唆恐怖活动、非法集会、结社、游行、示威、聚众扰乱社会秩序的；</li>
					<li>（10）散布淫秽、色情、赌博、暴力、恐怖或者教唆犯罪的；</li>
					<li>（11）侮辱或诽谤他人，侵害他人合法权益的；</li>
					<li>（12）侵犯他人肖像权、姓名权、名誉权、隐私权或其他人身权利的；</li>
					<li>（13）使用漫骂、辱骂、中伤、恐吓、诅咒等不文明语言的；</li>
					<li>（14）以非法民间组织名义活动的；</li>
					<li>（15）侵犯他人著作权、信息网络传播权等合法权益的；</li>
					<li>（16）含有法律、行政法规禁止的其他内容的。</li>
				</ul>
				<p>安世亚太将针对以上信息制定过滤和屏蔽机制，如用户在平台创建项目或储存文件时不能履行和遵守协议中的规定，安世亚太有权对违反协议的用户做出关闭帐户处理，同时保留依法追究当事人法律责任的权利。对恶意注册创成云平台账号或利用平台账号进行违法活动、捣乱、骚扰、欺骗、其他用户以及其他违反本协议的行为，安世亚太有权关闭其账号。同时，安世亚太会视司法部门的要求协助调查。 此外，安世亚太在采取移除等相应措施后不为此向原发布人承担违约责任或其他法律责任。安世亚太依据本协议约定获得处理违法违规内容的权利，该权利不构成安世亚太的义务或承诺，安世亚太不能保证及时发现违法行为或进行相应处理。</p>
				<h3>11. 信息的访问、修改和准确性</h3>
				<p>我们采取了一些合理步骤来确保我们从您那里收到的信息正确无误且始终为最新。您有权访问您的公司/团队和个人信息来验证其准确性，并纠正不准确的信息。您可以使用电子邮件地址和密码通过平台网站登录您的帐户并单击“个人设置”，随时更改您的信息。您也可以通过*******************与我们联系，访问并纠正个人信息中的错误。如果您不想获得有关产品、服务或营销计划的信息，或者希望从任何直接营销计划、企业调查、电话营销、直邮或电子邮件列表中删除您的信息，或者想限制我们对您的个人信息的使用或披露，请通过support@创成云平台.com与我们联系。</p>
				<h3>12. 业务转让</h3>
				<p>创成云平台收集的信息将被视作一项资产。如果平台、任何附属公司、部门或部分资产被另一家公司收购，则此类信息可能成为被转让资产之一。如果情况如此，收购方公司只能按照此隐私策略（或者向您收集信息时有效的任何后续策略）使用您的信息。 请注意，转让后提交或收集的信息可能必须遵守收购方公司所采用的新隐私策略。</p>
				<h3>13. 国际传输</h3>
				<p>您的信息可能被传送到或存储在您所在国家/地区、省/市/自治区或其他政府管辖区域以外的地方， 这些地方的隐私法律对您的个人信息的保护程度可能低于您所在的管辖区域。如果您位于中华人民共和国以外，您应知悉，平台可能将您的个人信息传输到中国并在中国进行处理。提交此类信息，即表示您同意此隐私策略，进而表示您同意进行此类传输。</p>
				<p>我们始终致力于改善我们的业务和运营。因此，我们的策略将不断完善。鉴于此，平台保留不时修改此隐私策略的权利。对此策略的更改将发布在创成云平台网站上，我们欢迎您不时访问我们的隐私策略页面，来查看最新隐私策略。如果我们对此隐私策略进行实质性更改，扩展了我们使用您的公司/团队和个人信息的权利，我们将通过电子邮件或者在我们网站上的显著位置告知您并征求您的同意。</p>
				<h3>14. 与我们联系</h3>
				<p>请将您对您的信息的任何访问请求、信息修改请求或有关此策略的问题发送至以下电子邮件地址：<EMAIL>。 ⼀般情况下，我们将在10个工作⽇内回复您的请求。</p>

			</div>
		</a-spin>
	</a-modal>
</template>

<script>
export default {
	name: 'DocModal',
	components: {},
	data () {
		return {
			title: '用户协议',
			drawerWidth: 1200,
			visible: false,
			confirmLoading: false,
			model: {},
		};
	},
	methods: {
		resetScreenSize () {
			const screenWidth = document.body.clientWidth;
			if (screenWidth < 500) {
				this.drawerWidth = screenWidth;
			} else {
				this.drawerWidth = 1200;
			}
		},
		// 窗口最大化切换
		toggleScreen () {
			if (this.modaltoggleFlag) {
				this.modalWidth = window.innerWidth;
			} else {
				this.modalWidth = 1200;
			}
			this.modaltoggleFlag = !this.modaltoggleFlag;
		},
		handleCancel () {
			this.close();
		},
		open () {
			this.visible = true;
		},
		close () {
			this.$emit('close');
			this.visible = false;
			this.disableSubmit = false;
		},
	},
};
</script>
<style lang="less" scoped>
.doc {
    padding: 20px;
    h3 {
        font-weight: 600;
    }
}
</style>
