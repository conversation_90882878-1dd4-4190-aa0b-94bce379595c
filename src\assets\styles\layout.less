.grid {
	display: flex;

	.grid-cell {
		flex: 1;
	}

	.grid-cell.u-full {
		flex: 0 0 100%;
	}

	.grid-cell.u-1of2 {
		flex: 0 0 50%;
	}

	.grid-cell.u-1of3 {
		flex: 0 0 33.3333%;
	}

	.grid-cell.u-1of4 {
		flex: 0 0 25%;
	}

	.grid-cell.right {
		display: flex;
		justify-content: flex-end;
	}

	.grid-cell.u-100 {
		flex: 0 0 100px;
	}

	.grid-cell.u-200 {
		flex: 0 0 200px;
	}
}

.box {
	display: flex;
	flex-wrap: wrap;
	align-content: space-between;

	.column {
		flex-basis: 100%;
		display: flex;
		justify-content: space-between;
	}
}

.display-flex-center {
	display: flex;
	align-items: center;
	justify-content: center;

	.opacity {
		margin-left: 20px;
		margin-top: 8px;
	}
}
