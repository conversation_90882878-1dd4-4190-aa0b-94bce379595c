/* ===================================
 * 项目全局配置文件
 * Created by cjking on 2021/04/08.
 * Copyright 2021, Inc.
 * =================================== */

const environments = {
	development: {
		production: false,
		wsUrl: "ws://" + location.host + "/dev/socket", // Websocket URL
		basePath: "/dev", // API地址(走代理)
		staticDomainURL: "/dev/static", // 静态资源地址(走代理)
	},
	test: {
		production: false,
		wsUrl: "ws://" + location.host + "/test/socket", // Websocket URL
		basePath: "/test", // API地址(走代理)
		staticDomainURL: "/test/static", // 静态资源地址(走代理)
	},
	production: {
		production: true,
		wsUrl: "ws://" + location.host + "/prod/socket", // Websocket URL
		basePath: "/prod", // API地址(走代理)
		staticDomainURL: "/prod/static", // 静态资源地址(走代理)
	},
};

export const Config = environments[process.env.VUE_APP_MODE] || {};
Config.environment = process.env.VUE_APP_MODE; // 执行环境
Config.log = process.env.VUE_APP_LOGGER === "true"; // 是否开启日志输出;
Config.logLine = false; // 是否开启日志行号输出;

export default Config;
