worker_processes auto;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    # websocket配置
    map $http_upgrade $connection_upgrade {
    	default upgrade;
    	'' close;
    }

    # docker自身内部不要配置127.0.0.1或localhost，因为访问的是docker自身内部，不然API接口访问不通
    # 此处写 API_INTRANET_IP 是作为模板，便于脚本替换
    upstream servers {
        server API_INTRANET_IP:8098;
    }
    upstream static {
        server STATIC_SERVER_IP:8901;
    }

    server {
        listen  80;
		server_name  localhost;
		client_max_body_size 100M;

    	location / {
			root   /usr/share/nginx/html;
            index  index.html;
            try_files $uri $uri/ /index.html =404;

	        gzip on;
	        gzip_static on;
	        gzip_buffers 32 4k;
	        gzip_comp_level 6;
	        gzip_min_length 200;
	        gzip_types text/plain text/xml application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
	        gzip_vary on;
	        keepalive_timeout  65;
        }

        # static assets proxy
        location /dev/static/ {
            proxy_pass http://static/;
        }
        location /test/static/ {
            proxy_pass http://static/;
        }
        location /prod/static/ {
            proxy_pass http://static/;
        }

        # webSocket proxy
        location /dev/socket/ {
            proxy_http_version 1.1;
            proxy_pass http://servers/;
            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_read_timeout 3600s;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
        }
        location /test/socket/ {
            proxy_http_version 1.1;
            proxy_pass http://servers/;
            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_read_timeout 3600s;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
        }
        location /prod/socket/ {
            proxy_http_version 1.1;
            proxy_pass http://servers/;
            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_read_timeout 3600s;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
        }

        # api proxy
        location /dev/ {
            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://servers/;
        }
        location /test/ {
            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://servers/;
        }
        location /prod/ {
            proxy_redirect off;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://servers/;
        }
    }
}
