<template>
	<div class="colorPicker" ref="colorPicker" v-click-outside="closePanel" @click="event => { event.stopPropagation() }">
		<!-- 颜色显示小方块 -->
		<div
			class="colorBtn"
			:style="`background-color: ${showColor}`"
			:class="{ disabled: disabled }"
			@click="openPanel"
		></div>
		<!-- 颜色色盘 -->
		<div class="box" :class="{ open: openStatus }">
			<div class="hd">
				<div class="colorView" :style="`background-color: ${showPanelColor}`"></div>
				<div
					class="defaultColor"
					@click="handleDefaultColor"
					@mouseover="hoveColor = defaultColor"
					@mouseout="hoveColor = null"
				>默认颜色
				</div>
			</div>
			<div class="bd">
				<h3>主题颜色</h3>
				<ul class="tColor">
					<li
						v-for="(color, index) of tColor"
						:key="index"
						:style="{ backgroundColor: color }"
						@mouseover="hoveColor = color"
						@mouseout="hoveColor = null"
						@click="updateValue(color)"
					></li>
				</ul>
				<ul class="bColor">
					<li v-for="(item, index) of colorPanel" :key="index">
						<ul>
							<li
								v-for="(color, cindex) of item"
								:key="cindex"
								:style="{ backgroundColor: color }"
								@mouseover="hoveColor = color"
								@mouseout="hoveColor = null"
								@click="updateValue(color)"
							></li>
						</ul>
					</li>
				</ul>
				<h3>标准颜色</h3>
				<ul class="tColor">
					<li
						v-for="(color, index) of bColor"
						:key="index"
						:style="{ backgroundColor: color }"
						@mouseover="hoveColor = color"
						@mouseout="hoveColor = null"
						@click="updateValue(color)"
					></li>
				</ul>
				<h3 @click="triggerHtml5Color">更多颜色...</h3>
				<!-- 用以激活HTML5颜色面板 -->
				<input
					type="color"
					ref="html5Color"
					v-model="html5Color"
					@change="updateValue(html5Color)"
				>
			</div>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 颜色选择器
 * Created by cjking on 2021/04/25.
 * Copyright 2021, Inc.
 * =================================== */
export default {
	name: 'ColorPicker',
	props: {
		// 当前颜色值
		value: {
			type: String,
			required: true,
		},
		// 默认颜色
		defaultColor: {
			type: String,
			default: '#000000',
		},
		// 禁用状态
		disabled: {
			type: Boolean,
			default: false,
		},
		// 白名单
		whitelist: {
			type: Array,
			default: () => [],
		},
	},
	data () {
		return {
			// 面板打开状态
			openStatus: false,
			// 鼠标经过的颜色块
			hoveColor: null,
			// 主题颜色
			tColor: ['#000000', '#FFFFFF', '#EEECE1', '#1E497B', '#4E81BB', '#E2534D', '#9ABA60', '#8165A0', '#47ACC5', '#F9974C'],
			// 颜色面板
			colorConfig: [
				['#7F7F7F', '#F2F2F2'],
				['#0D0D0D', '#808080'],
				['#1C1A10', '#DDD8C3'],
				['#0E243D', '#C6D9F0'],
				['#233F5E', '#DAE5F0'],
				['#632623', '#F2DBDB'],
				['#4D602C', '#EAF1DE'],
				['#3F3150', '#E6E0EC'],
				['#1E5867', '#D9EEF3'],
				['#99490F', '#FEE9DA'],
			],
			// 标准颜色
			bColor: ['#C21401', '#FF1E02', '#FFC12A', '#FFFF3A', '#90CF5B', '#00AF57', '#00AFEE', '#0071BE', '#00215F', '#72349D'],
			html5Color: this.value,
		};
	},
	computed: {
		// 显示面板颜色
		showPanelColor () {
			if (this.hoveColor) {
				return this.hoveColor;
			} else {
				return this.showColor;
			}
		},
		// 显示颜色
		showColor () {
			if (this.value) {
				return this.value;
			} else {
				return this.defaultColor;
			}
		},
		// 颜色面板
		colorPanel () {
			const colorArr = [];
			for (const color of this.colorConfig) {
				colorArr.push(this.gradient(color[1], color[0], 5));
			}
			return colorArr;
		},
	},
	methods: {
		openPanel () {
			this.openStatus = !this.disabled;
		},
		closePanel (e) {
			const elements = [];
			this.whitelist.forEach(ele => {
				if (ele) {
					elements.push(ele);
					if (ele.children?.length) {
						this.iteration(ele.children, elements);
					}
				}
			});
			if (!elements.includes(e.target)) {
				this.openStatus = false;
			}
		},
		iteration (list, elements = []) {
			for (const ele of list) {
				elements.push(ele);
			}
		},
		triggerHtml5Color () {
			this.$refs.html5Color.click();
		},
		// 更新组件的值 value
		updateValue (value) {
			if (value) {
				value = value.toUpperCase();
			}
			this.$emit('input', value);
			this.$emit('change', value);
			this.openStatus = false;
		},
		// 设置默认颜色
		handleDefaultColor () {
			this.updateValue(this.defaultColor);
		},
		// 格式化 hex 颜色值
		parseColor (hexStr) {
			if (hexStr.length === 4) {
				hexStr = '#' + hexStr[1] + hexStr[1] + hexStr[2] + hexStr[2] + hexStr[3] + hexStr[3];
			} else {
				return hexStr;
			}
		},
		// RGB 颜色 转 HEX 颜色
		rgbToHex (r, g, b) {
			const hex = ((r << 16) | (g << 8) | b).toString(16);
			return '#' + new Array(Math.abs(hex.length - 7)).join('0') + hex;
		},
		// HEX 转 RGB 颜色
		hexToRgb (hex) {
			hex = this.parseColor(hex);
			const rgb = [];
			for (let i = 1; i < 7; i += 2) {
				rgb.push(parseInt('0x' + hex.slice(i, i + 2)));
			}
			return rgb;
		},
		// 计算渐变过渡颜色
		gradient (startColor, endColor, step) {
			// 讲 hex 转换为 rgb
			const sColor = this.hexToRgb(startColor);
			const eColor = this.hexToRgb(endColor);

			// 计算R\G\B每一步的差值
			const rStep = (eColor[0] - sColor[0]) / step;
			const gStep = (eColor[1] - sColor[1]) / step;
			const bStep = (eColor[2] - sColor[2]) / step;

			const gradientColorArr = [];
			// 计算每一步的hex值
			for (let i = 0; i < step; i++) {
				gradientColorArr.push(this.rgbToHex(parseInt(rStep * i + sColor[0]), parseInt(gStep * i + sColor[1]), parseInt(bStep * i + sColor[2])));
			}
			return gradientColorArr;
		},
	},
};
</script>

<style lang="less" scoped>
.colorPicker {
	position: relative;
	text-align: left;
	font-size: 14px;
	display: inline-block;
	outline: none;

	ul, li, ol {
		list-style: none;
		margin: 0;
		padding: 0;
	}

	.colorBtn {
		width: 15px;
		height: 15px;

		&.disabled {
			cursor: no-drop;
		}
	}

	.box {
		position: absolute;
		width: 190px;
		background: #FFF;
		border: 1px solid #DDD;
		visibility: hidden;
		border-radius: 2px;
		margin-top: 2px;
		padding: 10px 10px 5px;
		box-shadow: 0 0 5px rgba(0, 0, 0, .15);
		opacity: 0;
		box-sizing: content-box;

		&.open {
			visibility: visible;
			opacity: 1;
			z-index: 1;
		}

		h3 {
			font-size: 14px;
			font-weight: normal;
			margin: 10px 0 5px;
			line-height: 1;
			color: #333;
		}

		input {
			visibility: hidden;
			position: absolute;
			left: 0;
			bottom: 0;
		}
	}

	.hd {
		overflow: hidden;
		line-height: 29px;

		.colorView {
			width: 100px;
			height: 30px;
			float: left;
			transition: background-color .3s ease;
		}

		.defaultColor {
			width: 80px;
			float: right;
			text-align: center;
			border: 1px solid #DDD;
			cursor: pointer;
			color: #333;
		}
	}

	.tColor {
		li {
			width: 15px;
			height: 15px;
			display: inline-block;
			margin: 0 2px;
		}

		li:hover {
			box-shadow: 0 0 5px rgba(0, 0, 0, .4);
			transform: scale(1.3);
		}
	}

	.bColor {
		li {
			width: 15px;
			display: inline-block;
			margin: 0 2px;

			li {
				display: block;
				width: 15px;
				height: 15px;
				margin: 0;
			}

			li:hover {
				box-shadow: 0 0 5px rgba(0, 0, 0, .4);
				transform: scale(1.3);
			}
		}
	}
}
</style>
