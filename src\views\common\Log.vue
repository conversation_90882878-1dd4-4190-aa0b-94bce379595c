<template>
	<div :class="{ 'log-wrap': true, 'collapsed': collapsed }">
		<div class="log-body box">
			<div class="column">
				<div class="log-resize" @mousedown="onMousedown">...</div>
				<div class="log-close" @click="closeWindow"><i>×</i></div>
			</div>
			<div class="column content" :style="{ height: height }">
				<div v-if="contents.length">
					<span style="display: block;" v-for="(content, index) in contents" :key="index">
						{{ content }}
					</span>
				</div>
				<template v-else>
					日志加载中...
				</template>
			</div>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 日志
 * Created by cjking on 2021/11/29.
 * Copyright 2021, Inc.
 * =================================== */
import { mapGetters } from 'vuex';
import { noop } from '@/utils/utils';
import { ProgressStatusEnum, WsTypeEnum } from '@/constants';
import { mixinWebSocket } from '@/mixins/modules/webSocket';

export default {
	name: 'Log',
	mixins: [
		mixinWebSocket,
	],
	props: {
		logType: {
			type: Number,
			required: true,
		},
	},
	data () {
		return {
			height: 0,
			pid: '',
			contents: [],
		};
	},
	mounted () {
		this.pid = this.$route.params.id || '';
		this.listenLogHandler();
	},
	computed: {
		...mapGetters(['userInfo', 'collapsed']),
	},
	methods: {
		/**
		 * 鼠标按下事件
		 * @param e
		 */
		onMousedown (e) {
			const el = e.target;
			// 颜色改变提醒
			el.style.background = '#818181';

			const contentEl = this.$el.querySelector('.content');
			const closeEl = this.$el.querySelector('.log-close');
			const clientHeight = document.body.clientHeight; // 765px

			// 鼠标拖动事件
			document.onmousemove = function (e2) {
				let moveLen = clientHeight - e2.clientY;
				if (moveLen < 200) moveLen = 200; // 区域的最小高度为 200px
				if (moveLen > clientHeight - 100) moveLen = clientHeight - 100; // 区域的最大高度为（clientHeight - 100px）

				this.height = -moveLen + 200 + 'px';
				el.style.top = this.height;
				closeEl.style.top = this.height;
				contentEl.style.height = moveLen + 'px';
			};
			// 鼠标松开事件
			document.onmouseup = function () {
				// 颜色恢复
				el.style.background = '#D6D6D6';
				document.onmousemove = null;
				document.onmouseup = null;
				el.releaseCapture && el.releaseCapture(); // 当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
			};
			el.setCapture && el.setCapture(); // 该函数在属于当前线程的指定窗口里设置鼠标捕获
			return false;
		},

		/**
		 * 监听日志处理程序
		 */
		listenLogHandler () {
			const type = this.logType === 0 ? WsTypeEnum.meshLog : WsTypeEnum.solveLog;
			const params = {
				type,
				user_id: this.userInfo.id,
				project_id: this.pid,
			};

			this.listenProcessStatus(params, {
				loading: false,
				callback: async (res) => {
					if (res.type === type) {
						this.contents = this.contents.concat((res?.content || '').split('\n'));
						this.$nextTick(() => {
							this.scrollToBottom();
						});
					}
				},
				errCallback: (res) => {
					if (res.type === type) {
						this.$modal.error({ content: '获取日志失败，请稍后再试!' });
					}
				},
			});
		},

		/**
		 * 监听处理状态
		 */
		async listenProcessStatus (params, options = {
			callback: noop,
			errCallback: noop,
			progressCallback: noop,
			afterProcessCallback: noop,
		}) {
			// const loading = options.loading ?? true;
			// const loadingMsg = options.loadingMsg ?? '';
			const callback = options.callback || noop;
			const errCallback = options.errCallback || noop;
			const progressCallback = options.progressCallback || noop;
			const afterProcessCallback = options.afterProcessCallback || noop;

			const key = Date.now();
			const socket = await this.initWebsocket(key, params.type);
			socket.send(params);
			this.$root.$on(`${ key }Callback`, res => {
				// 状态 1:创建中，6:后处理，8:完成，9:失败
				if (res.status === ProgressStatusEnum.SUCCESS) {
					callback(res);
				}
				if (res.status === ProgressStatusEnum.AFTER_PROCESS) {
					afterProcessCallback(res);
				}
				if (res.status === ProgressStatusEnum.FAIL) {
					errCallback(res);
				}
				if (res.status === ProgressStatusEnum.LOADING) {
					progressCallback(res);
				}
			});
		},

		/**
		 * 滚动到底部
		 */
		scrollToBottom () {
			const domWrapper = this.$el.querySelector('.content'); // 外层容器 出现滚动条的dom
			(function smoothScroll () {
				const currentScroll = domWrapper.scrollTop;   // 已经被卷掉的高度
				const clientHeight = domWrapper.offsetHeight; // 容器高度
				const scrollHeight = domWrapper.scrollHeight; // 内容总高度
				if (scrollHeight - 10 > currentScroll + clientHeight) {
					window.requestAnimationFrame(smoothScroll);
					domWrapper.scrollTo(0, 20 + currentScroll + (scrollHeight - currentScroll - clientHeight) / 2);
				}
			})();
		},

		/**
		 * 关闭窗口
		 */
		closeWindow () {
			this.$emit('close');
		},
	},
};
</script>

<style lang="less" scoped>
.log-wrap {
	position: absolute;
	bottom: 0;
	width: calc(100vw - 321px);
	margin-left: 321px;

	.no-select() {
		-ms-user-select: none;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;
	}

	.log-body {
		position: relative;
		width: 100%;
		min-height: 200px;
		display: flex;

		.log-resize {
			position: absolute;
			top: 0;
			border-radius: 3px;
			width: 20px;
			height: 12px;
			line-height: 2px;
			text-align: center;
			margin-top: -7px;
			margin-left: calc((100vw - 322px) / 2);
			background: #FFFFFF;
			cursor: ns-resize;
			z-index: 2;
			.no-select();
		}

		.log-close {
			z-index: 2;
			margin-right: 8px;
			position: absolute;
			right: 0;

			i {
				margin: 0;
				padding: 0;
				display: inline-block;
				width: 20px;
				height: 20px;
				text-align: center;
				color: #FFFFFF;
				font-style: normal;
				cursor: pointer;
			}

			&:hover {
				i {
					color: #0062A4;
				}
			}
		}

		.content {
			position: absolute;
			bottom: 0;
			width: 100%;
			overflow-y: auto;
			min-height: 200px;
			padding: 20px 20px;
			z-index: 1;
			color: #FFFFFF;
			background: #333333;
			border: 1px solid #909090;
		}
	}

	&.collapsed {
		width: 100vw;
		margin-left: 0;

		.log-body {
			.log-resize {
				margin-left: calc(100vw / 2);
			}
		}
	}
}

</style>
