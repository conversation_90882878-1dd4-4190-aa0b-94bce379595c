/**
 * 判断是否字符串
 * @returns {boolean}
 */
export const isString = (value) => typeof value === 'string';

/**
 * 判断是否对象
 * @returns {boolean}
 */
export const isObject = (args) => {
	return Object.prototype.toString.call(args) === '[object Object]';
};

/**
 * 加载文件
 * @param url
 * @param responseType arrayBuffer、blob、json、text
 * @return {Promise<any>}
 */
export async function loadFile (url, responseType = 'json') {
	try {
		const response = await fetch(url);
		return await response[responseType]();
	} catch (error) {
		// eslint-disable-next-line no-console
		console.log('Request Failed', error);
	}
}

// export function fetchAndInstantiate (url, importObject) {
// 	return fetch(url).then(response =>
// 		response.arrayBuffer(),
// 	).then(bytes =>
// 		WebAssembly.instantiate(bytes, importObject),
// 	).then(results =>
// 		results.instance,
// 	);
// }
