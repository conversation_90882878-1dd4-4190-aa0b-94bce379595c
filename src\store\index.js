import Vue from 'vue';
import Vuex from 'vuex';

import getters from '@/store/getters';
import user from '@/store/modules/user';
import spin from '@/store/modules/spin';
import task from '@/store/modules/task';

Vue.use(Vuex);

function createStore () {
	const $store = new Vuex.Store({
		state: {
			collapsed: false,
			solidNames: [],
		},
		getters: {
			...getters,
			collapsed: (state) => state.collapsed,
			solidNames: (state) => state.solidNames,
		},
		modules: {
			user,
			spin,
			task,
		},
		mutations: {
			setCollapsed (state, bool = false) {
				state.collapsed = bool;
			},
			saveSolidNames (state, solidNames) {
				state.solidNames = solidNames || [];
			},
		},
		actions: {
			saveSolidNames ({ commit }, solidNames) {
				commit('saveSolidNames', solidNames);
			},
			setCollapsed ({ commit }, bool) {
				commit('setCollapsed', bool);
			},
		},
	});

	return $store;
}

export default createStore();
