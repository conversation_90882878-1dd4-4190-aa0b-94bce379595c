<template>
	<RightMenu :visible="rightMenuVisible">
		<template #title>
			<div class="view-container" ref="viewContainer">
				<a-spin class="view-loading" size="large" :tip="tip" :spinning="loading" />
				<div
					class="js-view"
					ref="vtkContainer"
					:style="{ background: background, width: jsViewWidth }"
				>
					<!--<canvas id="mainCanvas" @contextmenu="onContextmenu" @click="onMouseClick"></canvas>-->
					<canvas id="mainCanvas" @click="onMouseClick"></canvas>
					<canvas id="arrowCanvas"></canvas>
					<div id="label"></div>
				</div>
				<div class="scale-plate">
					<CanvasRuler ref="canvasRulerRef" :parentRef="$refs.viewContainer" :long="long" v-if="rulerLoaded" />
				</div>
			</div>
		</template>
		<template #menu>
			<a-menu @click="({ key: type }) => onContextMenuClick(type)">
				<a-menu-item key="show">全部显示</a-menu-item>
				<a-menu-item key="hidden" :disabled="!contextMenuObject">隐藏</a-menu-item>
				<a-menu-item key="hiddenAllSolid">隐藏所有体</a-menu-item>
				<a-menu-item key="hiddenWireframe">隐藏线框</a-menu-item>
				<a-menu-item key="screenshot" @click="() => screenshot(taskId, false)">屏幕截图</a-menu-item>
			</a-menu>
		</template>
	</RightMenu>
</template>

<script>
/* ===================================
 * 纹理视图渲染器
 * Updated by cjking on 2022/02/18.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { mapActions, mapGetters } from 'vuex';
import { SelectModeEnum, DrawEnum, ViewEnum, RenderModeEnum, CameraModeEnum, ColorEnum, RenderOrderEnum, SOLID_GROUP, FACE_GROUP, WIREFRAME, DISPERSED_LINE_GROUP } from '@/constants';
import {
	hasOwnProperty, isEmpty, isString, pick, sleep, toExponential,
	addEventHandler, removeEventHandler, basename,
} from '@/utils/utils';

import * as THREE from 'three';
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils.js';

import {
	baseDecode, floatDecode,
	StatisticsUtils, getMaxVal,
	preventDefaults, toHexColor,
	getMinVal, getWireframeByFace, makeLine, setPolygonOffset,
} from '@/components/core/ViewUtils.js';
import { loadFile } from '@/api';
import Config from '@/config/Config';
import RightMenu from '@/components/common/RightMenu';
import CanvasRuler from '@/components/core/CanvasRuler';
import { mixinRhino } from '@/mixins/modules/rhino';
import { mixinViewAxes } from '@/mixins/modules/viewAxes';
import { ControlTypeEnum, defaultData, mixinViewCommonFun } from '@/mixins/modules/viewCommonFun';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { TrackballControls } from 'three/examples/jsm/controls/TrackballControls';

// 标记开关枚举
const TagSwitchEnum = {
	ON: 'ON',
	OFF: 'OFF',
};

const selectSwitch = TagSwitchEnum.OFF;

// -------------------------------------
// 此类属性不能放vue data 里，
// 不然严重影响性能问题，卡顿掉帧严重
let animationId;
let raycasterObjs = []; // 鼠标悬停/点击高亮显示的 obj 列表
const mouse = new THREE.Vector2();
// -------------------------------------

let selectionBox, helper, boxAllSelected;
let delCallback, enterCallback, escCallback,
	ctrlSCallback, ctrlCallback, ctrlZCallback,
	dragCallback;

// 是否独立 Ctrl 键激活
let independentCtrlKeyActivate = false;

const onKeydown = (event) => {
	const oEvent = event || window.event;
	// 获取键盘的keyCode值
	const keyCode = oEvent.keyCode || oEvent.which || oEvent.charCode;
	// 获取ctrl键对应的事件属性
	const ctrlKeyCode = oEvent.ctrlKey || oEvent.metaKey; // mac metaKey

	independentCtrlKeyActivate = ctrlKeyCode && keyCode === 17;

	logger.log('ctrlKeyCode, keyCode: ', ctrlKeyCode, keyCode);

	if (keyCode === 8) { // 删除键(回退键)
		delCallback && delCallback(event);
	}
	if (keyCode === 13) { // Enter
		enterCallback && enterCallback(event);
	}
	if (keyCode === 27) { // Esc
		escCallback && escCallback(event);
	}

	if (ctrlKeyCode && keyCode === 83) { // ctrl+s
		ctrlSCallback && ctrlSCallback(event);
	}
	if (ctrlKeyCode && keyCode === 90) { // ctrl+z
		ctrlZCallback && ctrlZCallback(event);
	}

	if (ctrlKeyCode && ctrlCallback) {
		ctrlCallback(event, independentCtrlKeyActivate);
	}
};

export default {
	name: 'TextureView',
	mixins: [mixinViewCommonFun, mixinViewAxes, mixinRhino],
	components: {
		RightMenu,
		CanvasRuler,
	},
	props: {
		hasCover: { // 是否有封面图(缩略图)
			type: Boolean,
			required: false,
			default: true,
		},
	},
	data () {
		return { ...defaultData, distanceLong: 0 };
	},
	watch: {
		collapsed () {
			this.resizeCurrentView();
		},
	},
	computed: {
		...mapGetters(['userInfo', 'partListCache']),

		curCamera: {
			cache: false,
			get () {
				return this.getCurrCamera();
			},
		},
		curControl: {
			cache: false,
			get () {
				return this.getCurrControl();
			},
		},
		defaultColor () {
			return ColorEnum.origin;
		},
		selectColor () {
			return this.selectParam ? this.selectParam.config.color : ColorEnum.white;
		},
		basePath () {
			if (this.userInfo.orgCode) {
				return `${ Config.staticDomainURL }/${ this.userInfo.orgCode }/${ this.projectId }/${ this.taskId }`;
			}
			return `${ Config.staticDomainURL }/${ this.projectId }/${ this.taskId }`;
		},
		dispersePath () {
			return `${ this.basePath }/dispersed`;
		},
		disperseStepPath () {
			return `${ this.basePath }/dispersed/step`;
		},
	},
	mounted () {
		this.initIds();
		this.initView();
	},
	methods: {
		...mapActions(['saveSolidNames']),

		initIds () {
			this.projectId = this.$route.params.id;
			this.taskId = this.$route.params.taskId;
		},

		/**
		 * 初始化视图
		 */
		async initView () {
			// this.initStats();
			this.initScene();
			this.initRenderer();
			this.initLight();
			this.initCamera();
			this.initAxes();
			this.initControls();
			this.updateRuler(true);
		},

		initDistance () {
			if (this.distanceLong) return;
			const OA = this.curCamera.position;
			const OC = this.curControl.target;
			this.distanceLong = OA.distanceTo(OC);
		},

		/**
		 * 加载场景数据
		 */
		async loadData (filePath) {
			this.initIds();
			this.openLoading();
			this.initTHREE();
			const data = await this.getData(filePath);
			this.parseData(data);
			await this.initData(data);
			this.initCamera(data);
			this.reInitControls();
			this.initEvents();
			this.initStatistics();
			this.updateRuler();
			this.closeLoading();
			this.animate();
			this.$nextTick(() => {
				this.updateView(ViewEnum.default);
			});
		},

		/**
		 * 加载场景数据
		 */
		async loadDataTxt (filePath) {
			this.initIds();
			this.openLoading();
			this.initTHREE();
			const data = await this.getData(filePath);
			this.parseData(data);
			await this.initData(data);
			this.initCamera(data);
			this.reInitControls();
			this.initEvents();
			this.initStatistics();
			this.updateRuler();
			this.closeLoading();
			this.animate();
			this.$nextTick(() => {
				this.updateView(ViewEnum.default);
			});
		},

		/**
		 * 加载场景数据
		 */
		async loadDataV2 (result) {
			this.openLoading();
			if (result['solid']) {
				const solid = await this.loadRhinoBase64('solid', result);
				raycasterObjs.push(...solid);
			}
			if (result['faces']) {
				const faces = await this.loadRhinoBase64('faces', result);
				raycasterObjs.push(...faces);
			}
			if (result['lines']) {
				const lines = await this.loadRhinoBase64('lines', result);
				raycasterObjs.push(...lines);
			}
			if (result['other_curve_points']) {
				const other_curve_points = await this.loadRhinoBase64('other_curve_points', result);
				raycasterObjs.push(...other_curve_points);
			}

			logger.log('raycasterObjs.length: ', raycasterObjs.length);
			if (raycasterObjs.length && result.boundingBox) {
				await this.initSceneByBoundingBox(result.relative_path + '/' + result.boundingBox);
			}

			// 初始化缩略图
			this.initThumbnail();

			this.closeLoading();
		},

		/**
		 * 加载场景数据
		 */
		async loadDataV3 (result) {

			if (result?.error) {
				this.$modal.error({ content: result.error });
			}

			this.initIds();
			this.openLoading();

			let meshes, faces, crk_curves, other_curves;
			if (result['meshes']) {
				meshes = await this.loadRhinoFacesJson('meshes', result);
				if (meshes?.length) {
					raycasterObjs.push(...meshes);
				}
			}
			if (result['faces']) {
				faces = await this.loadRhinoFacesJson('faces', result);
				if (faces?.length) {
					raycasterObjs.push(...faces);
				}
			}
			if (result['crk_curves']) {
				const filePath = `${ this.dispersePath }/${ result['crk_curves'] }`;
				crk_curves = await this.loadRhinoLinesJson(filePath, { decompress: false, customLineAttributes: true });
				if (crk_curves?.length) {
					let curveIndex = 0;
					for (const crkCurve of crk_curves) {
						curveIndex++;
						crkCurve.canDelete = true;
						crkCurve.isWireframe = true; // 是否边框线
						crkCurve.visible = true;
						crkCurve.linewidth = 1;
						crkCurve.groupName = WIREFRAME;
						crkCurve.renderOrder = RenderOrderEnum.line;
						crkCurve.nickname = 'crk_' + curveIndex;
						raycasterObjs.push(crkCurve);
					}
				}
			}
			if (result['other_curves']) {
				const filePath = `${ this.dispersePath }/${ result['other_curves'] }`;
				other_curves = await this.loadRhinoLinesJson(filePath, { nicknamePrefix: 'zyx_', decompress: false });
				if (other_curves?.length) {
					raycasterObjs.push(...other_curves);
				}
			}
			logger.log('raycasterObjs.length: ', raycasterObjs.length);
			if (raycasterObjs.length && result.boundingBox) {
				await this.initSceneByBoundingBox(result.relative_path + '/' + result.boundingBox);
			}

			this.updateView(ViewEnum.default);

			// 初始化缩略图
			this.initThumbnail();

			// 释放，回收内存
			this.$nextTick(() => {
				meshes = null;
				faces = null;
				crk_curves = null;
				other_curves = null;
				this.closeLoading();
			});
		},

		async loadData3DMOrSTL (dispersedPath) {
			this.openLoading();
			dispersedPath = Config.staticDomainURL + '/' + dispersedPath;
			this.fileName = basename(dispersedPath);
			if (dispersedPath.endsWith('.3dm')) {
				const children = await this.load3DM(dispersedPath, { needChildren: true });
				logger.log('loadData3DMOrSTL children: ', children);
				const generationMesh = (child, idx) => {
					const material = this.getMeshMaterial(ColorEnum.default, 1);
					const mesh = new THREE.Mesh(child.geometry, material);
					mesh.isFace = true;
					mesh.name = mesh.uuid;
					mesh.solidGroup = 'solid_1';
					mesh.nickname = 'face_' + idx;
					mesh.canDelete = true; // 是否可以删除
					mesh.renderOrder = RenderOrderEnum.face;
					raycasterObjs.push(mesh);
				};
				let cIndex = 0;
				for (const child of children) {
					cIndex++;
					if (child instanceof THREE.Mesh) {
						generationMesh(child, cIndex);
					} else if (child instanceof THREE.Object3D) {
						let index = 0;
						for (const obj of child.children) {
							index++;
							if (obj instanceof THREE.Mesh) {
								generationMesh(child, index);
							}
						}
					}
					if (child instanceof THREE.Line) {
						const array = child.geometry.attributes.position.array;
						const { line } = makeLine(this.getCanvas(), array);
						raycasterObjs.push(line);
					}
				}
			}
			if (dispersedPath.endsWith('.stl')) {
				const mesh = await this.loadSTL(dispersedPath);
				mesh.isFace = true;
				raycasterObjs.push(mesh);
			}

			// await this.initSceneByBoundingBox(`${ this.basePath }/dispersed/vertices.txt`);
			await this.initSceneByBoundingBox('/npm/boundingBox.txt');
			this.closeLoading();

			// 初始化缩略图
			this.initThumbnail();
		},

		/**
		 * 通过边界框初始化场景
		 * @param boundingBoxPath
		 * @returns {Promise<void>}
		 */
		async initSceneByBoundingBox (boundingBoxPath) {
			const vertices = [];
			if (!boundingBoxPath.startsWith(Config.staticDomainURL)) {
				boundingBoxPath = Config.staticDomainURL + '/' + boundingBoxPath;
			}
			const textContent = await loadFile(boundingBoxPath, 'text', false);
			if (isString(textContent)) {
				const verticesArray = textContent.split('\n').filter(item => item).map(str => str.replace(/[{|}\s]/g, '').split(','));
				verticesArray.forEach(arr => {
					arr = arr.map(Number);
					vertices.push(arr[0], arr[1], arr[2]);
				});
			}

			const newData = { objects: [{ verts: vertices }] };

			// 求对角线
			// 公式：l = a^2 + b^2 + c^2
			const globalMaxMin = [
				{ min: null, max: null },
				{ min: null, max: null },
				{ min: null, max: null },
			];
			for (let v = 0; v < vertices.length; v++) {
				if (isEmpty(globalMaxMin[v % 3]) || vertices[v] < globalMaxMin[v % 3].min) {
					globalMaxMin[v % 3].min = vertices[v];
				}
				if (isEmpty(globalMaxMin[v % 3]) || vertices[v] > globalMaxMin[v % 3].max) {
					globalMaxMin[v % 3].max = vertices[v];
				}
			}
			const xMin = globalMaxMin[0].min;
			const xMax = globalMaxMin[0].max;
			const yMin = globalMaxMin[1].min;
			const yMax = globalMaxMin[1].max;
			const zMin = globalMaxMin[2].min;
			const zMax = globalMaxMin[2].max;
			const a = Math.pow(Math.abs(xMax - xMin), 2);
			const b = Math.pow(Math.abs(yMax - yMin), 2);
			const c = Math.pow(Math.abs(zMax - zMin), 2);
			const modelLong = Math.sqrt(a + b + c);
			this.modelLong = modelLong;

			for (let i = 2; i <= vertices.length; i += 3) {
				this.zList.push(vertices[i]);
			}

			this.getCenter(false, false);

			this.minZ = getMinVal(this.zList);
			this.maxZ = getMaxVal(this.zList);
			this.lenZ = Math.abs(this.maxZ - this.minZ);
			this.zList.length = 0;
			logger.log('this.maxZ: ', this.maxZ);
			logger.log('this.lenZ: ', this.lenZ);

			this.initCamera(newData);
			this.reInitControls();
			this.initEvents();
			this.initStatistics();
			this.updateRuler();
			this.closeLoading();
			this.animate();
		},

		/**
		 * 初始化缩略图
		 */
		initThumbnail () {
			if (raycasterObjs.length > 0 && !this.hasCover) {
				sleep(500, () => this.screenshot(this.taskId, true));
			}
		},

		/**
		 * 获取数据
		 */
		async getData (filePath) {
			if (!filePath.startsWith('/')) {
				filePath = '/' + filePath;
			}
			// const data = (await import('../../../public/models/json/data.json')).default;
			const data = await loadFile(Config.staticDomainURL + filePath, 'json', false);
			data.compressed = 'true';
			data.base = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!#$%&()*+-:;/=>?@[]^_,.{|}~`';
			data.baseFloat = ',.-0123456789';
			data.camera = {
				'type': 'Orthographic',
				// 'type': 'Perspective',
				'position_x': 0,
				'position_y': 0,
				'position_z': 200,
			};

			const { xMin, xMax, yMin, yMax, zMin, zMax } = data; // 获取显示的边界
			// 求对角线
			// 公式：l = a^2 + b^2 + c^2
			const a = Math.pow(Math.abs(xMax - xMin), 2);
			const b = Math.pow(Math.abs(yMax - yMin), 2);
			const c = Math.pow(Math.abs(zMax - zMin), 2);
			const modelLong = Math.sqrt(a + b + c);
			this.modelLong = modelLong;

			this.jsonData = data;

			return data;
		},

		/**
		 * 初始化THREE视图方向
		 */
		initTHREE (up = [0, 0, 1]) {
			// Z is up for FreeCAD
			// THREE.Object3D.DefaultUp = new THREE.Vector3(...up);

			this.curCamera.up.set(...up);
			this.curCamera.lookAt(0, 0, 0);
		},

		/**
		 * 初始化统计
		 */
		initStats () {
			this.stats = new Stats();
			document.body.appendChild(this.stats.dom);
		},

		/**
		 * 初始化场景
		 */
		initScene () {
			const scene = new THREE.Scene();
			const canvas = document.querySelector('#mainCanvas');
			this.setScene(scene);
			this.setCanvas(canvas);
		},

		/**
		 * 初始化渲染场景
		 */
		initRenderer () {
			const renderer = new THREE.WebGLRenderer({
				alpha: true,
				antialias: true,
				canvas: this.getCanvas(),
				sortObjects: true,
			});
			// Clear bg so we can set it with css
			renderer.setClearColor(ColorEnum.black, 0);
			this.setRenderer(renderer);
		},

		/**
		 * 初始化相机
		 */
		initCamera (data) {
			data = data || this.jsonData;
			// Get bounds for global clipping
			const globalMaxMin = [
				{ min: null, max: null },
				{ min: null, max: null },
				{ min: null, max: null },
			];
			if (data?.objects?.length) {
				for (const obj of data.objects) {
					for (let v = 0; v < obj.verts.length; v++) {
						if (isEmpty(globalMaxMin[v % 3]) || obj.verts[v] < globalMaxMin[v % 3].min) {
							globalMaxMin[v % 3].min = obj.verts[v];
						}
						if (isEmpty(globalMaxMin[v % 3]) || obj.verts[v] > globalMaxMin[v % 3].max) {
							globalMaxMin[v % 3].max = obj.verts[v];
						}
					}
				}
			}
			let bigRange = 0;
			// add a little extra
			for (const i of globalMaxMin) {
				const range = i.max - i.min;
				if (range > bigRange) {
					bigRange = range;
				}
				i.min -= range * 0.01;
				i.max += range * 0.01;
			}
			const camCenter = new THREE.Vector3(
				0.5 * (globalMaxMin[0].max - globalMaxMin[0].min) + globalMaxMin[0].min,
				0.5 * (globalMaxMin[1].max - globalMaxMin[1].min) + globalMaxMin[1].min,
				0.5 * (globalMaxMin[2].max - globalMaxMin[2].min) + globalMaxMin[2].min,
			);

			const viewSize = 1.5 * bigRange; // make the view area a little bigger than the object
			this.viewSize = viewSize;
			const canvas = this.getCanvas();
			const aspectRatio = canvas.clientWidth / canvas.clientHeight;
			this.originalAspect = aspectRatio;

			this.globalMaxMin = globalMaxMin;
			let cameraType = data?.camera?.type ?? 'Orthographic';
			if (this.zoomSpeed === 0) {
				cameraType = 'Perspective';
			}
			this.setCameraType(cameraType);

			const initCameraPosition = (camera) => {
				camera.position.set(0, 0, 200);
				camera.lookAt(camCenter);
				camera.updateMatrixWorld();

				this.camToSave[cameraType].position = camera.position.clone();
				this.camToSave[cameraType].rotation = camera.rotation.clone();
			};

			const perspectiveCamera = new THREE.PerspectiveCamera(50, aspectRatio, 1, this.far);
			initCameraPosition(perspectiveCamera);
			this.setPerspectiveCamera(perspectiveCamera);

			const orthographicCamera = new THREE.OrthographicCamera(-aspectRatio * viewSize / 2, aspectRatio * viewSize / 2, viewSize / 2, -viewSize / 2, -this.far, this.far);
			initCameraPosition(orthographicCamera);
			this.setOrthographicCamera(orthographicCamera);
		},

		/**
		 * 获取中心
		 */
		getCenter (openBoxHelper = true, drawCenterPoint = false) {

			// 在3D空间中表示一个盒子或立方体。其主要用于表示物体在世界坐标中的边界框
			const box = new THREE.Box3();

			// 体加载
			const solidGroup = new THREE.Group();
			solidGroup.name = SOLID_GROUP;
			solidGroup.isSolid = true; // 表示体
			solidGroup.canDelete = true;
			raycasterObjs.forEach(solid => solid.isSolid && solidGroup.add(solid));
			this.getScene().add(solidGroup);
			if (solidGroup.children.length) {
				box.expandByObject(solidGroup);
			}

			// 面加载
			const faceGroup = new THREE.Group();
			faceGroup.name = FACE_GROUP;
			faceGroup.isFace = true; // 表示面
			faceGroup.canDelete = true;
			raycasterObjs.forEach(mesh => mesh.isFace && faceGroup.add(mesh));
			// this.getScene().add(faceGroup);
			if (faceGroup.children.length) {
				box.expandByObject(faceGroup);
			}

			// --------- 面合并开始 -----------
			if (faceGroup.children.length) {
				const geometryArray = faceGroup.children.map(child => child.geometry.clone());
				// const materialArray = faceGroup.children.map(child => child.materialArray?.clone?.()).filter(m => m);
				const meshPhongMaterial = new THREE.MeshPhongMaterial({
					// emissive: ColorEnum.red,
					emissive: new THREE.Color('#333333'),
					// specular: 0x444444, // 高光部分的颜色
					// shininess: 20, // 高光部分的亮度，默认30
				});
				// 合并模型
				if (geometryArray.length) {
					const mergedGeometries = BufferGeometryUtils.mergeBufferGeometries(geometryArray, true);
					const singleMergeMesh = new THREE.Mesh(mergedGeometries, meshPhongMaterial);
					singleMergeMesh.isSolid = true;
					singleMergeMesh.solidGroup = 'solid_1';
					singleMergeMesh.visible = this.originVisible;
					singleMergeMesh.material.side = THREE.DoubleSide;
					singleMergeMesh.material.needsUpdate = true;
					singleMergeMesh.renderOrder = RenderOrderEnum.face;
					// 设置多边形偏移
					setPolygonOffset(singleMergeMesh.material, 2, 1);
					solidGroup.add(singleMergeMesh);
					raycasterObjs = raycasterObjs.filter(obj => !obj.isFace);
					raycasterObjs.push(singleMergeMesh);
				}
			}
			// --------- 面合并结束 -----------

			// 线框加载
			const wireframeGroup = new THREE.Group();
			wireframeGroup.name = WIREFRAME;
			wireframeGroup.isWireframe = true; // 表示线框
			wireframeGroup.canDelete = true;
			raycasterObjs.forEach(wire => wire.isWireframe && wireframeGroup.add(wire));
			this.getScene().add(wireframeGroup);

			// 离散线加载
			const lineGroup = new THREE.Group();
			lineGroup.name = DISPERSED_LINE_GROUP;
			lineGroup.isDispersed = true; // 表示离散线
			lineGroup.canDelete = true;
			raycasterObjs.forEach(line => line.isDispersed && lineGroup.add(line));
			this.getScene().add(lineGroup);

			// 返回包围盒的宽度，高度，和深度
			const boxSize = box.getSize(new THREE.Vector3());

			let group;
			if (solidGroup.children.length) {
				group = solidGroup;
			}
			if (faceGroup.children.length) {
				group = faceGroup;
			}
			// box辅助框，测试完毕可删除
			if (openBoxHelper) {
				const boxHelper = new THREE.BoxHelper(group, ColorEnum.yellow);
				this.getScene().add(boxHelper);
				boxHelper.geometry.computeBoundingBox(); // 绑定盒子模型
			}

			// 返回包围盒的中心点
			const center = box.getCenter(new THREE.Vector3());
			this.camCenter = center.clone();
			this.setCameraCenter(center.clone());
			if (drawCenterPoint) {
				this.drawSphere(this.camCenter, null, 2);
			}

			return { center, boxSize, group };
		},

		/**
		 * 分配网格
		 */
		assignMesh (positions, color, opacity, faces, name, faceName = undefined, isLackFace = false) {

			const hasNaN = positions.some(x => isNaN(x));
			if (hasNaN) {
				this.$error({ content: `模型面【${ faceName }】存在异常，请修复后重新上传！` });
			}

			color = isLackFace ? ColorEnum.red : color;
			const material = this.getMeshMaterial(color, opacity);
			const mesh = this.createMeshByPosition(positions, material);
			mesh.name = mesh.uuid;
			mesh.solidGroup = name;
			mesh.nickname = faceName;
			mesh.isFace = true;
			mesh.canDelete = true; // 是否可以删除
			mesh.isLackFace = isLackFace; // 是否坏面(缺失)
			mesh.visible = this.originVisible;
			mesh.renderOrder = RenderOrderEnum.face;
			faces.push(mesh.uuid);

			if (!isLackFace) raycasterObjs.push(mesh);
			else {
				this.getScene().add(mesh);
			}

			let list = mesh.geometry.attributes.position.array;
			for (let i = 2; i <= list.length; i += 3) {
				this.zList.push(list[i]);
			}

			if (!this.solidNames.includes(name)) {
				this.solidNames.push(name);
			}
			list = null;
		},

		/**
		 * 获取网格材质
		 */
		getMeshMaterial (color, opacity) {
			let depthWrite = false;
			let transparent = false;
			if (opacity !== 1.0) {
				transparent = true;
			} else {
				depthWrite = true;
			}
			const material = new THREE.MeshLambertMaterial({
				color: new THREE.Color(color),
				emissive: new THREE.Color(0x333333),
				side: THREE.DoubleSide,
				vertexColors: false,
				flatShading: false,
				opacity: opacity,
				depthWrite: depthWrite,
				transparent: transparent,
				fog: false,
			});
			return material;
		},

		/**
		 * 按 positions 创建网格
		 */
		createMeshByPosition (positions, material) {
			let baseGeometry = new THREE.BufferGeometry();
			baseGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
			baseGeometry = BufferGeometryUtils.mergeVertices(baseGeometry);
			baseGeometry.computeVertexNormals();
			baseGeometry.computeBoundingSphere();

			// // ==================================
			// // 修复表面光滑度，有缺陷，中心点有偏移!!!
			// // EdgeSplitModifier is used to combine verts so that smoothing normals can be generated WITHOUT removing the hard edges of the design
			// // REF: https://threejs.org/examples/?q=edge#webgl_modifier_edgesplit - https://github.com/mrdoob/three.js/pull/20535
			// const edgeSplit = new EdgeSplitModifier();
			// const cutOffAngle = 180;
			// baseGeometry = edgeSplit.modify(baseGeometry, cutOffAngle * Math.PI / 180);
			// baseGeometry.computeVertexNormals();
			// baseGeometry.computeBoundingSphere();
			// // ==================================

			const mesh = new THREE.Mesh(baseGeometry, material);
			return mesh;
		},

		/**
		 * 初始化数据
		 */
		async initData (data) {

			// sync global config
			if (this.partListCache?.length) {
				this.originColor = this.partListCache[0].color;
				this.originOpacity = this.partListCache[0].opacity;
				this.originVisible = this.partListCache[0].visible;
			}

			return new Promise(async (resolve) => {
				this.solidNames = [];
				if (data?.objects?.length) {
					for (const obj of data.objects) {
						// Each face gets its own material because they each can
						// have different colors
						const faces = [];
						if (obj.facesToFacets.length > 0) {
							for (let f = 0; f < obj.facesToFacets.length; f++) {
								const faceColor = obj.faceColors.length > 0 ? obj.faceColors[f] : obj.color;
								const positions = new Float32Array(obj.facesToFacets[f].length * 9);
								for (let a = 0; a < obj.facesToFacets[f].length; a++) {
									for (let b = 0; b < 3; b++) {
										for (let c = 0; c < 3; c++) {
											positions[9 * a + 3 * b + c] = obj.verts[3 * obj.facets[3 * obj.facesToFacets[f][a] + b] + c];
										}
									}
								}
								this.assignMesh(positions, faceColor, obj.opacity, faces, obj.name, obj.faceNames[f], obj.lackFaceNames?.includes(obj.faceNames[f]));
							}
						} else {
							// No facesToFacets means that there was a tessellate()
							// mismatch inside FreeCAD. Use all facets in object to
							// create this mesh
							if (obj.facets.length) {
								const positions = new Float32Array(obj.facets.length * 3);
								for (let a = 0; a < obj.facets.length; a++) {
									for (let b = 0; b < 3; b++) {
										positions[3 * a + b] = obj.verts[3 * obj.facets[a] + b];
									}
								}
								this.assignMesh(positions, obj.color, obj.opacity, faces, obj.name);
							}
						}
					}

					this.saveSolidNames(this.solidNames);
				}

				// 加载线框
				if (this.solidNames.length) {
					logger.log('----- 加载线框(前端绘制) ------');
					const group = new THREE.Group();
					group.isWireframe = true;
					group.name = 'wireframe';
					group.visible = this.originVisible;
					raycasterObjs.forEach((mesh, index) => {
						if (mesh.isFace) {
							const linePavement = getWireframeByFace(mesh, this.getCanvas(), 1);
							linePavement.name = linePavement.uuid;
							linePavement.canDelete = true;
							linePavement.isWireframe = true; // 是否线框
							linePavement.nickname = 'e_' + (index + 1);
							linePavement.groupName = 'wireframe';
							linePavement.renderOrder = RenderOrderEnum.line;
							group.add(linePavement);
						}
					});
					this.getScene().add(group);
					this.requestRender();
				} else {
					logger.log('----- 加载线框(后端返回) ------');
					const filePath = `${ this.disperseStepPath }/wire.3dm`;
					await this.load3DMLines(filePath, {
						message: '加载线框',
						groupAttr: 'isWireframe',
						groupName: 'wireframe',
						allowSelect: false,
						lineHandler: (line, index) => {
							line.nickname = 'e_' + (index++);
						},
					});
				}

				this.getCenter(false, false);

				this.initMinMaxZ();

				// setTimeout(async () => {
				// 	this.updateAllVisible(false);
				// 	this.updateAllResultVisible(false);
				// 	await sleep(100);
				// 	await this.loadRhinoBase64();
				// }, 3000);

				// 初始化缩略图
				this.initThumbnail();

				resolve();
			});
		},

		/**
		 * 初始化最小最大 Z
		 */
		initMinMaxZ () {
			this.minZ = getMinVal(this.zList);
			this.maxZ = getMaxVal(this.zList);
			this.lenZ = Math.abs(this.maxZ - this.minZ);
			this.zList.length = 0;
			logger.log('this.maxZ: ', this.maxZ);
			logger.log('this.lenZ: ', this.lenZ);
		},

		/**
		 * 初始化光
		 * HemisphereLight gives different colors of light from the top
		 * and bottom simulating reflected light from the 'ground' and
		 * 'sky'
		 */
		initLight () {
			const scene = this.getScene();

			// 从顶部和底部发出不同颜色的光，模拟来自“地面”和“天空”的反射光
			scene.add(new THREE.HemisphereLight(0xC7E8FF, 0xFFE3B3, 0.4));

			const light1 = new THREE.DirectionalLight(ColorEnum.white, 0.4);
			light1.position.set(5, -2, 3);
			scene.add(light1);

			const light2 = new THREE.DirectionalLight(ColorEnum.white, 0.4);
			light2.position.set(-5, 2, 3);
			scene.add(light2);
		},

		/**
		 * 导航变化(视图切换)
		 */
		navChange (v, flag) {
			if (!this.initPosition) {
				this.initPosition = new THREE.Vector3();
				new THREE.Box3().setFromObject(this.getScene()).getSize(this.initPosition);
			}
			const t = this.initPosition;
			const value = Math.max(t.x, t.y, t.z);
			const vector = flag ? (new THREE.Vector3(
				v[0] * t.x * 2 + (this.camCenter?.x || 0),
				v[1] * t.y * 2 + (this.camCenter?.y || 0),
				v[2] * t.z * 2 + (this.camCenter?.z || 0),
			)) : (new THREE.Vector3(
				v[0] * value * 2.5 + (this.camCenter?.x || 0),
				v[1] * value * 2.5 + (this.camCenter?.y || 0),
				v[2] * value * 2.5 + (this.camCenter?.z || 0),
			));

			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			if (perspectiveControls) {
				perspectiveControls.object?.position.copy(vector);
				if (this.camCenter) {
					perspectiveControls.target = this.camCenter.clone();
				}
				perspectiveControls.update();
			}

			if (orthographicControls) {
				orthographicControls.object.position.copy(vector);
				if (this.camCenter) {
					orthographicControls.target = this.camCenter.clone();
				}
				orthographicControls.update();
			}
		},

		/**
		 * 更改渲染模式
		 * @param mode
		 */
		changeRenderMode (mode) {
			this.curRenderMode = mode;
			// 面和体的材质变化
			if (this.curRenderMode === RenderModeEnum.surfaceAndWireframe) {
				// 体 + 线框模式
				this.setFaceAndEdgeVisible(true);
			} else if (this.curRenderMode === RenderModeEnum.surface) {
				// 体
				this.setFaceVisible(true);
				this.setEdgeVisible(false);
			} else if (this.curRenderMode === RenderModeEnum.wireframe) {
				// 线框模式
				this.setFaceVisible(false);
				this.setEdgeVisible(true);
			}
		},

		/**
		 * 更新剪裁
		 * @params clip
		 * @params clippingReverse 反转方向
		 */
		updateClipping (clip, clippingReverse) {
			const renderer = this.getRenderer();
			if (!renderer) {
				return;
			}
			if (clip.clippingX <= 100 || clip.clippingY <= 100 || clip.clippingZ <= 100) {
				if (!renderer.clippingPlanes) renderer.clippingPlanes = [];
				if (renderer.clippingPlanes.length === 0) {
					this.clipPlaneX = new THREE.Plane(new THREE.Vector3(+clip.xx, +clip.xy, +clip.xz), 0);
					this.clipPlaneY = new THREE.Plane(new THREE.Vector3(+clip.yx, +clip.yy, +clip.yz), 0);
					this.clipPlaneZ = new THREE.Plane(new THREE.Vector3(+clip.zx, +clip.zy, +clip.zz), 0);
					renderer.clippingPlanes.push(this.clipPlaneX, this.clipPlaneY, this.clipPlaneZ);
				} else {
					this.clipPlaneX.setComponents(+clip.xx, +clip.xy, +clip.xz, 0);
					this.clipPlaneY.setComponents(+clip.yx, +clip.yy, +clip.yz, 0);
					this.clipPlaneZ.setComponents(+clip.zx, +clip.zy, +clip.zz, 0);
				}
			}
			if (clippingReverse.x) {
				this.clipPlaneX.negate();
			} else {
				this.clipPlaneX.normalize();
			}

			if (clippingReverse.y) {
				this.clipPlaneY.negate();
			} else {
				this.clipPlaneY.normalize();
			}

			if (clippingReverse.z) {
				this.clipPlaneZ.negate();
			} else {
				this.clipPlaneZ.normalize();
			}
			this.clipPlaneX.constant = (this.globalMaxMin[0].max - this.globalMaxMin[0].min) * clip.clippingX / 100.0 + this.globalMaxMin[0].min;
			this.clipPlaneY.constant = (this.globalMaxMin[1].max - this.globalMaxMin[1].min) * clip.clippingY / 100.0 + this.globalMaxMin[1].min;
			this.clipPlaneZ.constant = (this.globalMaxMin[2].max - this.globalMaxMin[2].min) * clip.clippingZ / 100.0 + this.globalMaxMin[2].min;
			this.requestRender();
		},

		/**
		 * 相机切换
		 */
		cameraChange (v, bool) {
			this.setControlType(v);
			this.requestRender();
			setTimeout(() => {
				!bool && this.resetView();
			}, 1);
		},

		/**
		 * 鼠标左键设置
		 * @param value
		 */
		mouseLeftSetting (value) {
			// this.mouse.LEFT = value;
			// this.mouse.RIGHT = value === THREE.MOUSE.PAN ? THREE.MOUSE.ROTATE : THREE.MOUSE.PAN;
			// this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 鼠标右键设置
		 * @param value
		 */
		mouseRightSetting (value) {
			// this.mouse.RIGHT = value;
			// this.mouse.LEFT = value === THREE.MOUSE.PAN ? THREE.MOUSE.ROTATE : THREE.MOUSE.PAN;
			// this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 初始化控件
		 */
		initControls (controlType = ControlTypeEnum.Trackball) {

			this.setControlType(controlType);

			// if (this.getCameraType() === 'Perspective') {
			// 初始化透视相机控制器
			const perspectiveControls = this.createControl(this.getPerspectiveCamera(), this.camCenter);
			this.setPerspectiveControls(perspectiveControls);

			if (perspectiveControls?.target.clone) {
				this.camToSave['Perspective'].controlCenter = perspectiveControls.target.clone();
			}
			// } else {
			// 初始化正交相机控制器
			const orthographicControls = this.createControl(this.getOrthographicCamera(), this.camCenter);
			this.setOrthographicControls(orthographicControls);

			if (orthographicControls?.target.clone) {
				this.camToSave['Orthographic'].controlCenter = orthographicControls.target.clone();
			}
			// }
		},

		/**
		 * 重新初始化控件
		 */
		reInitControls (controlType) {
			this.getOrthographicControls()?.dispose();
			this.setOrthographicControls(null);

			this.getPerspectiveControls()?.dispose();
			this.setPerspectiveControls(null);

			this.initControls(controlType);

			cancelAnimationFrame(animationId);
			this.animate();
		},

		/**
		 * 关于控制器变化
		 */
		onControlChange (callRender = true) {
			this.updateRuler();
			this.updateGridPlane();
			callRender && this.requestRender();
		},

		/**
		 * 更新标尺
		 */
		updateRuler (isInit = false) {
			if (isInit) {
				this.rulerLoaded = true;
				sleep(1, () => this.$refs.canvasRulerRef?.init());
				return;
			}

			const zoom = this.curCamera.zoom;
			if (this.lastZoom !== zoom) {
				// logger.log('放大倍数: ', zoom);
				this.long = Number(toExponential(this.modelLong / zoom, 2));
				this.lastZoom = zoom;
				sleep(1, () => this.$refs.canvasRulerRef?.init(this.long));
			}
			this.rulerLoaded = true;
		},

		/**
		 * 初始化事件
		 */
		initEvents () {
			const renderer = this.getRenderer();

			ctrlSCallback = (event) => {
				preventDefaults(event);
				this.onSubmitHandler();
			};

			addEventHandler(window, 'keydown', onKeydown, true);
			addEventHandler(window, 'resize', this.onMainCanvasResize, true);

			// init renderer Event
			// 禁用右键默认行为
			document.oncontextmenu = (evt) => {
				evt.preventDefault();
				return false;
			};
			// addEventHandler(renderer.domElement, 'click', this.onMouseClick, true);
			addEventHandler(renderer.domElement, 'wheel', this.onMouseWheel, true);
			addEventHandler(renderer.domElement, 'mousemove', this.onMouseMove, true);

			this.onMainCanvasResize();
			this.requestRender();
		},

		/**
		 * 提交处理程序
		 */
		onSubmitHandler () {
			this.$emit('submit');
		},

		/**
		 * 在主画布上调整大小
		 */
		onMainCanvasResize () {
			const canvas = this.getCanvas();
			const renderer = this.getRenderer();
			const orthographicCamera = this.getOrthographicCamera();
			const perspectiveCamera = this.getPerspectiveCamera();
			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			const pixelRatio = window.devicePixelRatio;
			const width = canvas.clientWidth * pixelRatio | 0;
			const height = canvas.clientHeight * pixelRatio | 0;
			const needResize = canvas.width !== width || canvas.height !== height;
			const aspect = canvas.clientWidth / canvas.clientHeight;

			if (needResize) {
				renderer.setSize(width, height, false);
				const change = this.originalAspect / aspect;
				const newSize = this.viewSize * change;
				if (orthographicCamera) {
					orthographicCamera.left = -aspect * newSize / 2;
					orthographicCamera.right = aspect * newSize / 2;
					orthographicCamera.top = newSize / 2;
					orthographicCamera.bottom = -newSize / 2;
					orthographicCamera.updateProjectionMatrix();
				}

				if (perspectiveCamera) {
					perspectiveCamera.aspect = canvas.clientWidth / canvas.clientHeight;
					perspectiveCamera.updateProjectionMatrix();
				}

				if (this.openAnimate) {
					if (perspectiveControls) {
						perspectiveControls.handleResize();
					}
					if (orthographicControls) {
						orthographicControls.handleResize();
					}
				}
			}
			this.requestRender();
		},

		/**
		 * 初始化统计场景的组件和索引
		 */
		initStatistics () {
			const statisticsUtils = new StatisticsUtils();
			const info = statisticsUtils.statistic(this.getScene());
			logger.log('info: ', info);
		},

		/**
		 * 鼠标点击处理
		 */
		onMouseClick (e) {

			preventDefaults(e);

			this.rightMenuVisible = false;

			if (!this.solidNames.length) {
				return;
			}

			if (selectionBox) return;

			this.updatePointer(e);

			this.rightMenuVisible = false; // 关闭右键菜单

			const intersects = this.selectPartsHandle(e);
			logger.log('onMouseClick intersects: ', intersects);
		},

		/**
		 * 鼠标移动处理
		 */
		onMouseMove (e) {

			this.updatePointer(e);

			if (selectionBox) {
				return false;
			}

			if (!this.curCamera) {
				return false;
			}

			if (!this.selectParam) {
				return false;
			}

			let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());
			// 过滤出所有线
			if (this.selectMode === SelectModeEnum.line) {
				intersects = intersects.filter(item => this.isLine(item.object) && !this.isModelWireframe(item.object));
			}
			// 过滤出所有面
			if (this.selectMode === SelectModeEnum.surface && !this.curDraw) {
				intersects = intersects.filter(item => this.isFace(item.object));
			}

			let tipObj = {};
			if (intersects.length > 0) {
				// 多个相交物体获取离镜头最近的
				const clickVisibleMesh = intersects?.[0].object; // 需操作的网格模型
				if (clickVisibleMesh) {
					if (clickVisibleMesh.nickname) {
						tipObj = { nickname: clickVisibleMesh.nickname };
					} else if (clickVisibleMesh.groupName) {
						tipObj = { groupName: clickVisibleMesh.groupName };
					}
				}
			}
			this.$emit('tip', tipObj);
			return intersects;
		},

		/**
		 * 鼠标右键事件
		 */
		onContextmenu (e) {
			this.updatePointer(e);
			this.isMoved = false;
			this.contextMenuObject = null;

			// addEventHandler(this.getRenderer().domElement, 'pointermove', () => {
			// 	this.isMoved = true;
			// }, true);
			// addEventHandler(this.getRenderer().domElement, 'pointerup', async () => {
			// 	// 显示右键菜单
			// 	if (!this.isMoved) { // 不是右键平移，才显示右键菜单
			// 		const intersects = this.getIntersects(this.clientX, this.clientY);
			// 		if (intersects.length && intersects[0]?.object?.nickname) {
			// 			logger.log('鼠标右键事件 intersects: ', intersects[0]);
			// 			this.contextMenuObject = intersects[0];
			// 		}
			// 		this.rightMenuVisible = true;
			// 	}
			// }, true);

			const intersects = this.getIntersects(this.clientX, this.clientY);
			if (intersects.length && intersects[0]?.object?.nickname) {
				logger.log('鼠标右键事件 intersects: ', intersects[0]);
				if (intersects[0] && this.isFace(intersects[0].object)) {
					this.contextMenuObject = intersects[0];
				}
			}
			this.rightMenuVisible = true;
		},

		/**
		 * 鼠标滚轮事件处理
		 */
		onMouseWheel (event) {
			if (this.curControl.zoomSpeed > 5) {
				return;
			}
			this.scrolled = true;
			const deltaY = event.deltaY;
			const vector3Mesh = this.getIntersects(event.clientX, event.clientY)[0]?.point;
			const { x, y } = this.getPosition(event);
			const vector = vector3Mesh || new THREE.Vector3(x, y, 1);
			this.initDistance();
			if (vector) {
				const AE = new THREE.Vector3().subVectors(vector, this.curCamera.position);
				const AB = AE.clone().normalize().multiplyScalar(-deltaY * 0.1);
				const OB = new THREE.Vector3().addVectors(this.curCamera.position, AB);
				const distance = OB.distanceTo(vector);
				const AC = new THREE.Vector3().subVectors(this.curControl.target, this.curCamera.position);
				const BE = new THREE.Vector3().subVectors(AE, AB);
				const BF = new THREE.Vector3().multiplyVectors(AC, BE).divide(AE);
				const OF = new THREE.Vector3().addVectors(this.curCamera.position, AB).add(BF);
				if (distance < -deltaY * 0.1) return;
				this.curCamera.position.add(AB);
				this.curControl.position0.add(AB);
				this.curControl.target = OF;
				this.curControl.update();
				const distance1 = this.curCamera.position.distanceTo(OF);
				this.updateRuler(false, this.distanceLong / distance1);
			}
		},

		/**
		 * 更新指针缓存坐标
		 */
		updatePointer (e) {
			const { x, y } = this.getPosition(e);
			this.pointer.x = x;
			this.pointer.y = y;
			this.clientX = e.clientX;
			this.clientY = e.clientY;
		},

		/**
		 * 选择部件处理
		 */
		selectPartsHandle (e) {

			if (!this.curCamera) {
				return false;
			}

			// 选点直接返回
			if (this.selectMode === SelectModeEnum.point) {
				return false;
			}

			let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());

			// 过滤出所有线
			if (this.selectMode === SelectModeEnum.line) {
				intersects = intersects.filter(item => this.isLine(item.object) && !this.isModelWireframe(item.object));
			}

			// 过滤出所有面
			if (this.selectMode === SelectModeEnum.surface && !this.curDraw) {
				intersects = intersects.filter(item => this.isFace(item.object));
			}

			// 过滤出所有体
			if (this.selectMode === SelectModeEnum.solid && !this.curDraw) {
				intersects = intersects.filter(item => this.isSolid(item.object));
			}

			if (!this.curDraw) {
				if (intersects.length > 0) {
					// 多个相交物体获取离镜头最近的
					const clickVisibleObj = intersects[0];
					// const distance = clickVisibleObj.distance; // 距离
					// logger.log('distance: ', distance);
					const clickVisibleMesh = clickVisibleObj.object; // 需操作的网格模型

					if (!this.selectParam) {
						this.resetColorHandle();
					} else if (this.selectParam?.value.includes(clickVisibleMesh.nickname)) {
						this.updateColorByObj(clickVisibleMesh, this.originColor);
						if (clickVisibleMesh.nickname) {
							this.$emit('singleSelectedHandler', clickVisibleMesh.nickname);
						}
						return intersects;
					}

					const selectColor = toHexColor(this.selectParam?.config?.color);
					const chooseColor = this.selectParam ? selectColor : ColorEnum.white;

					// 体
					if (this.selectMode === SelectModeEnum.solid) {
						this.updateColorHandle(clickVisibleObj);
					}

					// 面
					if (this.selectMode === SelectModeEnum.surface && this.isFace(clickVisibleMesh)) {
						if (!clickVisibleMesh.isLackFace) { // 非坏面(缺失的面)才可操作
							clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
							clickVisibleMesh.material.emissive.set(new THREE.Color(chooseColor));
						}
					}

					// 线
					if (this.selectMode === SelectModeEnum.line && this.isLine(clickVisibleMesh)) {
						clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
					}

					clickVisibleMesh.material.needsUpdate = true;
					this.requestRender();

					if (clickVisibleMesh) {
						if (clickVisibleMesh.nickname) {
							this.$emit('singleSelectedHandler', clickVisibleMesh.nickname);
						}
						if (selectSwitch === TagSwitchEnum.ON && clickVisibleMesh.nickname) {
							this.createTag(clickVisibleMesh);
						}
					}
				} else {
					if (!this.selectParam) {
						this.resetColorHandle();
					}
					this.clearTag();
				}
			}
			return intersects;
		},

		/**
		 * 判断是否体
		 */
		isSolid (object) {
			return object?.isSolid;
		},

		/**
		 * 判断是否面
		 */
		isFace (object) {
			return object?.material?.type === 'MeshPhongMaterial';
		},

		/**
		 * 判断是否线
		 */
		isLine (object) {
			return object?.material?.type === 'LineMaterial';
		},

		/**
		 * 判断是否球体
		 */
		isSphere (object) {
			return object?.geometry?.type === 'SphereGeometry';
		},

		/**
		 * 颜色重置处理程序
		 */
		resetColorHandle () {
			for (const raycasterObj of raycasterObjs) {
				if (!raycasterObj.material) {
					break;
				}
				if (this.isModelFace(raycasterObj) || this.isSolid(raycasterObj)) {
					if (!raycasterObj.isLackFace) { // 非坏面(缺失的面)才可操作
						raycasterObj.material.color.set(new THREE.Color(this.originColor));
						raycasterObj.material.emissive.set(new THREE.Color(0x333333));
					}
				} else if (this.isLine(raycasterObj) && !raycasterObj.textureResultType) {
					raycasterObj.material.color.set(new THREE.Color(ColorEnum.black));
				}
				raycasterObj.material.needsUpdate = true;
			}
			this.requestRender();
		},

		/**
		 * 透明度重置处理程序
		 */
		resetOpacityHandle () {
			for (const raycasterObj of raycasterObjs) {
				if (this.isModelFace(raycasterObj)) {
					raycasterObj.material.opacity = this.originOpacity;
				}
				raycasterObj.material.needsUpdate = true;
			}
			this.requestRender();
		},

		/**
		 * 可见重置处理程序
		 */
		resetVisibleHandle () {
			this.changeRenderMode(this.curRenderMode);
		},

		/**
		 * 颜色更新处理程序
		 */
		updateColorHandle (intersect) {
			for (const raycasterObj of raycasterObjs) {
				if (raycasterObj.solidGroup === intersect.object.solidGroup && raycasterObj.material) {
					if ((this.isFace(raycasterObj) || this.isSolid(raycasterObj)) && !raycasterObj.isLackFace) {
						// 此种方式修改颜色会导致face表面看不见原来形象
						// raycasterObj.material.color.set(new THREE.Color(ColorEnum.white));
						// raycasterObj.material.emissive.set(new THREE.Color(ColorEnum.white));

						// updated color
						raycasterObj.material.color.set(new THREE.Color(ColorEnum.pink));
					}
					raycasterObj.material.needsUpdate = true;
				}
			}
			this.requestRender();
		},

		/**
		 * 添加标签
		 */
		createTag (object) {
			const canvas = this.getCanvas();
			const w = canvas.clientWidth / 2;
			const h = canvas.clientHeight / 2;
			const x = Math.round(this.pointer.x * w + w); // 标准设备坐标转屏幕坐标
			const y = Math.round(-this.pointer.y * h + h) - 35;

			// 修改 div 的位置
			const div = document.querySelector('#label');
			div.style.display = 'block';
			div.style.padding = '5px';
			div.style.position = 'absolute';
			div.style.backgroundColor = 'rgba(155, 0, 155, 0.8)';
			div.style.left = x + 'px';
			div.style.top = y + 'px';
			div.innerHTML = object.nickname; // 显示模型信息
		},

		/**
		 * 清除标签
		 */
		clearTag () {
			const divDom = document.querySelector('#label');
			divDom.style.display = 'none';
			divDom.innerHTML = '';
		},

		requestRender () {
			if (!this.renderRequested) {
				this.renderRequested = true;
				if (this.openAnimate) {
					this.render();
				} else {
					cancelAnimationFrame(animationId);
					animationId = requestAnimationFrame(this.render);
				}
			}
		},

		animate () {
			if (this.openAnimate) {
				cancelAnimationFrame(animationId);
				animationId = requestAnimationFrame(this.animate);
				this.render();
			}
		},

		render () {
			this.renderRequested = false;

			const scene = this.getScene();
			const renderer = this.getRenderer();
			const cameraType = this.getCameraType();
			const arrowScene = this.getArrowScene();
			const arrowCamera = this.getArrowCamera();
			const arrowRenderer = this.getArrowRenderer();
			const perspectiveCamera = this.getPerspectiveCamera();
			const orthographicCamera = this.getOrthographicCamera();
			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			perspectiveControls?.update();
			orthographicControls?.update();

			if (arrowCamera) {
				if (cameraType === 'Perspective' && perspectiveControls) {
					arrowCamera.position.copy(perspectiveCamera.position);
					arrowCamera.position.sub(perspectiveControls.target);
				}
				if (cameraType === 'Orthographic' && orthographicControls) {
					arrowCamera.position.copy(orthographicCamera.position);
					arrowCamera.position.sub(orthographicControls.target);
				}
				arrowCamera.lookAt(arrowScene.position);
				arrowCamera.position.setLength(200);
			}

			this.stats?.begin();
			if (cameraType === 'Perspective' && perspectiveControls) {
				renderer?.render(scene, perspectiveCamera);
			}
			if (cameraType === 'Orthographic' && orthographicControls) {
				renderer?.render(scene, orthographicCamera);
			}
			arrowRenderer?.render(arrowScene, arrowCamera);
			this.stats?.end();
		},

		/**
		 * 解析数据
		 */
		parseData (data) {
			if (data?.compressed) {
				// Decode from base90 and distribute the floats
				for (const obj of data.objects) {
					obj.floats = JSON.parse('[' + floatDecode(data, obj.floats) + ']');
					obj.verts = baseDecode(data, obj.verts).map(x => obj.floats[x]);
					obj.facets = baseDecode(data, obj.facets);
					obj.wires = obj.wires.map(w => baseDecode(data, w).map(x => obj.floats[x]));
					obj.facesToFacets = obj.facesToFacets.map(x => baseDecode(data, x));
				}
			}
			return data;
		},

		/**
		 * 模具的面或边
		 */
		isModelFaceOrEdge (obj) {
			return this.isModelFace(obj) || this.isModelEdge(obj);
		},

		/**
		 * 模具的面
		 */
		isModelFace (obj) {
			return obj.nickname?.startsWith('face_');
		},

		/**
		 * 模具的边
		 */
		isModelEdge (obj) {
			return obj.nickname?.startsWith('e_') && obj.allowSelect;
		},

		/**
		 * 模具的线框
		 */
		isModelWireframe (obj) {
			return obj.groupName === 'wireframe' || obj.isWireframe;
		},

		/**
		 * 模具
		 */
		isModel (obj) {
			return obj.solidGroup?.startsWith('solid_') || obj.isSolid;
		},

		/**
		 * 更新对象颜色(所有)
		 */
		updateAllAttr ({ color, opacity, visible }) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				this.updateObjAttr(child, { color, opacity, visible });
			}
			this.requestRender();
		},

		/**
		 * 根据传入名称列表更新对象颜色(多个)
		 */
		updateAttrByNameList (nameList, { selected, color, opacity, visible }) {
			color = selected ? toHexColor(color) : this.originColor;
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (nameList.includes(child.nickname) || nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { color, opacity, visible });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据名称更新对象颜色(单个)
		 */
		updateColorByName (name, selected) {
			const color = selected ? this.selectedColor : this.originColor;
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (name && (child.nickname === name || child.groupName === name)) {
					this.updateObjAttr(child, { color });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据名称更新对象可见性(单个)
		 */
		updateVisibleByName (name, visible, isRefreshView = true) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (name && (child.nickname === name || child.groupName === name)) {
					this.updateObjAttr(child, { visible });
				}
			}
			isRefreshView && this.requestRender();
		},

		/**
		 * 更新对象颜色(所有)
		 */
		updateAllColor (color) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (this.isModelFace(child)) { // 非坏面(缺失的面)才可操作
					this.updateObjAttr(child, { color });
				}
				if (this.isModelEdge(child)) {
					this.updateObjAttr(child, { color: ColorEnum.black });
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象不透明度(所有)
		 */
		updateAllOpacity (opacity) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				this.updateObjAttr(child, { opacity });
			}
			this.requestRender();
		},

		/**
		 * 更新对象可见性(所有)
		 */
		updateAllVisible (visible) {
			const allSolidAndWireframe = this.getSceneAllSolidAndAllWireframe();
			const allDispersedLine = this.getSceneAllDispersedLine();
			for (const child of [...allSolidAndWireframe, ...allDispersedLine]) {
				this.updateObjAttr(child, { visible });
			}
			this.requestRender();
		},

		/**
		 * 更新所有结果可见性(所有)
		 */
		updateAllResultVisible (visible) {
			for (const child of this.getSceneChildrenByCondition('resultWlLineGroup')) {
				this.updateObjAttr(child, { visible });
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象颜色(多个)
		 */
		updateColorByList (nameList, selected, color) {
			color = selected ? toHexColor(color) : this.originColor;
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (nameList.includes(child.nickname) || nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { color });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象不透明度(多个)
		 */
		updateOpacityByList (nameList, opacity) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (nameList.includes(child.nickname) || nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { opacity });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象可见性(多个)
		 */
		updateVisibleByList (nameList, visible) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if (nameList.includes(child.nickname) ||
					nameList.includes(child.groupName)) {
					this.updateObjAttr(child, { visible });
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象线宽(多个)
		 */
		updateLineWidthByList (nameList, lineWidth) {
			for (const child of this.getSceneAllSolidAndAllWireframe()) {
				if ((nameList.includes(child.nickname) || nameList.includes(child.groupName)) && this.isModelEdge(child)) {
					this.updateObjAttr(child, { lineWidth });
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象属性(单个)
		 */
		updateObjAttr (child, { color, opacity, visible, lineWidth } = {}, isSyncRaycasterObj = true) {
			if (child?.isLackFace) { // 坏面(缺失的面)不可操作
				return;
			}
			if (!isEmpty(visible)) {
				child.visible = !!visible;
				if (child?.parent instanceof THREE.Group) {
					child.parent.visible = true;
				}
			}
			if (child.material) {
				if (!isEmpty(color)) {
					child.material.color = new THREE.Color(color);
				}
				if (!isEmpty(opacity)) {
					const isOpacity = opacity === 100;
					opacity = opacity > 1 ? opacity / 100 : opacity;
					if (!isOpacity && opacity === 1) opacity /= 10;
					child.material.opacity = opacity;
					child.material.depthWrite = (opacity === 1);
					child.material.transparent = (opacity !== 1);
				}
				if (!isEmpty(lineWidth) && hasOwnProperty(child.material, 'lineWidth')) {
					child.material.linewidth = lineWidth; // lineWidth原始属性是全小写，注意！
					child.material.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
				}
				child.material.needsUpdate = true;
			}

			// 根据视图对象同步拾取列表内对象属性
			isSyncRaycasterObj && this.syncRaycasterObjAttr(child);
		},

		/**
		 * 同步 Raycaster 对象属性
		 */
		syncRaycasterObjAttr (child) {
			if (child instanceof THREE.Object3D) {
				const raycasterObj = raycasterObjs.find(raycasterObj => raycasterObj.uuid === child.uuid);
				if (!raycasterObj) return;
				for (const childKey in child) {
					if (['visible'].includes(childKey)) {
						raycasterObj[childKey] = child[childKey];
					}
					if (raycasterObj.material) {
						if (['opacity', 'color'].includes(childKey)) {
							raycasterObj.material[childKey] = child[childKey];
						}
						if (childKey === 'opacity') {
							child.material.depthWrite = (child[childKey] === 1);
							child.material.transparent = (child[childKey] !== 1);
						}
						if (childKey === 'lineWidth') {
							raycasterObj.material[childKey.toLowerCase()] = child[childKey.toLowerCase()];
							child.material.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
						}
					}
				}
			}
		},

		/**
		 * 重置视窗大小
		 */
		resizeCurrentView () {
			this.jsViewWidth = this.collapsed ? 'calc(100vw)' : 'calc(100vw - 320px)';
			this.$nextTick(() => this.onMainCanvasResize());
		},

		/**
		 * 开启加载中
		 */
		openLoading (loadingMsg = '', resetCount = false) {
			this.tip = loadingMsg || '加载中...';
			this.loading = true;
			if (resetCount) {
				this.loadingCount = 0;
			}
			this.loadingCount++;
			// logger.log('openLoading loadingCount: ', this.loadingCount);
		},

		/**
		 * 关闭加载中
		 */
		closeLoading () {
			this.loadingCount--;
			// logger.log('closeLoading loadingCount: ', this.loadingCount);
			if (this.loadingCount <= 0) {
				this.loadingCount = 0;
				this.loading = false;
				this.tip = '';
			}
		},

		changeViewCamera () {
			this.resetCamera(1);
			const type = this.direction === ViewEnum.default ? 'Perspective' : 'Orthographic';
			this.setCameraType(type);
			this.cameraChange(type, true);
		},

		/**
		 * 更新视图
		 */
		updateView (direction) {

			this.direction = direction;
			this.changeViewCamera();

			// 禁用缩放、旋转，允许平移
			if (this.curControl instanceof OrbitControls) {
				this.curControl.enableZoom = direction === ViewEnum.default;
				this.curControl.enableRotate = direction === ViewEnum.default;
			}
			if (this.curControl instanceof TrackballControls) {
				this.curControl.noZoom = direction !== ViewEnum.default;
				this.curControl.noRotate = direction !== ViewEnum.default;
			}

			this.mouse.RIGHT = THREE.MOUSE.PAN;
			switch (direction) {
				case ViewEnum.front:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, -1, 0]);
					break;
				case ViewEnum.back:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, 1, 0]);
					break;
				case ViewEnum.left:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([-1, 0, 0]);
					break;
				case ViewEnum.right:
					this.curCamera.up.set(0, 0, 1);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([1, 0, 0]);
					break;
				case ViewEnum.top:
					this.curCamera.up.set(0, 1, 0);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, -0.0001, 1]);
					break;
				case ViewEnum.bottom:
					this.curCamera.up.set(0, 1, 0);
					this.curCamera.lookAt(0, 0, 0);
					this.navChange([0, -0.0001, -1]);
					break;
				default:
					this.mouse.RIGHT = THREE.MOUSE.ROTATE;
					const vec = new THREE.Vector3(0.4, 0.4, 0.5).normalize();
					this.navChange([vec.x, vec.y, vec.z], true);
					break;
			}

			this.getArrowCamera().up = this.curCamera.up;
			this.getArrowCamera().lookAt(0, 0, 0);
			this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 更改投影模式
		 */
		changeCameraMode (type) {
			if (type === CameraModeEnum.perspective) {
				this.cameraChange('Perspective');
			} else if (type === CameraModeEnum.orthographic) {
				this.cameraChange('Orthographic');
			}
			this.resetCamera();
		},

		/**
		 * 选择模型(点、线、面、体)
		 */
		selectModeHandle (selectMode) {
			this.selectMode = selectMode;
		},

		/**
		 * 右键菜单上点击
		 */
		onContextMenuClick (type) {

			this.rightMenuVisible = false;

			if (type === 'show') {
				this.hiddenObjectMap = {};
				this.updateAllVisible(true);
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hidden') {
				this.hiddenObjectMap[this.contextMenuObject.object.nickname] = true;
				this.updateVisibleByName(this.contextMenuObject.object.nickname, false);
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hiddenAllSolid') {
				const solids = this.getSceneAllSolid();
				for (const solid of solids) {
					this.hiddenObjectMap[solid.nickname] = true;
					this.updateObjAttr(solid, { visible: false });
				}
				this.requestRender();
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hiddenWireframe') {
				const allWireframeMesh = this.getSceneAllWireframe();
				const allDispersedLine = this.getSceneAllDispersedLine();
				for (const wireframeMesh of [...allWireframeMesh, ...allDispersedLine]) {
					if (wireframeMesh.nickname) {
						this.hiddenObjectMap[wireframeMesh.nickname] = true;
					}
					this.updateObjAttr(wireframeMesh, { visible: false });
				}
				this.requestRender();
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
		},

		/**
		 * 绘制小球
		 */
		drawSphere (point, isCenterPoint, radius = 1, groupName = this.nickname) {
			if (!point) return;
			radius = this.getSphereRadius(radius);
			const geometry = new THREE.SphereGeometry(radius, 100, 100);
			const material = new THREE.MeshBasicMaterial({ color: 0xffff00 });
			const sphere = new THREE.Mesh(geometry, material);
			sphere.groupName = groupName;
			sphere.position.copy(new THREE.Vector3(point.x, point.y, point.z));
			sphere.isCenterPoint = !!isCenterPoint; // 是中心点
			sphere.isTemporary = true; // 是临时的
			sphere.isSphere = true;
			this.getScene().add(sphere);
			this.requestRender();
			return sphere;
		},

		/**
		 * 获取球体半径
		 */
		getSphereRadius (radius) {
			if (this.modelLong >= 170 && this.modelLong <= 200) {
				return 2;
			}
			if (this.modelLong >= 50000) {
				return 500;
			}
			return radius;
		},

		/**
		 * 按组名删除对象
		 */
		removeObjByGroupName (groupName, conditionHandler) {
			logger.log('按组名删除对象:', groupName);
			this.removeObjByGroupNameComeFromCommon(groupName, conditionHandler);
			raycasterObjs = this.deleteRaycasterObjByGroupName(raycasterObjs, groupName);
		},

		/**
		 * 获取鼠标位置
		 */
		getPosition (event) {
			const sliderWidth = 320;
			const headerHeight = 50;
			const navigationHeight = 0 + 2;
			const canvas = this.getCanvas();
			const x = ((event.clientX - (this.collapsed ? 0 : sliderWidth)) / canvas.clientWidth) * 2 - 1;
			const y = -((event.clientY - (headerHeight + navigationHeight)) / canvas.clientHeight) * 2 + 1;
			return { x, y };
		},

		/**
		 * 通过世界坐标转换为屏幕坐标
		 *  3D坐标转换成2D坐标
		 * @return {Object}
		 */
		worldVectorToScreenVector (worldVector) {
			const vector = worldVector.project(this.curCamera); // 通过世界坐标获取转标准设备坐标
			const w = window.innerWidth / 2;
			const h = window.innerHeight / 2;
			const screenX = Math.round(vector.x * w + w); // 标准设备坐标转屏幕坐标
			const screenY = Math.round(-vector.y * h + h);
			logger.log('屏幕坐标X: ', screenX);
			logger.log('屏幕坐标Y: ', screenY);
			return { screenX, screenY };
		},

		/**
		 * 获取当前世界坐标
		 *  将鼠标坐标转换为3D空间坐标
		 *  @param pointer
		 *  @return {THREE.Vector3}
		 */
		getWorldVector (pointer) {
			// 屏幕坐标转标准设备坐标
			// 标准设备坐标(z=0.5表示深度)
			const vector = new THREE.Vector3(pointer.x, pointer.y, 0.5);
			// 标准设备坐标转为世界坐标(函数vector.unproject(camera)则可以从屏幕2d坐标转换为3d空间坐标)
			const worldVector = vector.unproject(this.curCamera);
			logger.log('世界坐标 worldVector: ', worldVector);
			return worldVector;
		},

		/**
		 * 获取当前点坐标
		 *  @param event
		 *  @return {THREE.Vector3}
		 */
		getCurrPoint (event) {
			const intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());
			return intersects[0]?.point;
		},

		/**
		 * 重置视图
		 */
		resetView () {
			this.direction = ViewEnum.default;
			this.changeViewCamera();
			this.mouse.RIGHT = THREE.MOUSE.ROTATE;
			const vec = new THREE.Vector3(0.4, 0.4, 0.5).normalize();
			this.navChange([vec.x, vec.y, vec.z], true);
			this.curControl.enabled = true;
			// 禁用缩放、旋转，允许平移
			if (this.curControl instanceof OrbitControls) {
				this.curControl.enableZoom = true;
				this.curControl.enableRotate = true;
			}
			if (this.curControl instanceof TrackballControls) {
				this.curControl.noZoom = false;
				this.curControl.noRotate = false;
			}
			this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 重置相机
		 */
		resetCamera (zoom = null) {
			// this.curControl.target0.copy(this.curControl.target);
			// this.curControl.position0.copy(this.curControl.object.position);
			// this.curControl.zoom0 = zoom ?? (this.curControl.object instanceof PerspectiveCamera ? this.curControl.object.zoom : 1);
			// this.curControl.update();
			const zoom0 = zoom ?? this.curCamera.zoom;
			this.curControl.zoom0 = zoom0;
			this.curControl.reset();
		},

		/**
		 * 根据相关参数复位相机
		 */
		restoreCamera (position, rotation, controlCenter) {
			this.curCamera.position.set(position.x, position.y, position.z);
			this.curCamera.rotation.set(rotation.x, rotation.y, rotation.z);

			this.curControl.target.set(controlCenter.x, controlCenter.y, controlCenter.z);
			this.curControl.update();

			this.requestRender();
		},

		/**
		 * 创建网格平面
		 */
		createGridPlane () {
			if (this.gridHelper) return;
			this.gridHelper = new THREE.GridHelper(this.modelLong, 35);
			this.updateGridPlane();
			this.getScene().add(this.gridHelper);
			this.requestRender();
		},

		/**
		 * 更新网格平面
		 */
		updateGridPlane () {
			if (this.gridHelper) {
				this.gridHelper.position.copy(this.camCenter.clone());
				this.gridHelper.rotation.copy(this.curCamera.rotation.clone());
				this.gridHelper.rotateX(Math.PI / 2);
				this.gridHelper.updateMatrix();
			}
		},

		/**
		 * 删除网格平面
		 */
		removeGridPlane () {
			if (this.gridHelper) {
				this.removeSceneObj(this.gridHelper);
				this.gridHelper = null;
				this.requestRender();
			}
		},

		/**
		 * 创建部件边框
		 */
		createWidgetBorder () {
			// 在3D空间中表示一个盒子或立方体。其主要用于表示物体在世界坐标中的边界框
			const box = new THREE.Box3();
			// 通过传入的object3D对象来返回当前模型的最小大小，值可以使一个mesh也可以使group
			const group = new THREE.Group();
			raycasterObjs.forEach(mesh => group.add(mesh));
			this.getScene().add(group);
			box.expandByObject(group);
			const boxHelper = new THREE.BoxHelper(group, 0xffff00);
			logger.log('boxHelper: ', boxHelper);
			this.getScene().add(boxHelper);
			boxHelper.geometry.computeBoundingBox();
			logger.log('boundingBox: ', boxHelper.geometry.boundingBox);
			const centerVector = boxHelper.geometry.boundingSphere.center;
			logger.log('centerVector: ', centerVector);
		},

		/**
		 * 位置数组 转 文本（Rhino grasshopper call）
		 * @param positionArray
		 * @return {string}
		 */
		positionArray2Txt (positionArray) {
			const points = [];
			for (let i = 0; i < positionArray.length; i += 3) {
				const x = positionArray[i];
				const y = positionArray[i + 1];
				const z = positionArray[i + 2];
				points.push({ x, y, z });
			}
			return JSON.stringify(points);
		},

		/**
		 * 通过mesh获取世界坐标
		 */
		getWorldCoordinatesByMesh (mesh) {
			// 该语句默认在threeJs渲染的过程中执行  如果想获得世界矩阵属性、世界位置属性等属性，需要手动更新
			mesh.updateMatrixWorld(true);
			// 声明一个三维向量用来保存网格模型的世界坐标
			const worldPosition = new THREE.Vector3();
			// 获得世界坐标，执行getWorldPosition方法，提取网格模型的世界坐标结果保存到参数worldPosition中
			mesh.getWorldPosition(worldPosition);
			logger.log('查看网格模型', mesh);
			logger.log('查看网格模型世界坐标', worldPosition);
			logger.log('查看网格模型本地坐标', mesh.position);
			return worldPosition;
		},

		/**
		 * 获取点坐标
		 * @param vector3
		 * @return {{}}
		 */
		getPoint (vector3) {
			return pick(vector3, ['x', 'y', 'z']);
		},

		/**
		 * 获取点击对象
		 */
		getIntersects (clientX, clientY) {
			return this.getCanvasIntersects({ clientX, clientY }, this.getScene(), this.curCamera, this.getCanvas());
		},

		/**
		 * 获取拾取对象
		 */
		getRaycasterObjs () {
			return raycasterObjs;
		},

		/**
		 * 当canvas不占满整屏时射线拾取存在偏差，获取点击对象
		 * @param event 事件对象
		 * @param scene 场景对象
		 * @param camera 镜头对象
		 * @param canvas 绘制盒子
		 * @param isFilter 是否过滤掉隐藏的部件
		 */
		getCanvasIntersects (event, scene, camera, canvas, isFilter = true) {
			preventDefaults(event);
			// 获取元素的大小及其相对于视口的位置
			const getBoundingClientRect = canvas.getBoundingClientRect();
			// 屏幕坐标转标准设备坐标
			const x = ((event.clientX - getBoundingClientRect.left) / canvas.offsetWidth) * 2 - 1; // 标准设备横坐标
			const y = -((event.clientY - getBoundingClientRect.top) / canvas.offsetHeight) * 2 + 1; // 标准设备纵坐标
			const vector = new THREE.Vector3(x, y, 1); // 标准设备坐标
			// 创建射线投射器对象
			const raycaster = new THREE.Raycaster();
			// raycaster.params = {
			// 	Mesh: {},
			// 	Line: { threshold: 3 },
			// 	Line2: { threshold: 10 },
			// 	LOD: {},
			// 	Points: { threshold: 1 },
			// 	Sprite: {},
			// };
			raycaster.params.Line = { threshold: 3 };
			raycaster.params.Line2 = { threshold: 10 };
			raycaster.params.Points = { threshold: 1 };
			raycaster.setFromCamera(vector, camera);
			// 返回射线选中的对象 第二个参数如果不填 默认是false
			const objects = [...raycasterObjs];
			if (this.gridHelper && this.curDraw !== DrawEnum.point) {
				objects.push(this.gridHelper);
			}
			let intersects = raycaster.intersectObjects(objects, true);
			// 过滤掉隐藏的部件
			if (isFilter) {
				intersects = intersects.filter(item => item.object && item.object.visible);
			}
			// 返回选中的对象数组
			return intersects;
		},

		/**
		 * 当canvas不占满整屏时射线拾取存在偏差，获取点击管道对象
		 * @param event 事件对象
		 */
		getPipeLineIntersects (event) {
			if (event && event.preventDefault) {
				event.preventDefault();
			}
			const canvas = this.getCanvas();
			// 获取元素的大小及其相对于视口的位置
			const getBoundingClientRect = canvas.getBoundingClientRect();
			// 屏幕坐标转标准设备坐标
			const x = ((event.clientX - getBoundingClientRect.left) / canvas.offsetWidth) * 2 - 1; // 标准设备横坐标
			const y = -((event.clientY - getBoundingClientRect.top) / canvas.offsetHeight) * 2 + 1; // 标准设备纵坐标
			const vector = new THREE.Vector3(x, y, 1); // 标准设备坐标
			// 创建射线投射器对象
			const raycaster = new THREE.Raycaster();
			raycaster.setFromCamera(vector, this.curCamera);
			// 返回射线选中的对象 第二个参数如果不填 默认是false
			const objects = [];
			this.getScene().children.forEach((item) => {
				if (item.groupName === 'result_pipe_line') {
					objects.push(item);
				}
			});
			if (objects.length === 0) {
				return [];
			}
			const intersects = raycaster.intersectObjects(objects, true);
			// 返回选中的对象数组
			return intersects;
		},

		/**
		 * 重置
		 */
		async reset () {
			// 清空场景
			this.clearSceneAll();
			this.clearRaycasterAll(raycasterObjs);
			this.clearTextureResult();

			// // 重置data所有属性
			// const data = deepCopy(defaultData);
			// for (const $dataKey in this.$data) {
			// 	if (hasOwnProperty(this.$data, $dataKey) && hasOwnProperty(data, $dataKey)) {
			// 		this.$data[$dataKey] = data[$dataKey];
			// 	}
			// }

			// 重置全局对象
			raycasterObjs = [];
			delCallback = null;
			enterCallback = null;
			escCallback = null;
			ctrlSCallback = null;
			ctrlCallback = null;
			ctrlZCallback = null;
			dragCallback = null;

			this.requestRender();
		},

		/**
		 * 销毁数据
		 */
		destroy () {
			this.$nextTick(() => {
				this.$destroy();
			});
		},
	},
	beforeDestroy () {
		this.reset();
		removeEventHandler(window, 'keydown', onKeydown);
		removeEventHandler(window, 'resize', this.onMainCanvasResize);
		const renderer = this.getRenderer();
		if (renderer) {
			removeEventHandler(renderer.domElement, 'click', this.onMouseClick);
			removeEventHandler(renderer.domElement, 'wheel', this.onMouseWheel);
			removeEventHandler(renderer.domElement, 'mousemove', this.onMouseMove);

			const gl = renderer.domElement.getContext('webgl');
			gl && gl.getExtension('WEBGL_lose_context').loseContext();
		}
		this.clearRenderer();
		this.clearAxesRenderer();
		this.destroyScene();
		THREE.Cache.clear();
		cancelAnimationFrame(animationId); // 去除animationFrame
		this.destroy();
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/mixins/variables.less";

canvas {
	display: block;
}

#mainCanvas {
	width: 100%;
	height: 100%;
	background-image: linear-gradient(180deg, #E0E0FF 0%, #E3EAEE 100%);
}

#arrowCanvas {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 150px;
	height: 150px;
	z-index: 100;
}

select {
	width: 170px;
}

.view-container {
	position: relative;

	.view-loading {
		top: 50%;
		left: 50%;
		z-index: 1;
		width: 200px;
		font-weight: bold;
		position: absolute;
		transform: translate(-50%, -50%);

		/deep/ .ant-spin-text {
			display: block;
			font-size: 24px;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
			font-weight: bold;
			color: @primary;
		}

		/deep/ .ant-spin-dot-item {
			opacity: 1;
			background-color: @primary;
		}
	}

	.scale-plate {
		position: absolute;
		bottom: 0;
		pointer-events: none;
		.disable-selection();
	}

	.scalar-bar {
		position: absolute;
		top: 88%;
		width: 100%;
		height: 36px;
		line-height: 36px;

		.box {
			display: flex;
			justify-content: center;

			.row {
				display: flex;
				flex-direction: column;
				align-items: center;

				img {
					width: 14px;
					margin: 0 3px;
					cursor: pointer;
				}

				.link {
					cursor: pointer;
					margin-left: 5px;
					height: 36px;
					color: rgba(0, 0, 0, 0.65);
					border-bottom: 1px solid #CCCCCC;
				}

				.text {
					display: inline-block;
					width: 80px;
				}
			}
		}
	}
}

.js-view {
	position: relative;
	top: 0;
	left: 0;
	width: calc(100vw - 320px);
	min-width: 700px;
	height: calc(100vh - 52px);

	.js-frame {
		display: flex;
		justify-content: center;
		position: absolute;
		top: 0;
		width: 100%;
		background: transparent;
	}

	.lutImage {
		width: 100%;
		height: 20px;
		border: solid 1px #EEE;
		transform: rotate(-90deg);
	}
}

/deep/ .selectBox {
	border: 1px solid #55AAFF;
	background-color: rgba(75, 160, 255, 0.3);
	position: fixed;
}
</style>
