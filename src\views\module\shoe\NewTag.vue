<template>
	<div>
		<template v-for="tag in tags">
			<a-tooltip v-if="tag.length > 20" :key="tag" :title="tag">
				<a-tag class="tag" :closable="true" :key="tag" @close="() => handleClose">
					{{ `${ tag.slice(0, 20) }...` }}
				</a-tag>
			</a-tooltip>
			<a-tag class="tag" v-else :key="tag" :closable="true" @close="() => handleClose">
				{{ tag }}
			</a-tag>
		</template>
		<a-input
			type="text"
			ref="input"
			v-if="inputVisible"
			:value="inputValue"
			:style="{ width: '78px' }"
			@change="handleInputChange"
			@blur="handleInputConfirm"
			@keyup.enter="handleInputConfirm"
		/>
		<a-button v-else type="dashed" icon="plus" @click="showInput">
			{{ btnText }}
		</a-button>
	</div>
</template>

<script>
/* ===================================
 * tag标签公共组件
 * Created by cjking on 2022/02/17.
 * Copyright 2022, Inc.
 * =================================== */
import { deepCopy } from '@/utils/utils';

export default {
	name: 'NewTag',
	props: {
		dataList: {
			type: Array,
			required: true,
		},
		btnText: {
			type: String,
			required: false,
			default: ''
		},
	},
	data () {
		return {
			tags: [],
			inputVisible: false,
			inputValue: '',
		};
	},
	watch: {
		dataList: {
			immediate: true,
			handler: (list) => {
				this.tags = deepCopy(list);
			},
		},
	},
	methods: {
		/**
		 * 显示输入框
		 */
		showInput () {
			this.inputVisible = true;
			this.$nextTick(() => {
				this.$refs.input.focus();
			});
		},

		/**
		 * 处理输入变化值
		 */
		handleInputChange (e) {
			this.inputValue = e.target.value;
		},

		/**
		 * 处理输入确认值
		 */
		handleInputConfirm () {
			const inputValue = this.inputValue;
			let tags = this.tags;
			if (inputValue && tags.indexOf(inputValue) === -1) {
				tags = [...tags, inputValue];
			}
			Object.assign(this, {
				tags,
				inputVisible: false,
				inputValue: '',
			});
			this.$emit('change', this.tags);
		},

		/**
		 * 处理输入关闭值
		 */
		handleClose (removedTag) {
			const tags = this.tags.filter(tag => tag !== removedTag);
			this.tags = tags;
			this.$emit('change', this.tags);
		},
	},
};
</script>

<style lang="less" scoped>
.tag {
	height: 32px;
	line-height: 30px;
}
</style>
