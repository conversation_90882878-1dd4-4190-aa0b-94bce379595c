// feat::  类型为 feat 的提交表示在代码库中新增了一个功能（这和语义化版本中的 MINOR 相对应）。
// fix:：类型为 fix 的 提交表示在代码库中修复了一个 bug （这和语义化版本中的 PATCH 相对应）。
// docs:: 只是更改文档。
// style:: 不影响代码含义的变化（空白、格式化、缺少分号等）。
// refactor:: 代码重构，既不修复错误也不添加功能。
// perf:: 改进性能的代码更改。
// test:: 添加确实测试或更正现有的测试。
// build:: 影响构建系统或外部依赖关系的更改（示例范围：gulp、broccoli、NPM）。
// ci:: 更改持续集成文件和脚本（示例范围：Travis、Circle、BrowserStack、SauceLabs）。
// chore::  其他不修改src或test文件。
// revert:: commit 回退

module.exports = {
	extends: ['@commitlint/config-conventional'],
	rules: {
		'type-enum': [2, 'always', [
			'build', 'chore', 'ci', 'docs', 'feat', 'fix', 'perf', 'refactor', 'revert', 'style', 'test'
		]],
		'subject-full-stop': [0, 'never'],
		'subject-case': [0, 'never']
	}
};
