export const defaultPartData = {
	type: 'parts',
	key: 'model_part',
	value: [],
	visible: true,
	color: '#CCCCCC',
	opacity: 100,
	disabled: false,
	submitLoading: false,
};

export const defaultJsonData = [
	{
		type: 'shape',
		label: '其他需躲避',
		key: 'other',
		value: [],
		tip: '此项不必须，模型极复杂，常规项无法避开某些部件时可使用',
		mode: 'surface',
		visible: true,
		color: '#00FF00',
		opacity: 100,
		activate: false,
		valueActiveMap: {
			// 'other_face_1': true,
		},
	},
	{
		type: 'shape',
		label: '胶位面',
		key: 'jw_sur',
		value: [],
		tip: '从模型中选出所需冷却的面',
		mode: 'surface',
		required: true,
		visible: true,
		color: '#00FF00',
		opacity: 100,
		activate: false,
		valueActiveMap: {
			// 'jw_sur_face_1': true,
		},
	},
	{
		type: 'shape',
		label: '出入口面',
		key: 'crk_sur',
		value: [],
		tip: '从模型中选出含流道出入口的面',
		required: true,
		mode: 'surface',
		visible: true,
		color: '#00FF00',
		opacity: 100,
		activate: false,
		valueActiveMap: {
			// 'crk_sur_face_1': true,
		},
	},
	{
		type: 'slider',
		label: '管半径',
		key: 'radius_of_pipe',
		value: 2,
		tip: '',
		min: 0,
		max: 10,
		step: 0.01,
	},
	{
		type: 'slider',
		label: '距胶位面距离',
		key: 'distance_of_jwm',
		value: 2,
		tip: '',
		step: 0.1,
		min: 0,
		max: 10,
	},
	{
		type: 'slider',
		label: '距边壁距离',
		key: 'distance_of_bb',
		value: 2,
		tip: '',
		step: 0.1,
		min: 0,
		max: 10,
	},
	{
		type: 'switch',
		label: '计算设计空间开关',
		key: 'calc_design_space',
		value: 0,
		checked: !!0,
		tip: '默认状态关闭，待输入及数值调整完毕后再逐个打开，可提高计算速度，减少等待时长',
		disabled: false,
	},
	{
		type: 'slider',
		label: '出入口直段长度',
		key: 'length_of_crkzj',
		value: 5,
		tip: '出入口直段参数值过小时，生成水路出入口端会提示异常，建议出入口直段长度值是距边壁距离的2倍以上',
		min: 0,
		max: 200,
		step: 1,
	},
	{
		type: 'shape',
		label: '预设线及出入口边线',
		key: 'ys_line',
		value: [],
		tip: '在模型内草绘具有初步设计意向性曲线，不需要精准，并从模型中点选出入口边线',
		mode: 'line',
		required: true,
		visible: true,
		color: '#00FF00',
		opacity: 100,
		extra: [
			// {
			// 	id: 'id1',
			// 	ysLineName: 'curve_1',      // 预设线名称
			// 	ysLineValue: [],            // 预设线值（points）
			// 	ysLineActivate: false,      // 预设线激活
			// 	crkLineActivate: false,     // 出入口边线激活
			// 	crkLineNames: ['b_1', 'b_2', 'b_3', 'b_4', 'b_5', 'b_1', 'b_2', 'b_3', 'b_4', 'b_5'],   // 出入口边线名称集合
			// },
			// {
			// 	id: 'id2',
			// 	ysLineName: 'curve_2',      // 预设线名称
			// 	ysLineValue: [],            // 预设线值（points）
			// 	ysLineActivate: false,      // 预设线激活
			// 	crkLineActivate: false,     // 出入口边线激活
			// 	crkLineNames: ['b_1', 'b_2', 'b_3', 'b_4', 'b_5', 'b_1', 'b_2', 'b_3', 'b_4', 'b_5'],   // 出入口边线名称集合
			// },
		],
		activate: false,
		valueActiveMap: {
			// 'id1': true,
		},
	},
	{
		type: 'slider',
		label: '管径调整量',
		key: 'num_of_gjtz',
		value: 1,
		tip: '模型空间狭小水路不能使用圆形截面生成时使用，此数值=目标水路半径值-参数输入项“管半径“值',
		min: 0,
		max: 3,
		step: 0.01,
	},
	{
		type: 'slider',
		label: '生成参数',
		key: 'create_params',
		value: 40,
		tip: '水路生长的主要调节项，数值越大，水路越长',
		min: 1,
		max: 200,
		step: 0.1,
	},
	{
		type: 'slider',
		label: '长度系数',
		key: 'length_coefficient',
		value: 1,
		tip: '当生长参数结果未达到需求长度时，可调节此项增加生长倍数',
		min: 0.5,
		max: 20,
		step: 0.1,
	},
	{
		type: 'slider',
		label: '管间距',
		key: 'distance_of_pipe',
		value: 2,
		tip: '',
		min: 0,
		max: 10,
		step: 0.01,
	},
	{
		type: 'switch',
		label: '生成管线开关',
		key: 'make_pipe_line',
		value: 0,
		checked: !!0,
		tip: '默认状态关闭，待输入及数值调整完毕后再逐个打开，可提高计算速度，减少等待时长',
		disabled: false,
	},
	{
		type: 'switch',
		label: '生成管开关',
		key: 'make_pipe',
		value: 0,
		checked: !!0,
		tip: '默认状态关闭，待输入及数值调整完毕后再逐个打开，可提高计算速度，减少等待时长',
		disabled: false,
	},
	{
		type: 'shape',
		label: '变径点',
		key: 'bj_point',
		value: [],
		tip: '此项不必须，水路管径有变径需求时，可在界面中自由点出变径点',
		mode: 'point',
		visible: true,
		color: '#00FF00',
		opacity: 100,
		extra: [
			// {
			// 	id: 'id1',
			// 	pointNames: ['point_1', 'point_2'], // 变径点名称集合
			// 	groupName: 'points_group_2',        // 变径点单个tag组名称
			// 	points: [],                         // 变径点值（points）
			// },
		],
		activate: false,
		valueActiveMap: {
			// 'id1': true,
		},
	},
	{
		type: 'slider',
		label: '变径量',
		key: 'bjl',
		value: -2,
		tip: '',
		min: -3,
		max: 3,
		step: 0.1,
	},
];
