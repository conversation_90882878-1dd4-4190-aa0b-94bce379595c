// This file governs the 3D Viewport which displays the 3D Model
// It is also in charge of saving to STL and OBJ
/* global getNewFileHandle, writeFile, downloadFile */
import * as THREE from 'three';
import ResizeObserver from 'resize-observer-polyfill';
import { logger } from '@/utils/logger';
import { ColorEnum, DISPERSED_LINE_GROUP, FACE_GROUP, ModeTypeEnum, RenderOrderEnum, SelectModeEnum, SOLID_GROUP, ViewEnum, WIREFRAME } from '@/constants';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils';
import { setPolygonOffset } from '@/components/core/ViewUtils';

/** Create the base class for a 3D Viewport.
 *  This includes the floor, the grid, the fog, the camera, and lights */
const Environment = function (viewContainer) {
	this.viewContainer = viewContainer;

	this.initEnvironment = function () {
		// Get the current Width and Height of the Parent Element
		const parentWidth = this.viewContainer.clientWidth;
		const parentHeight = this.viewContainer.clientHeight;

		// Create the Canvas and WebGL Renderer
		this.curCanvas = document.createElement('canvas');
		this.viewContainer.appendChild(this.curCanvas);
		this.renderer = new THREE.WebGLRenderer({
			alpha: true,            // 是否可以设置背景色透明
			antialias: true,        // 是否开启反锯齿
			canvas: this.curCanvas, // 渲染器绘制其输出的画布。这对应于下面的domElement属性。如果此处未传入，则会创建一个新的画布元素
			webgl2: false,
		});
		// Clear bg so we can set it with css
		this.renderer.setClearColor(ColorEnum.black, 0);
		this.renderer.setPixelRatio(window.devicePixelRatio);
		this.renderer.setSize(parentWidth, parentHeight);
		const resizeObserver = new ResizeObserver(() => this.onWindowResize());
		resizeObserver.observe(this.viewContainer);

		// Create the Three.js Scene
		this.scene = new THREE.Scene();
		// this.backgroundColor = 0x222222; // 0xa0a0a0
		// this.scene.background = new THREE.Color(this.backgroundColor);
		// this.scene.fog = new THREE.Fog(this.backgroundColor, 200, 600);

		this.camera = new THREE.PerspectiveCamera(45, 1, 1, 5000);
		// this.camera = new THREE.OrthographicCamera(300 / -2, 300 / 2, 300 / 2, 300 / -2, 1, 1000);
		// Consider an Orthographic Camera.  It doesn't look so hot with the MatCap Material.
		this.camera.position.set(50, 100, 150);
		this.camera.lookAt(0, 45, 0);
		this.camera.aspect = parentWidth / parentHeight;
		this.camera.updateProjectionMatrix();

		// Create two lights to evenly illuminate the model and cast shadows
		this.light = new THREE.HemisphereLight(0xffffff, 0x444444);
		this.light.position.set(0, 200, 0);
		this.light2 = new THREE.DirectionalLight(0xbbbbbb);
		this.light2.position.set(6, 50, -12);
		this.light2.castShadow = true;
		this.light2.shadow.camera.top = 200;
		this.light2.shadow.camera.bottom = -200;
		this.light2.shadow.camera.left = -200;
		this.light2.shadow.camera.right = 200;
		this.light2.shadow.mapSize.width = 128;
		this.light2.shadow.mapSize.height = 128;
		this.scene.add(this.light);
		this.scene.add(this.light2);
		this.renderer.shadowMap.enabled = true;
		this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
		// this.scene.add(new THREE.CameraHelper(this.light2.shadow.camera));

		// Set up the orbit controls used for Cascade Studio
		this.controls = new OrbitControls(this.camera, this.renderer.domElement);
		this.controls.target.set(0, 45, 0);
		this.controls.panSpeed = 2;
		this.controls.zoomSpeed = 1;
		this.controls.screenSpacePanning = true;
		this.controls.update();

		// Keep track of the last time the scene was interacted with
		// This allows for lazy rendering to reduce power consumption
		this.controls.addEventListener('change', () => this.viewDirty = true);
		this.isVisible = true;
		this.viewDirty = true;
		this.time = new THREE.Clock();
		this.time.autoStart = true;
		this.lastTimeRendered = 0.0;

		this.zoomCameraToSelection = (group, { fitOffset = 2.0 } = {}) => {

			const box = new THREE.Box3();

			box.expandByObject(group);

			const size = box.getSize(new THREE.Vector3());
			logger.log('模式大小 size: ', size);
			const center = box.getCenter(new THREE.Vector3());
			logger.log('中心点 center: ', center);

			const maxSize = Math.max(size.x, size.y, size.z);
			const fitHeightDistance = maxSize / (2 * Math.atan(Math.PI * this.camera.fov / 360));
			const fitWidthDistance = fitHeightDistance / this.camera.aspect;
			const distance = fitOffset * Math.max(fitHeightDistance, fitWidthDistance);

			const direction = this.controls.target.clone()
				.sub(this.camera.position)
				.normalize()
				.multiplyScalar(distance);
			this.controls.maxDistance = distance * 10;
			this.controls.target.copy(center);

			this.camera.near = distance / 100;
			this.camera.far = distance * 100;
			this.camera.updateProjectionMatrix();
			this.camera.position.copy(this.controls.target).sub(direction);

			this.controls.update();

			return { size, center };
		};

		this.addBoundingBox = (group) => {
			// box辅助框（包围盒）
			const boxHelper = new THREE.BoxHelper(group, ColorEnum.blue);
			this.scene.add(boxHelper);
		};

		this.addCenterPoint = (center) => {
			// 绘制包围盒的中心点
			this.drawSphere(center.clone(), 'CENTER_POINT');
		};

		this.drawSphere = (point, { name, radius = 1 } = {}) => {
			if (!point) return;
			this.scene.remove(...this.scene.children.filter(child => child.name === name));
			const geometry = new THREE.SphereGeometry(radius, 100, 100);
			const material = new THREE.MeshBasicMaterial({ color: ColorEnum.yellow });
			const sphere = new THREE.Mesh(geometry, material);
			sphere.position.copy(new THREE.Vector3(point.x, point.y, point.z));
			sphere.name = name;
			this.scene.add(sphere);
			this.renderer.render(this.scene, this.camera);
		};
	};

	// Resize the container, canvas, and renderer when the window resizes
	this.onWindowResize = function () {
		const parentWidth = this.viewContainer.clientWidth;
		const parentHeight = this.viewContainer.clientHeight;
		this.camera.aspect = parentWidth / parentHeight;
		this.camera.updateProjectionMatrix();
		this.renderer.setPixelRatio(window.devicePixelRatio);
		this.renderer.setSize(parentWidth, parentHeight);
		this.renderer.render(this.scene, this.camera);
		this.viewDirty = true;
	};

	// Initialize the Environment!
	this.initEnvironment();
};

/** This "inherits" from Environment (by including it as a sub object) */
const ViewEnvironment = function (viewContainer) {
	this.active = true;
	this.viewContainer = viewContainer;
	this.environment = new Environment(this.viewContainer);

	this.mainObject = new THREE.Object3D();
	this.cubeAxesObject = new THREE.Group();

	// State for the Hover Highlighting
	this.raycaster = new THREE.Raycaster();
	this.highlightedObj = null;
	this.chooseModel = SelectModeEnum.surface; // 默认选择面

	// State for the Handles
	this.handles = [];
	this.gizmoMode = 'translate';
	this.gizmoSpace = 'local';

	// Load the Shiny Dull Metal MatCap Material
	this.loader = new THREE.TextureLoader();
	this.loader.setCrossOrigin('');
	this.matcap = this.loader.load(require('@/assets/img/texture/dullFrontLitMetal.png'), () => { this.environment.viewDirty = true; });
	// this.matcap = this.loader.load(require('@/assets/img/texture/front.png'), () => { this.environment.viewDirty = true; });
	// this.matcapMaterial = new THREE.MeshMatcapMaterial({
	// 	color: new THREE.Color(0xF5F5F5),
	// 	matcap: this.matcap,
	// 	polygonOffset: true, // Push the mesh back for line drawing
	// 	polygonOffsetFactor: 2.0,
	// 	polygonOffsetUnits: 1.0,
	// });

	// 初始化数据并将其添加到场景
	this.initData = async (result) => {
		let meshes, faces, crk_curves, other_curves;
		if (result['meshes']) {
			meshes = await this.loadRhinoFacesJson('meshes', result);
			if (meshes?.length) {
				this.insertIntoScene(ModeTypeEnum.solid, meshes);
			}
		}
		if (result['faces']) {
			faces = await this.loadRhinoFacesJson('faces', result);
			if (faces?.length) {
				faces = faces.filter(face => face.isFace);
				const wireframe = faces.filter(face => face.isWireframe);
				this.insertIntoScene(ModeTypeEnum.face, faces);
				this.insertIntoScene(ModeTypeEnum.wireframe, wireframe);
			}
		}
		if (result['crk_curves']) {
			const filePath = `${ this.dispersePath }/${ result['crk_curves'] }`;
			crk_curves = await this.loadRhinoLinesJson(filePath, { decompress: false, customLineAttributes: true });
			if (crk_curves?.length) {
				let curveIndex = 0;
				const objs = [];
				for (const crkCurve of crk_curves) {
					curveIndex++;
					crkCurve.canDelete = true;
					crkCurve.isWireframe = true; // 是否边框线
					crkCurve.visible = true;
					crkCurve.linewidth = 1;
					crkCurve.groupName = WIREFRAME;
					crkCurve.renderOrder = RenderOrderEnum.line;
					crkCurve.nickname = 'crk_' + curveIndex;
					objs.push(crkCurve);
				}
				this.insertIntoScene(ModeTypeEnum.disperseLine, objs);
			}
		}
		if (result['other_curves']) {
			const filePath = `${ this.dispersePath }/${ result['other_curves'] }`;
			other_curves = await this.loadRhinoLinesJson(filePath, { nicknamePrefix: 'zyx_', decompress: false });
			if (other_curves?.length) {
				this.insertIntoScene(ModeTypeEnum.disperseLine, other_curves);
			}
		}

		this.environment.scene.add(this.mainObject);
		this.environment.viewDirty = true;

		// zoom to extents
		const { center } = this.environment.zoomCameraToSelection(this.mainObject);
		// 添加边框及中心点
		this.environment.addBoundingBox(this.mainObject);
		this.environment.addCenterPoint(center);

		logger.log('Generation Complete!');
		logger.timeEnd('模型离散加载时间');

		// this.updateView(ViewEnum.default);

		// 初始化缩略图
		// this.initThumbnail();

		// 释放，回收内存
		this.$nextTick(() => {
			meshes = null;
			faces = null;
			crk_curves = null;
			other_curves = null;
			// this.closeLoading();
		});
	};

	this.combineAndRenderShapes = ([[faceList, edgeList], sceneOptions]) => {
		logger.log('faceList: ', faceList);
		logger.log('edgeList: ', edgeList);

		window.workerWorking = false;     // Untick this flag to allow Evaluations again
		if (!faceList) { return; }  // Do nothing if the results are null

		sceneOptions = sceneOptions || {};
		// sceneOptions.gridVisible = true;
		// sceneOptions.groundPlaneVisible = true;

		// The old mainObject is dead!  Long live the mainObject!
		this.environment.scene.remove(this.mainObject);

		this.environment.scene.remove(this.groundMesh);
		// sceneOptions.groundPlaneVisible = true;
		// sceneOptions.gridVisible = true;
		if (sceneOptions.groundPlaneVisible) {
			// Create the ground mesh
			this.groundMesh = new THREE.Mesh(new THREE.PlaneBufferGeometry(2000, 2000),
				new THREE.MeshPhongMaterial({
					color: 0x080808,
					depthWrite: true,
					dithering: true,
					polygonOffset: true, // Push the mesh back for line drawing
					polygonOffsetFactor: 6.0,
					polygonOffsetUnits: 1.0,
				}));
			this.groundMesh.position.y = -0.1;
			this.groundMesh.rotation.x = -Math.PI / 2;
			this.groundMesh.receiveShadow = true;
			this.environment.scene.add(this.groundMesh);
		}

		this.environment.scene.remove(this.grid);
		if (sceneOptions.gridVisible) {
			// Create the Ground Grid; one line every 100 units
			this.grid = new THREE.GridHelper(2000, 20, 0xcccccc, 0xcccccc);
			this.grid.position.y = -0.01;
			this.grid.material.opacity = 0.3;
			this.grid.material.transparent = true;
			this.environment.scene.add(this.grid);
		}

		this.mainObject = new THREE.Group();
		this.mainObject.name = 'shape';
		this.mainObject.rotation.x = -Math.PI / 2;

		// Add Triangulated Faces to Object
		const vertices = [], normals = [], triangles = [], uvs = [], colors = [];
		let vInd = 0;
		let globalFaceIndex = 0;
		const globalFaceMetadata = {};
		globalFaceMetadata[-1] = { start: -1, end: -1 };
		faceList.forEach((face) => {
			// Copy Vertices into three.js Vector3 List
			vertices.push(...face.vertex_coord);
			normals.push(...face.normal_coord);
			uvs.push(...face.uv_coord);

			// Sort Triangles into a three.js Face List
			for (let i = 0; i < face.tri_indexes.length; i += 3) {
				triangles.push(
					face.tri_indexes[i + 0] + vInd,
					face.tri_indexes[i + 1] + vInd,
					face.tri_indexes[i + 2] + vInd,
				);
			}

			// Use Vertex Color to label this face's indices for raycast picking
			for (let i = 0; i < face.vertex_coord.length; i += 3) {
				colors.push(1, 1, 1);
			}

			// globalFaceIndex++;
			vInd += face.vertex_coord.length / 3;

			const faceMetadata = {};
			faceMetadata.localFaceIndex = face.face_index;
			faceMetadata.start = colors.length;
			faceMetadata.end = colors.length - 1;
			globalFaceMetadata[globalFaceIndex] = faceMetadata;
			globalFaceIndex++;
		});

		// Compile the connected vertices and faces into a model
		// And add to the scene
		const geometry = new THREE.BufferGeometry();
		geometry.setIndex(triangles);
		geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
		geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
		geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
		geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
		geometry.setAttribute('uv2', new THREE.Float32BufferAttribute(uvs, 2));
		geometry.computeBoundingSphere();
		geometry.computeBoundingBox();
		const matcapMaterial = new THREE.MeshMatcapMaterial({
			matcap: this.matcap,
			polygonOffset: true, // Push the mesh back for line drawing
			polygonOffsetFactor: 2.0,
			polygonOffsetUnits: 1.0,
			vertexColors: true,
		});
		// update color
		// matcapMaterial.color.setHex('0x00ff00');
		// matcapMaterial.color.convertSRGBToLinear();

		const mats = [
			new THREE.MeshBasicMaterial({ color: 0xff0000 }),
			new THREE.MeshBasicMaterial({ color: 0xff00ff }),
			new THREE.MeshBasicMaterial({ color: 0x00ff00 }),
			new THREE.MeshBasicMaterial({ color: 0xffff00 }),
			new THREE.MeshBasicMaterial({ color: 0x0000ff }),
			new THREE.MeshBasicMaterial({ color: 0x00ffff }),
		];

		const mesh = new THREE.Mesh(geometry, matcapMaterial);
		mesh.castShadow = true;
		mesh.name = 'Model Faces';

		const faceGroup = new THREE.Group();
		faceGroup.name = 'Model Faces';
		faceGroup.add(mesh);
		if (this.chooseModel === SelectModeEnum.line) {
			this.mainObject.add(faceGroup);
		} else {
			this.mainObject.add(...faceGroup.children);
		}
		// End Adding Triangulated Faces

		// // -------------------------------------------
		// // custom handler face
		// const faceGroup = new THREE.Group();
		// faceGroup.name = 'Model Faces';
		// faceList.forEach((face) => {
		//
		// 	const vertices = [];
		// 	const normals = [];
		// 	const triangles = [];
		// 	const uvs = [];
		// 	const colors = [];
		//
		// 	// Copy Vertices into three.js Vector3 List
		// 	vertices.push(...face.vertex_coord);
		// 	normals.push(...face.normal_coord);
		// 	uvs.push(...face.uv_coord);
		//
		// 	// Sort Triangles into a three.js Face List
		// 	for (let i = 0; i < face.tri_indexes.length; i += 3) {
		// 		triangles.push(
		// 			face.tri_indexes[i + 0],
		// 			face.tri_indexes[i + 1],
		// 			face.tri_indexes[i + 2],
		// 		);
		// 	}
		//
		// 	// Use Vertex Color to label this face's indices for raycast picking
		// 	for (let i = 0; i < face.vertex_coord.length; i += 3) {
		// 		colors.push(face.face_index, 0, 0);
		// 	}
		//
		// 	const matCapMaterial = new THREE.MeshMatcapMaterial({
		// 		matcap: this.matcap,
		// 		// matcap: this.loader.load(require('@/assets/img/texture/front2.png')),
		// 		side: THREE.DoubleSide,
		// 		polygonOffset: true, // Push the mesh back for line drawing
		// 		polygonOffsetFactor: 2.0,
		// 		polygonOffsetUnits: 1.0,
		// 	});
		// 	const loader = new THREE.CubeTextureLoader();
		// 	loader.setPath('/img/');
		// 	const textureCube = loader.load([
		// 		// 'px.png', 'nx.png',
		// 		// 'py.png', 'ny.png',
		// 		// 'pz.png', 'nz.png',
		// 		'front.png', 'front.png',
		// 		'front.png', 'front.png',
		// 		'front.png', 'front.png',
		// 	]);
		// 	const material = new THREE.MeshBasicMaterial({ color: 0xffffff, envMap: textureCube });
		//
		// 	const geometry = new THREE.BufferGeometry();
		// 	geometry.setIndex(triangles);
		// 	geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
		// 	geometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
		// 	geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
		// 	geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
		// 	geometry.setAttribute('uv2', new THREE.Float32BufferAttribute(uvs, 2));
		// 	geometry.computeBoundingSphere();
		// 	geometry.computeBoundingBox();
		// 	const faceMesh = new THREE.Mesh(geometry, matCapMaterial);
		// 	// const faceMesh = new THREE.Mesh(geometry, material);
		// 	faceMesh.castShadow = true;
		// 	faceMesh.name = 'ModelFace_' + face.face_index;
		// 	faceGroup.add(faceMesh);
		// });
		// if (this.chooseModel === SelectModeEnum.line) {
		// 	this.mainObject.add(faceGroup);
		// } else {
		// 	this.mainObject.add(...faceGroup.children);
		// }
		// // -------------------------------------------

		// Add Highlightable Edges to Object
		// This wild complexity is what allows all of the lines to be drawn in a single draw call
		// AND highlighted on a per-edge basis by the mouse hover.  On the docket for refactoring.
		const lineVertices = [];
		const globalEdgeIndices = [];
		let curGlobalEdgeIndex = 0;
		let edgeVertices = 0;
		const globalEdgeMetadata = {};
		globalEdgeMetadata[-1] = { start: -1, end: -1 };
		edgeList.forEach((edge) => {
			const edgeMetadata = {};
			edgeMetadata.localEdgeIndex = edge.edge_index;
			edgeMetadata.start = globalEdgeIndices.length;
			for (let i = 0; i < edge.vertex_coord.length - 3; i += 3) {
				// 当前坐标点
				lineVertices.push(new THREE.Vector3(
					edge.vertex_coord[i + 0],
					edge.vertex_coord[i + 1],
					edge.vertex_coord[i + 2]),
				);
				// 下一个坐标点
				lineVertices.push(new THREE.Vector3(
					edge.vertex_coord[i + 0 + 3],
					edge.vertex_coord[i + 1 + 3],
					edge.vertex_coord[i + 2 + 3]),
				);
				globalEdgeIndices.push(curGlobalEdgeIndex); // 对应当前坐标点下标
				globalEdgeIndices.push(curGlobalEdgeIndex);// 对应下一个坐标点下标
				edgeVertices++;
			}
			edgeMetadata.end = globalEdgeIndices.length - 1;
			globalEdgeMetadata[curGlobalEdgeIndex] = edgeMetadata;
			curGlobalEdgeIndex++;
		});

		const lineGeometry = new THREE.BufferGeometry().setFromPoints(lineVertices);
		const lineColors = [];
		for (let i = 0; i < lineVertices.length; i++) { lineColors.push(0, 0, 0); }
		lineGeometry.setAttribute('color', new THREE.Float32BufferAttribute(lineColors, 3));
		const lineMaterial = new THREE.LineBasicMaterial({
			color: 0xffffff, vertexColors: true,
		});
		const line = new THREE.LineSegments(lineGeometry, lineMaterial);
		line.globalEdgeIndices = globalEdgeIndices;
		line.name = 'Model Edges';
		line.lineColors = lineColors;
		line.globalEdgeMetadata = globalEdgeMetadata;
		line.highlightEdgeAtLineIndex = function (lineIndex) {
			const edgeIndex = lineIndex >= 0 ? this.globalEdgeIndices[lineIndex] : lineIndex;
			const startIndex = this.globalEdgeMetadata[edgeIndex].start;
			const endIndex = this.globalEdgeMetadata[edgeIndex].end;
			for (let i = 0; i < this.lineColors.length; i++) {
				// 列索引
				const colIndex = Math.floor(i / 3);
				this.lineColors[i] = (colIndex >= startIndex && colIndex <= endIndex) ? 1 : 0;
			}
			this.geometry.setAttribute('color', new THREE.Float32BufferAttribute(this.lineColors, 3));
			this.geometry.colorsNeedUpdate = true;
		}.bind(line);
		line.getEdgeMetadataAtLineIndex = function (lineIndex) {
			return this.globalEdgeMetadata[this.globalEdgeIndices[lineIndex]];
		}.bind(line);
		line.clearHighlights = function () {
			return this.highlightEdgeAtLineIndex(-1);
		}.bind(line);
		if (this.chooseModel === SelectModeEnum.line) {
			this.mainObject.add(line);
		} else {
			const lineGroup = new THREE.Group();
			lineGroup.name = 'Model Edges';
			lineGroup.add(line);
			this.mainObject.add(lineGroup);
		}
		// End Adding Highlightable Edges

		// export cubeAxes geometry to json file
		// const jsonData = this.mainObject.toJSON();
		// console.log('jsonData: ', jsonData);
		// saveJson(JSON.stringify(jsonData, null, 4), 'cubeAxes.json');

		this.environment.scene.add(this.mainObject);
		this.environment.viewDirty = true;

		// zoom to extents
		const { center } = this.environment.zoomCameraToSelection(this.mainObject);
		this.environment.addBoundingBox(this.mainObject);
		this.environment.addCenterPoint(center);

		logger.log('Generation Complete!');
		logger.timeEnd('模型离散加载时间');

		setTimeout(() => {
			// eslint-disable-next-line no-console
			console.log(this.environment.renderer.info);
		}, 1000);
	};

	this.insertIntoScene = (type, objs = [], { mergeFaces = false } = {}) => {
		// 体加载
		if (type === ModeTypeEnum.solid) {
			const solidGroup = new THREE.Group();
			solidGroup.name = SOLID_GROUP;
			solidGroup.isSolid = true; // 表示体
			solidGroup.canDelete = true;
			objs.forEach(solid => solid.isSolid && solidGroup.add(solid));
			this.mainObject.add(solidGroup);
		}

		// 面加载
		if (type === ModeTypeEnum.face) {
			const faceGroup = new THREE.Group();
			faceGroup.name = FACE_GROUP;
			faceGroup.isFace = true; // 表示面
			faceGroup.canDelete = true;
			objs.forEach(mesh => mesh.isFace && faceGroup.add(mesh));

			// --------- 面合并开始 -----------
			if (mergeFaces) {
				const geometryArray = faceGroup.children.map(child => child.geometry.clone());
				const meshPhongMaterial = new THREE.MeshPhongMaterial({ emissive: new THREE.Color('#333333') });
				// 合并模型
				if (geometryArray.length) {
					const mergedGeometries = BufferGeometryUtils.mergeBufferGeometries(geometryArray, true);
					const singleMergeMesh = new THREE.Mesh(mergedGeometries, meshPhongMaterial);
					singleMergeMesh.isSolid = true;
					singleMergeMesh.solidGroup = 'solid_1';
					singleMergeMesh.visible = this.originVisible;
					singleMergeMesh.material.side = THREE.DoubleSide;
					singleMergeMesh.material.needsUpdate = true;
					singleMergeMesh.renderOrder = RenderOrderEnum.face;
					// 设置多边形偏移
					setPolygonOffset(singleMergeMesh.material, 2, 1);
					this.mainObject.add(singleMergeMesh);
				}
			} else {
				this.mainObject.add(faceGroup);
			}
			// --------- 面合并结束 -----------
		}

		// 线框加载
		if (type === ModeTypeEnum.wireframe) {
			const wireframeGroup = new THREE.Group();
			wireframeGroup.name = WIREFRAME;
			wireframeGroup.isWireframe = true; // 表示线框
			wireframeGroup.canDelete = true;
			objs.forEach(wire => wire.isWireframe && wireframeGroup.add(wire));
			this.mainObject.add(wireframeGroup);
		}

		// 离散线加载
		if (type === ModeTypeEnum.disperseLine) {
			const lineGroup = new THREE.Group();
			lineGroup.name = DISPERSED_LINE_GROUP;
			lineGroup.isDispersed = true; // 表示离散线
			lineGroup.canDelete = true;
			objs.forEach(line => line.isDispersed && lineGroup.add(line));
			this.mainObject.add(lineGroup);
		}
	};

	/** Set up the the Mouse Move Callback */
	this.mouse = { x: 0, y: 0 };
	this.viewContainer.addEventListener('mousemove', (event) => {
		this.mouse.x = (event.offsetX / this.viewContainer.clientWidth) * 2 - 1;
		this.mouse.y = -(event.offsetY / this.viewContainer.clientHeight) * 2 + 1;
	}, false);

	this.viewContainer.addEventListener('click', (event) => {

		if (!this.mainObject) return;

		// const point = this.getMousePoint(event);
		// this.environment.drawSphere(point, { name: 'sphere_1' });

		// update face color
		event.preventDefault();
		const raycaster = new THREE.Raycaster();
		const mouse = new THREE.Vector2();
		// mouse.x = (event.clientX / this.environment.renderer.domElement.offsetWidth) * 2 - 1;
		// mouse.y = -(event.clientY / this.environment.renderer.domElement.offsetHeight) * 2 + 1;
		mouse.x = (event.offsetX / this.viewContainer.clientWidth) * 2 - 1;
		mouse.y = -(event.offsetY / this.viewContainer.clientHeight) * 2 + 1;
		raycaster.setFromCamera(this.mouse, this.environment.camera);
		const intersects = raycaster.intersectObjects(this.mainObject.children);
		// console.log('intersects: ', intersects);
		if (intersects.length) {
			const colorAttribute = intersects[0].object.geometry.getAttribute('color');
			const face = intersects[0].face;

			// face.materialIndex = 1;

			// const color = new THREE.Color(ColorEnum.red);
			//
			// colorAttribute.setXYZ(face.a, color.r, color.g, color.b);
			// colorAttribute.setXYZ(face.b, color.r, color.g, color.b);
			// colorAttribute.setXYZ(face.c, color.r, color.g, color.b);
			//
			// colorAttribute.needsUpdate = true;

			this.environment.renderer.render(this.environment.scene, this.environment.camera);
		}
	}, false);

	this.getMousePoint = (event) => {
		if (!this.mainObject) return;
		const raycaster = new THREE.Raycaster();
		const mouse = new THREE.Vector2();
		// 通过鼠标点击位置,计算出 raycaster 所需点的位置,以屏幕为中心点,范围 -1 到 1
		mouse.x = (event.offsetX / this.viewContainer.clientWidth) * 2 - 1;
		mouse.y = -(event.offsetY / this.viewContainer.clientHeight) * 2 + 1;
		// 通过鼠标点击的位置(二维坐标)和当前相机的矩阵计算出射线位置
		raycaster.setFromCamera(mouse, this.environment.camera);
		// 获取与射线相交的对象数组，其中的元素按照距离排序，越近的越靠前
		const intersects = raycaster.intersectObjects(this.mainObject.children);
		if (intersects.length > 0) {
			// 选中第一个射线相交的物体
			const intersected = intersects[0];
			return intersected.point;
		}
		return null;
	};

	this.animate = () => {
		// Don't continue this callback if the View has been destroyed.
		if (!this.active) { return; }

		requestAnimationFrame(() => this.animate());

		// Lightly Highlight the faces of the object and the current face/edge index
		// This wild complexity is largely to handle the fact that all the faces and lines
		// are being drawn in a single drawcall.  This is also on the docket for refactoring.
		if (this.mainObject || this.cubeAxesObject) {
			this.raycaster.setFromCamera(this.mouse, this.environment.camera);
			const objects = [];
			if (this.mainObject?.children) {
				objects.push(...this.mainObject.children);
			}
			if (this.cubeAxesObject?.children) {
				objects.push(...this.cubeAxesObject.children);
			}
			const intersects = this.raycaster.intersectObjects(objects);
			if (intersects.length > 0) {
				const isMesh = intersects[0].object.type === 'Mesh';
				const isLine = intersects[0].object.type === 'LineSegments';
				const newIndex = isLine ? intersects[0].object.getEdgeMetadataAtLineIndex(intersects[0].index).localEdgeIndex
					: intersects[0].object.geometry.attributes.color?.getX(intersects[0].face.a);
				if (isLine && (this.highlightedObj !== intersects[0].object || this.highlightedIndex !== newIndex)) {
					if (this.highlightedObj) {
						this.highlightedObj.material.color.setHex(this.highlightedObj.currentHex);
						if (this.highlightedObj && this.highlightedObj.clearHighlights) { this.highlightedObj.clearHighlights(); }
					}
					this.highlightedObj = intersects[0].object;
					this.highlightedObj.currentHex = this.highlightedObj.material.color.getHex();
					this.highlightedObj.material.color.setHex(0xffffff);
					this.highlightedIndex = newIndex;
					if (isLine) { this.highlightedObj.highlightEdgeAtLineIndex(intersects[0].index); }
					this.environment.viewDirty = true;
					const indexHelper = (isLine ? 'Edge' : 'Face') + ' Index: ' + this.highlightedIndex;
					this.viewContainer.title = indexHelper;
				}
				if (isMesh && this.highlightedObj !== intersects[0].object) {
					if (this.highlightedObj) {
						if (this.highlightedObj.type === 'LineSegments') {
							this.highlightedObj.material.color.setHex(this.highlightedObj.currentHex); // 0x000000
							if (this.highlightedObj && this.highlightedObj.clearHighlights) { this.highlightedObj.clearHighlights(); }
						} else {
							this.highlightedObj.material.color.setHex(0xffffff);
							if (this.highlightedObj.name === 'Model Face') {
								this.highlightedObj.material.color.setHex(0xffffff);
							}
						}
					}
					this.highlightedObj = intersects[0].object;
					this.highlightedObj.currentHex = this.highlightedObj.material.color.getHex();
					this.highlightedObj.material.color.setHex(ColorEnum.pink);
					this.environment.viewDirty = true;
				}
			} else {
				if (this.highlightedObj) {
					this.highlightedObj.material.color.setHex(this.highlightedObj.currentHex);
					if (this.highlightedObj.clearHighlights) { this.highlightedObj.clearHighlights(); }
					this.environment.viewDirty = true;
				}

				this.highlightedObj = null;
				this.viewContainer.title = '';
			}
		}

		if (this.handles && this.handles.length > 0) {
			for (let i = 0; i < this.handles.length; i++) {
				this.environment.viewDirty = this.handles[i].dragging || this.environment.viewDirty;
			}
		}

		// Only render the Three.js Viewport if the View is Dirty
		// This saves on rendering time/cost now, but may
		// create headaches in the future.
		if (this.environment.viewDirty) {
			this.environment.renderer.render(this.environment.scene, this.environment.camera);
			this.environment.viewDirty = false;
		}
	};

	// Patch in the Handle Gizmo Code
	// initializeHandleGizmos(this);

	this.animate();
	// Initialize the view in-case we're lazy rendering...
	this.environment.renderer.render(this.environment.scene, this.environment.camera);
};

export { ViewEnvironment };
