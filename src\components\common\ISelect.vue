<template>
	<a-select
		ref="iSelectRef"
		:allow-clear="allowClear"
		:show-arrow="showArrow"
		:placeholder="placeholder"
		:mode="isMultiple ? 'multiple' : 'default'"
		:class="iClass"
		v-model="selectedKeys"
		:disabled="disabled"
		:label-in-value="labelInValue"
		:style="iStyle"
		:show-search="showSearch"
		:option-filter-prop="optionFilterProp"
		:option-label-prop="optionLabelProp"
		:filter-option="filterOption"
		:get-popup-container="triggerNode => triggerNode.parentNode"
		@blur="onBlur"
		@select="onSelect"
		@deselect="onDeselect"
		@change="onChange"
		@mouseenter="mouseEnter"
	>
		<template v-for="(option, index) of options">
			<a-select-option v-if="!showJoinKey && !showIndex" :key="option[optionKey]" :value="option[optionKey]" :disabled="option.disabled" @click="clickHandle">
				<unit :unit="option[optionTitle]" />
			</a-select-option>
			<a-select-option v-if="showJoinKey" :key="option[optionKey]" :value="option[optionKey]" :disabled="option.disabled" @click="clickHandle">
				<unit v-if="sort === 'desc'" :unit="`${ option[optionTitle] }${ separator }${ option[joinKey] }`" />
				<unit v-else :unit="`${ option[joinKey] }${ separator }${ option[optionTitle] }`" />
			</a-select-option>
			<a-select-option v-if="showIndex" :key="option[optionKey]" :value="option[optionKey]" :disabled="option.disabled" @click="clickHandle">
				<unit :unit="`${ index + 1 }${ option[optionTitle] }`" />
			</a-select-option>
		</template>
		<template slot="notFoundContent">
			<span>暂无数据</span>
		</template>
	</a-select>
</template>

<script>
/* ===================================
 * select二次封装
 * Created by cjking on 2020/06/02.
 * Copyright 2020, Inc.
 * =================================== */
import { isEmpty, noop } from '@/utils/utils';
import Unit from '@/components/unit/Unit';

export default {
	name: 'ISelect',
	components: { Unit },
	props: {
		value: {
			type: [String, Number, Array, undefined],
			required: false,
			default: undefined,
		},
		options: {
			type: Array,
			required: true,
			default: () => [],
		},
		isMultiple: {
			type: Boolean,
			required: false,
			default: false,
		},
		optionKey: {
			type: String,
			required: false,
			default: 'id',
		},
		optionTitle: {
			type: String,
			required: false,
			default: 'name',
		},
		placeholder: {
			type: String,
			required: false,
			default: '请选择',
		},
		iClass: {
			type: [Object, Array],
			required: false,
			default: () => {},
		},
		iStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
		allowClear: {
			type: Boolean,
			required: false,
			default: true,
		},
		showArrow: {
			type: Boolean,
			required: false,
			default: true,
		},
		showSearch: {
			type: Boolean,
			required: false,
			default: false,
		},
		disabled: {
			type: Boolean,
			required: false,
			default: false,
		},
		labelInValue: {
			type: Boolean,
			required: false,
			default: false,
		},
		showJoinKey: {
			type: Boolean,
			required: false,
			default: false,
		},
		showIndex: {
			type: Boolean,
			required: false,
			default: false,
		},
		separator: {
			type: String,
			required: false,
			default: '-',
		},
		joinKey: {
			type: String,
			required: false,
			default: 'value',
		},
		optionFilterProp: {
			type: String,
			required: false,
			default: 'value',
		},
		optionLabelProp: {
			type: String,
			required: false,
			default: 'children',
		},
		visible: {
			type: Boolean,
			required: false,
			default: false,
		},
		clickOptionClose: { // 点击option选项时关闭下拉框
			type: Boolean,
			required: false,
			default: true,
		},
		sort: {
			type: String,
			required: false,
			default: 'desc', // or asc
		},
		limit: {
			type: Number,
			required: false,
			default: 15,
		},
	},
	model: {
		prop: 'value',
		event: 'change',
	},
	data () {
		return {
			open: false,
			selectedKeys: [],
		};
	},
	watch: {
		value: {
			immediate: true,
			handler (value) {
				this.initData(value);
			},
		},
		visible: {
			immediate: true,
			handler () {
				this.open = this.visible;
			},
		},
	},
	methods: {
		/**
		 * 初始化数据
		 * @param value
		 */
		initData (value) {
			const selectedKeys = isEmpty(value) ? undefined : value;
			if (Array.isArray(selectedKeys)) {
				this.selectedValues = [];
				for (const key of selectedKeys) {
					if (!this.selectedKeys.includes(key) && this.selectedKeys.length < this.limit) {
						this.selectedKeys.push(key);
					}
				}
			} else {
				this.selectedKeys = selectedKeys;
			}
		},

		/**
		 * 获得焦点时回调
		 * @param value
		 */
		onFocus (value) {
			this.open = true;
			this.$emit('focus', value);
		},

		/**
		 * 失去焦点的时回调
		 * @param value
		 */
		onBlur (value) {
			this.open = false;
			this.$emit('blur', value);
		},

		/**
		 * 被选中时调用
		 * @param value
		 */
		onSelect (value) {
			this.$emit('select', value);
		},

		/**
		 * 值变更时调用
		 * @param {Array} selectedKeys
		 */
		onChange (selectedKeys) {
			if (Array.isArray(selectedKeys)) {
				selectedKeys = selectedKeys.slice(0, this.limit);
			}
			this.selectedKeys = this.isMultiple ? selectedKeys : (Array.isArray(selectedKeys) ? selectedKeys[0] : selectedKeys);
			this.$emit('change', this.selectedKeys);
		},

		/**
		 * 取消选中
		 */
		onDeselect (value) {
			this.$emit('deselect', value);
		},

		/**
		 * 输入搜索过滤
		 * @param inputValue
		 * @param option
		 * @return {boolean}
		 */
		filterOption (inputValue, option) {
			const componentOptions = option.componentOptions.children[0]?.componentOptions;
			const tag = componentOptions.tag;
			return componentOptions?.propsData[tag].toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
		},

		/**
		 * 点击时回调
		 */
		clickHandle () {
			if (this.clickOptionClose) {
				this.$refs.iSelectRef.blur();
			}
		},

		/**
		 * 鼠标移入时回调
		 */
		mouseEnter () {
			const clearIcon = this.$refs.iSelectRef?.$el.querySelector('.ant-select-clear-icon');
			if (clearIcon) {
				clearIcon.addEventListener('click', (e) => {
					e.stopPropagation();
					this.onChange([]);
					clearIcon.removeEventListener('click', noop);
				});
			}
		},
	},
};
</script>
