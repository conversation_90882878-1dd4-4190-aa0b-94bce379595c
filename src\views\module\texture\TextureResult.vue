<template>
	<div class="collapse-result" v-if="dataList.length">
		<a-collapse expandIconPosition="left">
			<a-collapse-panel class="collapse-panel" key="1">

				<template slot="header">
					<div class="collapse-panel-header">
						<div class="title">结果</div>
						<div class="extra" @click="e => preventDefaults(e)">
							<slot name="extra">
								<a-button type="default" size="small" icon="download" class="btn-down" @click="e => showDownloadModal(e)">
									下载结果
								</a-button>
							</slot>
						</div>
					</div>
				</template>

				<!-- 内容区域 -->
				<slot name="content">
					<div class="box">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">2D纹理线条模型</span>
								</div>
								<div class="item right">
									<div class="mr-10" @click="visibleHandler(TextureResultTypeEnum.visible2dWlLine)">
										<svg-icon :type="inner2DLineVisible ? 'param-visible' : 'param-hidden'" />
									</div>

									<div class="mr-10">
										<input type="color" v-model="inner2DLineColor" @input="colorHandler(TextureResultTypeEnum.visible2dWlLine)">
									</div>

									<a-popover :title="null" placement="topRight">
										<template #content>
											<div class="display-flex-center">
												<a-slider
													:min="0"
													:max="100"
													:step="1"
													v-model="inner2DLineOpacity"
													:style="{ width: '100px', margin: '10px 0 0 10px' }"
													@change="opacityHandler(TextureResultTypeEnum.visible2dWlLine)"
												/>
												<div class="opacity">{{ inner2DLineOpacity }}%</div>
											</div>
										</template>
										<svg-icon type="param-opacity" />
									</a-popover>
								</div>
							</div>
						</div>
						<div class="column">
							<div class="action grid">
								<!--<div class="grid-cell u-1of3 text-left">
									<span class="text">.3dm</span>
									<span class="icon">
										&lt;!&ndash;<img v-if="!innerData['3dmGenerating']" class="down" :src="downIcon" @click="downloadFileByName('3dmGenerating', 'result_wl_line_3dm')">&ndash;&gt;
										&lt;!&ndash;<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />&ndash;&gt;
									</span>
								</div>
								<div class="grid-cell u-1of3 text-center">
									&lt;!&ndash;<span class="text">.iges</span>
									<span class="icon">
										<img v-if="innerData['result_wl_line_iges']" class="down" :src="downIcon" @click="downloadFileByName('result_wl_line_iges')">
										<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
									</span>&ndash;&gt;
								</div>
								<div class="grid-cell u-1of3 text-right">
									<span class="text">.step</span>
									<span class="icon">
										&lt;!&ndash;<img v-if="!innerData['stepGenerating']" class="down" :src="downIcon" @click="downloadFileByName('stepGenerating', 'result_wl_line_step')">&ndash;&gt;
										&lt;!&ndash;<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />&ndash;&gt;
									</span>
								</div>-->

								<div class="grid-cell text-left ext">
									<span class="text">文件格式：.3dm，.step</span>
								</div>
							</div>
						</div>
					</div>

					<!--<div class="box mtb-16">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">2D纹理线条矢量</span>
								</div>
								<div class="item right">

									<div class="mr-10" @click="visibleHandler(TextureResultTypeEnum.visible2dWlVector)">
										<svg-icon :type="inner2DVectorVisible ? 'param-visible' : 'param-hidden'" />
									</div>

									<div class="mr-10">
										<input type="color" v-model="inner2DVectorColor" @input="colorHandler(TextureResultTypeEnum.visible2dWlVector)">
									</div>

									<a-popover :title="null" placement="topRight">
										<template #content>
											<div class="display-flex-center">
												<a-slider
													:min="0"
													:max="100"
													:step="1"
													v-model="inner2DVectorOpacity"
													:style="{ width: '100px', margin: '10px 0 0 10px' }"
													@change="opacityHandler(TextureResultTypeEnum.visible2dWlVector)"
												/>
												<div class="opacity">{{ inner2DVectorOpacity }}%</div>
											</div>
										</template>
										<svg-icon type="param-opacity" />
									</a-popover>
								</div>
							</div>
						</div>

						<div class="column">
							<div class="action grid jc-sb">
								<span class="title">.ai</span>
								<span class="icon">
									<img v-if="innerData['result_wl_line_ai']" class="down" :src="downIcon" @click="downloadFileByName('result_wl_line_ai')">
									<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
								</span>
							</div>
						</div>
					</div>-->

					<div class="box mtb-16">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">2D纹理线条参数文档</span>
								</div>
							</div>
						</div>

						<div class="column">
							<div class="action grid jc-sb">
								<span class="title">文件格式：.txt</span>
								<span class="icon">
									<a-tooltip placement="topLeft" style="margin-right: 10px;">
										<template slot="title">
											<a-descriptions class="desc" :column="1" bordered size="small">
												<template v-for="(paramsDoc, index) in paramsDocs">
													<a-descriptions-item :key="index" :label="paramsDoc[0]" style="min-width: 200px;">
														{{ paramsDoc[1] }}
													</a-descriptions-item>
												</template>
											</a-descriptions>
										</template>
										<span><a-icon type="info-circle" /></span>
									</a-tooltip>
									<img v-if="innerData['result_wl_line_params']" class="down" :src="downIcon" @click="downloadFileByPath(innerData['result_wl_line_params'])">
									<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
								</span>
							</div>
						</div>
					</div>

					<!--<div class="box mtb-16">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">PNG图片格式</span>
								</div>
							</div>
						</div>

						<div class="column">
							<div class="action grid jc-sb">
								<span class="title">.png</span>
								<span class="icon">
									<img v-if="innerData['result_wl_line_png']" class="down" :src="downIcon" @click="downloadFileByPath(innerData['result_wl_line_png'])">
									<svg-icon v-else className="sync" type="sync" spin @click="fileLoading" />
								</span>
							</div>
						</div>
					</div>-->
					<div class="box mtb-16">
						<div class="column">
							<div class="content">
								<div class="item">
									<span class="label">PNG图片（截屏）</span>
								</div>
							</div>
						</div>

						<div class="column">
							<div class="action grid jc-sb">
								<span class="title">文件格式：.png</span>
								<span class="icon">
									<img class="screenshot" :src="screenshotIcon" @click="screenshot()">
								</span>
							</div>
						</div>
					</div>
				</slot>

			</a-collapse-panel>
		</a-collapse>

		<DownloadModal v-if="downloadModalVisible" :dataList="dataList" ref="downloadModal" @ok="showDownloadModal = false" />
	</div>
</template>

<script>
/* ===================================
 * 纹理-求解结果页
 * Created by cjking on 2022/04/26.
 * Copyright 2022, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { preventDefaults } from '@/components/core/ViewUtils';
import { basename, downloadFileByNewWindow } from '@/utils/utils';
import { downloadWlFile, getProjectTaskDetail, loadFile } from '@/api';
import { TextureFileTypeEnum, TextureResultTypeEnum } from '@/constants';

import downIcon from '@/assets/img/texture/down.png';
import screenshotIcon from '@/assets/img/texture/screenshot.png';

import DownloadModal from '@/views/module/texture/DownloadModal';

const icons = { downIcon, screenshotIcon };

export default {
	name: 'TextureResult',
	components: {
		DownloadModal,
	},
	props: {
		activeKey: {
			type: Array,
			required: false,
			default: () => ['1'],
		},
		dataList: {
			type: Array,
			required: false,
			default: () => [],
		},
		view: {
			type: Object,
			required: false,
			default: () => {},
		},
	},
	data () {
		return {
			...icons,
			TextureFileTypeEnum,
			TextureResultTypeEnum,

			// 显示
			inner2DLineVisible: true,
			inner2DVectorVisible: true,

			downloadModalVisible: false,

			// 颜色
			inner2DLineColor: '#6362FF',
			inner2DVectorColor: '#6362FF',

			// 透明度
			inner2DLineOpacity: 100,
			inner2DVectorOpacity: 100,

			// 参数文档数据
			paramsDocs: [],

			innerData: {
				'3dmGenerating': false,
				'stepGenerating': false,
			},

			taskId: '',
		};
	},
	mounted () {
		this.taskId = this.$route.params.taskId || '';
	},
	watch: {
		dataList: {
			deep: true,
			immediate: true,
			handler () {
				this.dataList.forEach(data => {
					if (data.name === 'result_wl_line_3dm' && data.url) {
						this.innerData['result_wl_line_3dm'] = data.url;
					}
					// if (data.name === 'result_wl_line_iges' && data.url) {
					// 	this.innerData['result_wl_line_iges'] = data.url;
					// }
					if (data.name === 'result_wl_line_step' && data.url) {
						this.innerData['result_wl_line_step'] = data.url;
					}
					// if (data.name === 'result_wl_line_ai' && data.url) {
					// 	this.innerData['result_wl_line_ai'] = data.url;
					// }
					if (data.name === 'result_wl_line_params' && data.url) {
						this.innerData['result_wl_line_params'] = data.url;
					}
					if (data.name === 'result_wl_line_png' && data.url) {
						this.innerData['result_wl_line_png'] = data.url;
					}
				});
				if (this.innerData['result_wl_line_params']) {
					this.initData();
				}
				logger.log('纹理结果数据 this.innerData: ', this.innerData);
			},
		},
	},
	methods: {
		preventDefaults,

		async initData () {
			this.paramsDocs = [];
			const url = this.innerData['result_wl_line_params'];
			const textContent = await loadFile(url, 'text', false);
			const strArray = textContent.split('\n').filter(s => s);
			for (const str of strArray) {
				const fieldArray = str.split('：');
				this.paramsDocs.push(fieldArray);
			}
		},

		/**
		 * 显示/隐藏处理
		 */
		visibleHandler (type) {
			let visible;
			if (type === TextureResultTypeEnum.visible2dWlLine) {
				this.inner2DLineVisible = !this.inner2DLineVisible;
				visible = this.inner2DLineVisible;
			}
			if (type === TextureResultTypeEnum.visible2dWlVector) {
				this.inner2DVectorVisible = !this.inner2DVectorVisible;
				visible = this.inner2DVectorVisible;
			}
			this.$emit('onVisible', { type, visible });
		},

		/**
		 * 颜色处理
		 */
		colorHandler (type) {
			let color;
			if (type === TextureResultTypeEnum.visible2dWlLine) {
				color = this.inner2DLineColor;
			}
			if (type === TextureResultTypeEnum.visible2dWlVector) {
				color = this.inner2DVectorColor;
			}
			this.$emit('onColor', { type, color });
		},

		/**
		 * 透明度处理
		 */
		opacityHandler (type) {
			let opacity;
			if (type === TextureResultTypeEnum.visible2dWlLine) {
				opacity = this.inner2DLineOpacity;
			}
			if (type === TextureResultTypeEnum.visible2dWlVector) {
				opacity = this.inner2DVectorOpacity;
			}
			this.$emit('onOpacity', { type, opacity });
		},

		/**
		 * 按路径下载文件
		 */
		downloadFileByPath (filePath) {
			if (!filePath) {
				this.$message.error('未找到下载所需要的文件');
				return;
			}
			downloadFileByNewWindow(filePath, basename(filePath));
		},

		/**
		 * 按名称下载文件
		 */
		// async downloadFileByName (loadingKey, typeName) {
		// 	const params = {
		// 		id: this.taskId,
		// 		typeName: typeName,
		// 	};
		// 	const res = await downloadWlFile(params);
		// 	if (res?.success) {
		// 		this.$message.success('文件生成中，请稍后片刻...');
		// 		this.innerData[loadingKey] = true;
		// 		this.$forceUpdate();
		// 	}
		// },

		/**
		 * 截屏
		 */
		screenshot () {
			const taskId = this.taskId;
			this.view.screenshot(taskId, false, (imgData) => {
				const fileName = taskId + '.png';
				downloadFileByNewWindow(imgData, fileName);
			});
		},

		/**
		 * 文件加载中
		 */
		fileLoading () {
			this.$message.error('文件正在生成中，请稍后再试');
		},

		/**
		 * 下载弹窗
		 */
		async showDownloadModal () {
			const dataList = this.dataList;
			const res = await getProjectTaskDetail({ id: this.taskId });
			dataList.forEach(data => {
				if (data.label.includes('.3dm')) {
					data.label = data.label.replace('.3dm', '');
				}
				if (data.label.includes('.step')) {
					data.label = data.label.replace('.step', '');
				}
				data.label += data.name.endsWith('_3dm') ? '.3dm' : '.step';
				data.status = res.result?.sysTask?.resultJson[data.name + '_status'];
			});
			this.downloadModalVisible = true;
			this.$nextTick(() => {
				this.$refs.downloadModal.open();
			});
		},
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.collapse-result {
	width: 320px;
	position: relative;

	.btn-down {
		position: absolute;
		top: 8px;
		right: 18px;
		//width: 56px;
		height: 24px;
		border-radius: 2px;
		color: #8E8AFF;
		background-color: #FFF;
		border-color: #8E8AFF;

		&:hover {
			color: rgba(142, 138, 255, 0.5);
			background-color: rgba(255, 255, 255, 0.5);
			border-color: rgba(142, 138, 255, 0.5);
		}
	}

	/deep/ .ant-collapse > .ant-collapse-item {
		border-bottom: 3px solid #E5E5E5;

		.ant-collapse-header {
			border-left: 2px solid @primary;
			border-radius: 0;
			background: #F9F9FF;
			padding: 9px 16px;
			padding-right: 40px;
		}
	}

	.collapse-panel {

		.disable-selection();

		.collapse-panel-header {
			display: flex;

			.icon {
				flex: 0 0 20px;
			}

			.title {
				flex: 1;
				margin-left: 18px;
				font-size: 16px;
				font-weight: bold;
				color: #5C5C5C;
				.disable-selection();
			}

			.extra {
				flex: 0 0 70px;
				text-align: end;
				.disable-selection();
			}
		}

		.label {
			height: 20px;
			line-height: 20px;
			font-size: 14px;
			font-weight: bold;
			color: #111111;
		}

		input[type="color"] {
			width: 20px;
			height: 22px;
			border: 0;
			background-color: transparent;
		}

		.box {
			&.mtb-16 {
				margin: 16px 0;
			}

			.content {
				display: flex;
				width: 100%;

				.item {
					flex: 0 0 50%;

					svg {
						width: 20px;
						height: 20px;
						color: @primary;
					}

					&.right {
						display: flex;
						justify-content: flex-end;
						align-items: center;

						svg, input[type="color"] {
							cursor: pointer;
						}

						.mr-10 {
							margin-right: 10px;
						}
					}
				}
			}

			.action {
				background: #F9F9F9;
				width: 100%;
				text-align: center;
				margin-top: 6px;

				.text-right {
					text-align: right;
					padding-right: 8px;
				}

				.text {
					font-size: 14px;
					font-weight: 600;
					color: #5C5C5C;
					margin-left: 10px;
					display: inline-block;
					padding: 6px 0;

					&.left {
						text-align: left;
					}
				}

				.icon {
					display: inline-block;
					margin-left: 8px;
					color: @primary;
					cursor: pointer;

					&.right {
						text-align: right;
					}
				}

				&.jc-sb {
					justify-content: space-between;
					padding: 6px 12px;

					.title {
						font-size: 14px;
						font-weight: 600;
						color: #4F4E4E;
					}
				}
			}
		}
	}

	.down {
		width: 20px;
		margin-top: -3px;
	}

	.screenshot {
		width: 15px;
		margin-top: -3px;
	}

	.sync {
		width: 20px;
		height: 20px;
		vertical-align: -5px;
	}
}

.desc {
	background-color: #FFFFFF;
	padding: 1px;
}
</style>
