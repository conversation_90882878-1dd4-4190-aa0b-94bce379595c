/* ===================================
 * 指令：科学计数法及数字互相转换
 * Created by cjking on 2021/10/18.
 * Copyright 2021, Inc.
 * =================================== */
import { SCIENTIFIC_COUNTING_METHOD } from '@/utils/validate';
import { isEmpty, toExponential, toNonExponential } from '@/utils/utils';

const addListener = function (el, type, fn) {
	el.addEventListener(type, fn, false);
};

const clickHandler = function (el) {
	addListener(el, 'click', () => {
		if (!isEmpty(el.value)) {
			// 科学计数法转数字字符串
			if (!isEmpty(el.value) && SCIENTIFIC_COUNTING_METHOD.test(el.value.toString())) {
				el.value = toNonExponential(el.value);
			}
		}
	});
};

const blurHandler = function (el, config) {
	addListener(el, 'blur', () => {
		if (!isEmpty(el.value) && el.value.toString().length >= config.limit) { // 值的长度大等于5时，开始转换为科学计数法
			// 数字转科学计数法
			el.value = toExponential(el.value, config.fractionDigits);
		}
	});
};

export const ExponentialToNumberDirective = Vue => Vue.directive('exponentialToNumber', {
	bind (el, binding, vnode) {
		if (el.tagName.toLowerCase() !== 'input') {
			el = el.getElementsByTagName('input')[0];
		}

		const initConfig = binding.value;
		const config = Object.assign({ isActive: true, fractionDigits: undefined, limit: 5 }, initConfig);
		clickHandler(el);
		if (config.isActive) {
			blurHandler(el, config);
		}
	},
});
