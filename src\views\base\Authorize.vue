<template>
	<div>
		<div class="company-header">
			<h2>{{ info.departName }}</h2>
			<span>企业账号数：{{ userCount }}</span><span style="margin-left: 30px"> 注册日期：{{ info.createTime ? info.createTime.replace(/\s.*$/g, '') : '' }}</span>
		</div>
		<div :style="{marginTop: '12px', maxWidth: '1200px', margin: '-91px auto 20px auto'}">

			<a-card v-for="item in dataSource.filter(i=>i.packageName)" :key="item.moduleName" :style="{ borderRadius: '10px', marginBottom: '20px', boxShadow: '0px 0px 4px 0px rgba(99,98,255,0.2000)'}" class="company-module">
				<a-row>
					<a-col :span="20">
						<div class="flex-start">
							<span
								class="large-text"
							>{{ item.packageName }}</span>

							<a-tag
								color="#E0E0FF"
								style="margin-left:10px;line-height: 1.5;color:#6362FF"
							>{{ item.moduleName }}
							</a-tag>
						</div>
						<table style="width:100%;">
							<tr>
								<td style="width:50%"><div style="margin-top:20px;margin-bottom:10px;">有效期</div></td>
								<td style="width:25%"><div style="margin-top:20px;margin-bottom:10px;">套餐下载次数</div></td>
								<td style="width:25%"><div style="margin-top:20px;margin-bottom:10px;">剩余下载次数</div></td>
							</tr>
							<tr>
								<td>
									<span class="big-text" v-if="item.serveEnd && item.serveEnd < currentDate" :style="{color:'red'}">
										{{ item.serveBegin }}
										<span v-if="item.serveBegin">~</span>
										{{ item.serveEnd }}
									</span>
									<span class="big-text" v-else>
										{{ item.serveBegin }}
										<span v-if="item.serveBegin">~</span>
										{{ item.serveEnd }}
									</span>
								</td>
								<td>
									<span
										v-if="item.downMethod === '1'"
										class="big-text"
									>{{ item.downCount }}
									</span>
									<span
										v-if="item.downMethod === '0'"
										class="big-text"
									>不限</span>
								</td>
								<td>
									<span
										v-if="item.downMethod === '1'"
									>
										<span
											v-if="item.surplusDownCount > 0"
											class="big-text"
										>{{ item.surplusDownCount }}
										</span>
										<span v-else class="big-text" :style="{color: 'red'}">
											{{ item.surplusDownCount }}
										</span>
									</span>
									<span
										v-if="item.downMethod === '0'"
										class="big-text"
									>不限</span>
								</td>
							</tr>
						</table>
						<!-- <a-descriptions layout="vertical" :colon="false">
							<template slot="title">
								<span
									class="large-text"
								>{{ item.packageName }}</span>

								<a-tag
									color="#E0E0FF"
									style="margin-left:20px;color:#6362FF"
								>{{ item.moduleName }}
								</a-tag>
							</template>
							<a-descriptions-item label="有效期">
								<span class="big-text" v-if="item.serveEnd && item.serveEnd < currentDate" :style="{color:'red'}">
									{{ item.serveBegin }}
									<span v-if="item.serveBegin">~</span>
									{{ item.serveEnd }}
								</span>
								<span class="big-text" v-else>
									{{ item.serveBegin }}
									<span v-if="item.serveBegin">~</span>
									{{ item.serveEnd }}
								</span>
							</a-descriptions-item>
							<a-descriptions-item label="套餐下载次数">
								<span
									v-if="item.downMethod === '1'"
									class="big-text"
								>{{ item.downCount }}
								</span>
								<span
									v-if="item.downMethod === '0'"
									class="big-text"
								>不限</span>
							</a-descriptions-item>
							<a-descriptions-item label="剩余下载次数">
								<span
									v-if="item.downMethod === '1'"
								>
									<span
										v-if="item.surplusDownCount > 0"
										class="big-text"
									>{{ item.surplusDownCount }}
									</span>
									<span v-else class="big-text" :style="{color: 'red'}">
										{{ item.surplusDownCount }}
									</span>
								</span>
								<span
									v-if="item.downMethod === '0'"
									class="big-text"
								>不限</span>
							</a-descriptions-item>
						</a-descriptions> -->
					</a-col>
					<a-col :span="4" :style="{textAlign:'right', paddingTop: '80px'}">
						<a v-if="currentModuleId === item.moduleId" @click="handleDetail(item.moduleId, false)">收起使用记录</a>
						<a v-else @click="handleDetail(item.moduleId, true, 1)">展开使用记录</a>
					</a-col>
				</a-row>
				<a-row v-if="currentModuleId === item.moduleId" :style="{marginTop: '4px'}">
					<a-col>
						<a-table
							style="margin-top:10px"
							ref="table"
							size="middle"
							rowKey="id"
							:bordered="false"
							:columns="columns"
							:dataSource="dataSource2"
							:pagination="pagination"
							@change="handleTableChange"
						>
							<span slot="operateTypeSlot" slot-scope="record">
								{{ operateNameMap[record.operateType] }}
							</span>
							<span slot="downCountSlot" slot-scope="record">
								{{ record.operateType === '9' ? 1 : '' }}
							</span>
						</a-table>
					</a-col>
				</a-row>
			</a-card>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';
import { queryDepartById, queryDepartModulePackageList, queryDepartPowerLog } from '@/api';
import { logger } from '@/utils/logger';
import dayjs from 'dayjs';

export default {
	components: {},
	data () {
		return {
			disableMixinCreated: true,
			info: {},
			dataSource: [],
			dataSource2: [],
			selectedModules: [],
			packageList: [],
			showAuthorizeModal: false,
			showChartModal: false,
			userCount: 0,
			useCount: 0,
			expireCount: 0,
			currentModuleId: '',
			columns: [
				{
					title: '时间',
					align: 'left',
					dataIndex: 'createTime',
				},
				{
					title: '操作类型',
					align: 'left',
					scopedSlots: { customRender: 'operateTypeSlot' },
				},
				{
					title: '操作',
					align: 'left',
					dataIndex: 'logContent',
				},
				{
					title: '消耗下载次数',
					align: 'left',
					scopedSlots: { customRender: 'downCountSlot' }
				},
				{
					title: '项目名称',
					align: 'left',
					dataIndex: 'projectName',
				},
				{
					title: '任务名称',
					align: 'left',
					dataIndex: 'taskName',
				},
				{
					title: '操作人',
					align: 'left',
					dataIndex: 'realname',
				},
			],
			pagination: {
				current: 1,
				pageSize: 10,
				pageSizeOptions: ['10', '20', '30'],
				showTotal: (total, range) => {
					return range[0] + '-' + range[1] + ' 共' + total + '条';
				},
				showQuickJumper: false,
				showSizeChanger: false,
				total: 0
			},
			operateNameMap: {
				'1': '添加模块',
				'2': '删除模块',
				'3': '编辑模块',
				'4': '模块添加套餐信息',
				'5': '修改模块套餐日期',
				'6': '修改模块套餐剩余次数',
				'7': '模块套餐已过期',
				'8': '模块套餐下载次数已为0',
				'9': '更新剩余下载次数'
			}
		};
	},
	computed: {
		...mapGetters(['userInfo', 'moduleList']),
		currentDate () {
			return dayjs().format('YYYY-MM-DD');
		},
	},
	async created () {
		this.departId = this.userInfo.departIds;
		await this.loadData();
	},
	methods: {
		async loadData () {
			const res = await queryDepartById({ id: this.departId });
			if (res.success) {
				this.info = res.result.sysDepart;
				this.userCount = res.result.userCount;
				this.useCount = res.result.useCount;
				this.expireCount = res.result.expireCount;
			}
			const res1 = await queryDepartModulePackageList({ departId: this.departId });
			if (res1.success) {
				this.dataSource = res1.result;
			}
		},
		async handleDetail (moduleId, show, pageNo) {
			if (show) {
				if (this.currentModuleId !== moduleId) {
					this.pagination.current = 1;
				}
				this.currentModuleId = moduleId;
			} else {
				this.currentModuleId = '';
				this.dataSource2 = [];
				this.pagination.total = 0;
				return;
			}
			const params = {}; // 查询条件
			if (pageNo) {
				params.pageNo = 1;
				params.pageSize = 10;
			} else {
				params.pageNo = this.pagination.current;
				params.pageSize = this.pagination.pageSize;
			}
			params.departId = this.info.id;
			params.moduleId = this.currentModuleId;
			params.column = 'createTime';
			params.order = 'desc';
			logger.log(params);
			const res2 = await queryDepartPowerLog(params);
			if (res2.success) {
				this.$nextTick(() => {
					this.dataSource2 = res2.result.records;
					this.pagination.total = res2.result.total;
				});
			}
		},
		handleViewChart (record) {
			this.showChartModal = true;
			this.$nextTick(() => {
				this.$refs.chartModal.open(record);
			});
		},
		handleEdit (record) {
			this.showAuthorizeModal = true;
			this.$nextTick(() => {
				this.$refs.modalForm.edit(record);
			});
		},
		handleModalCallback () {
			this.showAuthorizeModal = false;
			this.showChartModal = false;
			this.loadData();
		},
		handleTableChange (pagination) {
			this.pagination = pagination;
			this.handleDetail(this.currentModuleId, true);
		},
	},
};
</script>

<style lang="less" scoped>
.company-header {
    h2 {
        padding-top: 40px;
        font-size: 24px;
        color: white;
    }
    text-align: center;
    color: white;
    width: 100%;
    height: 240px;
    background: url(~@/assets/img/company/header.png) no-repeat center center;
}
.company-module {
    /deep/ .ant-descriptions-item-label {
        color: #666666
    }
    /deep/ .ant-descriptions-row > td {
        padding-bottom: 10px;
    }
    /deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th {
        padding: 8px 8px;
    }
    /deep/ .ant-table-middle > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td {
        padding: 8px 8px;
    }
}
.tag-names {
	/deep/ .ant-tag {
		margin-bottom: 8px;
	}
}

.large-text {
	font-size: 22px;
    font-weight: 500;
}

.big-text {
	font-size: 20px;
    font-weight: 500;
}

.red-text {
	color: red;
	margin-left: 40px;
}

.flex-start {
	display: flex;
	align-items: center;
	justify-content: flex-start;
}
</style>
