import * as THREE from 'three';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';
import { LineSegments2 } from 'three/examples/jsm/lines/LineSegments2';
import { LineSegmentsGeometry } from 'three/examples/jsm/lines/LineSegmentsGeometry';
import { ColorEnum, RenderOrderEnum, PlaneModeEnum } from '@/constants';
import { logger } from '@/utils/logger';

export const mixinPlane = {
	data () {
		return {
			curPlaneMode: '', // 参考平面模式
			curPrePlaneMode: '', // 选中参考平面模式后，如果时自定义或曲面，接后续动作，在鼠标点击事件中使用，区分绘图和普通模式
		};
	},
	methods: {
		/**
		 * 创建网格平面
		 */
		createGridPlane (planeMode = this.curPlaneMode) {
			if (this.gridHelper) return;

			const diy = this.centerPoint.xyz ?? this.camCenter;
			this.diyPoint = diy.clone();

			this.gridHelper = this.createHelpPlane(planeMode, diy);
			this.gridHelper.isHelper = true;
			this.gridHelper.renderOrder = RenderOrderEnum.grid;
			this.getScene().add(this.gridHelper);
			this.requestRender();
		},

		/**
		 * 创建帮助平面
		 * @param planeMode 参照PlaneModeEnum
		 * @param {THREE.Vector3} diy
		 */
		createHelpPlane (planeMode, diy) {
			// 创建网格平面
			const gridHelperSmall = this.createGridHelper(
				this.modelLong * 2,
				60,
				0xa2a2a2,
				0xa2a2a2
			);
			const gridHelper = this.createGridHelper(
				this.modelLong * 2,
				10,
				0x000000,
				0x000000
			);

			// 创建网格平面坐标轴(默认和自定义都有)
			let planeAxes;
			if (planeMode.match(/XY|YZ|XZ/)) {
				planeAxes = this.createPlaneAxes(planeMode);
				// 自定义原点坐标
				if (!planeMode.match(/diy/i)) {
					diy = this.camCenter;
				}
			}
			const axesGroup = new THREE.Group();
			axesGroup.add(gridHelperSmall);
			axesGroup.add(gridHelper);
			if (planeAxes) {
				axesGroup.add(planeAxes);
			}
			axesGroup.updateMatrix();
			axesGroup.updateMatrixWorld(true);

			if ([PlaneModeEnum.planeXY, PlaneModeEnum.planeDiyXY].includes(this.curPlaneMode)) {
				axesGroup.rotateX(Math.PI / 2);
				axesGroup.position.copy(diy.clone());
				axesGroup.diyNormal = new THREE.Vector3(0, 0, 1);

			} else if ([PlaneModeEnum.planeXZ, PlaneModeEnum.planeDiyXZ].includes(this.curPlaneMode)) {
				axesGroup.rotateZ(Math.PI);
				axesGroup.rotateY(Math.PI);
				axesGroup.position.copy(diy.clone());
				axesGroup.diyNormal = new THREE.Vector3(0, 1, 0);

			} else if ([PlaneModeEnum.planeYZ, PlaneModeEnum.planeDiyYZ].includes(this.curPlaneMode)) {
				axesGroup.rotateX(Math.PI / 2);
				axesGroup.rotateZ(Math.PI / 2);
				axesGroup.position.copy(diy.clone());
				axesGroup.diyNormal = new THREE.Vector3(1, 0, 0);

			} else if (this.curPlaneMode === PlaneModeEnum.curveTangentPlane) {
				axesGroup.lookAt(this.centerPoint.normal);
				axesGroup.rotateX(Math.PI / 2);
				axesGroup.position.copy(diy.clone());
				axesGroup.diyNormal = this.centerPoint.normal;

			} else if (this.curPlaneMode === PlaneModeEnum.curveNormalPlane) {
				axesGroup.lookAt(this.centerPoint.normal);
				axesGroup.rotateX(Math.PI / 2);
				axesGroup.position.copy(diy.clone());
				axesGroup.diyNormal = this.centerPoint.normal;
			}

			axesGroup.updateMatrix();
			axesGroup.updateMatrixWorld(true);
			return axesGroup;
		},

		/**
		 * 创建网格助手(网格平面)
		 * @param size
		 * @param divisions
		 * @param centerLineColor
		 * @param gridColor
		 */
		createGridHelper (
			size = 10,
			divisions = 10,
			centerLineColor = 0x444444,
			gridColor = 0x888888
		) {
			// size -- 网格的大小。默认值为 10。
			// divisions -- 网格中的分割数。默认值为 10。
			// centerLineColor -- 中心线的颜色。这可以是Color、十六进制值和 CSS-Color 名称。默认为 0x444444
			// gridColor -- 网格线的颜色。这可以是Color、十六进制值和 CSS-Color 名称。默认为 0x888888
			const gridHelper = new THREE.GridHelper(
				size,
				divisions,
				centerLineColor,
				gridColor
			);
			return gridHelper;
		},

		/**
		 * 创建网格平面坐标轴
		 * @param planeMode 参照PlaneModeEnum
		 * @param length 坐标boundingBox
		 */
		createPlaneAxes (planeMode = PlaneModeEnum.planeXY, length = this.modelLong) {
			const positions = [];
			const colors = [];
			const points = [
				new THREE.Vector3(0, 0, 0),
				new THREE.Vector3(length, 0, 0),
				new THREE.Vector3(0, 0, 0),
				new THREE.Vector3(0, 0, -length),
			];

			// x、y、z => 红、绿、蓝
			const redColor = new THREE.Color('red');
			const greenColor = new THREE.Color('green');
			const blueColor = new THREE.Color('blue');

			let axesUpColor, axesRightColor;
			if (planeMode.match(/XY/)) {
				axesUpColor = redColor;
				axesRightColor = greenColor;
			} else if (planeMode.match(/XZ/)) {
				axesUpColor = redColor;
				axesRightColor = blueColor;
			} else if (planeMode.match(/YZ/)) {
				axesUpColor = blueColor;
				axesRightColor = greenColor;
			}

			for (let i = 0; i < points.length; i++) {
				const point = points[i];
				positions.push(point.x, point.y, point.z);
				if (i < 2) {
					colors.push(axesUpColor.r, axesUpColor.g, axesUpColor.b);
				} else {
					colors.push(
						axesRightColor.r,
						axesRightColor.g,
						axesRightColor.b
					);
				}
			}

			const segmentsGeometry = new LineSegmentsGeometry();
			segmentsGeometry.setPositions(positions);
			segmentsGeometry.setColors(colors);

			const matLine = new LineMaterial({
				color: 0xffffff,
				linewidth: 4, // in world units with size attenuation, pixels otherwise
				// worldUnits: true,
				vertexColors: true,

				// resolution:  // to be set by renderer, eventually
				alphaToCoverage: true,
			});
			matLine.resolution.set(window.innerWidth, window.innerHeight);

			const segments = new LineSegments2(segmentsGeometry, matLine);
			segments.computeLineDistances();
			segments.scale.set(1, 1, 1);

			return segments;
		},

		/**
		 * 更新网格平面
		 */
		updateGridPlane (planeMode) {
			if (this.gridHelper) {
				this.removeGridPlane();
			}
			this.createGridPlane(planeMode);
		},

		/**
		 * 删除网格平面
		 */
		removeGridPlane () {
			if (this.gridHelper) {
				this.removeSceneObj(this.gridHelper);
				this.gridHelper = null;
				this.requestRender();
			}
		},

		/**
		 * 显示
		 */
		showGridPlane (flag) {
			this.gridHelper.visible = flag;
		},
	},
};
