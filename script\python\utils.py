#!/usr/bin/python3
# -*- coding: UTF-8 -*-
###################################
# 工具类
# Created by cjking on 2020/07/06.
###################################

# 不能空格和tab同时用
import os
import sys
import json
import logger
import asyncio
# import urllib3  # pip3 install -U urllib3
import platform
import subprocess
import propertiesUtil

logger = logger.Logger()

# config = propertiesUtil.Properties(f'{os.getcwd()}/script/python/config.properties').getProperties()
config = propertiesUtil.Properties('./script/python/config.properties').getProperties()
username = config.get('username')
password = config.get('password')


# 判断操作系统
def checkSystem():
	system = platform.system()
	# plat_tuple = platform.architecture()
	# plat_version = platform.platform()
	logger.info('system: %s' % system)
	return system


# 判断是否Windows系统
def isWin():
	return platform.system() == "Windows"


# 去除字符串前后空格
def trim(string):
	return string.strip()


# 去除字符串所有空格
def trimAll(string):
	return string.replace(' ', '').strip()


# 判断是否全部为数字
def isNumber(num):
	# 为True表示输入的所有字符都是数字，
	# 否则，不是全部为数字
	return num.isdigit()


def isString(args):
	return type(args) == str


def isInt(args):
	return type(args) == int


def isBoolean(args):
	return type(args) == bool


def isFloat(args):
	return type(args) == float


def isArray(args):
	return isinstance(args, list)


# 用于将一个字符串或数字转换为整型
def toNumber(args, base=None):
	# base: 进制数，默认十进制
	if base is None:
		base = 10
	args = str(args)
	return int(args, base=base) if args.isalnum() else args


# list过滤
def listFilter(func, my_list):
	tmp_list = filter(func, my_list)
	new_list = list(tmp_list)
	return new_list


# 判断是否为空
def isEmpty(args):
	# 判断是否为None
	# None的特殊之处在于，
	# 它既不是数值0，也不是某个数据结构的空值，
	# 它本身就是一个空值对象。它的类型是NoneType
	if args is None:
		return True

	# 判断字符串是否为空
	elif isinstance(args, str) and len(args) == 0:
		return True

	# 判断元组(tuple)是否为空
	elif isinstance(args, tuple) and not args:
		return True

	# 判断数组(list)是否为空
	elif isinstance(args, list) and not args:
		return True

	# 判断对象(dict)是否为空
	elif isinstance(args, dict) and not args:
		return True

	else:
		return False


# 判断是否不为空
def notEmpty(args):
	if args is None:
		return False
	# 判断字符串是否不为空
	# 判断元组(tuple)是否不为空
	# 判断数组(list)是否不为空
	# 判断对象(dict)是否不为空
	elif (isinstance(args, str) or isinstance(args, tuple) or isinstance(args, list) or isinstance(args, dict)) and len(args) > 0:
		return True
	else:
		return False


# 获取最后一个元素
def getLastItem(array):
	return array[-1]


# 字符串是否包含某个子字符串
def includes(child_item, source):
	return child_item in source


# 删除元组元素
def removeTupleItem(tuple_data, item):
	# converting the tuple to list
	list_temp = list(tuple_data)
	# use different ways to remove an item of the list
	list_temp.remove(item)
	# converting the tuple to list
	tuple_data = tuple(list_temp)
	return tuple_data


# 获取json的值，通过key
def getJsonValue(json_data=None, key=""):
	if json_data is None:
		json_data = {}
	return json_data[key]


def runCmd(command):
	"""
	# Usage
	# runCmd(["dir", "/b"])  # 序列参数
	# runCmd(["ls -al"])
	# runCmd("exit 1")  # 字符串参数
	"""
	ret = subprocess.run(
		command,
		shell=True,
		encoding="utf-8",
		timeout=1)
	if ret.returncode == 0:
		print("success:", ret)
	else:
		print("error:", ret)


def popCmd(command, cwd=None, msg=""):
	"""
	# Usage
	popCmd(["pwd"])
	"""

	# 常用编码
	GBK = 'gbk'
	UTF8 = 'utf-8'

	# 解码方式
	current_encoding = GBK if isWin() else UTF8

	if cwd is None:
		cwd = "."  # 默认当前目录
	ret = subprocess.Popen(
		command,
		shell=True,
		cwd=cwd,
		stderr=subprocess.PIPE,
		stdout=subprocess.PIPE,
		encoding=current_encoding)
	out, err = ret.communicate()  # Read data from stdout and stderr
	if ret.returncode == 0:
		logger.info("popCmd success: %s" % (out or msg))
	else:
		logger.error("popCmd error: %s" % err)


# 实时输出
def quickCmd(command, cwd=None):
	# 常用编码
	GBK = 'gbk'
	UTF8 = 'utf-8'

	# 解码方式
	current_encoding = GBK if isWin() else UTF8

	if cwd is None:
		cwd = "."  # 默认当前目录

	poPen = subprocess.Popen(
		command,
		cwd=cwd,
		shell=True,
		stdout=subprocess.PIPE,
		stderr=subprocess.PIPE
	)

	# 重定向标准输出
	while poPen.poll() is None:  # None表示正在执行中
		r = poPen.stdout.readline().decode(encoding=current_encoding, errors='ignore')
		sys.stdout.write(r)  # 可修改输出方式，比如控制台、文件等

	# 重定向错误输出
	if poPen.poll() != 0:  # 不为0表示执行错误
		err = poPen.stderr.read().decode(encoding=current_encoding, errors='ignore')
		sys.stdout.write(err)  # 可修改输出方式，比如控制台、文件等


# 是否确认覆盖
def confirm(msg, callback, select=None):
	chooseList = ['是', '否']
	if chooseList is not None:
		chooseList = select
	for i, chooseStr in enumerate(chooseList):
		logger.choose("%s）%s" % (i + 1, chooseStr))
	while True:
		index = input("%s" % msg)
		if index.isdigit():
			index = int(index)
			if 0 < index <= len(chooseList):
				callback(index)
				break
		else:
			pass


# 获取单选框
def chooseHandle(future, pleaseSelectMsg, defaultValue=None):
	try:
		while True:
			selectIndex = input(pleaseSelectMsg)
			if selectIndex.isdigit():
				selectIndex = int(selectIndex)
				future.set_result(selectIndex)
				break
			else:
				# pass
				if defaultValue is None:
					pass
				else:
					future.set_result(0)
					break
	except KeyboardInterrupt:
		print('退出程序')
		exit(0)


# 异步获取返回值
async def getAsyncValue(callback, pleaseSelectMsg: str, defaultValue):
	loop = asyncio.get_event_loop()
	future = loop.create_future()
	response = loop.call_soon(callback, future, pleaseSelectMsg, defaultValue)
	try:
		return await future
	finally:
		response.cancel()


def forEach(data_list, callback):
	# 列表解析
	[callback(i, item) for i, item in enumerate(data_list)]


# 打印select下拉选项
def forInHandle(i: int, name: str):
	logger.choose("%s）%s" % (i + 1, name))


async def getSelectValue(data_list, pleaseSelectMsg, defaultValue=None):
	defaultShow = '' if isEmpty(defaultValue) else '(' + defaultValue + ')'

	# 列表解析
	forEach(data_list, forInHandle)

	index = await getAsyncValue(chooseHandle, str(pleaseSelectMsg) + defaultShow + '：', defaultValue)
	if 0 <= index <= len(data_list):
		return index


def setTimeout(delay, callback):
	loop = asyncio.get_event_loop()

	def endHandle(in_loop):
		callback()
		in_loop.stop()

	loop.call_at(delay, endHandle, loop)  # 指定时间运行, 时间是协程内部的时间，相当于js setTimeout
	loop.run_forever()


# json美化输出
def jsonFormatPrint(json_data, indent=None):
	if indent is None:
		indent = 4
	json_data = json.dumps(json_data, indent=indent)
	logger.info(json_data)


# 版本号排序
def versionSort(versions, order='desc'):
	tmp_dict = dict()
	for vs in versions:
		vs = vs.replace('v', '')
		x, y, z = str(vs).split('.')
		tmp_dict[int(x) * 10 ** 4 + int(y) * 10 ** 2 + int(z)] = 'v' + vs

	# 通过list将字典中的keys和values转化为列表
	keys = list(tmp_dict.keys())
	# values = list(tmp_dict.values())
	if order == 'asc':
		keys.sort()
	else:
		keys.sort(reverse=True)
	vsList = []
	for key in keys:
		vsList.append(tmp_dict[key])
	return vsList


# # 查询镜像
# def httpTest():
# 	http = urllib3.PoolManager()
# 	# 发起一个GET请求并且获取请求的响应结果
# 	url = 'http://httpbin.org/robots.txt'
# 	ret = http.request('GET', url, headers={'Content-Type': 'application/json'})
# 	# 输出响应的数据
# 	print('res: ', ret.data)


# 处理放回结果
def handleResponse(result, key=None):
	if isEmpty(result):
		return []
	if len(result) > 0 and includes('404', result[0]):
		return []
	result = json.loads(result[0])
	if key is None:
		return result
	if key in result:
		return result[key]
	else:
		return []


# 查询镜像
def getRepositories(env, like):
	def handle(item):
		if like is None:
			return item
		if includes(like, item):
			return item

	netrc = config.get('config_out_network') if env == 'prod' else config.get('config_in_network')
	basePath = config.get('api_out_network') if env == 'prod' else config.get('api_in_network')
	# result = os.popen('curl --user %s:%s -X GET -s --netrc-file ~/%s %s/v2/_catalog' % (username, password, netrc, basePath)).readlines()
	result = os.popen('curl -X GET -s --netrc-file ~/%s %s/v2/_catalog' % (netrc, basePath)).readlines()
	logger.choose("getRepositories result: %s" % result)
	res_list = handleResponse(result, 'repositories')
	new_list = listFilter(handle, res_list)
	return new_list


# 查询镜像tag
def getTagList(job_name, env):
	netrc = config.get('config_out_network') if env == 'prod' else config.get('config_in_network')
	basePath = config.get('api_out_network') if env == 'prod' else config.get('api_in_network')
	logger.choose('curl -X GET -s --netrc-file ~/%s %s/v2/%s/tags/list' % (netrc, basePath, job_name))
	result = os.popen('curl -X GET -s --netrc-file ~/%s %s/v2/%s/tags/list' % (netrc, basePath, job_name)).readlines()
	logger.choose('查询镜像tag result: %s' % (result))
	tagList = handleResponse(result, 'tags')

	# 判断是否为数组
	if isinstance(tagList, list) is True:
		tagList = versionSort(tagList)
	return tagList


# 查询镜像digest_hash
def getTagDigest(job_name, tag, env):
	netrc = config.get('config_out_network') if env == 'prod' else config.get('config_in_network')
	basePath = config.get('api_out_network') if env == 'prod' else config.get('api_in_network')
	# result = os.popen('curl --header "Accept:application/vnd.docker.distribution.manifest.v2+json" --user %s:%s -s -I -X GET --netrc-file ~/%s %s/v2/%s/manifests/%s' % (username, password, netrc, basePath, job_name, tag)).readlines()
	result = os.popen('curl --header "Accept:application/vnd.docker.distribution.manifest.v2+json" -s -I -X GET --netrc-file ~/%s %s/v2/%s/manifests/%s' % (netrc, basePath, job_name, tag)).readlines()
	if len(result) == 1:
		result = json.loads(result[0])
		return result['errors'][0]['message']
	index = 0
	for i, item in enumerate(result):
		if includes('Docker-Content-Digest', item):
			index = i
			break
	return trim(result[index]).replace('Docker-Content-Digest: ', '').replace('"', '')


# 删除私有库镜像
def delTagByDigest(repository, tag_digest, env):
	netrc = config.get('config_out_network') if env == 'prod' else config.get('config_in_network')
	basePath = config.get('api_out_network') if env == 'prod' else config.get('api_in_network')
	# command = 'curl --user %s:%s -s -I -X DELETE --netrc-file ~/%s %s/v2/%s/manifests/%s' % (username, password, netrc, basePath, repository, tag_digest)
	command = 'curl -s -I -X DELETE --netrc-file ~/%s %s/v2/%s/manifests/%s' % (netrc, basePath, repository, tag_digest)
	result = os.popen(command).readlines()
	return trim(result[0])
