#!/usr/bin/python3
# -*- coding: UTF-8 -*-
###################################
# docker编译、推送
# Created by cjking on 2020/07/06.
###################################
import os
import utils
import logger
import version
import propertiesUtil
from server import getServer, getInsideIP

logger = logger.Logger()
config = propertiesUtil.Properties(f'{os.getcwd()}/script/python/config.properties').getProperties()


async def startBuild(env, g_server=None, g_clean=None, g_intranet=None, g_static_server=None):
	try:
		# 1、获取库名称、tag版本
		repository_name = config.get('repository_name')

		# 清理dist
		if g_clean is True:
			msg = '(包含编译缓存)'
			clean_command = 'rmdir /s/q dist && rmdir /s/q node_modules/.cache' if utils.isWin() else 'rm -rf dist && rm -rf node_modules/.cache'
		else:
			msg = ''
			clean_command = 'rmdir /s/q dist' if utils.isWin() else 'rm -rf dist'
		if os.path.exists('dist'):
			utils.popCmd(clean_command, None, '清理dist目录%s成功!' % msg)

		# 服务器地址
		server = g_server
		if server is None:
			server = await getServer(env)
		else:
			logger.choose("您选择的服务器地址是: %s" % server, "blue")

		repository_name = repository_name if env == 'prod' else ''.join([repository_name, '-', env])
		logger.choose('仓库名称: %s' % repository_name)
		# 获取tag版本
		tag_list = utils.getTagList(repository_name, env)
		if utils.isEmpty(tag_list):
			last_version = "v0.0.0"
		else:
			last_version = tag_list[0]
		logger.choose('仓库最大开发版本号: %s' % last_version)
		# 获取下一个新版本号
		new_version = version.increment_version(last_version)
		logger.choose('新开发版本号: %s' % new_version)

		dockerBuild(repository_name, new_version, server, env, g_intranet, g_static_server)
	except KeyboardInterrupt:
		print('退出程序')
		exit(0)


def nextStep(repository, my_version, env):
	try:
		if not os.path.exists('dist'):
			logger.info("开始项目编译...")
			utils.quickCmd('npm run build:%s' % env)
			logger.info("项目编译成功。")

		basePath = config.get('api_out_network') if env == 'prod' else config.get('api_in_network')
		registry_url = basePath.replace('https://', '')

		logger.info("开始docker编译...")
		docker_build_cmd = 'docker build -f Dockerfile -t %s:%s .' % (repository, my_version)
		logger.info(docker_build_cmd)
		utils.quickCmd(docker_build_cmd)
		logger.info("docker编译成功。")

		# 删除临时文件
		logger.info("删除临时文件...")
		if utils.isWin():
			utils.popCmd('del /F nginx_temp.conf')
		else:
			utils.popCmd('rm -rf nginx_temp.conf')
		logger.info("删除临时文件成功。")

		# 根据镜像名字或者ID创建标签，缺省为latest
		logger.info("docker tag 打包中...")
		utils.quickCmd('docker tag %s:%s %s/%s:%s' % (repository, my_version, registry_url, repository, my_version))
		logger.info("docker tag 打包成功。")

		logger.info("docker开始推送至仓库...")
		utils.quickCmd('docker push %s/%s:%s' % (registry_url, repository, my_version))
		logger.info("docker推送至仓库成功。")

		# 获取最新tag版本列表
		tagList = utils.getTagList(repository, env)
		logger.info('tagList: %s' % tagList)
	except KeyboardInterrupt:
		print('退出程序')
		exit(0)


# docker编译
def dockerBuild(repository, my_version, server, env, g_intranet, g_static_server):
	logger.info('repository: %s' % repository)
	logger.info('version: %s' % my_version)
	logger.info('server: %s' % server)
	logger.info('env: %s' % env)
	logger.info('intranet: %s' % g_intranet)
	logger.info('static server: %s' % g_static_server)

	try:
		clean_command = 'del /F nginx_temp.conf && copy nginx.conf nginx_temp.conf' if utils.isWin() else 'rm -rf nginx_temp.conf && cp nginx.conf nginx_temp.conf'
		utils.popCmd(clean_command)

		if utils.checkSystem() == 'Darwin':
			i = '-i ""'
		else:
			i = '-i'

		intranet = g_intranet if g_intranet is not None else getInsideIP(server)
		logger.choose("您选择的内网IP是: %s" % intranet, "blue")
		if utils.isWin():
			utils.popCmd('C:\"Program Files"\\Git\\usr\\bin\\sed.exe %s "s/server localhost/server %s/g" nginx_temp.conf' % (i, intranet))
		else:
			utils.popCmd('sed %s "s/API_INTRANET_IP/%s/g" nginx_temp.conf' % (i, intranet))
			if utils.isEmpty(g_static_server):
				g_static_server = intranet
			utils.popCmd('sed %s "s/STATIC_SERVER_IP/%s/g" nginx_temp.conf' % (i, g_static_server))

		# 执行下一步操作
		nextStep(repository, my_version, env)
	except KeyboardInterrupt:
		print('退出程序')
		exit(0)
