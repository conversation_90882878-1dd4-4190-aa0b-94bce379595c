@import "./fonts.less";
@import "./mixins/variables.less";

body {
	overflow: auto;
}

body.no-scroll {
	overflow: hidden !important;
}

body.overflow-auto {
	overflow: auto !important;

	/* 滚动条优化 start */
	::-webkit-scrollbar {
		width: 4px;
		height: 4px;
	}

	::-webkit-scrollbar-track {
		background: #F6F6F6;
		border-radius: 1px;
	}

	::-webkit-scrollbar-thumb {
		background: #CDCDCD;
		border-radius: 1px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: #747474;
	}

	::-webkit-scrollbar-corner {
		background: #F6F6F6;
	}
	/* 滚动条优化 end */
}

/* 阻止选中 */
.no-select {
	-ms-user-select: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
}

.ant-layout {
    background: white !important;
}

.ant-modal {
	z-index: @z-index-modal;
}

.ant-modal-mask {
	z-index: @z-index-modal-mask;
}

.ant-modal-wrap {
	z-index: @z-index-modal-wrap;
}

.ant-spin-nested-loading > div > .ant-spin {
	z-index: @z-index-loading;
	height: 100%;
	min-height: 100vh;
	max-height: 100vh;
	position: absolute;
	top: 50%;
}

.text-left {
	text-align: left;
}

.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}

@-webkit-keyframes spin {
	from {-webkit-transform: rotate(0deg);}
	to {-webkit-transform: rotate(360deg);}
}

.spin {
	-webkit-transform: rotate(360deg);
	animation: spin 1.5s linear infinite;
	-moz-animation: spin 1.5s linear infinite;
	-webkit-animation: spin 1.5s linear infinite;
	-o-animation: spin 1.5s linear infinite;
}
