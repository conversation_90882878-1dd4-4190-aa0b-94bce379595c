# Docker操作指南

docker启动

```shell script
service docker start
# 其他命令
service docker [stop|status]
```

docker登陆

```shell script
# Windows下:
docker logout https://docker-registry.fmock.cn && winpty docker login https://docker-registry.fmock.cn

# Mac Or Linux下:
docker logout https://docker-registry.fmock.cn && docker login https://docker-registry.fmock.cn

# 账号: admin
# 密码: 123456
```

### docker重装

```shell
# 卸载旧版本
yum remove docker \
  docker-client \
  docker-client-latest \
  docker-common \
  docker-latest \
  docker-latest-logrotate \
  docker-logrotate \
  docker-engine

yum remove docker docker-common docker-selinux docker-engine
```

```shell
# 安装docker
yum -y install docker
```

```shell
# CentOS 7.x
# 启动docker
systemctl start docker

# 查看docker状态
systemctl status docker

# Ubuntu
# Step 5: 开启Docker服务
service docker start

# 查看docker状态
service docker status
```

### docker批量删除含有同样名字的images

```shell
docker rmi -f  $(docker images | grep fzx-backend-* | awk '{print $3}')
```

### 停止、删除所有的docker容器和镜像

```shell
# 列出所有的容器 ID
docker ps -aq

# 停止所有的容器
docker stop $(docker ps -aq)

# 删除所有的容器
docker rm $(docker ps -aq)

# 删除所有的镜像
docker rmi $(docker images -q)

# 复制文件
docker cp mycontainer:/opt/file.txt /opt/local/
docker cp /opt/local/file.txt mycontainer:/opt/
```

### docker 认证文件

- Windows下 请用Git Bash功能执行以下命令!
- Windows下 Git Bash中文乱码解决方案：  
  1、打开Git Bash；  
  2、在命令框内右键 -> options -> Text(左侧栏) -> locale选择zh_CN，Character set选择GBK  
  3、重启Git Bash窗口即可。

```shell script
vim ~/.netrc
machine docker-registry.fmock.cn login admin password 123456
cat ~/.netrc

vim ~/.netrc_prod
machine registry.fangzhenxiu.com login admin password 123456
cat ~/.netrc_prod
```
