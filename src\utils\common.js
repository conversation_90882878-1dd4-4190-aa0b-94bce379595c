function getSolidId (id) {
	return id.split('_')[1];
}

function removeSuffix (name) {
	return name.replace(/\.drc|\.obj|\.vtu|\.vtp/, '');
}

/**
 * 获取部件清单
 * @param modeNames
 * @return {*[]}
 */
export const getPartList = (modeNames = []) => {
	const partList = [];
	const getConditions = (name, type) => name.startsWith(type) && name.includes('_');

	const edgeNameList = modeNames.filter(name => getConditions(name, 'edge')).map(removeSuffix);
	const faceNameList = modeNames.filter(name => getConditions(name, 'face')).map(removeSuffix);
	const solidNameList = modeNames.filter(name => getConditions(name, 'solid')).map(removeSuffix);
	const shellNameList = modeNames.filter(name => getConditions(name, 'shell')).map(removeSuffix);

	if (edgeNameList.length) {
		partList.push(nameListHandler('Edge', '0-0', edgeNameList));
	}
	if (faceNameList.length) {
		partList.push(nameListHandler('Solid', '1-0', faceNameList));
	}
	if (solidNameList.length) {
		partList.push(nameListHandler('Parts', '2-0', solidNameList));
	}
	if (shellNameList.length) {
		partList.push(nameListHandler('Shells', '3-0', shellNameList));
	}
	return partList;
};

function nameListHandler (title, key, nameList) {
	if (nameList.length) {
		const item = {
			key: '0-0',
			title: title,
			showEye: true,
			visible: true,
			children: [],
		};
		nameList.forEach((name, index) => {
			item.children.push({
				key: `${ key }-${ index + 1 }`,
				title: name,
				showEye: true,
				visible: true,
			});
		});
		return item;
	}
}

/**
 * 获取部件(solid)id列表
 * @param modeNames
 * @return {{solidIdList: *[], selectedItems: *[]}}
 */
export const getSolidIds = (modeNames) => {
	let solidIdList = [];
	const selectedItems = [];
	const getConditions = (name, type) => name.startsWith(type) && name.includes('_');

	const faceNameList = modeNames.filter(name => getConditions(name, 'face'));

	if (faceNameList.length) {
		faceNameList.forEach(faceName => {
			solidIdList.push(getSolidId(faceName));
			selectedItems.push({
				id: faceName,
				name: faceName,
				selected: true,
				visible: true,
			});
		});
		solidIdList = [...new Set(solidIdList)];
	}
	return { solidIdList, selectedItems };
};

/**
 * 通过实体 ID 获取所选项目
 * @param modeNames
 * @param solidIds
 */
export const getSelectedItemsBySolidIds = (modeNames, solidIds) => {
	const selectedItems = [];
	const getConditions = (name, type) => name.startsWith(type) && name.includes('_');
	const faceNameList = modeNames.filter(name => getConditions(name, 'face'));

	if (faceNameList.length) {
		faceNameList.forEach(faceName => {
			if (solidIds.some(solidId => solidId === getSolidId(faceName))) {
				selectedItems.push({
					id: faceName,
					name: faceName,
					selected: true,
					visible: true,
				});
			}
		});
	}
	return selectedItems;
};
