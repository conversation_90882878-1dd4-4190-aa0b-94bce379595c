[{"key": "alt+cmd+l", "command": "editor.action.formatDocument", "when": "editorHasDocumentFormattingProvider && editorHasDocumentFormattingProvider && editorTextFocus && !editorReadonly"}, {"key": "shift+alt+f", "command": "-editor.action.formatDocument", "when": "editorHasDocumentFormattingProvider && editorHasDocumentFormattingProvider && editorTextFocus && !editorReadonly"}, {"key": "alt+cmd+l", "command": "editor.action.formatDocument.none", "when": "editorTextFocus && !editorHasDocumentFormattingProvider && !editorHasDocumentFormattingProvider && !editorR<PERSON>only"}, {"key": "shift+alt+f", "command": "-editor.action.formatDocument.none", "when": "editorTextFocus && !editorHasDocumentFormattingProvider && !editorHasDocumentFormattingProvider && !editorR<PERSON>only"}, {"key": "cmd+left", "command": "workbench.action.navigateBack"}, {"key": "ctrl+-", "command": "-workbench.action.navigateBack"}, {"key": "cmd+right", "command": "workbench.action.navigateForward"}, {"key": "ctrl+shift+-", "command": "-workbench.action.navigateForward"}, {"key": "cmd+k cmd+/", "command": "-editor.foldAllBlockComments", "when": "editorTextFocus && foldingEnabled"}, {"key": "shift+cmd+/", "command": "editor.fold<PERSON>llBlockComments", "when": "editorTextFocus && !editorR<PERSON>only"}, {"key": "shift+cmd+down", "command": "-workbench.action.terminal.selectToNextCommand", "when": "terminalFocus"}, {"key": "shift+cmd+down", "command": "-cursorBottomSelect", "when": "textInputFocus"}, {"key": "ctrl+shift+o", "command": "workbench.view.debug"}, {"key": "shift+cmd+d", "command": "-workbench.view.debug"}, {"key": "shift+cmd+d", "command": "editor.action.copyLinesDownAction", "when": "editorTextFocus && !editorR<PERSON>only"}, {"key": "shift+alt+down", "command": "-editor.action.copyLinesDownAction", "when": "editorTextFocus && !editorR<PERSON>only"}]