<template>
	<a-modal
		:width="450"
		:mask="true"
		:maskClosable="false"
		title="修改密码"
		:visible="visible"
		@cancel="closeWindow"
	>
		<a-form-model ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
			<a-form-model-item label="旧密码" prop="oldPassword">
				<a-input type="password" v-model="form.oldPassword" placeholder="请输入旧密码" :max-length="30" />
			</a-form-model-item>

			<a-form-model-item label="新密码" prop="password">
				<a-input type="password" v-model="form.password" placeholder="请输入新密码" :max-length="30" />
			</a-form-model-item>

			<a-form-model-item label="确认密码" prop="confirmPassword">
				<a-input type="password" v-model="form.confirmPassword" placeholder="请输入确认密码" :max-length="30" />
			</a-form-model-item>
		</a-form-model>

		<template slot="footer">
			<a-button type="default" @click="closeWindow">取消</a-button>
			<a-button type="primary" @click="handleSubmit">确定</a-button>
		</template>
	</a-modal>
</template>

<script>
/* ===================================
 * 修改密码弹窗
 * Created by lzz on 2020/12/16.
 * Copyright 2020, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { mapGetters } from 'vuex';
import { updatePassword } from '@/api';
import { formValidate, PasswordReg } from '@/utils/validate';

export default {
	name: 'UpdatePwdModal',
	data () {
		return {
			visible: false,
			labelCol: { span: 6 },
			wrapperCol: { span: 16 },
			form: {
				oldPassword: '',        // 旧密码
				password: '',           // 新密码
				confirmPassword: '',    // 确认新密码
			},
			rules: {
				oldPassword: formValidate.noEmpty('请输入旧密码'),
				password: formValidate.customCondition({
					required: true,
					message: '请输入新密码，格式：长度为8~16位，数字、字母、特殊字符至少包含两种！',
					condition: () => PasswordReg.test(this.form.password),
				}),
				confirmPassword: formValidate.compare({
					required: true,
					message: '确认密码',
					lastValue: () => this.form.password,
				}),
			},
		};
	},
	computed: {
		...mapGetters(['userInfo']),
	},
	methods: {
		/**
		 * 显示弹窗
		 */
		showModal () {
			this.visible = true;
		},

		/**
		 * 确定
		 */
		handleSubmit () {
			this.$refs.ruleForm.validate(async valid => {
				if (!valid) return;
				try {
					const params = {
						username: this.userInfo.username,
						oldpassword: this.form.oldPassword,    // 旧密码
						password: this.form.password,       // 新密码
						confirmpassword: this.form.confirmPassword,       // 确认新密码
					};
					logger.log('修改密码参数 params: ', params);
					const res = await updatePassword(params);
					if (res?.success) {
						this.$message.success('修改密码成功');
						this.confirmAndCloseWin();
					}
				} catch (err) {
					logger.error('修改密码 err: ', err);
				}
			});
		},

		/**
		 * 关闭弹窗
		 */
		closeWindow () {
			this.$emit('close');
			this.$refs.ruleForm.clearValidate();
			this.visible = false;
		},

		/**
		 * 确认并关闭弹窗
		 */
		confirmAndCloseWin () {
			this.$emit('ok');
			this.$refs.ruleForm.clearValidate();
			this.visible = false;
		},
	},
};
</script>

<style lang="less" scoped>
</style>
