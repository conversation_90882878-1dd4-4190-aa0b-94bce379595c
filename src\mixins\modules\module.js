/* ===================================
 * 模块
 * Created by z<PERSON><PERSON><PERSON> on 2022/07/29.
 * Copyright 2022, Inc.
 * =================================== */
import { mapGetters } from 'vuex';
import dayjs from 'dayjs';

export const mixinModule = {
	data () {
		return {
			currentModule: '1', // 1:随行冷，2:纹理，3:定制鞋, 默认1
			moduleObject: {},
		};
	},
	computed: {
		...mapGetters(['moduleList']),
	},
	watch: {
		moduleList: {
			deep: true,
			immediate: true,
			handler (val) {
				this.setModuleObject(val, this.currentModule);
			},
		},
		currentModule (val) {
			this.setModuleObject(this.moduleList, val);
		},
	},
	methods: {
		setModuleObject (moduleList, currentModule) {
			this.currentModule = currentModule;
			const moduleItem = moduleList.find(i => i.moduleId === currentModule);
			if (!moduleItem) {
				this.moduleObject = {
					id: null,
					hasPackage: false,
					expired: true,
					zeroCount: true,
					canCreate: false,
					canDownload: false,
				};
				return;
			}
			if (moduleItem && !moduleItem?.serveEnd) {
				this.moduleObject = {
					id: moduleItem.moduleId,
					hasPackage: false,
					expired: true,
					zeroCount: true,
					canCreate: false,
					canDownload: false,
				};
				return;
			}
			let expired = false;
			const today = dayjs().format('YYYY-MM-DD');
			expired = !moduleItem?.serveEnd || !moduleItem?.serveBegin ||
				moduleItem?.serveEnd < today || moduleItem?.serveBegin > today;
			let zeroCount = false;
			if (moduleItem.downMethod === '0') {
				// 不限次
				zeroCount = false;
			} else if (moduleItem.downMethod === '1') {
				// 限次
				zeroCount = moduleItem.surplusDownCount <= 0;
			}
			this.moduleObject = {
				id: moduleItem.moduleId,
				hasPackage: !!moduleItem.packageId,
				expired,
				zeroCount,
				canCreate: !expired && !zeroCount,
				canDownload: !zeroCount,
			};
		},
	},
};
