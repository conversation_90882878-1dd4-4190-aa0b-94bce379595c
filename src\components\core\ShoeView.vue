<template>
	<RightMenu :visible="rightMenuVisible">
		<template #title>
			<div class="view-container" ref="viewContainer" @mouseleave="removeBoxSelectListen">
				<a-spin class="view-loading" size="large" :tip="tip" :spinning="loading" />
				<div
					class="js-view"
					ref="vtkContainer"
					:style="{ background: background, width: jsViewWidth }"
				>
					<!--<canvas id="mainCanvas" @contextmenu="onContextmenu"></canvas>-->
					<canvas id="mainCanvas"></canvas>
					<canvas id="arrowCanvas"></canvas>
					<div id="label"></div>
				</div>
				<div class="scale-plate">
					<CanvasRuler ref="canvasRulerRef" :parentRef="$refs.viewContainer" :long="long" v-if="rulerLoaded" />
				</div>
			</div>
		</template>
		<template #menu>
			<a-menu @click="({ key: type }) => onContextMenuClick(type)">
				<a-menu-item key="show">全部显示</a-menu-item>
				<a-menu-item key="hidden" :disabled="!contextMenuObject">隐藏</a-menu-item>
				<a-menu-item key="hiddenAllFace">隐藏所有面</a-menu-item>
				<a-menu-item key="hiddenWireframe">隐藏线框</a-menu-item>
			</a-menu>
		</template>
	</RightMenu>
</template>

<script>
/* ===================================
 * 客制化鞋视图渲染器
 * Updated by cjking on 2022/02/18.
 * Copyright 2022, Inc.
 * =================================== */
import { mapActions, mapGetters } from 'vuex';
import { logger } from '@/utils/logger';
import { SelectModeEnum, DrawEnum, ViewEnum, RenderModeEnum, CameraModeEnum, ColorEnum, RenderOrderEnum } from '@/constants';
import {
	hasOwnProperty, isEmpty, isString, pick, sleep, toExponential, randomNumber,
	throttle as utilsThrottle, isArray, addEventHandler, deepCopy, removeEventHandler, dirname, basename,
} from '@/utils/utils';

import * as THREE from 'three';
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { SelectionBox } from 'three/examples/jsm/interactive/SelectionBox.js';
import { SelectionHelper } from 'three/examples/jsm/interactive/SelectionHelper.js';
import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';
// import { EdgeSplitModifier } from 'three/examples/jsm/modifiers/EdgeSplitModifier.js';
// import * as BufferGeometryUtils from 'three/examples/jsm/utils/BufferGeometryUtils.js';
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils.js';

import {
	baseDecode, floatDecode,
	StatisticsUtils, getMaxVal,
	preventDefaults, throttle, toHexColor,
	getMinVal, delSuffix, commonMethodWithTimeHandler,
} from '@/components/core/ViewUtils.js';
import { loadFile } from '@/api';
import Config from '@/config/Config';
import RightMenu from '@/components/common/RightMenu';
import CanvasRuler from '@/components/core/CanvasRuler';
import { mixinRhino } from '@/mixins/modules/rhino';
import { mixinViewAxes } from '@/mixins/modules/viewAxes';
import { ControlTypeEnum, defaultData, mixinViewCommonFun } from '@/mixins/modules/viewCommonFun';

// 标记开关枚举
const TagSwitchEnum = {
	ON: 'ON',
	OFF: 'OFF',
};

const selectSwitch = TagSwitchEnum.OFF;

// -------------------------------------
// 此类属性不能放vue data 里，
// 不然严重影响性能问题，卡顿掉帧严重
let animationId;
let drawControl;
let raycasterObjs = []; // 鼠标悬停/点击高亮显示的 obj 列表
const mouse = new THREE.Vector2();
// -------------------------------------

let selectionBox, helper, boxAllSelected;
let delCallback, enterCallback, escCallback,
	ctrlSCallback, ctrlCallback, ctrlZCallback,
	dragCallback;

// 是否独立 Ctrl 键激活
let independentCtrlKeyActivate = false;

const onKeydown = (event) => {
	const oEvent = event || window.event;
	// 获取键盘的keyCode值
	const keyCode = oEvent.keyCode || oEvent.which || oEvent.charCode;
	// 获取ctrl键对应的事件属性
	const ctrlKeyCode = oEvent.ctrlKey || oEvent.metaKey; // mac metaKey

	independentCtrlKeyActivate = ctrlKeyCode && keyCode === 17;

	logger.log('ctrlKeyCode, keyCode: ', ctrlKeyCode, keyCode);

	if (keyCode === 8) { // 删除键(回退键)
		delCallback && delCallback(event);
	}
	if (keyCode === 13) { // Enter
		enterCallback && enterCallback(event);
	}
	if (keyCode === 27) { // Esc
		escCallback && escCallback(event);
	}

	if (ctrlKeyCode && keyCode === 83) { // ctrl+s
		ctrlSCallback && ctrlSCallback(event);
	}
	if (ctrlKeyCode && keyCode === 90) { // ctrl+z
		ctrlZCallback && ctrlZCallback(event);
	}

	if (ctrlKeyCode && ctrlCallback) {
		ctrlCallback(event, independentCtrlKeyActivate);
	}
};

export default {
	name: 'ShoeView',
	mixins: [mixinViewCommonFun, mixinViewAxes, mixinRhino],
	components: {
		RightMenu,
		CanvasRuler,
	},
	props: {
		hasCover: { // 是否有封面图(缩略图)
			type: Boolean,
			required: false,
			default: true,
		},
	},
	data () {
		return { ...defaultData };
	},
	watch: {
		collapsed () {
			this.resizeCurrentView();
		},
	},
	computed: {
		...mapGetters(['userInfo']),

		curCamera: {
			cache: false,
			get () {
				return this.getCurrCamera();
			},
		},
		curControl: {
			cache: false,
			get () {
				return this.getCurrControl();
			},
		},
		defaultColor () {
			return ColorEnum.origin;
		},
		selectColor () {
			return this.selectParam ? this.selectParam.config.color : ColorEnum.white;
		},
		basePath () {
			if (this.userInfo.orgCode) {
				return `${ Config.staticDomainURL }/${ this.userInfo.orgCode }/${ this.projectId }/${ this.taskId }`;
			}
			return `${ Config.staticDomainURL }/${ this.projectId }/${ this.taskId }`;
		},
		disperseStepPath () {
			return `${ this.basePath }/dispersed/step`;
		},
	},
	mounted () {
		this.initIds();
		this.initView();
	},
	methods: {
		...mapActions(['saveSolidNames']),

		initIds () {
			this.projectId = this.$route.params.id;
			this.taskId = this.$route.params.taskId;
		},

		/**
		 * 初始化视图
		 */
		async initView () {
			// this.initStats();
			this.initScene();
			this.initRenderer();
			this.initLight();
			this.initCamera();
			this.initAxes();
			this.initControls();
			this.updateRuler(true);
		},

		/**
		 * 加载场景数据
		 */
		async loadData (filePath) {
			this.initIds();
			this.openLoading();
			this.initTHREE();
			const data = await this.getData(filePath);
			this.parseData(data);
			await this.initData(data);
			this.initCamera(data);
			// this.initText();
			this.reInitControls();
			this.initEvents();
			this.initStatistics();
			this.updateRuler();
			this.closeLoading();
			this.animate();
			this.$nextTick(() => {
				this.updateOrientation(ViewEnum.default);
			});
		},

		/**
		 * 加载离散线数据
		 */
		loadLinesData (filePath) {
			commonMethodWithTimeHandler(async () => {
				if (!filePath.startsWith('/')) {
					filePath = '/' + filePath;
				}
				const faceName = delSuffix(basename(filePath), '.json');
				logger.log('加载线数据 faceName: ', faceName);

				this.openLoading();

				const line3dmUrl = `${ this.disperseStepPath }/${ faceName }/${ faceName }.3dm`;
				await this.load3DMLines(line3dmUrl, {
					message: '加载离散线数据',
					groupAttr: 'isDisperseLine',
					lineWidth: 2,
					allowSelect: true,
					groupName: faceName,
					visible: this.originVisible,
				});

				this.closeLoading();
				this.requestRender();
			});
		},

		/**
		 * 加载场景数据
		 */
		async loadDataV2 (result) {
			this.initIds();
			this.openLoading();
			let obj;
			if (result['solid']) {
				obj = await this.loadRhinoBase64('solid', result);
				raycasterObjs.push(...obj);
			}
			if (result['faces']) {
				obj = await this.loadRhinoBase64('faces', result);
				raycasterObjs.push(...obj);
			}
			if (result['lines']) {
				obj = await this.loadRhinoBase64('lines', result, { lineWidth: 2 });
				raycasterObjs.push(...obj);
			}
			if (result['other_curve_points']) {
				obj = await this.loadRhinoBase64('other_curve_points', result);
				raycasterObjs.push(...obj);
			}
			logger.log('raycasterObjs.length: ', raycasterObjs.length);
			if (raycasterObjs.length && result.boundingBox) {
				await this.initSceneByBoundingBox(result.relative_path + '/' + result.boundingBox);
			}

			this.updateOrientation(ViewEnum.default);

			// const canvas = document.querySelector('#mainCanvas');
			// this.threeJsUtils = new ThreeJsUtils(this.getScene(), canvas, this.getCurrCamera(), this.getRenderer(), this.getCurrControl());
			// this.threeJsUtils.initEvent();
			// logger.log('this.threeJsUtils: ', this.threeJsUtils);

			// 初始化缩略图
			this.initThumbnail();

			this.closeLoading();
		},

		/**
		 * 初始化缩略图
		 */
		initThumbnail () {
			if (raycasterObjs.length > 0 && !this.hasCover) {
				sleep(500, () => this.screenshot(this.taskId, true));
			}
		},

		/**
		 * 获取数据
		 */
		async getData (filePath) {
			if (!filePath.startsWith('/')) {
				filePath = '/' + filePath;
			}
			// const data = (await import('../../../public/models/json/data.json')).default;
			const data = await loadFile(Config.staticDomainURL + filePath, 'json', false);
			data.compressed = 'true';
			data.base = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!#$%&()*+-:;/=>?@[]^_,.{|}~`';
			data.baseFloat = ',.-0123456789';
			data.camera = {
				'type': 'Orthographic',
				// 'type': 'Perspective',
				'position_x': 0,
				'position_y': 0,
				'position_z': 200,
			};

			const { xMin, xMax, yMin, yMax, zMin, zMax } = data; // 获取显示的边界
			// 求对角线
			// 公式：l = a^2 + b^2 + c^2
			const a = Math.pow(Math.abs(xMax - xMin), 2);
			const b = Math.pow(Math.abs(yMax - yMin), 2);
			const c = Math.pow(Math.abs(zMax - zMin), 2);
			const modelLong = Math.sqrt(a + b + c);
			this.modelLong = modelLong;

			this.jsonData = data;

			return data;
		},

		/**
		 * 初始化THREE视图方向
		 */
		initTHREE (up = [0, 0, 1]) {
			// Z is up for FreeCAD
			// THREE.Object3D.DefaultUp = new THREE.Vector3(...up);

			this.curCamera.up.set(...up);
			this.curCamera.lookAt(0, 0, 0);
		},

		/**
		 * 初始化统计
		 */
		initStats () {
			this.stats = new Stats();
			document.body.appendChild(this.stats.dom);
		},

		/**
		 * 初始化场景
		 */
		initScene () {
			const scene = new THREE.Scene();
			const canvas = document.querySelector('#mainCanvas');
			this.setScene(scene);
			this.setCanvas(canvas);
		},

		/**
		 * 初始化渲染场景
		 */
		initRenderer () {
			const renderer = new THREE.WebGLRenderer({
				alpha: true,
				antialias: true,
				canvas: this.getCanvas(),
				sortObjects: true,
			});
			// Clear bg so we can set it with css
			renderer.setClearColor(ColorEnum.black, 0);
			this.setRenderer(renderer);
		},

		/**
		 * 初始化相机
		 */
		initCamera (data) {
			data = data || this.jsonData;
			// Get bounds for global clipping
			const globalMaxMin = [
				{ min: null, max: null },
				{ min: null, max: null },
				{ min: null, max: null },
			];
			if (data?.objects?.length) {
				for (const obj of data.objects) {
					for (let v = 0; v < obj.verts.length; v++) {
						if (isEmpty(globalMaxMin[v % 3]) || obj.verts[v] < globalMaxMin[v % 3].min) {
							globalMaxMin[v % 3].min = obj.verts[v];
						}
						if (isEmpty(globalMaxMin[v % 3]) || obj.verts[v] > globalMaxMin[v % 3].max) {
							globalMaxMin[v % 3].max = obj.verts[v];
						}
					}
				}
			}
			let bigRange = 0;
			// add a little extra
			for (const i of globalMaxMin) {
				const range = i.max - i.min;
				if (range > bigRange) {
					bigRange = range;
				}
				i.min -= range * 0.01;
				i.max += range * 0.01;
			}
			const camCenter = new THREE.Vector3(
				0.5 * (globalMaxMin[0].max - globalMaxMin[0].min) + globalMaxMin[0].min,
				0.5 * (globalMaxMin[1].max - globalMaxMin[1].min) + globalMaxMin[1].min,
				0.5 * (globalMaxMin[2].max - globalMaxMin[2].min) + globalMaxMin[2].min,
			);

			const viewSize = 1.5 * bigRange; // make the view area a little bigger than the object
			this.viewSize = viewSize;
			const canvas = this.getCanvas();
			const aspectRatio = canvas.clientWidth / canvas.clientHeight;
			this.originalAspect = aspectRatio;

			this.globalMaxMin = globalMaxMin;
			let cameraType = data?.camera?.type ?? 'Orthographic';
			if (this.zoomSpeed === 0) {
				cameraType = 'Perspective';
			}
			this.setCameraType(cameraType);

			const initCameraPosition = (camera) => {
				camera.position.set(0, 0, 200);
				camera.lookAt(camCenter);
				camera.updateMatrixWorld();

				this.camToSave[cameraType].position = camera.position.clone();
				this.camToSave[cameraType].rotation = camera.rotation.clone();
			};

			const perspectiveCamera = new THREE.PerspectiveCamera(50, aspectRatio, 1, this.far);
			initCameraPosition(perspectiveCamera);
			this.setPerspectiveCamera(perspectiveCamera);

			const orthographicCamera = new THREE.OrthographicCamera(-aspectRatio * viewSize / 2, aspectRatio * viewSize / 2, viewSize / 2, -viewSize / 2, -this.far, this.far);
			initCameraPosition(orthographicCamera);
			this.setOrthographicCamera(orthographicCamera);
		},

		/**
		 * 获取中心
		 */
		getCenter (openBoxHelper = false, drawCenterPoint = false) {
			// 在3D空间中表示一个盒子或立方体。其主要用于表示物体在世界坐标中的边界框
			const box = new THREE.Box3();
			// 通过传入的object3D对象来返回当前模型的最小大小，值可以使一个mesh也可以使group
			const group = new THREE.Group();
			group.isFace = true; // 表示面
			group.canDelete = true;
			raycasterObjs.forEach(mesh => mesh.isFace && group.add(mesh));
			this.getScene().add(group);
			box.expandByObject(group);

			// 返回包围盒的宽度，高度，和深度
			const boxSize = box.getSize(new THREE.Vector3());

			// box辅助框，测试完毕可删除
			if (openBoxHelper) {
				const boxHelper = new THREE.BoxHelper(group, 0xffff00);
				this.getScene().add(boxHelper);
				boxHelper.geometry.computeBoundingBox(); // 绑定盒子模型
			}

			// 返回包围盒的中心点
			const center = box.getCenter(new THREE.Vector3());
			this.camCenter = center.clone();
			this.setCameraCenter(center.clone());
			if (drawCenterPoint) {
				this.drawSphere(this.camCenter, null, 2);
			}

			return { center, boxSize, group };
		},

		/**
		 * 分配网格
		 */
		assignMesh (positions, color, opacity, faces, name, faceName = undefined, isLackFace = false) {

			const hasNaN = positions.some(x => isNaN(x));
			if (hasNaN) {
				this.$error({ content: `模型面【${ faceName }】存在异常，请修复后重新上传！` });
			}

			color = isLackFace ? ColorEnum.red : color;
			const material = this.getMeshMaterial(color, opacity);
			const mesh = this.createMeshByPosition(positions, material);
			mesh.name = mesh.uuid;
			mesh.solidGroup = name;
			mesh.nickname = faceName;
			mesh.isFace = true;
			mesh.canDelete = true; // 是否可以删除
			mesh.isLackFace = isLackFace; // 是否坏面(缺失)
			mesh.renderOrder = RenderOrderEnum.face;
			faces.push(mesh.uuid);

			if (!isLackFace) raycasterObjs.push(mesh);
			else {
				this.getScene().add(mesh);
			}

			const list = mesh.geometry.attributes.position.array;
			for (let i = 2; i <= list.length; i += 3) {
				this.zList.push(list[i]);
			}
			if (!this.solidNames.includes(name)) {
				this.solidNames.push(name);
			}

		},

		/**
		 * 获取网格材质
		 */
		getMeshMaterial (color, opacity) {
			let depthWrite = false;
			let transparent = false;
			if (opacity !== 1.0) {
				transparent = true;
			} else {
				depthWrite = true;
			}
			const material = new THREE.MeshLambertMaterial({
				color: new THREE.Color(color),
				emissive: new THREE.Color(0x333333),
				side: THREE.DoubleSide,
				vertexColors: false,
				flatShading: false,
				opacity: opacity,
				depthWrite: depthWrite,
				transparent: transparent,
				fog: false,
			});
			return material;
		},

		/**
		 * 按 positions 创建网格
		 */
		createMeshByPosition (positions, material) {
			let baseGeometry = new THREE.BufferGeometry();
			baseGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
			baseGeometry = BufferGeometryUtils.mergeVertices(baseGeometry);
			baseGeometry.computeVertexNormals();
			baseGeometry.computeBoundingSphere();

			// // ==================================
			// // 修复表面光滑度，有缺陷，中心点有偏移!!!
			// // EdgeSplitModifier is used to combine verts so that smoothing normals can be generated WITHOUT removing the hard edges of the design
			// // REF: https://threejs.org/examples/?q=edge#webgl_modifier_edgesplit - https://github.com/mrdoob/three.js/pull/20535
			// const edgeSplit = new EdgeSplitModifier();
			// const cutOffAngle = 180;
			// baseGeometry = edgeSplit.modify(baseGeometry, cutOffAngle * Math.PI / 180);
			// baseGeometry.computeVertexNormals();
			// baseGeometry.computeBoundingSphere();
			// // ==================================

			const mesh = new THREE.Mesh(baseGeometry, material);
			return mesh;
		},

		/**
		 * 初始化数据
		 */
		async initData (data) {
			return new Promise(async (resolve) => {
				this.solidNames = [];
				if (data?.objects?.length) {
					for (const obj of data.objects) {
						// Each face gets its own material because they each can
						// have different colors
						const faces = [];
						if (obj.facesToFacets.length > 0) {
							for (let f = 0; f < obj.facesToFacets.length; f++) {
								const faceColor = obj.faceColors.length > 0 ? obj.faceColors[f] : obj.color;
								const positions = new Float32Array(obj.facesToFacets[f].length * 9);
								for (let a = 0; a < obj.facesToFacets[f].length; a++) {
									for (let b = 0; b < 3; b++) {
										for (let c = 0; c < 3; c++) {
											positions[9 * a + 3 * b + c] = obj.verts[3 * obj.facets[3 * obj.facesToFacets[f][a] + b] + c];
										}
									}
								}
								this.assignMesh(positions, faceColor, obj.opacity, faces, obj.name, obj.faceNames[f], obj.lackFaceNames.includes(obj.faceNames[f]));
							}
						} else {
							// No facesToFacets means that there was a tessellate()
							// mismatch inside FreeCAD. Use all facets in object to
							// create this mesh
							if (obj.facets.length) {
								const positions = new Float32Array(obj.facets.length * 3);
								for (let a = 0; a < obj.facets.length; a++) {
									for (let b = 0; b < 3; b++) {
										positions[3 * a + b] = obj.verts[3 * obj.facets[a] + b];
									}
								}
								this.assignMesh(positions, obj.color, obj.opacity, faces, obj.name);
							}
						}
					}

					// 加载线框
					logger.log('----- 加载线框 wireframeGroup ------');
					const filePath = `${ this.basePath }/dispersed/step/wire.3dm`;
					const children = await this.load3DM(filePath, { needChildren: true });
					if (children) {
						const group = new THREE.Group();
						group.isWireframe = true;
						group.visible = this.originVisible;
						children.forEach((mesh) => {
							if (mesh instanceof THREE.Line) {
								const { line } = this.makeLine(mesh.geometry.attributes.position.array, 'wireframe', mesh.name || mesh.uuid, RenderOrderEnum.line, 1);
								group.add(line);
							}
						});
						this.getScene().add(group);
						this.requestRender();
					}

					this.getCenter(false, false);

					this.initMinMaxZ();

					this.saveSolidNames(this.solidNames);

					resolve();
				}
			});
		},

		/**
		 * 初始化最小最大 Z
		 */
		initMinMaxZ () {
			this.minZ = getMinVal(this.zList);
			this.maxZ = getMaxVal(this.zList);
			this.lenZ = Math.abs(this.maxZ - this.minZ);
			this.zList.length = 0;
			logger.log('this.maxZ: ', this.maxZ);
			logger.log('this.lenZ: ', this.lenZ);
		},

		/**
		 * 制作线
		 */
		makeLine (wire, groupName = undefined, nickname = undefined, renderOrder = RenderOrderEnum.line, lineWidth = 1) {
			const result = { line: null, material: null };
			if (!wire?.length) return result;
			const wireGeometry = new LineGeometry();
			wireGeometry.setPositions(wire);
			const material = this.getLineMaterial(ColorEnum.black, lineWidth);
			material.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
			const line = new Line2(wireGeometry, material);
			line.computeLineDistances();
			line.scale.set(1, 1, 1);
			line.name = line.uuid;
			line.isEdge = true; // 是边缘(轮廓)线
			line.canDelete = true;
			line.nickname = nickname;
			line.groupName = groupName;
			line.renderOrder = renderOrder;

			result.line = line;
			result.material = material;
			return result;
		},

		/**
		 * 制作管道
		 */
		makeTube (points) {
			const geometry = new THREE.TubeGeometry(new THREE.CatmullRomCurve3(points), 100, 1, 20, false);
			const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
			const mesh = new THREE.Mesh(geometry, material);
			this.getScene().add(mesh);
		},

		/**
		 * 初始化光
		 * HemisphereLight gives different colors of light from the top
		 * and bottom simulating reflected light from the 'ground' and
		 * 'sky'
		 */
		initLight () {
			const scene = this.getScene();

			// 从顶部和底部发出不同颜色的光，模拟来自“地面”和“天空”的反射光
			scene.add(new THREE.HemisphereLight(0xC7E8FF, 0xFFE3B3, 0.4));

			const light1 = new THREE.DirectionalLight(ColorEnum.white, 0.4);
			light1.position.set(5, -2, 3);
			scene.add(light1);

			const light2 = new THREE.DirectionalLight(ColorEnum.white, 0.4);
			light2.position.set(-5, 2, 3);
			scene.add(light2);
		},

		/**
		 * 导航变化(视图切换)
		 */
		navChange (v) {
			if (!this.initPosition) {
				this.initPosition = new THREE.Vector3();
				new THREE.Box3().setFromObject(this.getScene()).getSize(this.initPosition);
			}
			const t = this.initPosition;

			const vector = new THREE.Vector3(
				v[0] * t.x * 2 + (this.camCenter?.x || 0),
				v[1] * t.y * 2 + (this.camCenter?.y || 0),
				v[2] * t.z * 2 + (this.camCenter?.z || 0),
			);

			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			if (perspectiveControls) {
				perspectiveControls.object?.position.copy(vector);
				perspectiveControls.target = this.camCenter.clone();
				perspectiveControls.update();
			}

			if (orthographicControls) {
				orthographicControls.object.position.copy(vector);
				orthographicControls.target = this.camCenter.clone();
				orthographicControls.update();
			}
		},

		/**
		 * 更改渲染模式
		 * @param mode
		 */
		changeRenderMode (mode) {
			this.curRenderMode = mode;
			// 面和体的材质变化
			if (this.curRenderMode === RenderModeEnum.surfaceAndWireframe) {
				// 体 + 线框模式
				this.setFaceAndEdgeVisible(true);
			} else if (this.curRenderMode === RenderModeEnum.surface) {
				// 体
				this.setFaceVisible(true);
				this.setEdgeVisible(false);
			} else if (this.curRenderMode === RenderModeEnum.wireframe) {
				// 线框模式
				this.setFaceVisible(false);
				this.setEdgeVisible(true);
			}
		},

		/**
		 * 更新剪裁
		 * @params clip
		 * @params clippingReverse 反转方向
		 */
		updateClipping (clip, clippingReverse) {
			const renderer = this.getRenderer();
			if (!renderer) {
				return;
			}
			if (clip.clippingX <= 100 || clip.clippingY <= 100 || clip.clippingZ <= 100) {
				if (!renderer.clippingPlanes) renderer.clippingPlanes = [];
				if (renderer.clippingPlanes.length === 0) {
					this.clipPlaneX = new THREE.Plane(new THREE.Vector3(+clip.xx, +clip.xy, +clip.xz), 0);
					this.clipPlaneY = new THREE.Plane(new THREE.Vector3(+clip.yx, +clip.yy, +clip.yz), 0);
					this.clipPlaneZ = new THREE.Plane(new THREE.Vector3(+clip.zx, +clip.zy, +clip.zz), 0);
					renderer.clippingPlanes.push(this.clipPlaneX, this.clipPlaneY, this.clipPlaneZ);
				} else {
					this.clipPlaneX.setComponents(+clip.xx, +clip.xy, +clip.xz, 0);
					this.clipPlaneY.setComponents(+clip.yx, +clip.yy, +clip.yz, 0);
					this.clipPlaneZ.setComponents(+clip.zx, +clip.zy, +clip.zz, 0);
				}
			}
			if (clippingReverse.x) {
				this.clipPlaneX.negate();
			} else {
				this.clipPlaneX.normalize();
			}

			if (clippingReverse.y) {
				this.clipPlaneY.negate();
			} else {
				this.clipPlaneY.normalize();
			}

			if (clippingReverse.z) {
				this.clipPlaneZ.negate();
			} else {
				this.clipPlaneZ.normalize();
			}
			this.clipPlaneX.constant = (this.globalMaxMin[0].max - this.globalMaxMin[0].min) * clip.clippingX / 100.0 + this.globalMaxMin[0].min;
			this.clipPlaneY.constant = (this.globalMaxMin[1].max - this.globalMaxMin[1].min) * clip.clippingY / 100.0 + this.globalMaxMin[1].min;
			this.clipPlaneZ.constant = (this.globalMaxMin[2].max - this.globalMaxMin[2].min) * clip.clippingZ / 100.0 + this.globalMaxMin[2].min;
			this.requestRender();
		},

		/**
		 * 旋转视图
		 */
		updateRotate ({ axis, angle }) {
			// logger.log('旋转视图 axis, angle: ', axis, angle);
			if (axis === 'x') {
				this.rotate('X', angle);
			}
			if (axis === 'y') {
				this.rotate('Y', angle);
			}
		},

		/**
		 * 相机切换
		 */
		cameraChange (v) {
			this.setControlType(v);
			this.requestRender();
		},

		/**
		 * 鼠标左键设置
		 * @param value
		 */
		mouseLeftSetting (value) {
			this.mouse.LEFT = value;
			this.mouse.RIGHT = value === THREE.MOUSE.PAN ? THREE.MOUSE.ROTATE : THREE.MOUSE.PAN;
			this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 鼠标右键设置
		 * @param value
		 */
		mouseRightSetting (value) {
			this.mouse.RIGHT = value;
			this.mouse.LEFT = value === THREE.MOUSE.PAN ? THREE.MOUSE.ROTATE : THREE.MOUSE.PAN;
			this.reInitControls(ControlTypeEnum.Trackball);
		},

		/**
		 * 初始化控件
		 */
		initControls (controlType = ControlTypeEnum.Trackball) {

			this.setControlType(controlType);

			if (this.getCameraType() === 'Perspective') {
				// 初始化透视相机控制器
				const perspectiveControls = this.createControl(this.getPerspectiveCamera(), this.camCenter);
				this.setPerspectiveControls(perspectiveControls);

				if (perspectiveControls?.target.clone) {
					this.camToSave['Perspective'].controlCenter = perspectiveControls.target.clone();
				}
			} else {
				// 初始化正交相机控制器
				const orthographicControls = this.createControl(this.getOrthographicCamera(), this.camCenter);
				this.setOrthographicControls(orthographicControls);

				if (orthographicControls?.target.clone) {
					this.camToSave['Orthographic'].controlCenter = orthographicControls.target.clone();
				}
			}
		},

		/**
		 * 重新初始化控件
		 */
		reInitControls (controlType) {
			this.getOrthographicControls()?.dispose();
			this.setOrthographicControls(null);

			this.getPerspectiveControls()?.dispose();
			this.setPerspectiveControls(null);

			this.initControls(controlType);

			cancelAnimationFrame(animationId);
			this.animate();
		},

		/**
		 * 关于控制器变化
		 */
		onControlChange (callRender = true) {
			this.updateRuler();
			this.updateGridPlane();
			callRender && this.requestRender();
		},

		/**
		 * 更新标尺
		 */
		updateRuler (isInit = false) {
			if (isInit) {
				this.rulerLoaded = true;
				sleep(1, () => this.$refs.canvasRulerRef?.init());
				return;
			}

			const zoom = this.curCamera.zoom;
			if (this.lastZoom !== zoom) {
				// logger.log('放大倍数: ', zoom);
				this.long = Number(toExponential(this.modelLong / zoom, 2));
				this.lastZoom = zoom;
				this.$nextTick(() => this.$refs.canvasRulerRef?.init());
			}
		},

		/**
		 * 初始化事件
		 */
		initEvents () {
			const renderer = this.getRenderer();

			ctrlSCallback = (event) => {
				preventDefaults(event);
				this.onSubmitHandler();
			};
			ctrlCallback = (event, activate) => {
				if (activate) {
					logger.log('初始化框选事件.');
					this.initBoxSelectEvent();
				} else {
					logger.log('取消框选事件.');
					this.removeBoxSelectListen();
				}
			};

			addEventHandler(window, 'keydown', onKeydown, true);
			addEventHandler(window, 'resize', this.onMainCanvasResize, true);

			// init renderer Event
			addEventHandler(renderer.domElement, 'click', this.onMouseClick, true);
			addEventHandler(renderer.domElement, 'wheel', this.onMouseWheel, true);
			addEventHandler(renderer.domElement, 'mousemove', this.onMouseMove, true);

			this.onMainCanvasResize();
			this.requestRender();
		},

		/**
		 * 提交处理程序
		 */
		onSubmitHandler () {
			this.$emit('submit');
		},

		/**
		 * 初始化框选事件
		 */
		initBoxSelectEvent () {
			if (selectionBox) return;
			// 锁定模型，防止拖动时视图跟着转动
			this.curControl.enabled = false;
			this.clearBoxSelectHelper();
			selectionBox = new SelectionBox(this.curCamera, this.getScene());
			helper = new SelectionHelper(selectionBox, this.getRenderer(), 'selectBox');

			addEventHandler(document, 'pointerdown', this.pointerdownFun, true);
			addEventHandler(document, 'pointermove', this.pointermoveFun, true);
			addEventHandler(document, 'pointerup', this.pointerupFun, true);
		},

		clearBoxSelectHelper () {
			if (helper) {
				helper.element.classList.remove('selectBox');
				helper.element.remove();
			}
			if (selectionBox) {
				selectionBox.collection = [];
			}
			selectionBox = null;
			helper = null;
		},

		pointerdownFun (event) {
			if (selectionBox) {
				selectionBox.collection = [];
				this.updateColor(selectionBox.collection, this.defaultColor, ColorEnum.black);
				this.setPoint(event, selectionBox.startPoint);
			}
		},

		pointermoveFun (event) {
			if (helper && helper.isDown) {
				this.updateColor(selectionBox.collection, this.defaultColor, ColorEnum.black);
				this.setPoint(event, selectionBox.endPoint);
				const allSelected = selectionBox.select();
				this.updateColor(allSelected, this.selectColor);
			}
		},

		pointerupFun (event) {
			if (selectionBox) {
				this.setPoint(event, selectionBox.endPoint);
				const allSelected = selectionBox.select();
				let list = this.updateColor(allSelected, this.selectColor);
				boxAllSelected = this.formatNicknames(list);
				list = null;
				logger.log('boxAllSelected: ', boxAllSelected);

				if (this.selectParam) {
					this.$emit('boxSelectedHandler', boxAllSelected);
				}
				this.$emit('closeBoxSelect');

				this.removeBoxSelectListen();
			}
		},

		updateColor (list, hexColor, lineColor = null) {

			list = this.resultHandler(list);

			for (const item of list) {
				if (this.selectParam) {
					if (this.isModelFace(item) && this.selectPart === SelectModeEnum.surface) {
						item.material?.color.set(new THREE.Color(hexColor));
						item.material?.emissive.set(new THREE.Color(0x333333));
					}
					if (this.isModelEdge(item) && this.selectPart === SelectModeEnum.line) {
						item.material?.color.set(new THREE.Color(lineColor ?? hexColor));
					}
				} else {
					if (this.isModelFace(item)) {
						item.material?.color.set(new THREE.Color(hexColor));
						item.material?.emissive.set(new THREE.Color(0x333333));
					}
					if (this.isModelEdge(item)) {
						item.material?.color.set(new THREE.Color(lineColor ?? hexColor));
					}
				}
				item.material.needsUpdate = true;
			}

			return list;
		},

		/**
		 * 删除框选监听
		 */
		removeBoxSelectListen () {
			if (this.curControl) {
				this.curControl.enabled = true; // 释放模型锁定
			}

			if (!selectionBox) return;

			this.updateColor(selectionBox.collection, this.defaultColor, ColorEnum.black);
			this.clearBoxSelectHelper();

			removeEventHandler(document, 'pointerdown', this.pointerdownFun);
			removeEventHandler(document, 'pointermove', this.pointermoveFun);
			removeEventHandler(document, 'pointerup', this.pointerupFun);
		},

		/**
		 * 设置鼠标点击点坐标
		 */
		setPoint (event, point) {
			const sliderWidth = 320;
			const headerHeight = 50;
			const canvas = this.getCanvas();
			const x = ((event.clientX - (this.collapsed ? 0 : sliderWidth)) / canvas.clientWidth) * 2 - 1;
			const y = -((event.clientY - headerHeight) / canvas.clientHeight) * 2 + 1;
			point.set(x, y, 0.5);
		},

		/**
		 * 结果处理程序
		 * @param {Array} result
		 */
		resultHandler (result) {
			return [...result].filter(child => {
				if (this.selectPart === SelectModeEnum.surface && this.isModelFace(child)) {
					return true;
				}
				if (this.selectPart === SelectModeEnum.line && this.isModelEdge(child)) {
					return true;
				}
				if (this.selectPart === SelectModeEnum.point && child.isSphere) {
					return true;
				}
			});
		},

		/**
		 * 格式化昵称
		 * @param {Array} result
		 */
		formatNicknames (result) {
			return [...result].map(c => c.nickname);
		},

		/**
		 * 在主画布上调整大小
		 */
		onMainCanvasResize () {
			const canvas = this.getCanvas();
			const renderer = this.getRenderer();
			const orthographicCamera = this.getOrthographicCamera();
			const perspectiveCamera = this.getPerspectiveCamera();
			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			const pixelRatio = window.devicePixelRatio;
			const width = canvas.clientWidth * pixelRatio | 0;
			const height = canvas.clientHeight * pixelRatio | 0;
			const needResize = canvas.width !== width || canvas.height !== height;
			const aspect = canvas.clientWidth / canvas.clientHeight;

			if (needResize) {
				renderer.setSize(width, height, false);
				const change = this.originalAspect / aspect;
				const newSize = this.viewSize * change;
				if (orthographicCamera) {
					orthographicCamera.left = -aspect * newSize / 2;
					orthographicCamera.right = aspect * newSize / 2;
					orthographicCamera.top = newSize / 2;
					orthographicCamera.bottom = -newSize / 2;
					orthographicCamera.updateProjectionMatrix();
				}

				if (perspectiveCamera) {
					perspectiveCamera.aspect = canvas.clientWidth / canvas.clientHeight;
					perspectiveCamera.updateProjectionMatrix();
				}

				if (this.openAnimate) {
					if (perspectiveControls) {
						perspectiveControls.handleResize();
					}
					if (orthographicControls) {
						orthographicControls.handleResize();
					}
				}
			}
			this.requestRender();
		},

		/**
		 * 初始化统计场景的组件和索引
		 */
		initStatistics () {
			const statisticsUtils = new StatisticsUtils();
			const info = statisticsUtils.statistic(this.getScene());
			logger.log('info: ', info);
		},

		/**
		 * 鼠标点击处理
		 */
		onMouseClick (e) {

			preventDefaults(e);

			if (!this.solidNames.length) {
				return;
			}

			if (selectionBox) return;

			this.updatePointer(e);

			this.rightMenuVisible = false; // 关闭右键菜单

			if (this.curDraw) {
				if (this.curDraw === DrawEnum.curve) { // 绘制曲线
					if (!this.selectParam || this.selectParam.name !== 'ys_line') {
						this.$message.warn('请先点选预设线参数');
						return;
					}
					if (this.selectParam.name === 'ys_line') {
						this.drawCurve();
					}
				} else if (this.curDraw === DrawEnum.point) { // 绘制点
					if (!this.selectParam || this.selectParam.name !== 'bj_point') {
						this.$message.warn('请先点选变径点参数');
						return;
					}
					if (this.selectParam.name === 'bj_point') { // 变径点只能在管线上绘制
						const intersects = this.getPipeLineIntersects(e);
						if (intersects.length) {
							const point = intersects[0].point;
							const name = intersects[0].object.nickname.replace(/.*_/g, 'points_group_');
							const num = this.getScene().children.filter(i => i.nickname === name || i.groupName === name).length;
							if (num >= 2) {
								this.$message.warn('一条管线上支持两个变径点');
							} else {
								this.drawPoint(point, name);
								this.$emit('updateBjPoint', {
									nickname: name,
									value: { x: point.x, y: point.y, z: point.z },
								});
								this.pointCount++;
							}
						} else {
							this.$message.warn('变径点只能在管线上绘制');
						}
					}
				} else if (this.curDraw === DrawEnum.circle) { // 绘制圆
					const intersects = this.getIntersects(this.clientX, this.clientY);
					if (intersects.length) {
						const point = intersects[0].point;
						this.drawCircle(this.getPoint(point));
					}
				}
			} else {
				const intersects = this.selectPartsHandle(e);
				logger.log('onMouseClick intersects: ', intersects);
			}
		},

		/**
		 * 鼠标移动处理
		 */
		onMouseMove (e) {

			this.updatePointer(e);

			if (selectionBox) {
				return false;
			}

			if (!this.curCamera) {
				return false;
			}

			if (!this.selectParam) {
				return false;
			}

			let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());
			// 过滤出所有线
			if (this.selectPart === SelectModeEnum.line) {
				intersects = intersects.filter(item => this.isLine(item.object) && !this.isModelWireframe(item.object));
				if (this.selectParam && this.selectParam.name === 'ys_line') { // 预设线状态不允许选中普通线
					intersects = intersects.filter(item => item.nickname && !this.isModelEdge(item));
				}
			}
			// 过滤出所有面
			if (this.selectPart === SelectModeEnum.surface && !this.curDraw) {
				intersects = intersects.filter(item => this.isFace(item.object));
			}

			let tipObj = {};
			if (intersects.length > 0) {
				// 多个相交物体获取离镜头最近的
				const clickVisibleMesh = intersects?.[0].object; // 需操作的网格模型

				if (clickVisibleMesh) {
					if (clickVisibleMesh.nickname) {
						tipObj = { nickname: clickVisibleMesh.nickname };
					} else if (clickVisibleMesh.groupName) {
						tipObj = { groupName: clickVisibleMesh.groupName };
					}
				}
			}
			this.$emit('tip', tipObj);
			return intersects;
		},

		/**
		 * 鼠标右键事件
		 */
		onContextmenu (e) {
			this.updatePointer(e);
			this.isMoved = false;
			this.contextMenuObject = null;

			addEventHandler(this.getRenderer().domElement, 'pointermove', () => {
				this.isMoved = true;
			}, true);
			addEventHandler(this.getRenderer().domElement, 'pointerup', async () => {
				// 显示右键菜单
				if (!this.isMoved) { // 不是右键平移，才显示右键菜单
					const intersects = this.getIntersects(this.clientX, this.clientY);
					if (intersects.length && intersects[0]?.object?.nickname) {
						logger.log('鼠标右键事件 intersects: ', intersects[0]);
						if (intersects[0] && this.isFace(intersects[0].object)) {
							this.contextMenuObject = intersects[0];
						}
					}
					this.rightMenuVisible = true;
				}
			}, true);
		},

		/**
		 * 鼠标滚轮事件处理
		 */
		onMouseWheel (event) {
			if (this.curControl.zoomSpeed > 0) {
				return;
			}
			this.scrolled = true;
			const deltaY = event.deltaY;
			// 将鼠标的屏幕坐标转换为NDC坐标
			const { x, y } = this.getPosition(event);
			// 这里定义深度值为1，深度值越大，意味着精度越高
			const vector3 = new THREE.Vector3(x, y, 1);
			// 将鼠标坐标转换为3D空间坐标
			vector3.unproject(this.curCamera);
			// 获得从相机指向鼠标所对应的3D空间点的射线（归一化）
			const dir = new THREE.Vector3().copy(vector3).sub(this.curCamera.position.clone()).normalize();
			const shift = new THREE.Vector3().copy(dir).multiplyScalar(deltaY * 0.1);

			this.curCamera.position.add(shift);
			this.curControl.position0.add(shift);
			this.curControl.target.add(shift);
			this.curControl.update();
		},

		/**
		 * 更新指针缓存坐标
		 */
		updatePointer (e) {
			const { x, y } = this.getPosition(e);
			this.pointer.x = x;
			this.pointer.y = y;
			this.clientX = e.clientX;
			this.clientY = e.clientY;
		},

		/**
		 * 选择部件处理
		 */
		selectPartsHandle (e) {

			if (!this.curCamera) {
				return false;
			}

			// 选点直接返回
			if (this.selectPart === SelectModeEnum.point) {
				return false;
			}

			let intersects = this.getCanvasIntersects(e, this.getScene(), this.curCamera, this.getCanvas());
			// 过滤出所有线
			if (this.selectPart === SelectModeEnum.line) {
				intersects = intersects.filter(item => this.isLine(item.object) && !this.isModelWireframe(item.object));
				if (this.selectParam && this.selectParam.name === 'ys_line') { // 预设线状态不允许选中普通线
					intersects = intersects.filter(item => item.nickname && !this.isModelEdge(item));
				}
			}
			// 过滤出所有面
			if (this.selectPart === SelectModeEnum.surface && !this.curDraw) {
				intersects = intersects.filter(item => this.isFace(item.object));
			}

			if (!this.curDraw) {
				if (intersects.length > 0) {
					// 多个相交物体获取离镜头最近的
					const clickVisibleObj = intersects[0];
					// const distance = clickVisibleObj.distance; // 距离
					// logger.log('distance: ', distance);
					const clickVisibleMesh = clickVisibleObj.object; // 需操作的网格模型

					if (!this.selectParam) {
						this.resetColorHandle();
					} else if (this.selectParam?.value.includes(clickVisibleMesh.nickname)) {
						this.updateColorByObj(clickVisibleMesh, this.originColor);
						if (clickVisibleMesh.nickname) {
							this.$emit('singleSelectedHandler', clickVisibleMesh.nickname);
						}
						return intersects;
					}

					const selectColor = toHexColor(this.selectParam?.config?.color);
					const chooseColor = this.selectParam ? selectColor : ColorEnum.white;

					// 体
					if (this.selectPart === SelectModeEnum.solid) {
						this.updateColorHandle(clickVisibleObj);
					}

					// 面
					if (this.selectPart === SelectModeEnum.surface && this.isFace(clickVisibleMesh)) {
						if (!clickVisibleMesh.isLackFace) { // 非坏面(缺失的面)才可操作
							clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
							clickVisibleMesh.material.emissive.set(new THREE.Color(chooseColor));
						}
					}

					// 线
					if (this.selectPart === SelectModeEnum.line && this.isLine(clickVisibleMesh)) {
						clickVisibleMesh.material.color.set(new THREE.Color(chooseColor));
					}

					clickVisibleMesh.material.needsUpdate = true;
					this.requestRender();

					if (clickVisibleMesh) {
						if (clickVisibleMesh.nickname) {
							this.$emit('singleSelectedHandler', clickVisibleMesh.nickname);
						}
						if (selectSwitch === TagSwitchEnum.ON && clickVisibleMesh.nickname) {
							this.createTag(clickVisibleMesh);
						}
					}
				} else {
					if (!this.selectParam) {
						this.resetColorHandle();
					}
					this.clearTag();
				}
			}
			return intersects;
		},

		/**
		 * 判断是否面
		 */
		isFace (object) {
			return object?.material?.type === 'MeshPhongMaterial';
		},

		/**
		 * 判断是否线
		 */
		isLine (object) {
			return object?.material?.type === 'LineMaterial';
		},

		/**
		 * 判断是否球体
		 */
		isSphere (object) {
			return object?.geometry?.type === 'SphereGeometry';
		},

		/**
		 * 颜色重置处理程序
		 */
		resetColorHandle () {
			for (const raycasterObj of raycasterObjs) {
				if (!raycasterObj.material) {
					break;
				}
				if (this.isModelFace(raycasterObj)) {
					if (!raycasterObj.isLackFace) { // 非坏面(缺失的面)才可操作
						raycasterObj.material.color.set(new THREE.Color(this.originColor));
						raycasterObj.material.emissive.set(new THREE.Color(0x333333));
					}
				} else if (this.isLine(raycasterObj)) {
					raycasterObj.material.color.set(new THREE.Color(ColorEnum.black));
				}
				raycasterObj.material.needsUpdate = true;
			}
			this.requestRender();
		},

		/**
		 * 透明度重置处理程序
		 */
		resetOpacityHandle () {
			for (const raycasterObj of raycasterObjs) {
				if (this.isModelFace(raycasterObj)) {
					raycasterObj.material.opacity = this.originOpacity;
				}
				raycasterObj.material.needsUpdate = true;
			}
			this.requestRender();
		},

		/**
		 * 可见重置处理程序
		 */
		resetVisibleHandle () {
			this.changeRenderMode(this.curRenderMode);
		},

		/**
		 * 颜色更新处理程序
		 */
		updateColorHandle (intersect) {
			for (const raycasterObj of raycasterObjs) {
				if (raycasterObj.solidGroup === intersect.object.solidGroup && raycasterObj.material) {
					if (this.isFace(raycasterObj) && !raycasterObj.isLackFace) { // 非坏面(缺失的面)才可操作
						raycasterObj.material.color.set(new THREE.Color(ColorEnum.white));
						raycasterObj.material.emissive.set(new THREE.Color(ColorEnum.white));
					}
					raycasterObj.material.needsUpdate = true;
				}
			}
			this.requestRender();
		},

		/**
		 * 添加标签
		 */
		createTag (object) {
			const canvas = this.getCanvas();
			const w = canvas.clientWidth / 2;
			const h = canvas.clientHeight / 2;
			const x = Math.round(this.pointer.x * w + w); // 标准设备坐标转屏幕坐标
			const y = Math.round(-this.pointer.y * h + h) - 35;

			// 修改 div 的位置
			const div = document.querySelector('#label');
			div.style.display = 'block';
			div.style.padding = '5px';
			div.style.position = 'absolute';
			div.style.backgroundColor = 'rgba(155, 0, 155, 0.8)';
			div.style.left = x + 'px';
			div.style.top = y + 'px';
			div.innerHTML = object.nickname; // 显示模型信息
		},

		/**
		 * 清除标签
		 */
		clearTag () {
			const divDom = document.querySelector('#label');
			divDom.style.display = 'none';
			divDom.innerHTML = '';
		},

		/**
		 * 获取线材
		 */
		getLineMaterial (color, lineWidth = 4) {
			const lineMaterial = new LineMaterial({
				color: new THREE.Color(color || ColorEnum.black),
				linewidth: lineWidth || 2,
				dashed: false,
				dashSize: 1,
				gapSize: 1,
				dashScale: 3,
				transparent: true,
				// fix: 模型重合相交部分闪烁
				polygonOffset: true, // 是否使用多边形偏移，默认值为false
				// 当这两个参数都为负时（深度减小），网格将被拉向摄影机（位于前面）。 当两个参数都为正（增加深度）时，网格会被推离摄影机（会落在后面）
				polygonOffsetFactor: -1.0, // 设置多边形偏移系数，默认值为0
				polygonOffsetUnits: -4.0, // 设置多边形偏移单位，默认值为0
			});
			const canvas = this.getCanvas();
			lineMaterial.resolution.set(
				canvas.clientWidth * window.devicePixelRatio,
				canvas.clientHeight * window.devicePixelRatio,
			);
			return lineMaterial;
		},

		requestRender () {
			if (!this.renderRequested) {
				this.renderRequested = true;
				if (this.openAnimate) {
					this.render();
				} else {
					cancelAnimationFrame(animationId);
					animationId = requestAnimationFrame(this.render);
				}
			}
		},

		animate () {
			if (this.openAnimate) {
				cancelAnimationFrame(animationId);
				animationId = requestAnimationFrame(this.animate);
				this.render();
			}
		},

		render () {
			this.renderRequested = false;

			const scene = this.getScene();
			const renderer = this.getRenderer();
			const cameraType = this.getCameraType();
			const arrowScene = this.getArrowScene();
			const arrowCamera = this.getArrowCamera();
			const arrowRenderer = this.getArrowRenderer();
			const perspectiveCamera = this.getPerspectiveCamera();
			const orthographicCamera = this.getOrthographicCamera();
			const perspectiveControls = this.getPerspectiveControls();
			const orthographicControls = this.getOrthographicControls();

			perspectiveControls?.update();
			orthographicControls?.update();

			if (arrowCamera) {
				if (cameraType === 'Perspective' && perspectiveControls) {
					arrowCamera.position.copy(perspectiveCamera.position);
					arrowCamera.position.sub(perspectiveControls.target);
				}
				if (cameraType === 'Orthographic' && orthographicControls) {
					arrowCamera.position.copy(orthographicCamera.position);
					arrowCamera.position.sub(orthographicControls.target);
				}
				arrowCamera.lookAt(arrowScene.position);
				arrowCamera.position.setLength(200);
			}

			this.stats?.begin();
			if (cameraType === 'Perspective' && perspectiveControls) {
				renderer?.render(scene, perspectiveCamera);
			}
			if (cameraType === 'Orthographic' && orthographicControls) {
				renderer?.render(scene, orthographicCamera);
			}
			arrowRenderer?.render(arrowScene, arrowCamera);
			this.stats?.end();
		},

		/**
		 * 解析数据
		 */
		parseData (data) {
			if (data?.compressed) {
				// Decode from base90 and distribute the floats
				for (const obj of data.objects) {
					obj.floats = JSON.parse('[' + floatDecode(data, obj.floats) + ']');
					obj.verts = baseDecode(data, obj.verts).map(x => obj.floats[x]);
					obj.facets = baseDecode(data, obj.facets);
					obj.wires = obj.wires.map(w => baseDecode(data, w).map(x => obj.floats[x]));
					obj.facesToFacets = obj.facesToFacets.map(x => baseDecode(data, x));
				}
			}
			return data;
		},

		/**
		 * 模具的面或边
		 */
		isModelFaceOrEdge (obj) {
			return this.isModelFace(obj) || this.isModelEdge(obj);
		},

		/**
		 * 模具的面
		 */
		isModelFace (obj) {
			return obj.nickname?.startsWith('face_');
		},

		/**
		 * 模具的边
		 */
		isModelEdge (obj) {
			return obj.nickname?.startsWith('e_') && obj.allowSelect;
		},

		/**
		 * 模具的线框
		 */
		isModelWireframe (obj) {
			return obj.groupName === 'wireframe' || obj.isWireframe;
		},

		/**
		 * 变径点
		 */
		isBjPoint (obj) {
			return (obj.nickname || obj.groupName)?.startsWith('points_');
		},

		/**
		 * 预设线
		 */
		isYsLine (obj, isLine) {

			if (isLine === obj.isSphere) {
				return false;
			}

			return (obj.nickname || obj.groupName)?.startsWith('curve_');
		},

		/**
		 * 更新对象颜色(所有)
		 */
		updateAllAttr ({ color, opacity, visible, reset }) {
			for (const raycasterObj of raycasterObjs) {
				this.updateAttrByObj(raycasterObj, { color, opacity, visible, reset });
			}
			for (const child of this.getScene().children) {
				this.updateAttrByObj(child, { color, opacity, visible, reset });
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象颜色(多个)
		 */
		updateAttrByList (nameList, { selected, color, opacity, visible, reset }) {
			// logger.log('根据传入列表更新对象颜色(多个)', nameList.join(','), selected, toHexColor(color), opacity, visible, reset);
			color = selected ? toHexColor(color) : this.originColor;
			for (const raycasterObj of raycasterObjs) {
				if (nameList.includes(raycasterObj.nickname) || nameList.includes(raycasterObj.groupName)) {
					this.updateAttrByObj(raycasterObj, { color, opacity, visible, reset });
				}
			}
			for (const child of this.getScene().children) {
				if (nameList.includes(child.nickname) || nameList.includes(child.groupName)) {
					this.updateAttrByObj(child, { color, opacity, visible, reset });
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象可见性(单个)
		 */
		updateAttrByObj (obj, { visible, color, opacity, reset }) {
			if (visible !== undefined) {
				if (
					this.isModelFaceOrEdge(obj) ||
					this.isModelWireframe(obj) ||
					this.isYsLine(obj, true) ||
					this.isYsLine(obj, false) ||
					this.isBjPoint(obj)
				) {
					obj.visible = visible;
					obj.needsUpdate = true;
				}
			}
			const material = obj.material;
			if (color !== undefined && material) {
				if (this.isModelFace(obj)) {
					if (!obj.isLackFace) { // 非坏面(缺失的面)才可操作
						material.color.set(new THREE.Color(color));
						material.emissive.set(new THREE.Color(0x333333));
					}
				} else if (this.isModelEdge(obj) || this.isYsLine(obj, true)) {
					material.color.set(new THREE.Color(reset ? ColorEnum.black : color));
				} else if (this.isBjPoint(obj)) {
					material.color.set(new THREE.Color(reset ? 0xffff00 : color));
				}
				material.needsUpdate = true;
			}

			if (opacity !== undefined && material) {
				opacity = Number(opacity);
				// 只有线和模具的材料及预设线变径点可以设置
				if (this.isModelFaceOrEdge(obj) || this.isYsLine(obj, true)) {
					material.opacity = opacity;
					material.depthWrite = (opacity === 1.0);
					material.transparent = (opacity !== 1.0);
					material.needsUpdate = true;
				}
			}
		},

		/**
		 * 更新对象颜色(所有)
		 */
		updateAllColor (hexColor) {
			for (const raycasterObj of raycasterObjs) {
				if (this.isModelFace(raycasterObj)) {
					this.updateColorByObj(raycasterObj, hexColor);
				}
				if (this.isModelEdge(raycasterObj) || this.isYsLine(raycasterObj, true)) {
					this.updateColorByObj(raycasterObj, ColorEnum.black);
				}
			}
			for (const child of this.getScene().children) {
				if (this.isModelFace(child)) {
					this.updateColorByObj(child, hexColor);
				}
				if (this.isModelEdge(child) || this.isYsLine(child, true)) {
					this.updateColorByObj(child, ColorEnum.black);
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象颜色(多个)
		 */
		updateColorByList (nameList, selected, hexColor) {
			hexColor = selected ? toHexColor(hexColor) : this.originColor;
			for (const raycasterObj of raycasterObjs) {
				if (nameList.includes(raycasterObj.nickname) ||
					nameList.includes(raycasterObj.groupName)) {
					this.updateColorByObj(raycasterObj, hexColor);
				}
			}
			for (const child of this.getScene().children) {
				if (nameList.includes(child.nickname) ||
					nameList.includes(child.groupName)) {
					this.updateColorByObj(child, hexColor);
				}
			}
			this.requestRender();
		},

		/**
		 * 根据名称更新对象颜色(单个)
		 */
		updateColorByName (name, selected) {
			const hexColor = selected ? this.selectedColor : this.originColor;
			for (const raycasterObj of raycasterObjs) {
				if (name && (raycasterObj.nickname === name || raycasterObj.groupName === name)) {
					this.updateColorByObj(raycasterObj, hexColor);
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象颜色(单个)
		 */
		updateColorByObj (obj, hexColor) {
			const material = obj.material;
			if (!material) return;

			if (this.isModelFace(obj)) {
				if (!obj.isLackFace) { // 非坏面(缺失的面)才可操作
					material.color.set(new THREE.Color(hexColor));
					material.emissive.set(new THREE.Color(0x333333));
				}
			} else if (this.isModelEdge(obj) || this.isYsLine(obj, true)) {
				material.color.set(new THREE.Color(hexColor));
			} else if (this.isBjPoint(obj)) {
				material.color.set(new THREE.Color(hexColor));
			}
			material.needsUpdate = true;
		},

		/**
		 * 更新对象不透明度(所有)
		 */
		updateAllOpacity (opacity) {
			for (const raycasterObj of raycasterObjs) {
				if (this.isModelFaceOrEdge(raycasterObj)) {
					this.updateOpacityByObj(raycasterObj, opacity);
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象不透明度(多个)
		 */
		updateOpacityByList (nameList, opacity) {
			for (const raycasterObj of raycasterObjs) {
				if (nameList.includes(raycasterObj.nickname) ||
					nameList.includes(raycasterObj.groupName)) {
					this.updateOpacityByObj(raycasterObj, opacity);
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象不透明度(单个)
		 */
		updateOpacityByObj (obj, opacity) {
			opacity = Number(opacity);

			const material = obj.material;
			if (!material) return;

			// 只有线和模具的材料及预设线变径点可以设置
			if (this.isModelFaceOrEdge(obj) || this.isYsLine(obj, true)) {
				material.opacity = opacity;
				material.depthWrite = (opacity === 1.0);
				material.transparent = (opacity !== 1.0);
				material.needsUpdate = true;
			}
		},

		/**
		 * 更新对象可见性(所有)
		 */
		updateAllVisible (visible, filter) {
			if (!filter) {
				filter = this.isModelFaceOrEdge;
			}
			for (const raycasterObj of raycasterObjs) {
				if (filter(raycasterObj)) {
					this.updateVisibleByObj(raycasterObj, visible);
				}
			}
			this.requestRender();
		},

		/**
		 * 根据传入列表更新对象可见性(多个)
		 */
		updateVisibleByList (nameList, visible) {
			for (const raycasterObj of raycasterObjs) {
				if (nameList.includes(raycasterObj.nickname) ||
					nameList.includes(raycasterObj.groupName)) {
					this.updateVisibleByObj(raycasterObj, visible);
				}
			}
			for (const child of this.getScene().children) {
				if (nameList.includes(child.nickname) ||
					nameList.includes(child.groupName)) {
					this.updateVisibleByObj(child, visible);
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象可见性(单个)
		 */
		updateVisibleByObj (obj, visible) {
			obj.visible = visible;
			obj.needsUpdate = true;
		},

		/**
		 * 根据名称更新对象可见性(单个)
		 */
		updateVisibleByName (name, visible, isRefreshView = true) {
			for (const raycasterObj of raycasterObjs) {
				if (name && (raycasterObj.nickname === name || raycasterObj.groupName === name)) {
					this.updateVisibleByObj(raycasterObj, visible);
				}
			}
			isRefreshView && this.requestRender();
		},

		/**
		 * 根据传入列表更新对象线宽
		 */
		updateLineWidthByList (nameList, lineWidth) {
			for (const raycasterObj of raycasterObjs) {
				if ((nameList.includes(raycasterObj.nickname) ||
					nameList.includes(raycasterObj.groupName)) && this.isModelEdge(raycasterObj)) {
					this.updateLineWidthByObj(raycasterObj, lineWidth);
				}
			}
			for (const child of this.getScene().children) {
				if ((nameList.includes(child.nickname) ||
					nameList.includes(child.groupName)) && this.isModelEdge(child)) {
					this.updateLineWidthByObj(child, lineWidth);
				}
			}
			this.requestRender();
		},

		/**
		 * 更新对象线宽
		 */
		updateLineWidthByObj (obj, lineWidth) {
			obj.material.lineWidth = lineWidth;
			obj.material.resolution.set(this.getCanvas().clientWidth, this.getCanvas().clientHeight); // 这句如果不加宽度仍然无效
			obj.needsUpdate = true;
		},

		/**
		 * 重置视窗大小
		 */
		resizeCurrentView () {
			this.jsViewWidth = this.collapsed ? 'calc(100vw)' : 'calc(100vw - 320px)';
			this.$nextTick(() => this.onMainCanvasResize());
		},

		/**
		 * 开启加载中
		 */
		openLoading (loadingMsg = '', resetCount = false) {
			this.tip = loadingMsg || '加载中...';
			this.loading = true;
			if (resetCount) {
				this.loadingCount = 0;
			}
			this.loadingCount++;
			// logger.log('openLoading loadingCount: ', this.loadingCount);
		},

		/**
		 * 关闭加载中
		 */
		closeLoading () {
			this.loadingCount--;
			// logger.log('closeLoading loadingCount: ', this.loadingCount);
			if (this.loadingCount <= 0) {
				this.loadingCount = 0;
				this.loading = false;
				this.tip = '';
			}
		},

		/**
		 * 切换视图
		 */
		updateOrientation (direction) {
			this.direction = direction;
			this.resetCamera();
			switch (direction) {
				case ViewEnum.front:
					this.navChange([0, 0, 1]);
					break;
				case ViewEnum.back:
					this.navChange([0, 0, -1]);
					break;
				case ViewEnum.left:
					this.navChange([-1, 0, 0]);
					break;
				case ViewEnum.right:
					this.navChange([1, 0, 0]);
					break;
				case ViewEnum.top:
					this.navChange([0, 1, 0]);
					break;
				case ViewEnum.bottom:
					this.navChange([0, -1, 0]);
					break;
				default:
					this.curControl.reset();
					this.navChange([0.4, 0.4, 0.5]);
					break;
			}
		},

		/**
		 * 更改投影模式
		 */
		changeViewMode (type) {
			if (type === CameraModeEnum.perspective) {
				this.cameraChange('Perspective');
			} else if (type === CameraModeEnum.orthographic) {
				this.cameraChange('Orthographic');
			}
			this.resetCamera();
		},

		/**
		 * 选择模式
		 */
		chooseViewHandle (choose) {
			this.selectPart = choose;
		},

		/**
		 * 右键菜单上点击
		 */
		onContextMenuClick (type) {

			this.rightMenuVisible = false;

			if (type === 'show') {
				this.hiddenObjectMap = {};
				const filter = (child) => this.isModelFace(child) || this.isModelEdge(child) || this.isModelWireframe(child);
				this.updateAllVisible(true, filter);
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hidden') {
				this.hiddenObjectMap[this.contextMenuObject.object.nickname] = true;
				this.updateVisibleByName(this.contextMenuObject.object.nickname, false);
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hiddenAllFace') {
				const allFaceMesh = this.getSceneAllFace();
				for (const faceMesh of allFaceMesh) {
					this.hiddenObjectMap[faceMesh.nickname] = true;
					this.updateVisibleByName(faceMesh.nickname, false, false);
				}
				this.requestRender();
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
			if (type === 'hiddenWireframe') {
				const allWireframeMesh = this.getSceneAllWireframe();
				for (const wireframeMesh of allWireframeMesh) {
					this.hiddenObjectMap[wireframeMesh.nickname] = true;
					this.updateVisibleByName(wireframeMesh.nickname, false, false);
				}
				this.requestRender();
				this.$store.dispatch('saveHiddenObjectMap', this.hiddenObjectMap);
			}
		},

		/**
		 * 绘制曲线
		 */
		drawCurve (nickname, clickPoints, isInitDrawCurve = false, time = 300) {
			return new Promise(async (resolve) => {

				this.isInitDrawCurve = isInitDrawCurve;

				if (nickname && nickname.includes('_')) {
					this.curveCount = Number(nickname.split('_')[1]);
				}
				this.nickname = nickname || this.nickname;

				const resetCurvesMap = () => {
					this.curvesMap[this.nickname] = {
						spheres: [],
						clickPoints: [],
					};
				};

				const commonHandler = (pointVector, isTemporary) => {
					const sphere = this.drawSphere(pointVector);
					sphere.isTemporary = isTemporary;
					raycasterObjs.push(sphere);
					this.curvesMap[this.nickname].spheres.push(sphere);
					this.curvesMap[this.nickname].clickPoints.push(pointVector);
				};

				if (isArray(clickPoints) && clickPoints.length) {
					resetCurvesMap();
					for (const point of clickPoints) {
						const pointVector = new THREE.Vector3(point.x, point.y, point.z);
						commonHandler(pointVector, false);
					}

					sleep(time, () => {
						this.initCurveEnterAndRollbackEvent();
						enterCallback();
						return resolve();
					});
				} else {
					if (!this.curvesMap[this.nickname] || !this.drawing) {
						resetCurvesMap();
					}
					this.drawing = true;
					const pointVector = this.getCurrPoint({ clientX: this.clientX, clientY: this.clientY });
					if (!pointVector) return;
					commonHandler(pointVector);

					if (!this.inited && this.curDraw) {
						this.inited = true;
						this.throttleMouseMoveHandler = throttle(this.mouseMoveDrawCurve, 10);
						addEventHandler(this.getRenderer().domElement, 'mousemove', this.throttleMouseMoveHandler, true);
						this.initCurveEnterAndRollbackEvent();
					}
					return resolve();
				}
			});
		},

		/**
		 * 鼠标移动绘制曲线
		 */
		mouseMoveDrawCurve ({ clientX, clientY }) {
			this.clientX = clientX;
			this.clientY = clientY;
			const pointVector = this.getCurrPoint({ clientX, clientY });
			if (!pointVector) return;
			this.confirmDrawCurve(pointVector, true);
		},

		/**
		 * 确认绘制曲线
		 */
		confirmDrawCurve (pointVector = null, isTemporary = true) {

			const pointMap = this.curvesMap[this.nickname];
			if (!pointMap) return;

			const clickPoints = pointMap.clickPoints || [];

			const scene = this.getScene();
			const children = scene.children.filter(child => child && child.isCurve && child.groupName === this.nickname);
			this.clearCacheByArray(children);
			scene.remove(...children);

			const pointVectors = [...clickPoints];
			if (pointVector) pointVectors.push(pointVector);

			if (pointVectors.length < 2) return;

			const res = this.makeCurve(pointVectors, this.nickname, isTemporary);
			this.requestRender();

			return res;
		},

		/**
		 * 更新绘制曲线
		 */
		updateDrawCurve: utilsThrottle(function (pointVector) {

			if (!this.selectTarget) return;

			const groupName = this.selectTarget.groupName;
			const curvesMap = this.curvesMap[groupName];
			if (!curvesMap) return;

			const scene = this.getScene();
			const children = scene.children.filter(child => child && child.isCurve && child.groupName === groupName);
			this.clearCacheByArray(children);
			scene.remove(...children);
			raycasterObjs = raycasterObjs.filter(item => item.groupName !== groupName);

			const index = curvesMap.spheres.findIndex(sphere => sphere.uuid === this.selectTarget.uuid);
			curvesMap.clickPoints[index] = pointVector;

			const res = this.makeCurve(curvesMap.clickPoints, groupName);
			raycasterObjs.push(res.line);

			this.requestRender();
		}, 10),

		/**
		 * 制作曲线
		 */
		makeCurve (pointVectors, groupName, isTemporary) {
			const curve = new THREE.CatmullRomCurve3(pointVectors, false, 'centripetal');
			const curvePoints = curve.getPoints(pointVectors.length >= 6 ? 100 : 50);
			const geometry = new THREE.BufferGeometry().setFromPoints(curvePoints);
			const lineGeometry = new LineGeometry();
			lineGeometry.setPositions(geometry.attributes.position.array);
			let color = ColorEnum.black;
			if (['ys_line'].includes(this.selectParam?.name)) {
				color = toHexColor(this.selectParam?.config.color) ?? 0xffff00;
			}
			const canvas = this.getCanvas();
			const lineMaterial = this.getLineMaterial(color, 2);
			lineMaterial.resolution.set(canvas.clientWidth, canvas.clientHeight); // 这句如果不加宽度仍然无效
			const line = new Line2(lineGeometry, lineMaterial);
			line.computeLineDistances();
			line.isCurve = true;
			line.nickname = this.nickname;
			line.groupName = groupName;
			line.isTemporary = isTemporary;
			this.getScene().add(line);
			return { curve, line };
		},

		/**
		 * 初始化曲线回车和回退事件
		 */
		initCurveEnterAndRollbackEvent () {
			enterCallback = (event) => {
				preventDefaults(event);
				this.stopDrawn();
				this.detachSelected();
				this.removeGridPlane();
				this.$emit('clearDraw');
				if (this.curControl) {
					this.curControl.enabled = true;
				}

				this.inited = false;
				this.drawing = false; // 绘制结束

				if (this.getRenderer()?.domElement) {
					removeEventHandler(this.getRenderer().domElement, 'mousemove', this.throttleMouseMoveHandler);
					this.throttleMouseMoveHandler = null;
				}

				const pointMap = this.curvesMap[this.nickname];
				if (!pointMap) return;

				const res = this.confirmDrawCurve(null, false);
				if (res) {
					raycasterObjs.push(res.line);
					this.requestRender();
				}

				if (!this.isInitDrawCurve) {
					this.emitCurveData();
				}
				this.isInitDrawCurve = false;

				this.initCurveDrawEvent();
				this.detachSelected();
				enterCallback = null;
			};

			escCallback = () => {
				// 分离选定
				this.detachSelected();
				// 清除绘制线
				if (this.curDraw === DrawEnum.curve && this.curvesMap[this.nickname]) {
					const spheres = this.curvesMap[this.nickname].spheres;
					if (spheres.length) {
						this.removeObjByGroupName(spheres[0].groupName);
					}
					this.curvesMap[this.nickname].spheres = [];
					this.curvesMap[this.nickname].clickPoints = [];
					// TODO 待实现emit: 通知对应关联数据清空
				}
			};

			// 回退至上一步
			ctrlZCallback = () => {
				// 绘制完毕后不允许回退
				if (!this.drawing) return;

				if (this.curvesMap[this.nickname].clickPoints.length > 1) {
					this.curvesMap[this.nickname].clickPoints.pop();
					const sphere = this.curvesMap[this.nickname].spheres.pop();
					this.getScene().remove(sphere);
					raycasterObjs = raycasterObjs.filter(obj => obj.uuid !== sphere.uuid);
					this.mouseMoveDrawCurve({ clientX: this.clientX, clientY: this.clientY });
					this.requestRender();
				} else {
					this.$message.info('已回退到最后一步啦!');
				}
			};
		},

		/**
		 * 发出更新曲线数据
		 */
		emitCurveData () {
			const points = this.curvesMap[this.nickname].clickPoints.map(point => this.getPoint(point));
			this.$emit('updateCurve', { name: this.selectParam?.name, nickname: this.nickname, value: points });
			this.$emit('updateDrawCurveParams', points);
		},

		/**
		 * 初始化曲线绘制事件
		 */
		initCurveDrawEvent () {

			delCallback = () => {
				this.detachSelected();
				this.curControl.enabled = true;

				let intersects = this.getIntersects(this.clientX, this.clientY);
				intersects = intersects.filter(mesh => !mesh.object.isHelper);

				if (intersects.length) {
					const object = intersects[0].object;
					if (this.selectTarget) {
						const groupName = this.selectTarget.groupName;
						if (!groupName) return;

						const curvesMap = this.curvesMap[groupName];
						if (!curvesMap) return;

						if (object.geometry.type === 'SphereGeometry') {
							raycasterObjs = raycasterObjs.filter(obj => obj.uuid !== object.uuid);
							this.intersectObjects = this.intersectObjects.filter(obj => obj.uuid !== object.uuid);

							const sphereIndex = curvesMap.spheres.findIndex(obj => obj.uuid === object.uuid);
							curvesMap.spheres.splice(sphereIndex, 1);
							curvesMap.clickPoints.splice(sphereIndex, 1);

							this.getScene().remove(object);

							this.confirmDrawCurve(null, true);
						}
					} else {
						intersects.forEach(mesh => this.removeObjByGroupName(mesh.object.groupName));
					}

					sleep(1, () => this.requestRender());
				}
			};

			dragCallback = (event) => {
				// 锁定模型，防止拖动小球是视图跟着转动
				this.curControl.enabled = false;

				if (!event.value) {
					const selectSphere = event.target?.children[1]?.object;
					this.updateDrawCurve(selectSphere.position.clone());
				}
			};

			this.initDrawEvent(this.curvesMap[this.nickname].spheres);
		},

		/**
		 * 绘制圆
		 */
		async drawCircle (point) {

			if (!this.direction) {
				this.$modal.warning({ content: '请先选择视图模式' });
				return;
			}

			const sphere = this.drawSphere(point, true);

			let newPoint;
			let circlePoints;
			let drawCircleResult;
			const onMousemove = (event) => {
				const scene = this.getScene();
				for (const child of scene.children) {
					if (child && child.isTemporary && !child.isCenterPoint) {
						sleep(1, () => scene.remove(child));
					}
				}

				newPoint = null;
				circlePoints = null;
				drawCircleResult = null;
				const intersects = this.getCanvasIntersects(event, scene, this.curCamera, this.getCanvas());
				if (intersects.length) {
					newPoint = this.getPoint(intersects[0].point);
					sleep(1, () => {
						drawCircleResult = this.drawCircleByCondition(point, newPoint);
					});
				}
			};
			const throttleMouseHandler = throttle(onMousemove, 10);
			this.getRenderer().domElement.addEventListener('mousemove', throttleMouseHandler, false);

			this.requestRender();
		},

		/**
		 * 按条件画圆
		 */
		drawCircleByCondition (point, newPoint) {
			let radius;
			switch (this.direction) {
				case ViewEnum.front:
				case ViewEnum.back: {
					const width = Math.abs(newPoint.x - point.x);
					const height = Math.abs(newPoint.y - point.y);
					radius = width > height ? width : height;
					break;
				}
				case ViewEnum.left:
				case ViewEnum.right: {
					const width = Math.abs(newPoint.x - point.x);
					const zW = Math.abs(newPoint.z - point.z);
					radius = width > zW ? width : zW;
					break;
				}
				case ViewEnum.top:
				case ViewEnum.bottom: {
					const height = Math.abs(newPoint.y - point.y);
					const zW = Math.abs(newPoint.z - point.z);
					radius = height > zW ? height : zW;
					break;
				}
				default:
					logger.error('请选择视图');
					break;
			}

			return this.drawCircleNext(point, radius);
		},

		/**
		 * 框选
		 */
		onBoxSelect (action) {
			if (action) {
				logger.log('初始化框选事件.');
				this.initBoxSelectEvent();
			} else {
				logger.log('删除框选监听.');
				this.removeBoxSelectListen();
			}
		},

		/**
		 * 停止绘制
		 */
		stopDrawn () {
			this.curDraw = '';
		},

		/**
		 * 分离选定
		 */
		detachSelected () {
			drawControl?.detach();
			this.selectTarget = null;
		},

		/**
		 * 按组名删除对象
		 */
		removeObjByGroupName (groupName, conditionHandler) {
			logger.log('按组名删除对象:', groupName);
			this.removeObjByGroupNameComeFromCommon(groupName, conditionHandler);
			raycasterObjs = this.deleteRaycasterObjByGroupName(raycasterObjs, groupName);
		},

		/**
		 * 获取鼠标位置
		 */
		getPosition (event) {
			const sliderWidth = 320;
			const headerHeight = 50;
			const navigationHeight = 0 + 2;
			const canvas = this.getCanvas();
			const x = ((event.clientX - (this.collapsed ? 0 : sliderWidth)) / canvas.clientWidth) * 2 - 1;
			const y = -((event.clientY - (headerHeight + navigationHeight)) / canvas.clientHeight) * 2 + 1;
			return { x, y };
		},

		/**
		 * 通过世界坐标转换为屏幕坐标
		 *  3D坐标转换成2D坐标
		 * @return {Object}
		 */
		worldVectorToScreenVector (worldVector) {
			const vector = worldVector.project(this.curCamera); // 通过世界坐标获取转标准设备坐标
			const w = window.innerWidth / 2;
			const h = window.innerHeight / 2;
			const screenX = Math.round(vector.x * w + w); // 标准设备坐标转屏幕坐标
			const screenY = Math.round(-vector.y * h + h);
			logger.log('屏幕坐标X: ', screenX);
			logger.log('屏幕坐标Y: ', screenY);
			return { screenX, screenY };
		},

		/**
		 * 获取当前世界坐标
		 *  将鼠标坐标转换为3D空间坐标
		 *  @param pointer
		 *  @return {THREE.Vector3}
		 */
		getWorldVector (pointer) {
			// 屏幕坐标转标准设备坐标
			// 标准设备坐标(z=0.5表示深度)
			const vector = new THREE.Vector3(pointer.x, pointer.y, 0.5);
			// 标准设备坐标转为世界坐标(函数vector.unproject(camera)则可以从屏幕2d坐标转换为3d空间坐标)
			const worldVector = vector.unproject(this.curCamera);
			logger.log('世界坐标 worldVector: ', worldVector);
			return worldVector;
		},

		/**
		 * 获取当前点坐标
		 *  @param event
		 *  @return {THREE.Vector3}
		 */
		getCurrPoint (event) {
			const intersects = this.getCanvasIntersects(event, this.getScene(), this.curCamera, this.getCanvas());
			return intersects[0]?.point;
		},

		/**
		 * 重置相机
		 */
		resetCamera () {
			// 更新标尺
			this.curCamera.zoom = 1;
			this.curCamera.updateProjectionMatrix();
			this.updateRuler();

			const camToSave = this.camToSave[this.getCameraType()];
			this.restoreCamera(camToSave.position, camToSave.rotation, camToSave.controlCenter);
		},

		/**
		 * 根据相关参数复位相机
		 */
		restoreCamera (position, rotation, controlCenter) {
			this.curCamera.position.set(position.x, position.y, position.z);
			this.curCamera.rotation.set(rotation.x, rotation.y, rotation.z);

			this.curControl.target.set(controlCenter.x, controlCenter.y, controlCenter.z);
			this.curControl.update();

			this.requestRender();
		},

		/**
		 * 创建网格平面
		 */
		createGridPlane () {
			if (this.gridHelper) return;
			this.gridHelper = new THREE.GridHelper(this.modelLong, 35);
			this.updateGridPlane();
			this.getScene().add(this.gridHelper);
			this.requestRender();
		},

		/**
		 * 更新网格平面
		 */
		updateGridPlane () {
			if (this.gridHelper) {
				this.gridHelper.position.copy(this.camCenter.clone());
				this.gridHelper.rotation.copy(this.curCamera.rotation.clone());
				this.gridHelper.rotateX(Math.PI / 2);
				this.gridHelper.updateMatrix();
			}
		},

		/**
		 * 删除网格平面
		 */
		removeGridPlane () {
			if (this.gridHelper) {
				this.removeSceneObj(this.gridHelper);
				this.gridHelper = null;
				this.requestRender();
			}
		},

		/**
		 * 创建部件边框
		 */
		createWidgetBorder () {
			// 在3D空间中表示一个盒子或立方体。其主要用于表示物体在世界坐标中的边界框
			const box = new THREE.Box3();
			// 通过传入的object3D对象来返回当前模型的最小大小，值可以使一个mesh也可以使group
			const group = new THREE.Group();
			raycasterObjs.forEach(mesh => group.add(mesh));
			this.getScene().add(group);
			box.expandByObject(group);
			const boxHelper = new THREE.BoxHelper(group, 0xffff00);
			logger.log('boxHelper: ', boxHelper);
			this.getScene().add(boxHelper);
			boxHelper.geometry.computeBoundingBox();
			logger.log('boundingBox: ', boxHelper.geometry.boundingBox);
			const centerVector = boxHelper.geometry.boundingSphere.center;
			logger.log('centerVector: ', centerVector);
		},

		/**
		 * 位置数组 转 文本（Rhino grasshopper call）
		 * @param positionArray
		 * @return {string}
		 */
		positionArray2Txt (positionArray) {
			const points = [];
			for (let i = 0; i < positionArray.length; i += 3) {
				const x = positionArray[i];
				const y = positionArray[i + 1];
				const z = positionArray[i + 2];
				points.push({ x, y, z });
			}
			return JSON.stringify(points);
		},

		/**
		 * 通过mesh获取世界坐标
		 */
		getWorldCoordinatesByMesh (mesh) {
			// 该语句默认在threeJs渲染的过程中执行  如果想获得世界矩阵属性、世界位置属性等属性，需要手动更新
			mesh.updateMatrixWorld(true);
			// 声明一个三维向量用来保存网格模型的世界坐标
			const worldPosition = new THREE.Vector3();
			// 获得世界坐标，执行getWorldPosition方法，提取网格模型的世界坐标结果保存到参数worldPosition中
			mesh.getWorldPosition(worldPosition);
			logger.log('查看网格模型', mesh);
			logger.log('查看网格模型世界坐标', worldPosition);
			logger.log('查看网格模型本地坐标', mesh.position);
			return worldPosition;
		},

		/**
		 * 获取点坐标
		 * @param vector3
		 * @return {{}}
		 */
		getPoint (vector3) {
			return pick(vector3, ['x', 'y', 'z']);
		},

		/**
		 * 获取点击对象
		 */
		getIntersects (clientX, clientY) {
			return this.getCanvasIntersects({ clientX, clientY }, this.getScene(), this.curCamera, this.getCanvas());
		},

		/**
		 * 获取拾取对象
		 */
		getRaycasterObjs () {
			return raycasterObjs;
		},

		/**
		 * 当canvas不占满整屏时射线拾取存在偏差，获取点击对象
		 * @param event 事件对象
		 * @param scene 场景对象
		 * @param camera 镜头对象
		 * @param canvas 绘制盒子
		 * @param isFilter 是否过滤掉隐藏的部件
		 */
		getCanvasIntersects (event, scene, camera, canvas, isFilter = true) {
			preventDefaults(event);
			// 获取元素的大小及其相对于视口的位置
			const getBoundingClientRect = canvas.getBoundingClientRect();
			// 屏幕坐标转标准设备坐标
			const x = ((event.clientX - getBoundingClientRect.left) / canvas.offsetWidth) * 2 - 1; // 标准设备横坐标
			const y = -((event.clientY - getBoundingClientRect.top) / canvas.offsetHeight) * 2 + 1; // 标准设备纵坐标
			const vector = new THREE.Vector3(x, y, 1); // 标准设备坐标
			// 创建射线投射器对象
			const raycaster = new THREE.Raycaster();
			// raycaster.params = {
			// 	Mesh: {},
			// 	Line: { threshold: 3 },
			// 	Line2: { threshold: 10 },
			// 	LOD: {},
			// 	Points: { threshold: 1 },
			// 	Sprite: {},
			// };
			raycaster.params.Line = { threshold: 3 };
			raycaster.params.Line2 = { threshold: 10 };
			raycaster.params.Points = { threshold: 1 };
			raycaster.setFromCamera(vector, camera);
			// 返回射线选中的对象 第二个参数如果不填 默认是false
			const objects = [...raycasterObjs];
			if (this.gridHelper && this.curDraw !== DrawEnum.point) {
				objects.push(this.gridHelper);
			}
			let intersects = raycaster.intersectObjects(objects, true);
			// 过滤掉隐藏的部件
			if (isFilter) {
				intersects = intersects.filter(item => item.object && item.object.visible);
			}
			// 返回选中的对象数组
			return intersects;
		},

		/**
		 * 当canvas不占满整屏时射线拾取存在偏差，获取点击管道对象
		 * @param event 事件对象
		 */
		getPipeLineIntersects (event) {
			if (event && event.preventDefault) {
				event.preventDefault();
			}
			const canvas = this.getCanvas();
			// 获取元素的大小及其相对于视口的位置
			const getBoundingClientRect = canvas.getBoundingClientRect();
			// 屏幕坐标转标准设备坐标
			const x = ((event.clientX - getBoundingClientRect.left) / canvas.offsetWidth) * 2 - 1; // 标准设备横坐标
			const y = -((event.clientY - getBoundingClientRect.top) / canvas.offsetHeight) * 2 + 1; // 标准设备纵坐标
			const vector = new THREE.Vector3(x, y, 1); // 标准设备坐标
			// 创建射线投射器对象
			const raycaster = new THREE.Raycaster();
			raycaster.setFromCamera(vector, this.curCamera);
			// 返回射线选中的对象 第二个参数如果不填 默认是false
			const objects = [];
			this.getScene().children.forEach((item) => {
				if (item.groupName === 'result_pipe_line') {
					objects.push(item);
				}
			});
			if (objects.length === 0) {
				return [];
			}
			const intersects = raycaster.intersectObjects(objects, true);
			// 返回选中的对象数组
			return intersects;
		},

		/**
		 * 重置
		 */
		async reset (clearAll = false) {
			if (clearAll) {
				// 清空场景
				this.clearSceneAll();
				this.clearRaycasterAll(raycasterObjs);
			} else {
				this.clearLimitObj();
				// 清理结果文件
				this.clearResult();
			}

			// 重置data所有属性
			const data = deepCopy(defaultData);
			for (const $dataKey in this.$data) {
				if (hasOwnProperty(this.$data, $dataKey) && hasOwnProperty(data, $dataKey)) {
					this.$data[$dataKey] = data[$dataKey];
				}
			}

			// 重置全局对象
			if (drawControl) {
				drawControl.dispose?.();
				drawControl = null;
			}
			raycasterObjs = [];

			delCallback = null;
			enterCallback = null;
			escCallback = null;
			ctrlSCallback = null;
			ctrlCallback = null;
			ctrlZCallback = null;
			dragCallback = null;

			this.requestRender();
		},

		/**
		 * 销毁数据
		 */
		destroy () {
			this.$nextTick(() => {
				this.$destroy();
			});
		},
	},
	beforeDestroy () {
		this.reset(true);
		removeEventHandler(window, 'keydown', onKeydown);
		removeEventHandler(window, 'resize', this.onMainCanvasResize);
		const renderer = this.getRenderer();
		if (renderer) {
			removeEventHandler(renderer.domElement, 'click', this.onMouseClick);
			removeEventHandler(renderer.domElement, 'wheel', this.onMouseWheel);
			removeEventHandler(renderer.domElement, 'mousemove', this.onMouseMove);

			const gl = renderer.domElement.getContext('webgl');
			gl && gl.getExtension('WEBGL_lose_context').loseContext();
		}
		this.clearRenderer();
		this.clearAxesRenderer();
		this.destroyScene();
		THREE.Cache.clear();
		cancelAnimationFrame(animationId); // 去除animationFrame
		this.destroy();
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/mixins/variables.less";

canvas {
	display: block;
}

#mainCanvas {
	width: 100%;
	height: 100%;
}

#arrowCanvas {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 150px;
	height: 150px;
	z-index: 100;
}

select {
	width: 170px;
}

.view-container {
	position: relative;

	.view-loading {
		top: 50%;
		left: 50%;
		z-index: 1;
		width: 200px;
		font-weight: bold;
		position: absolute;
		transform: translate(-50%, -50%);

		/deep/ .ant-spin-text {
			display: block;
			font-size: 24px;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
			font-weight: bold;
			color: @primary;
		}

		/deep/ .ant-spin-dot-item {
			opacity: 1;
			background-color: @primary;
		}
	}

	.scale-plate {
		position: absolute;
		bottom: 0;
		pointer-events: none;
		.disable-selection();
	}

	.scalar-bar {
		position: absolute;
		top: 88%;
		width: 100%;
		height: 36px;
		line-height: 36px;

		.box {
			display: flex;
			justify-content: center;

			.row {
				display: flex;
				flex-direction: column;
				align-items: center;

				img {
					width: 14px;
					margin: 0 3px;
					cursor: pointer;
				}

				.link {
					cursor: pointer;
					margin-left: 5px;
					height: 36px;
					color: rgba(0, 0, 0, 0.65);
					border-bottom: 1px solid #CCCCCC;
				}

				.text {
					display: inline-block;
					width: 80px;
				}
			}
		}
	}
}

.js-view {
	position: relative;
	top: 0;
	left: 0;
	width: calc(100vw - 320px);
	min-width: 700px;
	height: calc(100vh - 52px);

	.js-frame {
		display: flex;
		justify-content: center;
		position: absolute;
		top: 0;
		width: 100%;
		background: transparent;
	}

	.lutImage {
		width: 100%;
		height: 20px;
		border: solid 1px #EEE;
		transform: rotate(-90deg);
	}
}

/deep/ .selectBox {
	border: 1px solid #55AAFF;
	background-color: rgba(75, 160, 255, 0.3);
	position: fixed;
}
</style>
