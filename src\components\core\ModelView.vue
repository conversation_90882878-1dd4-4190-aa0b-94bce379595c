<template>
	<div class="view-container">
		<a-spin class="view-loading" size="large" :tip="tip" :spinning="loading" />
		<div
			class="js-view"
			ref="viewContainer"
			:style="{ background: background, width: jsViewWidth }"
		>
			<!--<canvas id="mainCanvas"></canvas>
			<canvas id="arrowCanvas"></canvas>
			<div id="label"></div>-->
		</div>
	</div>
</template>

<script>
/* ===================================
 * 模型视图渲染器
 * Updated by cjking on 2022/07/20.
 * Copyright 2022, Inc.
 * =================================== */
import * as THREE from 'three';
import { mapGetters } from 'vuex';
import { getText, sleep } from '@/utils/utils';
import { logger } from '@/utils/logger';
import { ViewEnvironment } from '@/components/core/ThreeJsUtils';
import { mixinRhino } from '@/mixins/modules/rhino';
import * as Comlink from 'comlink';
import { SelectModeEnum } from '@/constants';

export default {
	name: 'ModelView',
	mixins: [mixinRhino],
	components: {},
	data () {
		return {
			tip: '',
			loading: false,
			loadingCount: 0,
			background: [0.2, 0.2, 0.2, 0.2],
			jsViewWidth: 'calc(100vw - 320px)',
		};
	},
	computed: {
		...mapGetters(['userInfo']),
	},
	mounted () {
		const viewContainer = this.$refs.viewContainer;
		this.$nextTick(async () => {
			await sleep(10);

			// Print friendly welcoming messages
			this.threeJsViewport = new ViewEnvironment(viewContainer);
			logger.log('threeJsViewport: ', this.threeJsViewport);
		});
	},
	methods: {
		/**
		 * 初始化worker
		 * @return {Promise<void>}
		 */
		async initWorker () {
			const createWorker = async (params = {}) => {
				this.worker = new Worker('/js/occ.worker.js', { type: 'module' });
				this.occWorker = Comlink.wrap(this.worker);
				const result = await this.occWorker.initOCC(params);
				logger.log('来自子线程返回的结果 result: ', result);
				return result;
			};
			await createWorker();
		},

		async loadDataV3 (file) {
			// if (result?.error) {
			// 	this.$modal.error({ content: result.error });
			// }
			//
			// this.initIds();
			// this.openLoading();
			//
			// this.threeJsViewport.initData(result);

			// 初始化worker
			await this.initWorker();

			const fileText = await getText(file);

			const response = await this.occWorker.initData({ fileName: file.name, fileText });
			if (response) this.threeJsViewport.combineAndRenderShapes(response);

			setTimeout(() => {
				this.worker.terminate(); // 关闭worker
				this.occWorker = null;
			}, 1000);
		},

		initIds () {
			this.projectId = this.$route.params.id;
			this.taskId = this.$route.params.taskId;
		},

		async initCube (threeJsViewport) {
			const loader = new THREE.ObjectLoader();
			loader.load('/models/json/cubeAxes.json', (group) => {
				threeJsViewport.cubeAxesObject = group;
				threeJsViewport.environment.scene.add(threeJsViewport.cubeAxesObject);
				threeJsViewport.environment.zoomCameraToSelection(threeJsViewport.cubeAxesObject);
				threeJsViewport.active = true;
				threeJsViewport.animate();
			});
		},

		/**
		 * 开启加载中
		 */
		openLoading (loadingMsg = '', resetCount = false) {
			this.tip = loadingMsg || '加载中...';
			this.loading = true;
			if (resetCount) {
				this.loadingCount = 0;
			}
			this.loadingCount++;
		},

		/**
		 * 关闭加载中
		 */
		closeLoading () {
			this.loadingCount--;
			if (this.loadingCount <= 0) {
				this.loadingCount = 0;
				this.loading = false;
				this.tip = '';
			}
		},
	},
	beforeDestroy () {
		// 页面卸载时关闭子线程
		this.worker?.terminate();
	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/base.less";
@import "~@/assets/styles/mixins/variables.less";

//canvas {
//	display: block;
//}

//#mainCanvas {
//	width: 100%;
//	height: 100%;
//	background-image: linear-gradient(180deg, #E0E0FF 0%, #E3EAEE 100%);
//}

//#arrowCanvas {
//	position: absolute;
//	left: 0;
//	bottom: 0;
//	width: 150px;
//	height: 150px;
//	z-index: 100;
//}

//select {
//	width: 170px;
//}

.view-container {
	position: relative;
	height: 100%;

	.view-loading {
		top: 50%;
		left: 50%;
		z-index: 1;
		width: 200px;
		font-weight: bold;
		position: absolute;
		transform: translate(-50%, -50%);

		/deep/ .ant-spin-text {
			display: block;
			font-size: 24px;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
			font-weight: bold;
			color: @primary;
		}

		/deep/ .ant-spin-dot-item {
			opacity: 1;
			background-color: @primary;
		}
	}
}

.js-view {
	position: relative;
	width: calc(100vw - 320px);
	min-width: 700px;
	height: 100%;
}
</style>
