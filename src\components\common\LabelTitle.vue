<template>
	<div class="attr-item">
		<div class="attr-item-title" v-if="label">
			<h3 style="margin-bottom:0px;margin-right: 6px ">
				<b><span v-if="required" style="margin-left: -6px;color:red">*</span>{{ label }}{{ label ? ':' : '' }}</b>
			</h3>
			<a-tooltip v-if="tip" placement="top">
				<template slot="title">
					{{ tip }}
				</template>
				<a-icon type="question-circle" style="font-size: 18px"></a-icon>
			</a-tooltip>
		</div>
	</div>
</template>

<script>
/* ===================================
 * 属性项
 * Created by zhangzheng on 2022/4/26.
 * Copyright 2022, Inc.
 * =================================== */
export default {
	components: {
	},
	props: {
		required: {
			type: Boolean,
			required: false,
			default: false,
		},
		label: {
			type: String,
			required: false,
			default: '',
		},
		tip: {
			type: String,
			required: false,
			default: '',
		},
	}
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/layout.less";
@import "~@/assets/styles/mixins/variables.less";

.attr-item {
	margin-bottom: 6px;
	/deep/ .ant-slider-mark-text {
		top: -34px
	}
	&-title {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 6px;
	}
}
</style>
