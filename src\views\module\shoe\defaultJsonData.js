export const defaultPartData = [
	{
		title: '标准鞋楦文件',
		key: 'norm',
		type: 'parts',
		exts: ['3dm', 'stl'],
		value: [],
		visible: true,
		color: '#CCCCCC',
		opacity: 100,
		disabled: false,
		submitLoading: false,
	},
	{
		title: '左脚脚型文件',
		key: 'left',
		type: 'parts',
		exts: ['stl'],
		value: [],
		visible: true,
		color: '#00FF00',
		opacity: 100,
		disabled: false,
		submitLoading: false,
	},
	{
		title: '右脚脚型文件',
		key: 'right',
		type: 'parts',
		exts: ['stl'],
		value: [],
		visible: true,
		color: '#0090FF',
		opacity: 100,
		disabled: false,
		submitLoading: false,
	},
];

export const defaultJsonData = [
	{
		label: '选择左右脚设置对应参数',
		key: 'left_foot',
		value: 1,
		tip: '',
		type: 'radio',
		show: true,
		options: [
			{
				label: '左脚',
				value: 1,
			},
			{
				label: '右脚',
				value: 0,
			}
		]
	},
	{
		type: 'divider'
	},
	{
		label: '设置右脚参数',
		type: 'title',
	},
	{
		label: '标准脚长',
		key: 'l_lenth_of_shoe',
		value: undefined,
		tip: '',
		type: 'slider',
		step: 1,
		hideSlider: true,
		show: true,
	},
	// lengthen,加长量：0～100，整型。步长：1
	{
		label: '加长量',
		key: 'l_lengthen',
		value: 38,
		tip: '',
		type: 'slider',
		step: 1,
		min: 0,
		max: 100,
		show: true,
	},
	// narrow,缩窄量：0～10，整型。步长：1
	{
		label: '缩窄量',
		key: 'l_narrow',
		value: 10,
		tip: '',
		type: 'slider',
		step: 0.1,
		min: 0,
		max: 10,
		show: true,
	},
	// heighten,增高量：0.0～20.0，浮点数，保留小数点后一位。步长：0.1。
	{
		label: '增高量',
		key: 'l_heighten',
		value: 15,
		tip: '',
		type: 'slider',
		step: 0.1,
		min: 0,
		max: 20,
		show: true,
	},
	// smooth1,顺滑参数1：0～100，整型。步长：1。
	{
		label: '顺滑参数1',
		key: 'l_smooth1',
		value: 25,
		tip: '',
		type: 'slider',
		step: 1,
		min: 0,
		max: 100,
		show: true,
	},
	// smooth2,顺滑参数2：0～30，整型。步长：1。
	{
		label: '顺滑参数2',
		key: 'l_smooth2',
		value: 10,
		tip: '',
		type: 'slider',
		step: 1,
		min: 0,
		max: 30,
		show: true,
	},
	// regulate,调节参数：1.00~3.00，浮点数，保留小数点后两位。步长：0.01.
	{
		label: '微调量',
		key: 'l_regulate',
		value: 0.03,
		tip: '',
		type: 'slider',
		step: 0.01,
		min: 1,
		max: 3,
		show: true,
	},
	// lenth_of_shoe 参数名：标准脚长 输入：input 框 类型：number，浮点数 限制：无，用户自定义输出
	{
		label: '标准脚长',
		key: 'r_lenth_of_shoe',
		value: undefined,
		tip: '',
		type: 'slider',
		step: 1,
		slider: false,
		show: false,
	},
	// lengthen,加长量：0～100，整型。步长：1
	{
		label: '加长量',
		key: 'r_lengthen',
		value: 38,
		tip: '',
		type: 'slider',
		step: 1,
		min: 0,
		max: 100,
		show: false,
	},
	// narrow,缩窄量：0～10，整型。步长：1
	{
		label: '缩窄量',
		key: 'r_narrow',
		value: 10,
		tip: '',
		type: 'slider',
		step: 0.1,
		min: 0,
		max: 10,
		show: false,
	},
	// heighten,增高量：0.0～20.0，浮点数，保留小数点后一位。步长：0.1。
	{
		label: '增高量',
		key: 'r_heighten',
		value: 15,
		tip: '',
		type: 'slider',
		step: 0.1,
		min: 0,
		max: 20,
		show: false,
	},
	// smooth1,顺滑参数1：0～100，整型。步长：1。
	{
		label: '顺滑参数1',
		key: 'r_smooth1',
		value: 25,
		tip: '',
		type: 'slider',
		step: 1,
		min: 0,
		max: 100,
		show: false,
	},
	// smooth2,顺滑参数2：0～30，整型。步长：1。
	{
		label: '顺滑参数2',
		key: 'r_smooth2',
		value: 10,
		tip: '',
		type: 'slider',
		step: 1,
		min: 0,
		max: 30,
		show: false,
	},
	// regulate,调节参数：1.00~3.00，浮点数，保留小数点后两位。步长：0.01.
	{
		label: '微调量',
		key: 'r_regulate',
		value: 0.03,
		tip: '',
		type: 'slider',
		step: 0.01,
		min: 1,
		max: 3,
		show: false,
	},
];
