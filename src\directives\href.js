/* ===================================
 * 指令：路由跳转及防止页面回滚
 * Created by cjking on 2021/04/08.
 * Copyright 2021, Inc.
 * =================================== */
import router from '@/router';
import { noop } from '@/utils/utils';

export const AHrefDirective = Vue => Vue.directive('href', {
	bind (el, binding, vNode) {
		const path = binding.value;
		if (!path) {
			el.setAttribute('href', 'javascript:void(0);');
			return;
		}
		const handler = () => {
			router.push(path);
		};
		el.removeEventListener('click', noop);
		el.addEventListener('click', handler);
	},
});
