<template>
	<div :style="divStyle">
		<a-tooltip :placement="placement" v-if="tooltip">
			<template slot="title">
				<span>{{ value }}</span>
			</template>
			<span :style="vStyle" class="no-wrap">
				{{ value | Ellipsis(length, showEllipsis) }}
			</span>
		</a-tooltip>
		<template v-else>
			<span :style="vStyle" class="no-wrap">
				{{ value | Ellipsis(length, showEllipsis) }}
			</span>
		</template>
	</div>
</template>

<script>
/* ===================================
 * 拦截器-字符串超长截取省略号显示
 * Created by cjking on 2021/04/07.
 * Copyright 2021, Inc.
 * =================================== */
export default {
	name: 'JEllipsis',
	props: {
		value: {
			type: String,
			required: false,
			default: '',
		},
		length: {
			type: Number,
			required: false,
			default: 25,
		},
		placement: {
			type: String,
			required: false,
			default: 'top',  // 气泡框位置，可选 top left right bottom topLeft topRight bottomLeft bottomRight leftTop leftBottom rightTop rightBottom
		},
		showEllipsis: {
			type: Boolean,
			required: false,
			default: true,
		},
		tooltip: {
			type: Boolean,
			required: false,
			default: true,
		},
		divStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
		vStyle: {
			type: Object,
			required: false,
			default: () => {},
		},
	},
};
</script>
<style lang="less" scoped>
.no-wrap {
	white-space: nowrap;
}
</style>
