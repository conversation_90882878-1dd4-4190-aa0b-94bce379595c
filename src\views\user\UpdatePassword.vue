<template>
	<div class="user-layout-forget">
		<a-form-model ref="ruleForm" :model="form" :rules="rules">
			<div class="user-layout-forget-title">
				<h2>修改密码</h2>
			</div>

			<a-form-model-item prop="password" has-feedback>
				<a-input type="password" size="large" v-model.trim="form.password" placeholder="请输入新密码">
					<a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }" />
				</a-input>
			</a-form-model-item>

			<a-form-model-item prop="confirmPassword" has-feedback>
				<a-input type="password" size="large" v-model.trim="form.confirmPassword" placeholder="确认新密码">
					<a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }" />
				</a-input>
			</a-form-model-item>

			<a-row :gutter="24">
				<a-col :span="24">
					<a-button size="large" type="primary" class="forget-button" @click="handleConfirm">确认</a-button>
				</a-col>
			</a-row>
		</a-form-model>
	</div>
</template>

<script>
/* ===================================
 * 通过邮件修改密码
 * Created by lzz on 2020/12/16.
 * Copyright 2020, Inc.
 * =================================== */
import { logger } from '@/utils/logger';
import { updatePasswordByEmail } from '@/api';
import { deleteNullAttr } from '@/utils/utils';
import { formValidate, PasswordReg } from '@/utils/validate';

export default {
	name: 'UpdatePassword',
	data () {
		return {
			labelCol: {
				xs: { span: 24 },
				sm: { span: 5 },
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: { span: 16 },
			},
			form: {
				password: '',       // 新密码
				confirmPassword: '', // 确认新密码
			},
			rules: {
				password: formValidate.customCondition({
					required: true,
					message: '请输入新密码，格式：长度为8~16位，数字、字母、特殊字符至少包含两种！',
					condition: () => PasswordReg.test(this.form.password),
				}),
				confirmPassword: formValidate.compare({
					required: true,
					message: '确认新密码',
					lastValue: () => this.form.password,
				}),
			},
		};
	},
	methods: {
		/**
		 * 表单验证-确定事件
		 */
		handleConfirm () {
			this.$refs.ruleForm.validate(async valid => {
				if (!valid) return;
				try {
					const params = {
						ticket: this.$route.query.ticket,           // 服务端认证key
						password: this.form.password,               // 新密码
					};
					logger.log('修改密码提交数据 params: ', params);
					const res = await updatePasswordByEmail(deleteNullAttr(params));
					if (res?.success) {
						this.$message.success('修改密码成功');
						setTimeout(() => {
							this.$router.push('/user/login');
						}, 1000);
					}
				} catch (err) {
					logger.error('修改密码 err: ', err);
				}
			});
		},
	},
};
</script>

<style lang="less" scoped>
.user-layout-forget {
	width: 315px;

	&-title {
		text-align: left;
	}

	button.forget-button {
		padding: 0 15px;
		font-size: 16px;
		height: 40px;
		width: 100%;
		margin-top: 5px;
	}
}
</style>
