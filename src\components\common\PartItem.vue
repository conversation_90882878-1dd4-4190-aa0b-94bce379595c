<template>
	<div>
		<div class="part-label" v-if="title">
			<div class="part-label-title">
				<h4 style="margin-bottom:0">
					<b>
						{{ title }}
					</b>
				</h4>
			</div>
			<div class="part-label-content">
				<slot name="titleExtra">
					<!-- <FileLoader @ok="uploadCallback" :style="{width: '100%'}" :disabled="disabled" :loading="submitLoading" /> -->
				</slot>
			</div>
		</div>
		<div class="part-item" v-if="label">
			<div class="part-item-title">
				<label>
					<b>
						<JEllipsis :value="label" :length="9" />
					</b>
				</label>
			</div>
			<div class="part-item-content">
				<div @click="handleVisibleChange">
					<svg-icon
						:type="myConfig.visible ? 'param-visible': 'param-hidden'"
						:class-name="myConfig.visible ? 'param-visible': 'param-hidden'"
						:vStyle="{width:'20px', height:'20px'}"
					/>
				</div>
				<input type="color" v-model="myConfig.color" @input="handleColorChange">
				<a-popover :title="null">
					<template #content>
						<div style="display:flex;justify-content:center;align-items:center">
							<a-slider
								:min="0"
								:max="100"
								:step="1"
								v-model="myConfig.opacity"
								:style="{ width: '100px', margin: '10px 0 0 10px' }"
								@change="handleOpacityChange"
							/>
							<div style="margin-left:20px;margin-top:8px">{{ config.opacity }}%</div>
						</div>
					</template>
					<svg-icon
						type="param-opacity"
						class-name="param-opacity"
						:vStyle="{width:'20px',height:'20px'}"
					/>
				</a-popover>
				<!-- <div @click="handlePartRemove">
						<svg-icon
							type="remove"
							class-name="remove"
							:vStyle="{width:'20px',height:'20px'}"
						></svg-icon>
					</div> -->
			</div>
		</div>
		<slot name="divider">

		</slot>
	</div>
</template>

<script>
/* ===================================
 * 部件属性项
 * Created by zhangzheng on 2022/03/02.
 * Copyright 2022, Inc.
 * =================================== */
import JEllipsis from '@/components/common/JEllipsis';

export default {
	props: {
		title: {
			type: [String, undefined],
			required: false,
			default: '',
		},
		label: {
			type: [String, undefined],
			required: false,
			default: '',
		},
		config: {
			type: Object,
			default: () => {
				return {
					color: '#00FF00',
					visible: true,
					opacity: 100,
				};
			},
		},
		index: {
			type: Number,
			default: 0,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
		// eslint-disable-next-line vue/require-default-prop
		value: [Object, Array, Boolean, String, Number],
	},
	data () {
		return {
			myConfig: this.config,
		};
	},
	components: {
		JEllipsis,
	},
	watch: {
		config: {
			immediate: true,
			deep: true,
			handler () {
				this.myConfig = this.config;
			},
		},
	},
	methods: {

		handleVisibleChange () {
			this.config.visible = !this.config.visible;
			this.$emit('change', { config: this.config, index: this.index, type: 'visible' });
		},

		handleColorChange (e) {
			this.config.color = e.target.value;
			this.$emit('change', { config: this.config, index: this.index, type: 'color' });
		},

		handleOpacityChange (value) {
			this.config.opacity = value;
			this.$emit('change', { config: this.config, index: this.index, type: 'opacity' });
		},

		handlePartRemove () {
			if (this.disabled) {
				this.$message.warning('只有创建者可以删除');
				return;
			}
			this.$emit('remove', this.index);
		},

	},
};
</script>

<style lang="less" scoped>
@import "~@/assets/styles/mixins/variables.less";
// * {
// 	border: solid 1px red;
// }
.part-label {
	display: flex;
	justify-content: space-between;
	align-items: center;
	justify-items: center;
	height: 32px;
	/deep/ .ant-slider-mark-text {
		top: -34px
	}
	&-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	&-content {
		padding: 10px;
		height: 20px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	&-icon {
		width: 20px;
		height: 20px;
	}

	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}

	.svg-icon {
		color: @primary;
	}
}

.part-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 20px;

	/deep/ .ant-slider-mark-text {
		top: -34px
	}

	&-title, &-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	&-content {
		padding: 10px;
		height: 20px;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.svg-icon, input {
			margin-left: 10px;
			cursor: pointer;
		}
	}

	&-icon {
		width: 20px;
		height: 20px;
	}
	input[type="color"] {
		width: 20px;
		height: 22px;
		border: 0;
		background-color: transparent;
	}
	.svg-icon {
		color: @primary;
	}
}

.param-active {
	border-radius: 2px;
	border: 1px solid @primary;
}
</style>
