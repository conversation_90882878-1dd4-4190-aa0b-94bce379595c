<template>
	<div>
		<p
			class="m-content overflow-line"
			:style="{ width: contentWidth + 'px', '-webkit-line-clamp': limitLine, margin: 0 }"
			v-html="text"
		></p>
		<template v-if="showMore">
			<button
				type="button"
				class="btn-more"
				v-if="isShowMore"
				@click="showMoreDesc($event)"
			>更多↓</button>
		</template>
	</div>
</template>
<script>
export default {
	name: 'LineEllipsis',
	data () {
		return {
			isShowMore: false,
			isDescStatus: true,
			contentWidth: 180,
		};
	},
	props: {
		text: {
			type: String,
			default: '',
		},
		limitLine: {
			type: Number,
			default: 6,
		},
		rowLength: {
			type: Number,
			required: false,
			default: 75,
		},
		showMore: {
			type: Boolean,
			required: false,
			default: true,
		},
	},
	mounted () {
		this.contentWidth = this.$el.clientWidth;
	},
	methods: {
		showMoreDesc (e) {
			const el = e.currentTarget;
			el.previousElementSibling.classList[
				!this.isDescStatus ? 'add' : 'remove'
			]('overflow-line');
			el.classList[this.isDescStatus ? 'add' : 'remove']('more-collapse');
			el.innerHTML = !this.isDescStatus ? '更多↓' : '收起↑';
			this.isDescStatus = !this.isDescStatus;
			this.isShowMore = true;
		},
	},
	watch: {
		text: {
			immediate: true,
			handler () {
				if (
					this.text &&
					this.text.length / this.rowLength > this.limitLine
				) {
					this.isShowMore = true;
				}
			},
		},
	},
};
</script>

<style lang="less" scoped>
.m-content {
	width: 100%;
	&.overflow-line {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3; /*设置p元素最大3行，父元素需填写宽度才明显*/
		text-overflow: ellipsis;
		overflow: hidden;
		word-wrap: break-word;
		word-break: break-all;
	}
}

.btn-more {
	float: right;
	color: #5383e7;
	background: white;
	position: relative;
	margin-top: -36px;
	padding-right: 4px;
	border: 0;
	cursor: pointer;
	outline: none;

	&.more-collapse {
		&::after,
		&::before {
			top: 2px;
			transform: rotate(180deg);
		}

		&::before {
			top: 4px;
		}
	}

	&::after,
	&::before {
		width: 0;
		height: 0;
		content: '';
		position: absolute;
		right: 0;
		top: 7px;
		border: rc(12) solid transparent;
	}

	&::after {
		border-top-color: #5383e7;
		z-index: 1;
	}

	&::before {
		border-top-color: #1c2239;
		z-index: 2;
		top: 5px;
	}
}
</style>
