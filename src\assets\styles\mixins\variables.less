/* ===================================
 * CSS 全局变量
 * Created by cjking on 2021/04/02.
 * Copyright 2021, Inc.
 * =================================== */
// Colors
@gray-base: #000;
@gray-darker: lighten(@gray-base, 13.5%); // #222
@gray-dark: lighten(@gray-base, 20%); // #333
@gray: lighten(@gray-base, 33.5%); // #555
@gray-light: lighten(@gray-base, 46.7%); // #777
@gray-lighter: lighten(@gray-base, 93.5%); // #eee

@gray-dark-3: rgba(0, 0, 0, .3);
@gray-dark-6: rgba(0, 0, 0, .6);
@gray-dark-9: rgba(0, 0, 0, .9);

@gray-dark-60: #606060;
@gray-dark-90: #909090;
@gray-dark-df: #DFDFDF;
@disabled-bg: #F5F5F5;

@primary: #6362FF;
@primary-bg: #C7D8E1;
@primary-assist: #E2F5FF;
@primary-gray-3: rgba(52, 175, 254, 0.3);
@primary-gray-4: rgba(52, 175, 254, 0.4);
@primary-gray-5: rgba(52, 175, 254, 0.5);
@disabled-text: #CCCCCC;
@primary-background: linear-gradient(180deg, #E0E0FF 0%, #E3EAEE 100%);

@blue-dark: #4C79FF;
@blue-light: #5D97FF;
@blue-primary: #0E3FFF;
@blue-primary-gray-3: rgba(14, 63, 255, 0.3);
@blue-primary-gray-4: rgba(14, 63, 255, 0.4);
@blue-primary-gray-5: rgba(14, 63, 255, 0.5);

@brand-primary: #4C79FF;
@brand-primary-gray-3: rgba(76, 121, 255, 0.3);
@brand-primary-gray-4: rgba(76, 121, 255, 0.4);
@brand-primary-gray-5: rgba(76, 121, 255, 0.5);
@brand-primary-light: #5D97FF;

@brand-success: #52C41A;

@brand-info: #4494F9;

@brand-warning-lighter: #FFF3E0;
@brand-warning-light: #FFE6BD;
@brand-warning: #FAAD14;

@brand-danger: #FF3000;

@sauce-purple: #722ED1;
@sauce-purple-gray: #722ED17F;
@sauce-purple-8: rgba(114, 46, 209, 0.8);
@sauce-purple-gray-5: rgba(114, 46, 209, 0.5);
@sauce-purple-gray-3: rgba(114, 46, 209, 0.3);

@white: #FFF;

@component-active-color: @white;
@component-active-bg: @brand-primary;
@caret-width-base: 4px;

// Scaffolding
@body-bg: @white;
@text-color: @font-color-dark; // 主文本色
@title-color: #111;
@text-color-secondary: rgba(0, 0, 0, 0.65); // 次文本色
@link-color: #4C79FF; // 链接色
@link-hover-color: #5D97FF;
@link-hover-decoration: none;
@box-bg: #F1F3F4;

@header-height: 55px;
@menu-width: 160px;

// line
@line-border: #BBC4D1;

// font color
@font-color-disabled: #CFD5DE;
@font-color-lighter: #BBC4D1;
@font-color-light: #828282; // 次要文字颜色
@font-color-base: #525252;
@font-color-dark: #323232;
@font-color-default: #323232;

@font-size-small: 12px;
@font-size-base: 14px;
@font-size-large: 16px;
@font-size-md: 18px;

// font-weight
@font-weight-lighter: lighter;
@font-weight-normal: normal;
@font-weight-bold: bold;

// Typography
@font-family-sans-serif: "PingFang SC", "Helvetica Neue", "Helvetica", "STHeitiSC-Light", "Arial", sans-serif;
@font-family-HanHei-serif: "HanHei SC", "PingFang SC", "Helvetica Neue", "Helvetica", "STHeitiSC-Light", "Arial", sans-serif;
@font-family-serif: Georgia, "Times New Roman", "Times", serif;
//** Default monospace fonts for `<code>`, `<kbd>`, and `<pre>`.
@font-family-monospace: "Myriad Set Pro", "Helvetica Neue", Helvetica, monospace;
@font-family-base: @font-family-sans-serif;

@font-size-h1: floor((@font-size-base * 2.6)); // ~36px
@font-size-h2: floor((@font-size-base * 2.15)); // ~30px
//@font-size-h3:            ceil((@font-size-base * 1.7)); // ~24px
@font-size-h3: @font-size-md; // 18px

@font-size-h4: ceil((@font-size-base * 1.125)); // ~16px
@font-size-h5: @font-size-base;
@font-size-h6: ceil((@font-size-base * 0.85)); // ~12px

@line-height-base: 1.428571429; // 20/14
@line-height-computed: floor((@font-size-base * @line-height-base)); // ~20px

//** By default, this inherits from the `<body>`.
@headings-font-family: @font-family-HanHei-serif;
@headings-font-weight: @font-weight-bold;
@headings-line-height: 1.1;
@headings-color: inherit;

// Components
@padding-base-vertical: 5px;
@padding-base-horizontal: 15px;

@padding-large-vertical: 8px;
@padding-large-horizontal: 24px;

@padding-small-vertical: 5px;
@padding-small-horizontal: 10px;

@padding-xs-vertical: 1px;
@padding-xs-horizontal: 5px;

@line-height-large: 2.12; // extra decimals for Win 8.1 Chrome
@line-height-small: 1.5;

@border-radius-base: 4px;
@border-radius-large: 8px;
@border-radius-small: 2px;

// corner radius
@radius-small: 2px;
@radius-base: 4px;
@radius-large: 8px;
@radius-largest: 16px;
@radius-circle: 100%;
@radius-zero: 0;

@border-color: #EDEDED;

// Tables
@table-cell-padding: 8px;
@table-condensed-cell-padding: 5px;
@table-height: 45px;
@table-title-height: 40px;

@table-bg: #FFFFFF;
@table-title-bg: #F2F5F6;
@table-color: #323232;
@table-title-color: #525252;
@table-border-color: #EEE;
@table-link-color: #4C79FF;
@table-link-color-hover: #5584FF;

@table-font-size: 13px;
@table-font-weight: 600;
@table-title-font-weight: 500;

// Buttons
@btn-font-weight: 400;

@btn-default-color: #525252;
@btn-default-bg: #FFF;
@btn-default-border: #D6D9DF;

@btn-primary-color: #FFF;
@btn-primary-bg-light: @brand-primary-light;
@btn-primary-bg: @brand-primary;
@btn-primary-bg-dark: #3F6DF7;
@btn-primary-border-light: @btn-primary-bg-light;
@btn-primary-border: @btn-primary-bg;

@btn-success-color: #FFF;
@btn-success-bg: @brand-success;
@btn-success-border: darken(@btn-success-bg, 5%);

@btn-info-color: #FFF;
@btn-info-bg: #54C8ED;
@btn-info-border: @btn-info-bg;

@btn-warning-color: #FFF;
@btn-warning-bg: #FC9B5E;
@btn-warning-border: darken(@btn-warning-bg, 5%);

@btn-danger-color: #FFF;
@btn-danger-bg: @brand-danger;
@btn-danger-border: darken(@btn-danger-bg, 5%);

@btn-link-disabled-color: @gray-light;

// Allows for customizing button radius independently from global border radius
@btn-border-radius-base: @border-radius-base;
@btn-border-radius-large: @border-radius-large;
@btn-border-radius-small: @border-radius-small;

@btn-width-base: 200px;
@btn-width-long: @btn-width-base*2;

@btn-padding-base-vertical: 7px;
@btn-padding-base-horizontal: 15px;

// Forms
@input-bg: #FFF;
@input-bg-disabled: @gray-lighter;
@input-color: #323232;

@input-border: #D6D9DF;
@input-border-radius: @border-radius-base;
@input-border-radius-large: @border-radius-large;
@input-border-radius-small: @border-radius-small;
@input-border-focus: @link-color;
@input-color-placeholder: #999;

@input-height-base: (@line-height-computed + @padding-base-vertical * 2) + 1;
@input-height-large: (ceil(@font-size-large * @line-height-large) + (@padding-large-vertical * 2) + 2);
@input-height-small: (floor(@font-size-small * @line-height-small) + (@padding-small-vertical * 2) + 2);

@legend-color: @gray-dark;
@legend-border-color: #E5E5E5;

@input-group-addon-bg: @gray-lighter;
@input-group-addon-border-color: @input-border;
@cursor-disabled: not-allowed;

// form element
@form-height-small: 28px;
@form-height-base: 32px;
@form-height-large: 36px;

// Media queries breakpoints
@screen-xs: 480px;
@screen-xs-min: @screen-xs/1.5;
@screen-phone: @screen-xs-min;
@screen-sm: 768px;
@screen-sm-min: @screen-sm;
@screen-tablet: @screen-sm-min;
// Medium screen / desktop
@screen-md: 992px;
@screen-md-min: @screen-md;
@screen-desktop: @screen-md-min;
@screen-lg: 1200px;
@screen-lg-min: @screen-lg;
@screen-lg-desktop: @screen-lg-min;
@screen-xs-max: (@screen-sm-min - 1);
@screen-sm-max: (@screen-md-min - 1);
@screen-md-max: (@screen-lg-min - 1);
@screen-content-width: 1000px;

// Grid system
@grid-columns: 12;
@grid-gutter-width: 30px;
@grid-float-breakpoint: @screen-sm-min;
@grid-float-breakpoint-max: (@grid-float-breakpoint - 1);

// space
@spacing-1: 5px;
@spacing-2: 10px;
@spacing-3: 15px;
@spacing-4: 20px;
@spacing-5: 25px;
@spacing-6: 30px;
@spacing-7: 35px;
@spacing-8: 40px;
@spacing-9: 45px;
@spacing-10: 50px;

// Container sizes
@container-min-width: 1200px;
@container-tablet: (720px + @grid-gutter-width);
@container-sm: @container-tablet;
@container-desktop: (970px + @grid-gutter-width);
@container-md: @container-desktop;
@container-large-desktop: (1170px + @grid-gutter-width);
@container-lg: @container-large-desktop;
@container-bg: #FFF;
@container-border-color: #E8ECEF;
@container-border-radius: @border-radius-base;

// Navs
@nav-height: 40px;

@nav-disabled-link-color: @gray-light;
@nav-disabled-link-hover-color: @gray-light;

// Tabs
@tab-height: @nav-height;
@tab-font-weight: 600;
@tab-border-color: #DDD;
@tab-default-color: #606060;
@tab-active-color: #4C79FF;

// Pagination
@pagination-color: #555;
@pagination-bg: #FFF;
@pagination-border: #CCC;
@pagination-total-color: @gray;

@pagination-hover-color: @link-hover-color;
@pagination-hover-bg: @gray-lighter;
@pagination-hover-border: #DDD;

@pagination-active-color: @link-hover-color;
@pagination-active-bg: @brand-primary;
@pagination-active-border: @brand-primary;

@pagination-disabled-color: #DDD;
@pagination-disabled-bg: #FFF;
@pagination-disabled-border: #DDD;

// Pager
@pager-bg: @pagination-bg;
@pager-border: @pagination-border;
@pager-border-radius: 15px;

@pager-hover-bg: @pagination-hover-bg;

@pager-active-bg: @pagination-active-bg;
@pager-active-color: @pagination-active-color;

@pager-disabled-color: @pagination-disabled-color;

// Form states and alerts
@state-success-text: #5CB85C;
@state-success-bg: #DFF0D8;
@state-success-border: darken(spin(@state-success-bg, -10), 5%);

@state-info-text: #1E88E5;
@state-info-bg: #D9EDF7;
@state-info-border: darken(spin(@state-info-bg, -10), 7%);

@state-warning-text: #8A6D3B;
@state-warning-bg: #FCF8E3;
@state-warning-border: darken(spin(@state-warning-bg, -10), 5%);

@state-danger-text: #D9534F;
@state-danger-bg: #F2DEDE;
@state-danger-border: darken(spin(@state-danger-bg, -10), 5%);

// Labels
@label-default-bg: @gray-light;
@label-primary-bg: @brand-primary;
@label-success-bg: @brand-success;
@label-info-bg: @brand-info;
@label-warning-bg: @brand-warning;
@label-danger-bg: @brand-danger;
@label-color: #FFF;
@label-link-hover-color: #FFF;

// Alerts
@alert-padding: 15px;
@alert-border-radius: @border-radius-base;
@alert-link-font-weight: 400;

@alert-success-bg: @state-success-bg;
@alert-success-text: @state-success-text;
@alert-success-border: @state-success-border;

@alert-info-bg: @state-info-bg;
@alert-info-text: @state-info-text;
@alert-info-border: @state-info-border;

@alert-warning-bg: @state-warning-bg;
@alert-warning-text: @state-warning-text;
@alert-warning-border: @state-warning-border;

@alert-danger-bg: @state-danger-bg;
@alert-danger-text: @state-danger-text;
@alert-danger-border: @state-danger-border;

// Progress bars
@progress-bg: #F5F5F5;
@progress-bar-color: #FFF;
@progress-border-radius: @border-radius-base;

@progress-bar-bg: @brand-primary;
@progress-bar-success-bg: @brand-success;
@progress-bar-warning-bg: @brand-warning;
@progress-bar-danger-bg: @brand-danger;
@progress-bar-info-bg: @brand-info;

// Panels
@panel-bg: #FFF;
@panel-margin: 30px;
@panel-body-padding: 20px 0;
@panel-heading-padding: 10px 0;
@panel-foot-padding: @panel-heading-padding;
@panel-border-radius: @border-radius-base;

//** Border color for elements within panels
@panel-inner-border: #DDD;
@panel-foot-bg: #F5F5F5;

@panel-default-text: @gray-dark;
@panel-default-border: #DDD;
@panel-default-heading-bg: #F5F5F5;

@panel-primary-text: #FFF;
@panel-primary-border: @brand-primary;
@panel-primary-heading-bg: @brand-primary;

@panel-success-text: @state-success-text;
@panel-success-border: @state-success-border;
@panel-success-heading-bg: @state-success-bg;

@panel-info-text: @state-info-text;
@panel-info-border: @state-info-border;
@panel-info-heading-bg: @state-info-bg;

@panel-warning-text: @state-warning-text;
@panel-warning-border: @state-warning-border;
@panel-warning-heading-bg: @state-warning-bg;

@panel-danger-text: @state-danger-text;
@panel-danger-border: @state-danger-border;
@panel-danger-heading-bg: @state-danger-bg;

// Thumbnails
@thumbnail-padding: 4px;
@thumbnail-bg: @body-bg;
@thumbnail-border: #DDD;
@thumbnail-border-radius: @border-radius-base;

@thumbnail-caption-color: @text-color;
@thumbnail-caption-padding: 9px;

// Wells
@well-bg: #F5F5F5;
@well-border: darken(@well-bg, 7%);

// Badges
@badge-color: #FFF;
@badge-link-hover-color: #FFF;
@badge-bg: @gray-light;

@badge-active-color: @link-color;
@badge-active-bg: #FFF;

@badge-font-weight: 400;
@badge-line-height: 1;
@badge-border-radius: 10px;

// card
@card-bg: #FFF;
@card-border-color: #EEE;

@card-padding-vertical: @padding-base-vertical;
@card-padding-horizontal: @padding-base-horizontal;

// Code
@code-color: #C7254E;
@code-bg: #F9F2F4;

@kbd-color: #FFF;
@kbd-bg: #333;

@pre-bg: #F5F5F5;
@pre-color: @gray-dark;
@pre-border-color: #CCC;
@pre-scrollable-max-height: 340px;

// modal
@modal-font-weight: 400;

@modal-btn-bg: rgba(76, 121, 255, 1);
@modal-btn-bg-light: rgba(93, 151, 255, 1);

// Type
@component-offset-horizontal: 180px;
@text-muted: @gray-light;
@abbr-border-color: @gray-light;
@headings-small-color: @gray-light;
@blockquote-small-color: @gray-light;
@blockquote-font-size: (@font-size-base * 1.25);
@blockquote-border-color: @gray-lighter;
@page-header-border-color: @gray-lighter;
@dl-horizontal-offset: @component-offset-horizontal;
@hr-border: @gray-lighter;

// Dropdowns
@dropdown-bg: #FFF;
@dropdown-border: rgba(0, 0, 0, .15);
@dropdown-fallback-border: #CCC;
@dropdown-divider-bg: #E5E5E5;
@dropdown-link-color: #323E59;
@dropdown-link-hover-color: darken(@link-color, 5%);
@dropdown-link-hover-bg: #F1F7FF;
@dropdown-link-active-color: @component-active-color;
@dropdown-link-active-bg: @component-active-bg;
@dropdown-link-disabled-color: @gray-light;
@dropdown-header-color: @gray-light;
@dropdown-caret-color: #000;

// Tooltips
@tooltip-max-width: 200px;
@tooltip-color: #FFFFFF;
@tooltip-bg: #000000;
@tooltip-opacity: .9;
@tooltip-padding-y: 3px;
@tooltip-padding-x: 8px;
@tooltip-margin: 3px;
@tooltip-arrow-width: 5px;
@tooltip-arrow-color: @tooltip-bg;

// spin
@spin-color-red: #F95372;
@spin-color-blue: #00ABFF;
@spin-color-yellow: #E7BA08;

// z-index master list
@z-index-modal: 1100;
@z-index-modal-mask: 1000;
@z-index-modal-wrap: 1001;

@z-index-loading: 2000;
