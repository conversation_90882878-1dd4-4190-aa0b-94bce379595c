/* ===================================
 * Grid system
 * Created by cjking on 2021/04/02.
 * Copyright 2021, Inc.
 * =================================== */

.flex(@flex) {
	-webkit-box-flex: @flex;
	-webkit-flex: @flex;
	-ms-flex: @flex;
	flex: @flex;
}

.flex-display() {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
}

.flex-flow(@flex) {
	-webkit-flex-flow: @flex;
	flex-flow: @flex;
}

.flex-align-items(@flex : center) {
	-webkit-box-align: @flex;
	-webkit-align-items: @flex;
	-ms-flex-align: @flex;
	align-items: @flex;
}

.flex-direction(@flex : column) {
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-moz-box-orient: vertical;
	-moz-box-direction: normal;
	-webkit-flex-direction: @flex;
	-ms-flex-direction: @flex;
	flex-direction: @flex;
}

.flex-wrap(@flex) {
	-webkit-flex-wrap: @flex;
	-ms-flex-wrap: @flex;
	flex-wrap: @flex;
}

.flex-justify-content(@flex : center) {
	-webkit-box-pack: justify;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	-webkit-justify-content: @flex;
	-moz-justify-content: @flex;
	justify-content: @flex;
}

.flex-display-align-items-justify-content(@align : center, @justify : center) {
	.flex-display();
	.flex-align-items(@align);
	.flex-justify-content(@justify);
}
